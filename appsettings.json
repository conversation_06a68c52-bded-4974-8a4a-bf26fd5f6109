{"Application": {"Name": "نظام إدارة المبيعات", "Version": "1.0.0", "Author": "Sales Management Team", "Description": "نظام شامل لإدارة المبيعات والمخزون"}, "Database": {"ConnectionString": "Data Source=SalesManagement.db", "BackupDirectory": "Backups", "AutoBackup": {"Enabled": false, "IntervalDays": 7, "MaxBackups": 30}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "File": {"Path": "Logs/SalesSystem-{Date}.log", "RollingInterval": "Day", "RetainedFileCountLimit": 30}}, "UI": {"DefaultLanguage": "ar-SA", "DefaultTheme": "Light", "SupportedLanguages": ["ar-SA", "en-US"], "SupportedThemes": ["Light", "Dark"]}, "Business": {"DefaultCurrency": "SAR", "DefaultTaxRate": 0.15, "LowStockThreshold": 10, "ShowLowStockAlerts": true, "AutoCalculateTax": true, "AllowNegativeStock": false}, "Printing": {"DefaultPrinter": "", "PrintReceipts": true, "ReceiptTemplate": "<PERSON><PERSON><PERSON>", "PaperSize": "A4", "AutoPrintInvoices": false, "PrintBarcodeOnInvoices": true, "ShowPrintPreview": true}, "Barcode": {"DefaultType": "CODE_128", "DefaultWidth": 300, "DefaultHeight": 100, "AutoGenerateForProducts": true, "IncludeProductCodeInBarcode": true}, "Security": {"RequireLogin": false, "SessionTimeoutMinutes": 60, "PasswordMinLength": 6, "RequirePasswordComplexity": false}, "Features": {"EnableCustomers": true, "EnableSuppliers": true, "EnableEmployees": true, "EnableExpenses": true, "EnableReports": true, "EnableBackup": true, "EnableMultiCurrency": false, "EnableBarcode": true, "EnableNotifications": true}, "Notifications": {"EnableNotifications": true, "ShowToastNotifications": true, "PlaySounds": true, "ToastDuration": 5, "MaxToasts": 5, "ShowLowStockNotifications": true, "ShowOutOfStockNotifications": true, "ShowPaymentDueNotifications": true, "ShowSystemNotifications": true, "ShowBackupNotifications": true, "ShowLowPriorityToast": false, "PlaySoundForHighPriority": true, "NeverAutoDismissCritical": true, "EnableScheduledNotifications": true, "AllowAfterHoursNotifications": false, "WorkHoursStart": "08:00", "WorkHoursEnd": "18:00", "EnableAutoCleanup": true, "CleanupAfterDays": 30}, "Performance": {"CacheSettings": true, "DatabaseTimeout": 30, "MaxRecordsPerPage": 100, "EnableLazyLoading": true}}