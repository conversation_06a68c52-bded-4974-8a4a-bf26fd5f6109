using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج المخزن المتقدم
    /// </summary>
    public class Warehouse : INotifyPropertyChanged
    {
        private int _id;
        private string _code = string.Empty;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private WarehouseType _type = WarehouseType.Main;
        private WarehouseStatus _status = WarehouseStatus.Active;
        private string _address = string.Empty;
        private string _city = string.Empty;
        private string _phone = string.Empty;
        private string _email = string.Empty;
        private string _managerName = string.Empty;
        private decimal _totalCapacity;
        private decimal _usedCapacity;
        private decimal _availableCapacity;
        private bool _isDefault;
        private string _notes = string.Empty;
        private string _createdBy = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private ObservableCollection<WarehouseLocation> _locations = new();
        private ObservableCollection<InventoryItem> _inventory = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public WarehouseType Type
        {
            get => _type;
            set
            {
                if (_type != value)
                {
                    _type = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TypeDisplay));
                    OnPropertyChanged(nameof(TypeIcon));
                }
            }
        }

        public WarehouseStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        public string Address
        {
            get => _address;
            set
            {
                if (_address != value)
                {
                    _address = value;
                    OnPropertyChanged();
                }
            }
        }

        public string City
        {
            get => _city;
            set
            {
                if (_city != value)
                {
                    _city = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Phone
        {
            get => _phone;
            set
            {
                if (_phone != value)
                {
                    _phone = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Email
        {
            get => _email;
            set
            {
                if (_email != value)
                {
                    _email = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ManagerName
        {
            get => _managerName;
            set
            {
                if (_managerName != value)
                {
                    _managerName = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal TotalCapacity
        {
            get => _totalCapacity;
            set
            {
                if (_totalCapacity != value)
                {
                    _totalCapacity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalCapacity));
                    UpdateAvailableCapacity();
                }
            }
        }

        public decimal UsedCapacity
        {
            get => _usedCapacity;
            set
            {
                if (_usedCapacity != value)
                {
                    _usedCapacity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedUsedCapacity));
                    OnPropertyChanged(nameof(CapacityUsagePercentage));
                    UpdateAvailableCapacity();
                }
            }
        }

        public decimal AvailableCapacity
        {
            get => _availableCapacity;
            set
            {
                if (_availableCapacity != value)
                {
                    _availableCapacity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAvailableCapacity));
                }
            }
        }

        public bool IsDefault
        {
            get => _isDefault;
            set
            {
                if (_isDefault != value)
                {
                    _isDefault = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<WarehouseLocation> Locations
        {
            get => _locations;
            set
            {
                if (_locations != value)
                {
                    _locations = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(LocationCount));
                }
            }
        }

        public ObservableCollection<InventoryItem> Inventory
        {
            get => _inventory;
            set
            {
                if (_inventory != value)
                {
                    _inventory = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TotalItems));
                    OnPropertyChanged(nameof(TotalValue));
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsActive => Status == WarehouseStatus.Active;
        public int LocationCount => Locations?.Count ?? 0;
        public int TotalItems => Inventory?.Sum(i => (int)i.Quantity) ?? 0;
        public decimal TotalValue => Inventory?.Sum(i => i.TotalValue) ?? 0;

        public double CapacityUsagePercentage
        {
            get
            {
                if (TotalCapacity == 0) return 0;
                return (double)(UsedCapacity / TotalCapacity) * 100;
            }
        }

        public string FormattedTotalCapacity => $"{TotalCapacity:N0} م³";
        public string FormattedUsedCapacity => $"{UsedCapacity:N0} م³";
        public string FormattedAvailableCapacity => $"{AvailableCapacity:N0} م³";
        public string FormattedTotalValue => TotalValue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        public string TypeDisplay
        {
            get
            {
                return Type switch
                {
                    WarehouseType.Main => "مخزن رئيسي",
                    WarehouseType.Branch => "مخزن فرع",
                    WarehouseType.Transit => "مخزن عبور",
                    WarehouseType.Returns => "مخزن مرتجعات",
                    WarehouseType.Damaged => "مخزن تالف",
                    WarehouseType.Virtual => "مخزن افتراضي",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    WarehouseStatus.Active => "نشط",
                    WarehouseStatus.Inactive => "غير نشط",
                    WarehouseStatus.Maintenance => "صيانة",
                    WarehouseStatus.Closed => "مغلق",
                    _ => "غير محدد"
                };
            }
        }

        public string TypeIcon
        {
            get
            {
                return Type switch
                {
                    WarehouseType.Main => "Warehouse",
                    WarehouseType.Branch => "StorefrontOutline",
                    WarehouseType.Transit => "TruckDelivery",
                    WarehouseType.Returns => "Undo",
                    WarehouseType.Damaged => "AlertCircle",
                    WarehouseType.Virtual => "Cloud",
                    _ => "Package"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    WarehouseStatus.Active => "Green",
                    WarehouseStatus.Inactive => "Gray",
                    WarehouseStatus.Maintenance => "Orange",
                    WarehouseStatus.Closed => "Red",
                    _ => "Gray"
                };
            }
        }

        #endregion

        #region Methods

        private void UpdateAvailableCapacity()
        {
            AvailableCapacity = TotalCapacity - UsedCapacity;
        }

        public void AddLocation(WarehouseLocation location)
        {
            location.WarehouseId = Id;
            Locations.Add(location);
            OnPropertyChanged(nameof(LocationCount));
        }

        public void RemoveLocation(WarehouseLocation location)
        {
            Locations.Remove(location);
            OnPropertyChanged(nameof(LocationCount));
        }

        public void UpdateInventory()
        {
            OnPropertyChanged(nameof(TotalItems));
            OnPropertyChanged(nameof(TotalValue));
            
            // حساب السعة المستخدمة بناءً على المخزون
            UsedCapacity = Inventory.Sum(i => i.Volume);
        }

        public void Activate()
        {
            Status = WarehouseStatus.Active;
            UpdatedAt = DateTime.Now;
        }

        public void Deactivate()
        {
            Status = WarehouseStatus.Inactive;
            UpdatedAt = DateTime.Now;
        }

        public void SetMaintenance()
        {
            Status = WarehouseStatus.Maintenance;
            UpdatedAt = DateTime.Now;
        }

        public void Close()
        {
            Status = WarehouseStatus.Closed;
            UpdatedAt = DateTime.Now;
        }

        public bool CanAcceptTransfer(decimal requiredCapacity)
        {
            return IsActive && AvailableCapacity >= requiredCapacity;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Enums

    public enum WarehouseType
    {
        Main,           // مخزن رئيسي
        Branch,         // مخزن فرع
        Transit,        // مخزن عبور
        Returns,        // مخزن مرتجعات
        Damaged,        // مخزن تالف
        Virtual         // مخزن افتراضي
    }

    public enum WarehouseStatus
    {
        Active,         // نشط
        Inactive,       // غير نشط
        Maintenance,    // صيانة
        Closed          // مغلق
    }

    #endregion

    #region Validation

    public class WarehouseValidator : AbstractValidator<Warehouse>
    {
        public WarehouseValidator()
        {
            RuleFor(w => w.Code)
                .NotEmpty().WithMessage("كود المخزن مطلوب")
                .MaximumLength(20).WithMessage("كود المخزن لا يمكن أن يتجاوز 20 حرف");

            RuleFor(w => w.Name)
                .NotEmpty().WithMessage("اسم المخزن مطلوب")
                .MaximumLength(100).WithMessage("اسم المخزن لا يمكن أن يتجاوز 100 حرف");

            RuleFor(w => w.TotalCapacity)
                .GreaterThan(0).WithMessage("السعة الإجمالية يجب أن تكون أكبر من صفر");

            RuleFor(w => w.UsedCapacity)
                .GreaterThanOrEqualTo(0).WithMessage("السعة المستخدمة لا يمكن أن تكون سالبة")
                .LessThanOrEqualTo(w => w.TotalCapacity).WithMessage("السعة المستخدمة لا يمكن أن تتجاوز السعة الإجمالية");

            RuleFor(w => w.Email)
                .EmailAddress().When(w => !string.IsNullOrEmpty(w.Email))
                .WithMessage("البريد الإلكتروني غير صحيح");
        }
    }

    #endregion
}
