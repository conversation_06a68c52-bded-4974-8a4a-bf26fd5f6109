using System;
using System.IO;
using System.Data.SQLite;
using SalesManagementSystem.Services;

namespace SalesManagementSystem
{
    public static class DatabaseFixer
    {
        private static readonly string DatabasePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "SalesManagementSystem",
            "SalesManagement.db");

        public static void FixDatabase()
        {
            try
            {
                // إنشاء مجلد التطبيق إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(DatabasePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                // حذف قاعدة البيانات القديمة لضمان إنشاء قاعدة جديدة بالمستخدم المطلوب
                if (File.Exists(DatabasePath))
                {
                    File.Delete(DatabasePath);
                    LoggingService.LogSystemEvent("قاعدة البيانات", "تم حذف قاعدة البيانات القديمة لإنشاء قاعدة جديدة");
                }

                CreateNewDatabase();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إصلاح قاعدة البيانات");

                // في حالة الفشل، محاولة إنشاء قاعدة بيانات جديدة
                try
                {
                    if (File.Exists(DatabasePath))
                    {
                        File.Delete(DatabasePath);
                    }
                    CreateNewDatabase();
                }
                catch (Exception createEx)
                {
                    LoggingService.LogError(createEx, "فشل في إنشاء قاعدة بيانات جديدة");
                    throw;
                }
            }
        }

        private static void OldFixDatabase()
        {
            try
            {
                // إنشاء مجلد التطبيق إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(DatabasePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                // حذف قاعدة البيانات القديمة إذا كانت تحتوي على أخطاء
                if (File.Exists(DatabasePath))
                {
                    try
                    {
                        // محاولة فتح قاعدة البيانات للتحقق من صحتها
                        using var testConnection = new SQLiteConnection($"Data Source={DatabasePath};Version=3;");
                        testConnection.Open();

                        // التحقق من وجود جدول Notifications
                        using var command = new SQLiteCommand(
                            "SELECT name FROM sqlite_master WHERE type='table' AND name='Notifications';",
                            testConnection);

                        var result = command.ExecuteScalar();
                        if (result == null)
                        {
                            // الجدول غير موجود، إعادة إنشاء قاعدة البيانات
                            testConnection.Close();
                            File.Delete(DatabasePath);
                            CreateNewDatabase();
                        }
                        else
                        {
                            // التحقق من بنية الجدول
                            using var structureCommand = new SQLiteCommand(
                                "PRAGMA table_info(Notifications);",
                                testConnection);

                            using var reader = structureCommand.ExecuteReader();
                            bool hasCorrectStructure = false;

                            while (reader.Read())
                            {
                                var columnName = reader["name"].ToString();
                                if (columnName == "IsRead")
                                {
                                    var columnType = reader["type"].ToString();
                                    hasCorrectStructure = columnType?.ToUpper() == "INTEGER";
                                    break;
                                }
                            }

                            if (!hasCorrectStructure)
                            {
                                // بنية الجدول خاطئة، إعادة إنشاء قاعدة البيانات
                                testConnection.Close();
                                File.Delete(DatabasePath);
                                CreateNewDatabase();
                            }
                        }
                    }
                    catch (Exception)
                    {
                        // خطأ في قاعدة البيانات، حذفها وإعادة إنشائها
                        if (File.Exists(DatabasePath))
                        {
                            File.Delete(DatabasePath);
                        }
                        CreateNewDatabase();
                    }
                }
                else
                {
                    CreateNewDatabase();
                }

                LoggingService.LogSystemEvent("إصلاح قاعدة البيانات", "تم إصلاح قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إصلاح قاعدة البيانات");
                throw;
            }
        }

        private static void CreateNewDatabase()
        {
            SQLiteConnection.CreateFile(DatabasePath);

            using var connection = new SQLiteConnection($"Data Source={DatabasePath};Version=3;");
            connection.Open();

            // إنشاء الجداول الأساسية فقط
            CreateEssentialTables(connection);
            InsertDefaultData(connection);
        }

        private static void CreateEssentialTables(SQLiteConnection connection)
        {
            var tables = new[]
            {
                // جدول المستخدمين
                @"CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT NOT NULL UNIQUE,
                    PasswordHash TEXT NOT NULL,
                    FullName TEXT NOT NULL,
                    Email TEXT,
                    Role TEXT NOT NULL,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    LastLogin TEXT,
                    LastPasswordChangeDate TEXT,
                    CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt TEXT
                );",

                // جدول الإعدادات
                @"CREATE TABLE IF NOT EXISTS Settings (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Key TEXT NOT NULL UNIQUE,
                    Value TEXT,
                    Description TEXT
                );",

                // جدول الإشعارات
                @"CREATE TABLE IF NOT EXISTS Notifications (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Title TEXT NOT NULL,
                    Message TEXT NOT NULL,
                    Type TEXT NOT NULL,
                    Priority TEXT DEFAULT 'Normal',
                    IsRead INTEGER DEFAULT 0,
                    IsScheduled INTEGER DEFAULT 0,
                    ScheduledTime TEXT,
                    ActionUrl TEXT,
                    CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                );",

                // جدول الفئات
                @"CREATE TABLE IF NOT EXISTS Categories (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Description TEXT
                );",

                // جدول المنتجات
                @"CREATE TABLE IF NOT EXISTS Products (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Code TEXT NOT NULL,
                    Name TEXT NOT NULL,
                    Description TEXT,
                    CategoryId INTEGER,
                    PurchasePrice REAL NOT NULL,
                    SellingPrice REAL NOT NULL,
                    Quantity INTEGER NOT NULL,
                    MinQuantity INTEGER NOT NULL,
                    CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt TEXT,
                    FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
                );",

                // جدول العملاء
                @"CREATE TABLE IF NOT EXISTS Customers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Phone TEXT,
                    Email TEXT,
                    Address TEXT,
                    Balance REAL NOT NULL DEFAULT 0,
                    CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt TEXT
                );",

                // جدول المبيعات
                @"CREATE TABLE IF NOT EXISTS Sales (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT NOT NULL,
                    CustomerId INTEGER,
                    Date TEXT NOT NULL,
                    Subtotal REAL NOT NULL,
                    Discount REAL NOT NULL DEFAULT 0,
                    Tax REAL NOT NULL DEFAULT 0,
                    Total REAL NOT NULL,
                    PaymentMethod TEXT NOT NULL,
                    PaymentStatus TEXT NOT NULL,
                    Notes TEXT,
                    CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt TEXT,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
                );"
            };

            foreach (var tableScript in tables)
            {
                try
                {
                    using var command = new SQLiteCommand(tableScript, connection);
                    command.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    LoggingService.LogError(ex, $"خطأ في إنشاء جدول: {tableScript.Substring(0, Math.Min(50, tableScript.Length))}");
                }
            }
        }

        private static void InsertDefaultData(SQLiteConnection connection)
        {
            try
            {
                // إنشاء المستخدمين الافتراضيين
                var defaultUsers = new[]
                {
                    new { Username = "admin", Password = "0000", FullName = "مدير النظام", Role = "Admin" },
                    new { Username = "manager", Password = "1234", FullName = "مدير المبيعات", Role = "Manager" },
                    new { Username = "cashier", Password = "5678", FullName = "أمين الصندوق", Role = "Cashier" },
                    new { Username = "user", Password = "9999", FullName = "مستخدم عادي", Role = "User" }
                };

                foreach (var user in defaultUsers)
                {
                    using var userCommand = new SQLiteCommand(
                        "INSERT OR IGNORE INTO Users (Username, PasswordHash, FullName, Role, IsActive, LastPasswordChangeDate) VALUES (@Username, @Password, @FullName, @Role, 1, @LastPasswordChangeDate)",
                        connection);
                    userCommand.Parameters.AddWithValue("@Username", user.Username);
                    userCommand.Parameters.AddWithValue("@Password", user.Password);
                    userCommand.Parameters.AddWithValue("@FullName", user.FullName);
                    userCommand.Parameters.AddWithValue("@Role", user.Role);
                    userCommand.Parameters.AddWithValue("@LastPasswordChangeDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    userCommand.ExecuteNonQuery();
                }

                // تم إنشاء جميع المستخدمين في الحلقة أعلاه

                // إدراج إعدادات افتراضية
                var settings = new[]
                {
                    ("CompanyName", "نظام إدارة المبيعات"),
                    ("Language", "ar-SA"),
                    ("Theme", "Light"),
                    ("Currency", "ريال"),
                    ("TaxRate", "15")
                };

                foreach (var (key, value) in settings)
                {
                    using var settingCommand = new SQLiteCommand(
                        "INSERT OR IGNORE INTO Settings (Key, Value) VALUES (@Key, @Value)",
                        connection);
                    settingCommand.Parameters.AddWithValue("@Key", key);
                    settingCommand.Parameters.AddWithValue("@Value", value);
                    settingCommand.ExecuteNonQuery();
                }

                // إدراج فئات افتراضية
                var categories = new[] { "إلكترونيات", "ملابس", "طعام", "مشروبات", "قرطاسية", "أخرى" };
                foreach (var category in categories)
                {
                    using var categoryCommand = new SQLiteCommand(
                        "INSERT OR IGNORE INTO Categories (Name) VALUES (@Name)",
                        connection);
                    categoryCommand.Parameters.AddWithValue("@Name", category);
                    categoryCommand.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إدراج البيانات الافتراضية");
            }
        }
    }
}
