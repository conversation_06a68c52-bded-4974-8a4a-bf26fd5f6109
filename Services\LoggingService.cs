using System;
using System.IO;
using System.Linq;
using NLog;
using NLog.Config;
using NLog.Targets;

namespace SalesManagementSystem.Services
{
    public class LoggingService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private static bool _isConfigured = false;

        static LoggingService()
        {
            ConfigureLogging();
        }

        private static void ConfigureLogging()
        {
            if (_isConfigured) return;

            var config = new LoggingConfiguration();

            // Create targets
            var fileTarget = new FileTarget("fileTarget")
            {
                FileName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "SalesSystem-${shortdate}.log"),
                Layout = "${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}",
                Encoding = System.Text.Encoding.UTF8,
                CreateDirs = true,
                MaxArchiveFiles = 30,
                ArchiveEvery = FileArchivePeriod.Day,
                ArchiveNumbering = ArchiveNumberingMode.Date
            };

            var consoleTarget = new ConsoleTarget("consoleTarget")
            {
                Layout = "${time} [${level:uppercase=true}] ${message} ${exception:format=tostring}"
            };

            // Add targets to configuration
            config.AddTarget(fileTarget);
            config.AddTarget(consoleTarget);

            // Define rules
            config.AddRule(LogLevel.Debug, LogLevel.Fatal, fileTarget);
            config.AddRule(LogLevel.Info, LogLevel.Fatal, consoleTarget);

            // Apply configuration
            LogManager.Configuration = config;
            _isConfigured = true;
        }

        public static void LogInfo(string message)
        {
            _logger.Info(message);
        }

        public static void LogWarning(string message)
        {
            _logger.Warn(message);
        }

        public static void LogError(string message)
        {
            _logger.Error(message);
        }

        public static void LogError(Exception exception, string message = "")
        {
            if (string.IsNullOrEmpty(message))
                _logger.Error(exception);
            else
                _logger.Error(exception, message);
        }

        public static void LogDebug(string message)
        {
            _logger.Debug(message);
        }

        public static void LogTrace(string message)
        {
            _logger.Trace(message);
        }

        public static void LogFatal(string message)
        {
            _logger.Fatal(message);
        }

        public static void LogFatal(Exception exception, string message = "")
        {
            if (string.IsNullOrEmpty(message))
                _logger.Fatal(exception);
            else
                _logger.Fatal(exception, message);
        }

        // Specific logging methods for business operations
        public static void LogUserAction(string username, string action, string details = "")
        {
            var message = $"المستخدم: {username} | الإجراء: {action}";
            if (!string.IsNullOrEmpty(details))
                message += $" | التفاصيل: {details}";

            LogInfo(message);
        }

        public static void LogSaleTransaction(string invoiceNumber, decimal amount, string customerName = "")
        {
            var message = $"عملية بيع - رقم الفاتورة: {invoiceNumber} | المبلغ: {amount:C}";
            if (!string.IsNullOrEmpty(customerName))
                message += $" | العميل: {customerName}";

            LogInfo(message);
        }

        public static void LogPurchaseTransaction(string invoiceNumber, decimal amount, string supplierName = "")
        {
            var message = $"عملية شراء - رقم الفاتورة: {invoiceNumber} | المبلغ: {amount:C}";
            if (!string.IsNullOrEmpty(supplierName))
                message += $" | المورد: {supplierName}";

            LogInfo(message);
        }

        public static void LogProductUpdate(string productName, string action, string details = "")
        {
            var message = $"تحديث منتج - المنتج: {productName} | الإجراء: {action}";
            if (!string.IsNullOrEmpty(details))
                message += $" | التفاصيل: {details}";

            LogInfo(message);
        }

        public static void LogCustomerUpdate(string customerName, string action, string details = "")
        {
            var message = $"تحديث عميل - العميل: {customerName} | الإجراء: {action}";
            if (!string.IsNullOrEmpty(details))
                message += $" | التفاصيل: {details}";

            LogInfo(message);
        }

        public static void LogSupplierUpdate(string supplierName, string action, string details = "")
        {
            var message = $"تحديث مورد - المورد: {supplierName} | الإجراء: {action}";
            if (!string.IsNullOrEmpty(details))
                message += $" | التفاصيل: {details}";

            LogInfo(message);
        }

        public static void LogDatabaseOperation(string operation, string tableName, bool success, string error = "")
        {
            var message = $"عملية قاعدة بيانات - العملية: {operation} | الجدول: {tableName} | النتيجة: {(success ? "نجح" : "فشل")}";
            if (!success && !string.IsNullOrEmpty(error))
                message += $" | الخطأ: {error}";

            if (success)
                LogInfo(message);
            else
                LogError(message);
        }

        public static void LogSystemEvent(string eventType, string description)
        {
            var message = $"حدث نظام - النوع: {eventType} | الوصف: {description}";
            LogInfo(message);
        }

        public static void LogPerformance(string operation, TimeSpan duration, string details = "")
        {
            var message = $"أداء العملية - العملية: {operation} | المدة: {duration.TotalMilliseconds:F2} مللي ثانية";
            if (!string.IsNullOrEmpty(details))
                message += $" | التفاصيل: {details}";

            LogDebug(message);
        }

        public static void LogBackupOperation(string operation, bool success, string filePath = "", string error = "")
        {
            var message = $"عملية نسخ احتياطي - العملية: {operation} | النتيجة: {(success ? "نجح" : "فشل")}";
            if (!string.IsNullOrEmpty(filePath))
                message += $" | المسار: {filePath}";
            if (!success && !string.IsNullOrEmpty(error))
                message += $" | الخطأ: {error}";

            if (success)
                LogInfo(message);
            else
                LogError(message);
        }

        public static void LogSecurityEvent(string eventType, string username, string ipAddress = "", string details = "")
        {
            var message = $"حدث أمني - النوع: {eventType} | المستخدم: {username}";
            if (!string.IsNullOrEmpty(ipAddress))
                message += $" | عنوان IP: {ipAddress}";
            if (!string.IsNullOrEmpty(details))
                message += $" | التفاصيل: {details}";

            LogWarning(message);
        }

        // Method to get log file path for current date
        public static string GetCurrentLogFilePath()
        {
            var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            var fileName = $"SalesSystem-{DateTime.Now:yyyy-MM-dd}.log";
            return Path.Combine(logDirectory, fileName);
        }

        // Method to get all log files
        public static string[] GetAllLogFiles()
        {
            var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(logDirectory))
                return Array.Empty<string>();

            return Directory.GetFiles(logDirectory, "SalesSystem-*.log")
                           .OrderByDescending(f => f)
                           .ToArray();
        }

        // Method to clean old log files
        public static void CleanOldLogs(int daysToKeep = 30)
        {
            try
            {
                var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
                if (!Directory.Exists(logDirectory))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var files = Directory.GetFiles(logDirectory, "SalesSystem-*.log");

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                        LogInfo($"تم حذف ملف السجل القديم: {fileInfo.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, "خطأ في تنظيف ملفات السجل القديمة");
            }
        }
    }
}
