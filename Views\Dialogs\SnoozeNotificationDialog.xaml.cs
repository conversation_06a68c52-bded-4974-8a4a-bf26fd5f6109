using System;
using System.Windows;

namespace SalesManagementSystem.Views.Dialogs
{
    /// <summary>
    /// حوار تأجيل الإشعار
    /// </summary>
    public partial class SnoozeNotificationDialog : Window
    {
        public int SnoozeMinutes { get; private set; }
        public bool IsConfirmed { get; private set; }

        public SnoozeNotificationDialog()
        {
            InitializeComponent();
            SnoozeDurationComboBox.SelectedIndex = 1; // Default to 15 minutes
        }

        private void SnoozeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Check if custom minutes is entered
                if (!string.IsNullOrWhiteSpace(CustomMinutesTextBox.Text))
                {
                    if (int.TryParse(CustomMinutesTextBox.Text, out int customMinutes) && customMinutes > 0)
                    {
                        SnoozeMinutes = customMinutes;
                    }
                    else
                    {
                        MessageBox.Show("يرجى إدخال رقم صحيح للدقائق", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }
                else if (SnoozeDurationComboBox.SelectedItem != null)
                {
                    var selectedItem = SnoozeDurationComboBox.SelectedItem as System.Windows.Controls.ComboBoxItem;
                    if (selectedItem?.Tag != null && int.TryParse(selectedItem.Tag.ToString(), out int minutes))
                    {
                        SnoozeMinutes = minutes;
                    }
                    else
                    {
                        MessageBox.Show("يرجى اختيار مدة التأجيل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار مدة التأجيل أو إدخال عدد الدقائق", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                IsConfirmed = true;
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            IsConfirmed = false;
            DialogResult = false;
            Close();
        }
    }
}
