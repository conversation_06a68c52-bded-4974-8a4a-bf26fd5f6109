using System;
using System.Windows;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Views
{
    public partial class EditInvoiceItemDialog : Window
    {
        public InvoiceItem EditedItem { get; private set; }
        private InvoiceItem _originalItem;

        public EditInvoiceItemDialog(InvoiceItem item)
        {
            InitializeComponent();

            _originalItem = item;
            EditedItem = new InvoiceItem
            {
                Id = item.Id,
                InvoiceId = item.InvoiceId,
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                Description = item.Description,
                Quantity = item.Quantity,
                UnitPrice = item.UnitPrice,
                Discount = item.Discount
            };

            LoadItemData();
            CalculateTotal();
        }

        private void LoadItemData()
        {
            ProductNameTextBox.Text = EditedItem.ProductName;
            QuantityTextBox.Text = EditedItem.Quantity.ToString();
            UnitPriceTextBox.Text = EditedItem.UnitPrice.ToString("F2");
            DiscountTextBox.Text = EditedItem.Discount.ToString("F2");
            DiscountPercentageTextBox.Text = "0.0";

            // ربط الأحداث
            QuantityTextBox.TextChanged += CalculateTotal_TextChanged;
            UnitPriceTextBox.TextChanged += CalculateTotal_TextChanged;
            DiscountTextBox.TextChanged += CalculateTotal_TextChanged;
        }

        private void CalculateTotal_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            CalculateTotal();
        }

        private void DiscountPercentageTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            if (decimal.TryParse(DiscountPercentageTextBox.Text, out decimal percentage))
            {
                if (decimal.TryParse(QuantityTextBox.Text, out decimal quantity) &&
                    decimal.TryParse(UnitPriceTextBox.Text, out decimal unitPrice))
                {
                    var subtotal = quantity * unitPrice;
                    var discountAmount = subtotal * (percentage / 100);
                    DiscountTextBox.Text = discountAmount.ToString("F2");
                }
            }
            CalculateTotal();
        }

        private void CalculateTotal()
        {
            try
            {
                if (decimal.TryParse(QuantityTextBox.Text, out decimal quantity) &&
                    decimal.TryParse(UnitPriceTextBox.Text, out decimal unitPrice) &&
                    decimal.TryParse(DiscountTextBox.Text, out decimal discount))
                {
                    var total = (quantity * unitPrice) - discount;
                    TotalTextBox.Text = total.ToString("F2") + " دج";
                }
                else
                {
                    TotalTextBox.Text = "0.00 دج";
                }
            }
            catch
            {
                TotalTextBox.Text = "0.00 دج";
            }
        }

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!decimal.TryParse(QuantityTextBox.Text, out decimal quantity) || quantity <= 0)
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    QuantityTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(UnitPriceTextBox.Text, out decimal unitPrice) || unitPrice < 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    UnitPriceTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(DiscountTextBox.Text, out decimal discount) || discount < 0)
                {
                    MessageBox.Show("يرجى إدخال خصم صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    DiscountTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(DiscountPercentageTextBox.Text, out decimal discountPercentage) || discountPercentage < 0 || discountPercentage > 100)
                {
                    MessageBox.Show("يرجى إدخال نسبة خصم صحيحة (0-100)", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    DiscountPercentageTextBox.Focus();
                    return;
                }

                // تحديث العنصر
                EditedItem.Quantity = quantity;
                EditedItem.UnitPrice = unitPrice;
                EditedItem.Discount = discount;
                // DiscountPercentage غير متاح في InvoiceItem

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
