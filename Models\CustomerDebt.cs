using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج دين العميل
    /// </summary>
    public class CustomerDebt : INotifyPropertyChanged
    {
        private int _customerId;
        private string _customerName = "";
        private string _phone = "";
        private decimal _totalPurchases;
        private decimal _totalPaid;
        private decimal _remainingDebt;
        private DateTime _lastPaymentDate;
        private string _status = "";

        /// <summary>
        /// معرف العميل
        /// </summary>
        public int CustomerId
        {
            get => _customerId;
            set
            {
                _customerId = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// اسم العميل
        /// </summary>
        public string CustomerName
        {
            get => _customerName;
            set
            {
                _customerName = value ?? "";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        public string Phone
        {
            get => _phone;
            set
            {
                _phone = value ?? "";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// إجمالي المشتريات
        /// </summary>
        public decimal TotalPurchases
        {
            get => _totalPurchases;
            set
            {
                _totalPurchases = value;
                OnPropertyChanged();
                UpdateRemainingDebt();
            }
        }

        /// <summary>
        /// إجمالي المدفوع
        /// </summary>
        public decimal TotalPaid
        {
            get => _totalPaid;
            set
            {
                _totalPaid = value;
                OnPropertyChanged();
                UpdateRemainingDebt();
            }
        }

        /// <summary>
        /// المبلغ المتبقي (الدين)
        /// </summary>
        public decimal RemainingDebt
        {
            get => _remainingDebt;
            set
            {
                _remainingDebt = value;
                OnPropertyChanged();
                UpdateStatus();
            }
        }

        /// <summary>
        /// تاريخ آخر دفعة
        /// </summary>
        public DateTime LastPaymentDate
        {
            get => _lastPaymentDate;
            set
            {
                _lastPaymentDate = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// حالة الدين
        /// </summary>
        public string Status
        {
            get => _status;
            set
            {
                _status = value ?? "";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// تحديث المبلغ المتبقي
        /// </summary>
        private void UpdateRemainingDebt()
        {
            RemainingDebt = Math.Max(0, TotalPurchases - TotalPaid);
        }

        /// <summary>
        /// تحديث حالة الدين
        /// </summary>
        private void UpdateStatus()
        {
            Status = RemainingDebt > 0 ? "عليه ديون" : "مسدد";
        }

        /// <summary>
        /// إضافة دفعة جديدة
        /// </summary>
        /// <param name="amount">مبلغ الدفعة</param>
        /// <returns>true إذا تمت الإضافة بنجاح</returns>
        public bool AddPayment(decimal amount)
        {
            if (amount <= 0 || amount > RemainingDebt)
                return false;

            TotalPaid += amount;
            LastPaymentDate = DateTime.Now;
            return true;
        }

        /// <summary>
        /// إضافة مشتريات جديدة
        /// </summary>
        /// <param name="amount">مبلغ المشتريات</param>
        /// <returns>true إذا تمت الإضافة بنجاح</returns>
        public bool AddPurchase(decimal amount)
        {
            if (amount <= 0)
                return false;

            TotalPurchases += amount;
            return true;
        }

        /// <summary>
        /// التحقق من وجود دين
        /// </summary>
        public bool HasDebt => RemainingDebt > 0;

        /// <summary>
        /// نسبة السداد
        /// </summary>
        public decimal PaymentPercentage => TotalPurchases > 0 ? (TotalPaid / TotalPurchases) * 100 : 0;

        /// <summary>
        /// عدد الأيام منذ آخر دفعة
        /// </summary>
        public int DaysSinceLastPayment => (DateTime.Now - LastPaymentDate).Days;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
