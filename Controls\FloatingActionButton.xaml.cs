using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using MaterialDesignThemes.Wpf;

namespace SalesManagementSystem.Controls
{
    /// <summary>
    /// زر عائم مع إمكانيات متقدمة مثل Speed Dial والتوسع
    /// </summary>
    public partial class FloatingActionButton : UserControl
    {
        #region Dependency Properties

        public static readonly DependencyProperty IconKindProperty =
            DependencyProperty.Register(nameof(IconKind), typeof(PackIconKind), typeof(FloatingActionButton),
                new PropertyMetadata(PackIconKind.Plus));

        public static readonly DependencyProperty IconSizeProperty =
            DependencyProperty.Register(nameof(IconSize), typeof(double), typeof(FloatingActionButton),
                new PropertyMetadata(24.0));

        public static readonly DependencyProperty FabSizeProperty =
            DependencyProperty.Register(nameof(FabSize), typeof(double), typeof(FloatingActionButton),
                new PropertyMetadata(56.0));

        public static readonly DependencyProperty FabBackgroundProperty =
            DependencyProperty.Register(nameof(FabBackground), typeof(Brush), typeof(FloatingActionButton),
                new PropertyMetadata(Application.Current.Resources["PrimaryHueMidBrush"]));

        public static readonly DependencyProperty FabForegroundProperty =
            DependencyProperty.Register(nameof(FabForeground), typeof(Brush), typeof(FloatingActionButton),
                new PropertyMetadata(Brushes.White));

        public static readonly DependencyProperty RippleColorProperty =
            DependencyProperty.Register(nameof(RippleColor), typeof(Brush), typeof(FloatingActionButton),
                new PropertyMetadata(new SolidColorBrush(Colors.White) { Opacity = 0.3 }));

        public static readonly DependencyProperty FabCornerRadiusProperty =
            DependencyProperty.Register(nameof(FabCornerRadius), typeof(CornerRadius), typeof(FloatingActionButton),
                new PropertyMetadata(new CornerRadius(28)));

        public static readonly DependencyProperty IsExtendedProperty =
            DependencyProperty.Register(nameof(IsExtended), typeof(bool), typeof(FloatingActionButton),
                new PropertyMetadata(false, OnIsExtendedChanged));

        public static readonly DependencyProperty ExtendedTextProperty =
            DependencyProperty.Register(nameof(ExtendedText), typeof(string), typeof(FloatingActionButton));

        public static readonly DependencyProperty TextSizeProperty =
            DependencyProperty.Register(nameof(TextSize), typeof(double), typeof(FloatingActionButton),
                new PropertyMetadata(14.0));

        public static readonly DependencyProperty ShowMiniFabsProperty =
            DependencyProperty.Register(nameof(ShowMiniFabs), typeof(bool), typeof(FloatingActionButton),
                new PropertyMetadata(false));

        public static readonly DependencyProperty CommandProperty =
            DependencyProperty.Register(nameof(Command), typeof(ICommand), typeof(FloatingActionButton));

        public static readonly DependencyProperty CommandParameterProperty =
            DependencyProperty.Register(nameof(CommandParameter), typeof(object), typeof(FloatingActionButton));

        #endregion

        #region Properties

        public PackIconKind IconKind
        {
            get => (PackIconKind)GetValue(IconKindProperty);
            set => SetValue(IconKindProperty, value);
        }

        public double IconSize
        {
            get => (double)GetValue(IconSizeProperty);
            set => SetValue(IconSizeProperty, value);
        }

        public double FabSize
        {
            get => (double)GetValue(FabSizeProperty);
            set => SetValue(FabSizeProperty, value);
        }

        public Brush FabBackground
        {
            get => (Brush)GetValue(FabBackgroundProperty);
            set => SetValue(FabBackgroundProperty, value);
        }

        public Brush FabForeground
        {
            get => (Brush)GetValue(FabForegroundProperty);
            set => SetValue(FabForegroundProperty, value);
        }

        public Brush RippleColor
        {
            get => (Brush)GetValue(RippleColorProperty);
            set => SetValue(RippleColorProperty, value);
        }

        public CornerRadius FabCornerRadius
        {
            get => (CornerRadius)GetValue(FabCornerRadiusProperty);
            set => SetValue(FabCornerRadiusProperty, value);
        }

        public bool IsExtended
        {
            get => (bool)GetValue(IsExtendedProperty);
            set => SetValue(IsExtendedProperty, value);
        }

        public string ExtendedText
        {
            get => (string)GetValue(ExtendedTextProperty);
            set => SetValue(ExtendedTextProperty, value);
        }

        public double TextSize
        {
            get => (double)GetValue(TextSizeProperty);
            set => SetValue(TextSizeProperty, value);
        }

        public bool ShowMiniFabs
        {
            get => (bool)GetValue(ShowMiniFabsProperty);
            set => SetValue(ShowMiniFabsProperty, value);
        }

        public ICommand? Command
        {
            get => (ICommand?)GetValue(CommandProperty);
            set => SetValue(CommandProperty, value);
        }

        public object CommandParameter
        {
            get => GetValue(CommandParameterProperty);
            set => SetValue(CommandParameterProperty, value);
        }

        public List<MiniFabItem> MiniFabItems { get; } = new List<MiniFabItem>();

        #endregion

        #region Events

        public event EventHandler<RoutedEventArgs>? Click;

        #endregion

        #region Constructor

        public FloatingActionButton()
        {
            InitializeComponent();
            DataContext = this;
        }

        #endregion

        #region Event Handlers

        protected override void OnMouseEnter(MouseEventArgs e)
        {
            base.OnMouseEnter(e);
            var storyboard = FindResource("FabHoverIn") as Storyboard;
            storyboard?.Begin(FabBorder);
        }

        protected override void OnMouseLeave(MouseEventArgs e)
        {
            base.OnMouseLeave(e);
            var storyboard = FindResource("FabHoverOut") as Storyboard;
            storyboard?.Begin(FabBorder);
        }

        protected override void OnPreviewMouseLeftButtonDown(MouseButtonEventArgs e)
        {
            base.OnPreviewMouseLeftButtonDown(e);
            var storyboard = FindResource("FabPress") as Storyboard;
            storyboard?.Begin(FabBorder);

            CreateRippleEffect(e.GetPosition(RippleContainer));
        }

        protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonUp(e);
            var storyboard = FindResource("FabRelease") as Storyboard;
            storyboard?.Begin(FabBorder);

            // تنفيذ الأمر أو الحدث
            if (Command?.CanExecute(CommandParameter) == true)
            {
                Command.Execute(CommandParameter);
            }

            Click?.Invoke(this, e);
        }

        private static void OnIsExtendedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FloatingActionButton fab)
            {
                fab.AnimateExtension((bool)e.NewValue);
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// إضافة عنصر Mini FAB
        /// </summary>
        public void AddMiniFab(PackIconKind icon, string text, ICommand? command = null)
        {
            var miniFab = new MiniFabItem
            {
                Icon = icon,
                Text = text,
                Command = command
            };

            MiniFabItems.Add(miniFab);
            CreateMiniFabButton(miniFab);
        }

        /// <summary>
        /// إزالة جميع عناصر Mini FAB
        /// </summary>
        public void ClearMiniFabs()
        {
            MiniFabItems.Clear();
            MiniFabsContainer.Children.Clear();
        }

        /// <summary>
        /// تبديل حالة العرض الموسع
        /// </summary>
        public void ToggleExtended()
        {
            IsExtended = !IsExtended;
        }

        /// <summary>
        /// تبديل عرض Mini FABs
        /// </summary>
        public void ToggleMiniFabs()
        {
            ShowMiniFabs = !ShowMiniFabs;
            AnimateMiniFabs(ShowMiniFabs);
        }

        #endregion

        #region Private Methods

        private void AnimateExtension(bool extend)
        {
            if (extend)
            {
                var extendStoryboard = FindResource("ExtendFab") as Storyboard;
                if (extendStoryboard != null)
                {
                    var widthAnimation = extendStoryboard.Children[0] as DoubleAnimation;
                    if (widthAnimation != null)
                    {
                        widthAnimation.To = CalculateExtendedWidth();
                    }
                    extendStoryboard.Begin(FabBorder);
                }
            }
            else
            {
                var collapseStoryboard = FindResource("CollapseFab") as Storyboard;
                if (collapseStoryboard != null)
                {
                    var widthAnimation = collapseStoryboard.Children[1] as DoubleAnimation;
                    if (widthAnimation != null)
                    {
                        widthAnimation.To = FabSize;
                    }
                    collapseStoryboard.Begin(FabBorder);
                }
            }
        }

        private double CalculateExtendedWidth()
        {
            if (string.IsNullOrEmpty(ExtendedText))
                return FabSize;

            // تقدير عرض النص
            var textWidth = ExtendedText.Length * (TextSize * 0.6);
            return FabSize + textWidth + 32; // إضافة مساحة للأيقونة والحشو
        }

        private void CreateMiniFabButton(MiniFabItem item)
        {
            var button = new Button
            {
                Width = 40,
                Height = 40,
                Margin = new Thickness(0, 4, 0, 0),
                Background = FabBackground,
                BorderThickness = new Thickness(0),
                Content = new PackIcon
                {
                    Kind = item.Icon,
                    Width = 20,
                    Height = 20,
                    Foreground = FabForeground
                },
                ToolTip = item.Text,
                Command = item.Command,
                Style = Application.Current.Resources["MaterialDesignFloatingActionMiniButton"] as Style
            };

            MiniFabsContainer.Children.Insert(0, button);
        }

        private void AnimateMiniFabs(bool show)
        {
            for (int i = 0; i < MiniFabsContainer.Children.Count; i++)
            {
                var child = MiniFabsContainer.Children[i];
                var delay = TimeSpan.FromMilliseconds(i * 50);

                if (show)
                {
                    child.Visibility = Visibility.Visible;
                    var slideIn = new DoubleAnimation
                    {
                        From = 100,
                        To = 0,
                        Duration = TimeSpan.FromMilliseconds(300),
                        EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut },
                        BeginTime = delay
                    };

                    var fadeIn = new DoubleAnimation
                    {
                        From = 0,
                        To = 1,
                        Duration = TimeSpan.FromMilliseconds(300),
                        BeginTime = delay
                    };

                    child.RenderTransform = new TranslateTransform();
                    child.RenderTransform.BeginAnimation(TranslateTransform.YProperty, slideIn);
                    child.BeginAnimation(OpacityProperty, fadeIn);
                }
                else
                {
                    var slideOut = new DoubleAnimation
                    {
                        From = 0,
                        To = 100,
                        Duration = TimeSpan.FromMilliseconds(200),
                        EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn },
                        BeginTime = delay
                    };

                    var fadeOut = new DoubleAnimation
                    {
                        From = 1,
                        To = 0,
                        Duration = TimeSpan.FromMilliseconds(200),
                        BeginTime = delay
                    };

                    slideOut.Completed += (s, e) => child.Visibility = Visibility.Collapsed;

                    child.RenderTransform.BeginAnimation(TranslateTransform.YProperty, slideOut);
                    child.BeginAnimation(OpacityProperty, fadeOut);
                }
            }
        }

        private void CreateRippleEffect(Point position)
        {
            try
            {
                var ripple = RippleEllipse;
                var container = RippleContainer;

                if (ripple == null || container == null) return;

                var maxRadius = Math.Max(container.ActualWidth, container.ActualHeight);

                ripple.Width = ripple.Height = maxRadius * 2;
                Canvas.SetLeft(ripple, position.X - maxRadius);
                Canvas.SetTop(ripple, position.Y - maxRadius);

                var storyboard = new Storyboard();

                var scaleXAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = TimeSpan.FromMilliseconds(600),
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                };

                var scaleYAnimation = scaleXAnimation.Clone();
                var opacityAnimation = new DoubleAnimation
                {
                    From = 0.3,
                    To = 0,
                    Duration = TimeSpan.FromMilliseconds(600)
                };

                Storyboard.SetTarget(scaleXAnimation, ripple);
                Storyboard.SetTargetProperty(scaleXAnimation,
                    new PropertyPath("(UIElement.RenderTransform).(ScaleTransform.ScaleX)"));

                Storyboard.SetTarget(scaleYAnimation, ripple);
                Storyboard.SetTargetProperty(scaleYAnimation,
                    new PropertyPath("(UIElement.RenderTransform).(ScaleTransform.ScaleY)"));

                Storyboard.SetTarget(opacityAnimation, ripple);
                Storyboard.SetTargetProperty(opacityAnimation, new PropertyPath("Opacity"));

                storyboard.Children.Add(scaleXAnimation);
                storyboard.Children.Add(scaleYAnimation);
                storyboard.Children.Add(opacityAnimation);

                storyboard.Begin();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تأثير الريبل: {ex.Message}");
            }
        }

        #endregion
    }

    #region Helper Classes

    public class MiniFabItem
    {
        public PackIconKind Icon { get; set; }
        public string Text { get; set; } = string.Empty;
        public ICommand? Command { get; set; }
    }

    #endregion
}
