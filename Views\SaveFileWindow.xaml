<Window x:Class="SalesManagementSystem.Views.SaveFileWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="حفظ ملف"
        Height="450" Width="650"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Purple">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="💾" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="حفظ الملف" FontSize="18" 
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel>
                <GroupBox Header="تفاصيل الحفظ" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الملف:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Margin="0,0,0,10" Text="نظام_المبيعات_2024"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="نوع الملف:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" Margin="0,0,0,10" SelectedIndex="0">
                            <ComboBoxItem Content="قاعدة بيانات (.db)"/>
                            <ComboBoxItem Content="ملف Excel (.xlsx)"/>
                            <ComboBoxItem Content="ملف PDF (.pdf)"/>
                            <ComboBoxItem Content="نسخة احتياطية (.bak)"/>
                            <ComboBoxItem Content="ملف نصي (.txt)"/>
                        </ComboBox>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="المجلد:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBox Width="350" Text="C:\SalesSystem\Exports"/>
                            <Button Content="..." Width="30" Margin="5,0,0,0"/>
                        </StackPanel>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="ملاحظات:" VerticalAlignment="Top" Margin="0,0,10,10"/>
                        <TextBox Grid.Row="3" Grid.Column="1" Height="60" TextWrapping="Wrap" AcceptsReturn="True" 
                                Margin="0,0,0,10" Text="حفظ بيانات النظام"/>
                    </Grid>
                </GroupBox>

                <GroupBox Header="خيارات الحفظ">
                    <StackPanel Margin="10">
                        <CheckBox Content="ضغط الملف لتوفير المساحة" IsChecked="True" Margin="0,5"/>
                        <CheckBox Content="تشفير الملف بكلمة مرور" Margin="0,5"/>
                        <CheckBox Content="إنشاء نسخة احتياطية قبل الحفظ" IsChecked="True" Margin="0,5"/>
                        <CheckBox Content="حفظ معلومات التاريخ والوقت" IsChecked="True" Margin="0,5"/>
                        <CheckBox Content="تضمين البيانات المحذوفة" Margin="0,5"/>
                        
                        <GroupBox Header="تنسيق التصدير" Margin="0,10,0,0">
                            <StackPanel Margin="10">
                                <RadioButton Content="تصدير جميع البيانات" IsChecked="True" Margin="0,5"/>
                                <RadioButton Content="تصدير البيانات المحددة فقط" Margin="0,5"/>
                                <RadioButton Content="تصدير ملخص البيانات" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ الآن" Width="120" Height="35" Margin="10" 
                       Background="Purple" Foreground="White" FontWeight="Bold" Click="Save_Click"/>
                <Button Content="👁️ معاينة" Width="100" Height="35" Margin="10" 
                       Background="Blue" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إلغاء" Width="100" Height="35" Margin="10" 
                       Background="Red" Foreground="White" FontWeight="Bold" Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
