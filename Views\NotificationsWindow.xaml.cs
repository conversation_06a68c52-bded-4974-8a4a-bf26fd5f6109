using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class NotificationsWindow : Window
    {
        private EnhancedNotificationService _notificationService;

        public NotificationsWindow()
        {
            InitializeComponent();
            _notificationService = EnhancedNotificationService.Instance;
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // ربط البيانات
            NotificationsItemsControl.ItemsSource = _notificationService.Notifications;
            
            // ربط الأحداث
            _notificationService.PropertyChanged += NotificationService_PropertyChanged;
            
            // تحديث العرض
            UpdateDisplay();
        }

        private void NotificationService_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(_notificationService.UnreadCount) || 
                e.PropertyName == nameof(_notificationService.HasUnreadNotifications))
            {
                Dispatcher.Invoke(UpdateDisplay);
            }
        }

        private void UpdateDisplay()
        {
            var unreadCount = _notificationService.UnreadCount;
            var totalCount = _notificationService.Notifications.Count;
            
            // تحديث عداد الإشعارات
            NotificationCountLabel.Text = unreadCount > 0 ? 
                $"{unreadCount} إشعار جديد من أصل {totalCount}" : 
                $"{totalCount} إشعار";
            
            // تحديث شارة العدد
            if (unreadCount > 0)
            {
                UnreadBadge.Visibility = Visibility.Visible;
                UnreadCountText.Text = _notificationService.UnreadCountText;
            }
            else
            {
                UnreadBadge.Visibility = Visibility.Collapsed;
            }
            
            // إظهار/إخفاء حالة الفراغ
            if (totalCount == 0)
            {
                EmptyStatePanel.Visibility = Visibility.Visible;
                NotificationsItemsControl.Visibility = Visibility.Collapsed;
            }
            else
            {
                EmptyStatePanel.Visibility = Visibility.Collapsed;
                NotificationsItemsControl.Visibility = Visibility.Visible;
            }
            
            // تحديث شريط الحالة
            StatusLabel.Text = totalCount == 0 ? "لا توجد إشعارات" : 
                              unreadCount > 0 ? $"{unreadCount} إشعار غير مقروء" : "جميع الإشعارات مقروءة";
            LastUpdateLabel.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
        }

        private void NotificationItem_Click(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.Tag is int notificationId)
            {
                // تحديد الإشعار كمقروء
                _notificationService.MarkAsRead(notificationId);
                
                // البحث عن الإشعار لعرض تفاصيله
                var notification = _notificationService.Notifications.FirstOrDefault(n => n.Id == notificationId);
                if (notification != null)
                {
                    ShowNotificationDetails(notification);
                }
            }
        }

        private void ShowNotificationDetails(EnhancedNotification notification)
        {
            var detailsWindow = new NotificationDetailsWindow(notification);
            detailsWindow.Owner = this;
            detailsWindow.ShowDialog();
        }

        private void RemoveNotification_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int notificationId)
            {
                var notification = _notificationService.Notifications.FirstOrDefault(n => n.Id == notificationId);
                if (notification != null)
                {
                    if (notification.IsSticky)
                    {
                        var result = MessageBox.Show(
                            "هذا إشعار مهم. هل تريد حذفه فعلاً؟",
                            "تأكيد الحذف",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);
                        
                        if (result != MessageBoxResult.Yes)
                            return;
                    }
                    
                    _notificationService.RemoveNotification(notificationId);
                }
            }
        }

        private void MarkAllAsRead_Click(object sender, RoutedEventArgs e)
        {
            _notificationService.MarkAllAsRead();
            MessageBox.Show("تم تحديد جميع الإشعارات كمقروءة", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ClearOld_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد حذف الإشعارات القديمة (أكثر من 7 أيام)؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _notificationService.ClearOldNotifications();
                MessageBox.Show("تم حذف الإشعارات القديمة", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            UpdateDisplay();
            
            // إضافة إشعار تجريبي للاختبار
            _notificationService.AddNotification(
                "تحديث تجريبي",
                $"تم تحديث البيانات في {DateTime.Now:HH:mm:ss}",
                EnhancedNotificationType.Info,
                EnhancedNotificationPriority.Low,
                "نظام");
        }

        protected override void OnClosed(EventArgs e)
        {
            // إلغاء ربط الأحداث
            _notificationService.PropertyChanged -= NotificationService_PropertyChanged;
            base.OnClosed(e);
        }
    }

    /// <summary>
    /// نافذة تفاصيل الإشعار
    /// </summary>
    public class NotificationDetailsWindow : Window
    {
        public NotificationDetailsWindow(EnhancedNotification notification)
        {
            Title = "تفاصيل الإشعار";
            Width = 400;
            Height = 300;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            FlowDirection = FlowDirection.RightToLeft;
            
            var content = new StackPanel { Margin = new Thickness(20, 20, 20, 20) };
            
            // العنوان
            content.Children.Add(new TextBlock 
            { 
                Text = $"{notification.TypeIcon} {notification.Title}",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            });
            
            // الرسالة
            content.Children.Add(new TextBlock 
            { 
                Text = notification.Message,
                FontSize = 12,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 15)
            });
            
            // التفاصيل
            var detailsPanel = new StackPanel();
            
            detailsPanel.Children.Add(new TextBlock 
            { 
                Text = $"الوقت: {notification.CreatedAt:yyyy/MM/dd HH:mm:ss}",
                FontSize = 11,
                Foreground = System.Windows.Media.Brushes.Gray,
                Margin = new Thickness(0, 2, 0, 0)
            });
            
            detailsPanel.Children.Add(new TextBlock 
            { 
                Text = $"الفئة: {notification.Category}",
                FontSize = 11,
                Foreground = System.Windows.Media.Brushes.Gray,
                Margin = new Thickness(0, 2, 0, 0)
            });
            
            detailsPanel.Children.Add(new TextBlock 
            { 
                Text = $"الأولوية: {notification.Priority}",
                FontSize = 11,
                Foreground = System.Windows.Media.Brushes.Gray,
                Margin = new Thickness(0, 2, 0, 0)
            });
            
            detailsPanel.Children.Add(new TextBlock 
            { 
                Text = $"الحالة: {(notification.IsRead ? "مقروء" : "غير مقروء")}",
                FontSize = 11,
                Foreground = System.Windows.Media.Brushes.Gray,
                Margin = new Thickness(0, 2, 0, 0)
            });
            
            content.Children.Add(detailsPanel);
            
            // زر الإغلاق
            var closeButton = new Button
            {
                Content = "إغلاق",
                Width = 80,
                Height = 30,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 20, 0, 0)
            };
            closeButton.Click += (s, e) => Close();
            content.Children.Add(closeButton);
            
            Content = new ScrollViewer { Content = content };
        }
    }
}
