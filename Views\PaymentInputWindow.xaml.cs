using System;
using System.Globalization;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class PaymentInputWindow : Window
    {
        private readonly UnifiedSalesService _unifiedSalesService;
        private Customer _customer;
        private decimal _invoiceSubtotal;
        private decimal _invoiceDiscount;
        private decimal _invoiceTax;
        private decimal _invoiceAmount;
        private decimal _previousDebt;
        private decimal _totalRequiredAmount;

        public decimal PaidAmount { get; private set; }
        public string PaymentMethod { get; private set; } = "نقدي";
        public decimal RemainingAmount { get; private set; }
        public bool IsConfirmed { get; private set; }

        public PaymentInputWindow(Customer customer, decimal subtotal, decimal discount, decimal tax)
        {
            InitializeComponent();
            
            var dbService = new DatabaseService();
            _unifiedSalesService = new UnifiedSalesService(dbService);
            
            _customer = customer;
            _invoiceSubtotal = subtotal;
            _invoiceDiscount = discount;
            _invoiceTax = tax;
            _invoiceAmount = subtotal - discount + tax;

            Loaded += PaymentInputWindow_Loaded;
        }

        private async void PaymentInputWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadCustomerDataAsync();
            UpdateDisplay();
        }

        private async Task LoadCustomerDataAsync()
        {
            try
            {
                // الحصول على الديون السابقة للعميل
                _previousDebt = await _unifiedSalesService.GetCustomerCurrentDebtAsync(_customer.Id);
                _totalRequiredAmount = _invoiceAmount + _previousDebt;

                LoggingService.LogInfo($"تم تحميل بيانات العميل {_customer.Name} - الديون السابقة: {_previousDebt:N2}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحميل بيانات العميل {_customer.Name}");
                _previousDebt = 0;
                _totalRequiredAmount = _invoiceAmount;
            }
        }

        private void UpdateDisplay()
        {
            // معلومات العميل
            CustomerNameText.Text = _customer.Name;
            PreviousDebtText.Text = $"{_previousDebt:N2} دج";

            // تفاصيل الفاتورة
            SubtotalText.Text = $"{_invoiceSubtotal:N2} دج";
            DiscountText.Text = $"{_invoiceDiscount:N2} دج";
            TaxText.Text = $"{_invoiceTax:N2} دج";
            InvoiceAmountText.Text = $"{_invoiceAmount:N2} دج";

            // المبلغ الإجمالي المطلوب
            TotalRequiredAmountText.Text = $"{_totalRequiredAmount:N2} دج";

            // تحديث المبلغ المتبقي
            UpdateRemainingAmount();
        }

        private void UpdateRemainingAmount()
        {
            var paidAmount = GetPaidAmountFromTextBox();
            var remaining = _totalRequiredAmount - paidAmount;
            
            RemainingAmountText.Text = $"{remaining:N2} دج";
            RemainingAmountText.Foreground = remaining > 0 ? 
                System.Windows.Media.Brushes.Red : 
                System.Windows.Media.Brushes.Green;
        }

        private decimal GetPaidAmountFromTextBox()
        {
            if (decimal.TryParse(PaidAmountTextBox.Text, NumberStyles.Number, CultureInfo.CurrentCulture, out decimal amount))
            {
                return Math.Max(0, amount);
            }
            return 0;
        }

        private void PaidAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateRemainingAmount();
        }

        private void PayFullAmountButton_Click(object sender, RoutedEventArgs e)
        {
            PaidAmountTextBox.Text = _totalRequiredAmount.ToString("N2");
            UpdateRemainingAmount();
        }

        private void PayInvoiceOnlyButton_Click(object sender, RoutedEventArgs e)
        {
            PaidAmountTextBox.Text = _invoiceAmount.ToString("N2");
            UpdateRemainingAmount();
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            PaidAmountTextBox.Text = "0";
            UpdateRemainingAmount();
        }

        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var paidAmount = GetPaidAmountFromTextBox();
                
                if (paidAmount < 0)
                {
                    MessageBox.Show("المبلغ المدفوع لا يمكن أن يكون سالباً!", "خطأ في الإدخال", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (paidAmount > _totalRequiredAmount * 1.1m) // السماح بزيادة 10% للتسامح
                {
                    var result = MessageBox.Show(
                        $"المبلغ المدفوع ({paidAmount:N2} دج) أكبر من المبلغ المطلوب ({_totalRequiredAmount:N2} دج).\n\n" +
                        "هل تريد المتابعة؟",
                        "تأكيد المبلغ الزائد",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.No)
                        return;
                }

                // الحصول على طريقة الدفع
                var selectedPaymentMethod = (PaymentMethodComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();
                if (string.IsNullOrEmpty(selectedPaymentMethod))
                {
                    MessageBox.Show("يرجى اختيار طريقة الدفع!", "خطأ في الإدخال", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تعيين القيم
                PaidAmount = paidAmount;
                PaymentMethod = selectedPaymentMethod;
                RemainingAmount = Math.Max(0, _totalRequiredAmount - paidAmount);
                IsConfirmed = true;

                LoggingService.LogInfo($"تم تأكيد الدفع - العميل: {_customer.Name}, المبلغ المدفوع: {PaidAmount:N2}, المتبقي: {RemainingAmount:N2}");

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تأكيد الدفع: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                LoggingService.LogError(ex, "خطأ في تأكيد الدفع");
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            IsConfirmed = false;
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// الحصول على معلومات الدفع المؤكدة
        /// </summary>
        public PaymentInfo GetPaymentInfo()
        {
            return new PaymentInfo
            {
                PaidAmount = PaidAmount,
                PaymentMethod = PaymentMethod,
                RemainingAmount = RemainingAmount,
                TotalRequiredAmount = _totalRequiredAmount,
                InvoiceAmount = _invoiceAmount,
                PreviousDebt = _previousDebt,
                IsConfirmed = IsConfirmed
            };
        }
    }

    /// <summary>
    /// معلومات الدفع
    /// </summary>
    public class PaymentInfo
    {
        public decimal PaidAmount { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public decimal RemainingAmount { get; set; }
        public decimal TotalRequiredAmount { get; set; }
        public decimal InvoiceAmount { get; set; }
        public decimal PreviousDebt { get; set; }
        public bool IsConfirmed { get; set; }
    }
}
