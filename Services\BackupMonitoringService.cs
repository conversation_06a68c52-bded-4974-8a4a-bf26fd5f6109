using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة مراقبة النسخ الاحتياطي
    /// </summary>
    public class BackupMonitoringService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;
        private readonly AdvancedBackupService _backupService;
        private readonly Timer _monitoringTimer;
        private readonly BackupMonitor _monitor;
        private readonly PerformanceCounter? _cpuCounter;
        private readonly PerformanceCounter? _memoryCounter;

        public BackupMonitoringService(
            DatabaseService dbService,
            NotificationService notificationService,
            AdvancedBackupService backupService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));

            _monitor = new BackupMonitor
            {
                MonitorName = "مراقب النسخ الاحتياطي الرئيسي",
                IsEnabled = true
            };

            // إعداد عدادات الأداء
            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"تعذر إعداد عدادات الأداء: {ex.Message}");
            }

            // تشغيل المراقب كل 30 ثانية
            _monitoringTimer = new Timer(MonitorSystem, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
        }

        #region Public Properties

        public BackupMonitor Monitor => _monitor;

        #endregion

        #region Monitoring Methods

        /// <summary>
        /// مراقبة النظام
        /// </summary>
        private async void MonitorSystem(object? state)
        {
            try
            {
                if (!_monitor.IsEnabled) return;

                // جمع مقاييس النظام
                await CollectSystemMetricsAsync();

                // جمع مقاييس المهام
                await CollectJobMetricsAsync();

                // فحص التنبيهات
                await CheckAlertsAsync();

                // فحص المساحة المتاحة
                await CheckDiskSpaceAsync();

                // فحص المهام المتأخرة
                await CheckOverdueJobsAsync();

                // تحديث النشاط الحالي
                await UpdateCurrentActivityAsync();

                _monitor.LastCheckTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في مراقبة النظام");
                _monitor.AddAlert($"خطأ في مراقبة النظام: {ex.Message}", AlertSeverity.Error);
            }
        }

        /// <summary>
        /// جمع مقاييس النظام
        /// </summary>
        private async Task CollectSystemMetricsAsync()
        {
            try
            {
                double cpuUsage = 0;
                double memoryUsage = 0;
                double diskUsage = 0;
                double bandwidthUsage = 0;

                // قياس استخدام المعالج
                if (_cpuCounter != null)
                {
                    cpuUsage = _cpuCounter.NextValue();
                }

                // قياس استخدام الذاكرة
                if (_memoryCounter != null)
                {
                    var availableMemory = _memoryCounter.NextValue();
                    var totalMemory = GetTotalPhysicalMemory();
                    memoryUsage = ((totalMemory - availableMemory) / totalMemory) * 100;
                }

                // قياس استخدام القرص
                diskUsage = await GetDiskUsageAsync();

                // قياس استخدام الشبكة (تقديري)
                bandwidthUsage = await GetNetworkUsageAsync();

                _monitor.UpdateSystemMetrics(cpuUsage, memoryUsage, diskUsage, bandwidthUsage);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في جمع مقاييس النظام");
            }
        }

        /// <summary>
        /// جمع مقاييس المهام
        /// </summary>
        private async Task CollectJobMetricsAsync()
        {
            try
            {
                var jobs = await _backupService.GetAllBackupJobsAsync();

                var totalJobs = jobs.Count();
                var runningJobs = jobs.Count(j => j.IsRunning);
                var scheduledJobs = jobs.Count(j => j.Status == BackupStatus.Scheduled);
                var completedJobs = jobs.Count(j => j.Status == BackupStatus.Completed);
                var failedJobs = jobs.Count(j => j.Status == BackupStatus.Failed);
                var cancelledJobs = jobs.Count(j => j.Status == BackupStatus.Cancelled);

                _monitor.UpdateJobMetrics(totalJobs, runningJobs, scheduledJobs, completedJobs, failedJobs, cancelledJobs);

                // حساب إجمالي حجم النسخ الاحتياطية
                var totalBackupSize = jobs.Sum(j => j.ActualSize);
                _monitor.TotalBackupSize = totalBackupSize;

                // حساب متوسط مدة النسخ الاحتياطي
                var completedJobsWithDuration = jobs.Where(j => j.ActualDuration.HasValue);
                if (completedJobsWithDuration.Any())
                {
                    var averageTicks = completedJobsWithDuration.Average(j => j.ActualDuration!.Value.Ticks);
                    _monitor.AverageBackupDuration = new TimeSpan((long)averageTicks);
                }

                // حساب إجمالي وقت النسخ الاحتياطي
                var totalBackupTime = completedJobsWithDuration.Aggregate(TimeSpan.Zero, (sum, j) => sum.Add(j.ActualDuration!.Value));
                _monitor.TotalBackupTime = totalBackupTime;

                // تحديد المهمة التالية المجدولة
                var nextJob = jobs.Where(j => j.Status == BackupStatus.Scheduled)
                                 .OrderBy(j => j.NextRunTime)
                                 .FirstOrDefault();

                if (nextJob != null)
                {
                    _monitor.NextScheduledJob = nextJob.NextRunTime;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في جمع مقاييس المهام");
            }
        }

        /// <summary>
        /// فحص التنبيهات
        /// </summary>
        private async Task CheckAlertsAsync()
        {
            try
            {
                // فحص معدل النجاح
                if (_monitor.OverallSuccessRate < 80)
                {
                    _monitor.AddAlert(
                        $"معدل نجاح النسخ الاحتياطي منخفض: {_monitor.OverallSuccessRate:F1}%",
                        AlertSeverity.Warning,
                        "يُنصح بمراجعة إعدادات النسخ الاحتياطي والتحقق من الأخطاء"
                    );
                }

                // فحص استخدام الموارد
                if (_monitor.SystemCpuUsage > 90)
                {
                    _monitor.AddAlert(
                        $"استخدام المعالج مرتفع: {_monitor.SystemCpuUsage:F1}%",
                        AlertSeverity.Warning,
                        "قد يؤثر على أداء النسخ الاحتياطي"
                    );
                }

                if (_monitor.SystemMemoryUsage > 90)
                {
                    _monitor.AddAlert(
                        $"استخدام الذاكرة مرتفع: {_monitor.SystemMemoryUsage:F1}%",
                        AlertSeverity.Warning,
                        "قد يؤثر على أداء النسخ الاحتياطي"
                    );
                }

                if (_monitor.SystemDiskUsage > 90)
                {
                    _monitor.AddAlert(
                        $"مساحة القرص منخفضة: {_monitor.SystemDiskUsage:F1}%",
                        AlertSeverity.Critical,
                        "يجب تحرير مساحة أو تنظيف النسخ القديمة"
                    );
                }

                // فحص المهام الفاشلة
                if (_monitor.FailedJobs > 0)
                {
                    var recentFailures = await GetRecentFailuresAsync();
                    if (recentFailures > 3)
                    {
                        _monitor.AddAlert(
                            $"عدد كبير من المهام الفاشلة: {recentFailures}",
                            AlertSeverity.Error,
                            "يجب مراجعة سجلات الأخطاء وإصلاح المشاكل"
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في فحص التنبيهات");
            }
        }

        /// <summary>
        /// فحص مساحة القرص
        /// </summary>
        private async Task CheckDiskSpaceAsync()
        {
            try
            {
                var backupPaths = await GetBackupPathsAsync();

                foreach (var path in backupPaths)
                {
                    if (Directory.Exists(path))
                    {
                        var driveInfo = new DriveInfo(Path.GetPathRoot(path) ?? "C:\\");
                        var usagePercentage = (double)(driveInfo.TotalSize - driveInfo.AvailableFreeSpace) / driveInfo.TotalSize * 100;

                        if (usagePercentage > 95)
                        {
                            _monitor.AddAlert(
                                $"مساحة القرص ممتلئة تقريباً في {path}: {usagePercentage:F1}%",
                                AlertSeverity.Critical,
                                "يجب تحرير مساحة فوراً لتجنب فشل النسخ الاحتياطي"
                            );
                        }
                        else if (usagePercentage > 85)
                        {
                            _monitor.AddAlert(
                                $"مساحة القرص منخفضة في {path}: {usagePercentage:F1}%",
                                AlertSeverity.Warning,
                                "يُنصح بتحرير مساحة أو تنظيف النسخ القديمة"
                            );
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في فحص مساحة القرص");
            }
        }

        /// <summary>
        /// فحص المهام المتأخرة
        /// </summary>
        private async Task CheckOverdueJobsAsync()
        {
            try
            {
                var overdueJobs = await GetOverdueJobsAsync();

                if (overdueJobs.Any())
                {
                    _monitor.AddAlert(
                        $"يوجد {overdueJobs.Count()} مهمة متأخرة",
                        AlertSeverity.Warning,
                        "بعض المهام المجدولة لم يتم تنفيذها في الوقت المحدد"
                    );
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في فحص المهام المتأخرة");
            }
        }

        /// <summary>
        /// تحديث النشاط الحالي
        /// </summary>
        private async Task UpdateCurrentActivityAsync()
        {
            try
            {
                var runningJobs = await GetRunningJobsAsync();

                if (runningJobs.Any())
                {
                    var currentJob = runningJobs.First();
                    _monitor.CurrentActivity = $"تشغيل: {currentJob.JobName}";
                }
                else
                {
                    _monitor.CurrentActivity = "في وضع الاستعداد";
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحديث النشاط الحالي");
                _monitor.CurrentActivity = "خطأ في المراقبة";
            }
        }

        #endregion

        #region Helper Methods

        private double GetTotalPhysicalMemory()
        {
            try
            {
                var totalMemoryCounter = new PerformanceCounter("Memory", "Commit Limit");
                return totalMemoryCounter.NextValue() / 1024 / 1024; // Convert to MB
            }
            catch
            {
                return 8192; // Default 8GB
            }
        }

        private async Task<double> GetDiskUsageAsync()
        {
            try
            {
                await Task.Delay(1); // إضافة await لتجنب التحذير
                var systemDrive = DriveInfo.GetDrives().FirstOrDefault(d => d.Name == Path.GetPathRoot(Environment.SystemDirectory));
                if (systemDrive != null)
                {
                    return (double)(systemDrive.TotalSize - systemDrive.AvailableFreeSpace) / systemDrive.TotalSize * 100;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<double> GetNetworkUsageAsync()
        {
            // تقدير بسيط لاستخدام الشبكة
            // في التطبيق الحقيقي، يمكن استخدام عدادات الأداء المناسبة
            await Task.Delay(1); // إضافة await لتجنب التحذير
            return 0;
        }

        private async Task<IEnumerable<string>> GetBackupPathsAsync()
        {
            try
            {
                const string sql = "SELECT DISTINCT DestinationPath FROM BackupJobs WHERE DestinationPath IS NOT NULL";
                var paths = await _dbService.QueryAsync<string>(sql);
                return paths.Where(p => !string.IsNullOrEmpty(p));
            }
            catch
            {
                return new List<string>();
            }
        }

        private async Task<int> GetRecentFailuresAsync()
        {
            try
            {
                const string sql = @"
                    SELECT COUNT(*) FROM BackupHistory
                    WHERE Status = 'Failed' AND StartTime >= @Since";

                var since = DateTime.Now.AddHours(-24);
                return await _dbService.QuerySingleAsync<int>(sql, new { Since = since.ToString("yyyy-MM-dd HH:mm:ss") });
            }
            catch
            {
                return 0;
            }
        }

        private async Task<IEnumerable<BackupJob>> GetOverdueJobsAsync()
        {
            try
            {
                const string sql = @"
                    SELECT * FROM BackupJobs
                    WHERE Status = 'Scheduled' AND NextRunTime < @Now";

                var jobsData = await _dbService.QueryAsync<dynamic>(sql, new { Now = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
                return jobsData.Select(MapToBackupJob);
            }
            catch
            {
                return new List<BackupJob>();
            }
        }

        private async Task<IEnumerable<BackupJob>> GetRunningJobsAsync()
        {
            try
            {
                const string sql = "SELECT * FROM BackupJobs WHERE Status = 'Running'";
                var jobsData = await _dbService.QueryAsync<dynamic>(sql);
                return jobsData.Select(MapToBackupJob);
            }
            catch
            {
                return new List<BackupJob>();
            }
        }

        private BackupJob MapToBackupJob(dynamic data)
        {
            // Implementation similar to AdvancedBackupService
            return new BackupJob
            {
                Id = data.Id,
                JobName = data.JobName ?? "",
                JobCode = data.JobCode ?? ""
                // ... other properties
            };
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// تفعيل المراقبة
        /// </summary>
        public void EnableMonitoring()
        {
            _monitor.IsEnabled = true;
            LoggingService.LogInfo("تم تفعيل مراقبة النسخ الاحتياطي");
        }

        /// <summary>
        /// تعطيل المراقبة
        /// </summary>
        public void DisableMonitoring()
        {
            _monitor.IsEnabled = false;
            LoggingService.LogInfo("تم تعطيل مراقبة النسخ الاحتياطي");
        }

        /// <summary>
        /// مسح التنبيهات
        /// </summary>
        public void ClearAlerts()
        {
            _monitor.ClearAlerts();
            LoggingService.LogInfo("تم مسح تنبيهات النسخ الاحتياطي");
        }

        /// <summary>
        /// إضافة تنبيه مخصص
        /// </summary>
        public void AddCustomAlert(string message, AlertSeverity severity, string details = "")
        {
            _monitor.AddAlert(message, severity, details);

            // إرسال إشعار إذا كان التنبيه حرج
            if (severity == AlertSeverity.Critical || severity == AlertSeverity.Error)
            {
                _ = Task.Run(async () => await SendCriticalAlertNotificationAsync(message, details));
            }
        }

        /// <summary>
        /// الحصول على تقرير الحالة
        /// </summary>
        public async Task<string> GenerateStatusReportAsync()
        {
            try
            {
                await Task.Delay(1); // إضافة await لتجنب التحذير
                var report = $@"
تقرير حالة النسخ الاحتياطي
=======================

الوقت: {DateTime.Now:dd/MM/yyyy HH:mm:ss}
الحالة: {_monitor.StatusDisplay}

إحصائيات المهام:
- إجمالي المهام: {_monitor.TotalJobs}
- قيد التشغيل: {_monitor.RunningJobs}
- مجدولة: {_monitor.ScheduledJobs}
- مكتملة: {_monitor.CompletedJobs}
- فاشلة: {_monitor.FailedJobs}
- معدل النجاح: {_monitor.FormattedSuccessRate}

موارد النظام:
- استخدام المعالج: {_monitor.FormattedCpuUsage}
- استخدام الذاكرة: {_monitor.FormattedMemoryUsage}
- استخدام القرص: {_monitor.FormattedDiskUsage}

إحصائيات النسخ:
- إجمالي حجم النسخ: {_monitor.FormattedTotalBackupSize}
- متوسط مدة النسخ: {_monitor.FormattedAverageBackupDuration}
- إجمالي وقت النسخ: {_monitor.FormattedTotalBackupTime}

التنبيهات:
- إجمالي التنبيهات: {_monitor.AlertsCount}
- تحذيرات: {_monitor.WarningsCount}
- أخطاء: {_monitor.ErrorsCount}

النشاط الحالي: {_monitor.CurrentActivity}
المهمة التالية: {_monitor.FormattedNextScheduledJob}
";

                return report;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير الحالة");
                return "خطأ في إنشاء التقرير";
            }
        }

        private async Task SendCriticalAlertNotificationAsync(string message, string details)
        {
            try
            {
                await _notificationService.SendNotificationAsync(
                    "تنبيه حرج - النسخ الاحتياطي",
                    $"{message}\n\nالتفاصيل: {details}",
                    Models.NotificationType.Error
                );
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إرسال إشعار التنبيه الحرج");
            }
        }

        #endregion

        #region Disposal

        public void Dispose()
        {
            _monitoringTimer?.Dispose();
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
        }

        #endregion
    }
}
