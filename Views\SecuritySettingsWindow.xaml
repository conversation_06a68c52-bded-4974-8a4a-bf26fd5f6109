<Window x:Class="SalesManagementSystem.Views.SecuritySettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات الأمان - نظام إدارة المبيعات" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                               Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Toggle Switch Style -->
        <Style x:Key="ToggleSwitchStyle" TargetType="CheckBox">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="CheckBox">
                        <Grid>
                            <Border x:Name="SwitchTrack" Width="50" Height="25" CornerRadius="12.5"
                                   Background="#FFCCCCCC" BorderThickness="1" BorderBrush="#FFAAAAAA"/>
                            <Border x:Name="SwitchThumb" Width="21" Height="21" CornerRadius="10.5"
                                   Background="White" HorizontalAlignment="Left" Margin="2,2,0,2">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" BlurRadius="3" ShadowDepth="1" Opacity="0.3"/>
                                </Border.Effect>
                            </Border>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="SwitchTrack" Property="Background" Value="#FF4CAF50"/>
                                <Setter TargetName="SwitchThumb" Property="HorizontalAlignment" Value="Right"/>
                                <Setter TargetName="SwitchThumb" Property="Margin" Value="0,2,2,2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#FFF5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#FF795548">
            <Border.Effect>
                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
            </Border.Effect>
            <Grid Margin="20,0">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🔐" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="إعدادات الأمان والحماية" FontSize="18"
                              FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- العمود الأيسر -->
                <StackPanel Grid.Column="0">
                    
                    <!-- إعدادات كلمات المرور -->
                    <Border Background="White" CornerRadius="10" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="🔑 إعدادات كلمات المرور" FontSize="16" FontWeight="Bold" 
                                      Foreground="#FF795548" Margin="0,0,0,15"/>

                            <StackPanel Grid.Row="1">
                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="الحد الأدنى لطول كلمة المرور:" FontSize="12" VerticalAlignment="Center"/>
                                    <TextBox x:Name="MinPasswordLengthTextBox" Grid.Column="1" Text="4" Width="50" Height="25" 
                                            TextAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إجبار استخدام أحرف كبيرة وصغيرة:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="RequireMixedCaseCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="False"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إجبار استخدام الأرقام:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="RequireNumbersCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="False"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="منع كلمات المرور الضعيفة:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="PreventWeakPasswordsCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- إعدادات الجلسات -->
                    <Border Background="White" CornerRadius="10" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="⏱️ إعدادات الجلسات" FontSize="16" FontWeight="Bold" 
                                      Foreground="#FF795548" Margin="0,0,0,15"/>

                            <StackPanel Grid.Row="1">
                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="انتهاء الجلسة التلقائي (بالدقائق):" FontSize="12" VerticalAlignment="Center"/>
                                    <TextBox x:Name="SessionTimeoutTextBox" Grid.Column="1" Text="30" Width="50" Height="25" 
                                            TextAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="تفعيل انتهاء الجلسة التلقائي:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="EnableSessionTimeoutCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="False"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="تذكير قبل انتهاء الجلسة:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="SessionWarningCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- إعدادات المحاولات -->
                    <Border Background="White" CornerRadius="10">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="🚫 إعدادات المحاولات الفاشلة" FontSize="16" FontWeight="Bold" 
                                      Foreground="#FF795548" Margin="0,0,0,15"/>

                            <StackPanel Grid.Row="1">
                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="عدد المحاولات المسموحة:" FontSize="12" VerticalAlignment="Center"/>
                                    <TextBox x:Name="MaxLoginAttemptsTextBox" Grid.Column="1" Text="3" Width="50" Height="25" 
                                            TextAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="مدة الحظر (بالدقائق):" FontSize="12" VerticalAlignment="Center"/>
                                    <TextBox x:Name="LockoutDurationTextBox" Grid.Column="1" Text="15" Width="50" Height="25" 
                                            TextAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="تفعيل حظر المحاولات الفاشلة:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="EnableLockoutCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>
                </StackPanel>

                <!-- العمود الأيمن -->
                <StackPanel Grid.Column="2">
                    
                    <!-- إعدادات التسجيل -->
                    <Border Background="White" CornerRadius="10" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="📝 إعدادات التسجيل والمراقبة" FontSize="16" FontWeight="Bold" 
                                      Foreground="#FF795548" Margin="0,0,0,15"/>

                            <StackPanel Grid.Row="1">
                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="تسجيل محاولات تسجيل الدخول:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="LogLoginAttemptsCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="تسجيل العمليات الحساسة:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="LogSensitiveOperationsCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="تسجيل تغييرات البيانات:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="LogDataChangesCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="False"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="الاحتفاظ بالسجلات (بالأيام):" FontSize="12" VerticalAlignment="Center"/>
                                    <TextBox x:Name="LogRetentionDaysTextBox" Grid.Column="1" Text="90" Width="50" Height="25" 
                                            TextAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- إعدادات النسخ الاحتياطي -->
                    <Border Background="White" CornerRadius="10" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="💾 إعدادات النسخ الاحتياطي" FontSize="16" FontWeight="Bold" 
                                      Foreground="#FF795548" Margin="0,0,0,15"/>

                            <StackPanel Grid.Row="1">
                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="النسخ الاحتياطي التلقائي:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="AutoBackupCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="تكرار النسخ (بالساعات):" FontSize="12" VerticalAlignment="Center"/>
                                    <TextBox x:Name="BackupIntervalTextBox" Grid.Column="1" Text="24" Width="50" Height="25" 
                                            TextAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="تشفير النسخ الاحتياطية:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="EncryptBackupsCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Button Content="📁 تحديد مجلد النسخ الاحتياطي" 
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FFFF9800" Click="SelectBackupFolder_Click" 
                                       HorizontalAlignment="Stretch" Margin="0,10,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- إعدادات متقدمة -->
                    <Border Background="White" CornerRadius="10">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="⚙️ إعدادات متقدمة" FontSize="16" FontWeight="Bold" 
                                      Foreground="#FF795548" Margin="0,0,0,15"/>

                            <StackPanel Grid.Row="1">
                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="تفعيل وضع المطور:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="DeveloperModeCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="False"/>
                                </Grid>

                                <Grid Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="السماح بالوصول عن بُعد:" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="RemoteAccessCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}" 
                                             VerticalAlignment="Center" IsChecked="False"/>
                                </Grid>

                                <Button Content="🔄 إعادة تعيين جميع الإعدادات" 
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FFF44336" Click="ResetAllSettings_Click" 
                                       HorizontalAlignment="Stretch" Margin="0,10,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- شريط الأزرار -->
        <Border Grid.Row="2" Background="White">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button x:Name="SaveButton" Content="💾 حفظ الإعدادات" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF4CAF50" Click="Save_Click" Width="130"/>
                
                <Button x:Name="ExportButton" Content="📤 تصدير الإعدادات" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF2196F3" Click="Export_Click" Width="130"/>
                
                <Button x:Name="ImportButton" Content="📥 استيراد الإعدادات" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FFFF9800" Click="Import_Click" Width="130"/>
                
                <Button x:Name="CloseButton" Content="❌ إغلاق" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF607D8B" Click="Close_Click" Width="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
