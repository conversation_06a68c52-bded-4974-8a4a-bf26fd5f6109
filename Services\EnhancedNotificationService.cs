using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Threading;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الإشعارات المحسنة
    /// </summary>
    public class EnhancedNotificationService : INotifyPropertyChanged
    {
        private static EnhancedNotificationService? _instance;
        private ObservableCollection<EnhancedNotification> _notifications;
        private int _nextId = 1;
        private DispatcherTimer _updateTimer;
        private readonly NotificationSettingsService _notificationSettings;

        public static EnhancedNotificationService Instance => _instance ??= new EnhancedNotificationService();

        private EnhancedNotificationService()
        {
            _notifications = new ObservableCollection<EnhancedNotification>();
            _notificationSettings = NotificationSettingsService.Instance;

            // تحديث الوقت كل دقيقة
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(1)
            };
            _updateTimer.Tick += (s, e) => UpdateTimeAgo();
            _updateTimer.Start();

            LoadSampleNotifications();
        }

        public ObservableCollection<EnhancedNotification> Notifications
        {
            get => _notifications;
            private set
            {
                _notifications = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(UnreadCount));
                OnPropertyChanged(nameof(HasUnreadNotifications));
                OnPropertyChanged(nameof(UnreadCountText));
            }
        }

        public int UnreadCount => _notifications.Count(n => !n.IsRead);
        public bool HasUnreadNotifications => UnreadCount > 0;
        public string UnreadCountText => UnreadCount > 99 ? "99+" : UnreadCount.ToString();

        public void AddNotification(string title, string message,
            EnhancedNotificationType type = EnhancedNotificationType.Info,
            EnhancedNotificationPriority priority = EnhancedNotificationPriority.Normal,
            string category = "", bool isSticky = false, string notificationType = "")
        {
            // التحقق من إعدادات الإشعارات
            if (!string.IsNullOrEmpty(notificationType) && !_notificationSettings.IsNotificationEnabled(notificationType))
            {
                return; // لا تعرض الإشعار إذا كان معطلاً
            }

            var notification = new EnhancedNotification
            {
                Id = _nextId++,
                Title = title,
                Message = message,
                Type = type,
                Priority = priority,
                Category = category,
                IsSticky = isSticky,
                CreatedAt = DateTime.Now,
                IsRead = false
            };

            _notifications.Insert(0, notification);
            OnPropertyChanged(nameof(UnreadCount));
            OnPropertyChanged(nameof(HasUnreadNotifications));
            OnPropertyChanged(nameof(UnreadCountText));

            // إشعار صوتي للإشعارات المهمة (إذا كان مفعلاً)
            var settings = _notificationSettings.GetCurrentSettings();
            if (settings.NotificationSounds &&
                (priority == EnhancedNotificationPriority.High || priority == EnhancedNotificationPriority.Critical))
            {
                System.Media.SystemSounds.Asterisk.Play();
            }

            // تسجيل الإشعار في السجل (إذا كان مفعلاً)
            if (!string.IsNullOrEmpty(notificationType))
            {
                _notificationSettings.LogNotification(notificationType, $"{title}: {message}");
            }
        }

        public void MarkAsRead(int notificationId)
        {
            var notification = _notifications.FirstOrDefault(n => n.Id == notificationId);
            if (notification != null && !notification.IsRead)
            {
                notification.IsRead = true;
                OnPropertyChanged(nameof(UnreadCount));
                OnPropertyChanged(nameof(HasUnreadNotifications));
                OnPropertyChanged(nameof(UnreadCountText));
            }
        }

        public void MarkAllAsRead()
        {
            foreach (var notification in _notifications.Where(n => !n.IsRead))
            {
                notification.IsRead = true;
            }
            OnPropertyChanged(nameof(UnreadCount));
            OnPropertyChanged(nameof(HasUnreadNotifications));
            OnPropertyChanged(nameof(UnreadCountText));
        }

        public void RemoveNotification(int notificationId)
        {
            var notification = _notifications.FirstOrDefault(n => n.Id == notificationId);
            if (notification != null && !notification.IsSticky)
            {
                _notifications.Remove(notification);
                OnPropertyChanged(nameof(UnreadCount));
                OnPropertyChanged(nameof(HasUnreadNotifications));
                OnPropertyChanged(nameof(UnreadCountText));
            }
        }

        public void ClearOldNotifications(int daysOld = 7)
        {
            var cutoffDate = DateTime.Now.AddDays(-daysOld);
            var oldNotifications = _notifications.Where(n => n.CreatedAt < cutoffDate && !n.IsSticky).ToList();

            foreach (var notification in oldNotifications)
            {
                _notifications.Remove(notification);
            }

            OnPropertyChanged(nameof(UnreadCount));
            OnPropertyChanged(nameof(HasUnreadNotifications));
            OnPropertyChanged(nameof(UnreadCountText));
        }

        public void ClearAllNotifications()
        {
            var nonStickyNotifications = _notifications.Where(n => !n.IsSticky).ToList();
            foreach (var notification in nonStickyNotifications)
            {
                _notifications.Remove(notification);
            }

            OnPropertyChanged(nameof(UnreadCount));
            OnPropertyChanged(nameof(HasUnreadNotifications));
            OnPropertyChanged(nameof(UnreadCountText));
        }

        private void UpdateTimeAgo()
        {
            // تحديث الوقت النسبي للإشعارات
            // سيتم تحديث TimeAgo تلقائياً عند الوصول إليه
        }

        private void LoadSampleNotifications()
        {
            // إشعارات تجريبية متنوعة
            AddNotification("مبيعات جديدة", "تم إتمام عملية بيع بقيمة 15,000 دج لعميل أحمد محمد",
                EnhancedNotificationType.Sale, EnhancedNotificationPriority.Normal, "مبيعات");

            AddNotification("مخزون منخفض", "المنتج 'لابتوب HP' أوشك على النفاد (5 قطع متبقية)",
                EnhancedNotificationType.LowStock, EnhancedNotificationPriority.High, "مخزون");

            AddNotification("مصروف جديد", "تم إضافة مصروف: فاتورة كهرباء - 5,000 دج",
                EnhancedNotificationType.Expense, EnhancedNotificationPriority.Normal, "مصاريف");

            AddNotification("عميل جديد", "تم إضافة عميل جديد: فاطمة علي - رقم الهاتف: 0555123456",
                EnhancedNotificationType.NewCustomer, EnhancedNotificationPriority.Normal, "عملاء");

            AddNotification("دفعة جديدة", "تم استلام دفعة بقيمة 25,000 دج من عميل محمد حسن",
                EnhancedNotificationType.Payment, EnhancedNotificationPriority.Normal, "مدفوعات");

            AddNotification("تحديث النظام", "تم تحديث النظام إلى الإصدار 2.1.0 بنجاح",
                EnhancedNotificationType.SystemUpdate, EnhancedNotificationPriority.Low, "نظام");

            AddNotification("تنبيه هام", "يرجى مراجعة تقرير المبيعات الشهري قبل نهاية اليوم",
                EnhancedNotificationType.Warning, EnhancedNotificationPriority.High, "تقارير", true);

            AddNotification("نجح النسخ الاحتياطي", "تم إنشاء نسخة احتياطية من البيانات بنجاح",
                EnhancedNotificationType.Success, EnhancedNotificationPriority.Low, "نظام");

            // إضافة بعض الإشعارات القديمة (مقروءة)
            var oldNotification = new EnhancedNotification
            {
                Id = _nextId++,
                Title = "مبيعات أمس",
                Message = "تم تحقيق مبيعات بقيمة 45,000 دج أمس",
                Type = EnhancedNotificationType.Sale,
                Priority = EnhancedNotificationPriority.Normal,
                Category = "مبيعات",
                CreatedAt = DateTime.Now.AddHours(-25),
                IsRead = true
            };
            _notifications.Add(oldNotification);
        }

        // إضافة إشعارات تلقائية للأحداث
        public void NotifySaleCompleted(decimal amount, string customerName)
        {
            AddNotification("مبيعات جديدة",
                $"تم إتمام عملية بيع بقيمة {amount:F2} دج للعميل {customerName}",
                EnhancedNotificationType.Sale, EnhancedNotificationPriority.Normal, "مبيعات",
                notificationType: "sale_completed");
        }

        public void NotifyExpenseAdded(decimal amount, string title)
        {
            AddNotification("مصروف جديد",
                $"تم إضافة مصروف: {title} - {amount:F2} دج",
                EnhancedNotificationType.Expense, EnhancedNotificationPriority.Normal, "مصاريف");
        }

        public void NotifyLowStock(string productName, int quantity)
        {
            AddNotification("مخزون منخفض",
                $"المنتج '{productName}' أوشك على النفاد ({quantity} قطع متبقية)",
                EnhancedNotificationType.LowStock, EnhancedNotificationPriority.High, "مخزون",
                notificationType: "low_stock");
        }

        public void NotifyNewCustomer(string customerName)
        {
            AddNotification("عميل جديد",
                $"تم إضافة عميل جديد: {customerName}",
                EnhancedNotificationType.NewCustomer, EnhancedNotificationPriority.Normal, "عملاء",
                notificationType: "new_customer");
        }

        public void NotifyPaymentReceived(decimal amount, string customerName)
        {
            AddNotification("دفعة جديدة",
                $"تم استلام دفعة بقيمة {amount:F2} دج من العميل {customerName}",
                EnhancedNotificationType.Payment, EnhancedNotificationPriority.Normal, "مدفوعات");
        }

        public void NotifyUserLogin(string username)
        {
            AddNotification("تسجيل دخول",
                $"قام المستخدم {username} بتسجيل الدخول",
                EnhancedNotificationType.Info, EnhancedNotificationPriority.Low, "نظام",
                notificationType: "user_login");
        }

        public void NotifyBackupCreated()
        {
            AddNotification("نسخة احتياطية",
                "تم إنشاء نسخة احتياطية من البيانات بنجاح",
                EnhancedNotificationType.Success, EnhancedNotificationPriority.Normal, "نظام",
                notificationType: "backup_created");
        }

        public void NotifyFailedLogin(string username)
        {
            AddNotification("محاولة دخول فاشلة",
                $"محاولة دخول فاشلة للمستخدم {username}",
                EnhancedNotificationType.Warning, EnhancedNotificationPriority.High, "أمان",
                notificationType: "failed_login");
        }

        public void NotifyPasswordChanged(string username)
        {
            AddNotification("تغيير كلمة المرور",
                $"تم تغيير كلمة المرور للمستخدم {username}",
                EnhancedNotificationType.Info, EnhancedNotificationPriority.Normal, "أمان",
                notificationType: "password_change");
        }

        public void NotifyNewUser(string username)
        {
            AddNotification("مستخدم جديد",
                $"تم إضافة مستخدم جديد: {username}",
                EnhancedNotificationType.Info, EnhancedNotificationPriority.Normal, "أمان",
                notificationType: "new_user");
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
