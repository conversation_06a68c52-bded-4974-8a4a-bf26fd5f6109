using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class SampleDataService
    {
        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private readonly CategoryService _categoryService;
        private readonly WarehouseService _warehouseService;
        private readonly CustomerService _customerService;
        private readonly SupplierService _supplierService;
        private readonly InvoiceService _invoiceService;

        public SampleDataService(DatabaseService dbService)
        {
            _dbService = dbService;
            _productService = new ProductService(dbService);
            _categoryService = new CategoryService(dbService);
            _customerService = new CustomerService(dbService);
            _supplierService = new SupplierService(dbService);

            var settingsService = new SettingsService(dbService);
            var notificationService = new NotificationService(dbService, settingsService);
            var paymentService = new PaymentService(dbService, notificationService);
            _invoiceService = new InvoiceService(dbService, notificationService, paymentService);
            _warehouseService = new WarehouseService(dbService, notificationService);
        }

        public async Task<bool> HasExistingDataAsync()
        {
            try
            {
                // التحقق من وجود بيانات في الجداول الرئيسية
                var customersCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Customers WHERE Id > 1");
                var suppliersCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Suppliers");
                var productsCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Products");
                var invoicesCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Invoices");
                var salesCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Sales");

                return customersCount > 0 || suppliersCount > 0 || productsCount > 0 || invoicesCount > 0 || salesCount > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task CreateCompleteSampleDataAsync()
        {
            try
            {
                LoggingService.LogInfo("بدء إنشاء البيانات التجريبية المترابطة...");

                // 1. إنشاء العملاء
                await CreateSampleCustomersAsync();
                LoggingService.LogInfo("تم إنشاء العملاء التجريبيين");

                // 2. إنشاء الموردين
                await CreateSampleSuppliersAsync();
                LoggingService.LogInfo("تم إنشاء الموردين التجريبيين");

                // 3. إنشاء الفئات والمخازن والمنتجات
                await CreateSampleProductsAsync();
                LoggingService.LogInfo("تم إنشاء المنتجات التجريبية");

                // 4. إنشاء فواتير الشراء (لإضافة مخزون)
                await CreateSamplePurchaseInvoicesAsync();
                LoggingService.LogInfo("تم إنشاء فواتير الشراء التجريبية");

                // 5. إنشاء فواتير البيع (بعضها مدفوع وبعضها بدين)
                await CreateSampleSalesInvoicesAsync();
                LoggingService.LogInfo("تم إنشاء فواتير البيع التجريبية");

                // 6. إنشاء عمليات البيع السريع
                await CreateSampleQuickSalesAsync();
                LoggingService.LogInfo("تم إنشاء عمليات البيع السريع التجريبية");

                // 7. إنشاء الديون والمستحقات
                await CreateSampleDebtsAndPayablesAsync();
                LoggingService.LogInfo("تم إنشاء الديون والمستحقات التجريبية");

                LoggingService.LogInfo("تم إنشاء جميع البيانات التجريبية المترابطة بنجاح!");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء البيانات التجريبية");
                throw;
            }
        }

        public async Task<bool> CreateSampleProductsAsync()
        {
            try
            {
                // التحقق من وجود منتجات مسبقاً
                var existingProducts = await _productService.GetAllProductsAsync();
                if (existingProducts.Any())
                {
                    LoggingService.LogInfo("المنتجات الافتراضية موجودة مسبقاً");
                    return true;
                }

                // إنشاء فئات افتراضية
                await CreateSampleCategoriesAsync();

                // إنشاء مخازن افتراضية
                await CreateSampleWarehousesAsync();

                // الحصول على الفئات والمخازن
                var categories = await _categoryService.GetAllCategoriesAsync();
                var warehouses = await _warehouseService.GetAllWarehousesAsync();

                var category1 = categories.FirstOrDefault(c => c.Name == "إلكترونيات");
                var category2 = categories.FirstOrDefault(c => c.Name == "ملابس");
                var category3 = categories.FirstOrDefault(c => c.Name == "أطعمة");
                var warehouse = warehouses.FirstOrDefault();

                // إنشاء 4 منتجات افتراضية
                var sampleProducts = new List<Product>
                {
                    new Product
                    {
                        Code = "ELEC001",
                        Name = "هاتف ذكي سامسونج",
                        Description = "هاتف ذكي حديث بمواصفات عالية",
                        CategoryId = category1?.Id,

                        PurchasePrice = 800.00m,
                        SalePrice = 1200.00m,
                        SalePrice2 = 1150.00m,
                        Unit = "قطعة",
                        Barcode = "1234567890123",
                        Quantity = 50,
                        MinQuantity = 10,
                        WarehouseId = warehouse?.Id,

                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    },
                    new Product
                    {
                        Code = "ELEC002",
                        Name = "لابتوب ديل",
                        Description = "لابتوب للأعمال والدراسة",
                        CategoryId = category1?.Id,

                        PurchasePrice = 1500.00m,
                        SalePrice = 2200.00m,
                        SalePrice2 = 2100.00m,
                        Unit = "قطعة",
                        Barcode = "2345678901234",
                        Quantity = 25,
                        MinQuantity = 5,
                        WarehouseId = warehouse?.Id,

                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    },
                    new Product
                    {
                        Code = "CLOTH001",
                        Name = "قميص قطني رجالي",
                        Description = "قميص قطني عالي الجودة",
                        CategoryId = category2?.Id,

                        PurchasePrice = 25.00m,
                        SalePrice = 45.00m,
                        SalePrice2 = 40.00m,
                        Unit = "قطعة",
                        Barcode = "3456789012345",
                        Quantity = 100,
                        MinQuantity = 20,
                        WarehouseId = warehouse?.Id,

                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    },
                    new Product
                    {
                        Code = "FOOD001",
                        Name = "أرز بسمتي",
                        Description = "أرز بسمتي فاخر 5 كيلو",
                        CategoryId = category3?.Id,

                        PurchasePrice = 15.00m,
                        SalePrice = 25.00m,
                        SalePrice2 = 23.00m,
                        Unit = "كيس",
                        Barcode = "4567890123456",
                        Quantity = 200,
                        MinQuantity = 50,
                        WarehouseId = warehouse?.Id,

                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    }
                };

                // حفظ المنتجات
                foreach (var product in sampleProducts)
                {
                    var result = await _productService.AddProductAsync(product);
                    if (result != null)
                    {
                        LoggingService.LogInfo($"تم إنشاء المنتج الافتراضي: {product.Name}");
                    }
                    else
                    {
                        LoggingService.LogError($"فشل في إنشاء المنتج الافتراضي: {product.Name}");
                    }
                }

                LoggingService.LogInfo("تم إنشاء جميع المنتجات الافتراضية بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء المنتجات الافتراضية");
                return false;
            }
        }

        private async Task CreateSampleCategoriesAsync()
        {
            try
            {
                var categories = new List<Category>
                {
                    new Category { Name = "إلكترونيات", Description = "أجهزة إلكترونية متنوعة" },
                    new Category { Name = "ملابس", Description = "ملابس رجالية ونسائية" },
                    new Category { Name = "أطعمة", Description = "مواد غذائية متنوعة" },
                    new Category { Name = "أدوات منزلية", Description = "أدوات ومستلزمات منزلية" }
                };

                foreach (var category in categories)
                {
                    var existing = await _categoryService.GetCategoryByNameAsync(category.Name);
                    if (existing == null)
                    {
                        await _categoryService.AddCategoryAsync(category);
                        LoggingService.LogInfo($"تم إنشاء الفئة الافتراضية: {category.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء الفئات الافتراضية");
            }
        }

        private async Task CreateSampleWarehousesAsync()
        {
            try
            {
                var warehouses = new List<Warehouse>
                {
                    new Warehouse
                    {
                        Name = "المخزن الرئيسي",
                        Address = "الطابق الأرضي",
                        Description = "المخزن الرئيسي للمتجر",
                        Status = WarehouseStatus.Active,
                        Type = WarehouseType.Main,
                        IsDefault = true,
                        CreatedAt = DateTime.Now
                    },
                    new Warehouse
                    {
                        Name = "مخزن الإلكترونيات",
                        Address = "الطابق الثاني",
                        Description = "مخزن مخصص للأجهزة الإلكترونية",
                        Status = WarehouseStatus.Active,
                        Type = WarehouseType.Branch,
                        IsDefault = false,
                        CreatedAt = DateTime.Now
                    }
                };

                foreach (var warehouse in warehouses)
                {
                    var existing = await _warehouseService.GetWarehouseByNameAsync(warehouse.Name);
                    if (existing == null)
                    {
                        await _warehouseService.AddWarehouseAsync(warehouse);
                        LoggingService.LogInfo($"تم إنشاء المخزن الافتراضي: {warehouse.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء المخازن الافتراضية");
            }
        }

        private async Task CreateSampleCustomersAsync()
        {
            try
            {
                var customers = new[]
                {
                    new Customer
                    {
                        Name = "أحمد محمد علي",
                        Phone = "0551234567",
                        Email = "<EMAIL>",
                        Address = "حي النصر، الجزائر العاصمة",
                        CustomerType = "عميل دائم",
                        CreditLimit = 50000.00m,
                        Notes = "عميل مميز - خصم 5%"
                    },
                    new Customer
                    {
                        Name = "فاطمة الزهراء بن علي",
                        Phone = "0661234567",
                        Email = "<EMAIL>",
                        Address = "حي الرياض، وهران",
                        CustomerType = "عميل جديد",
                        CreditLimit = 25000.00m,
                        Notes = "عميل جديد - مراقبة الائتمان"
                    },
                    new Customer
                    {
                        Name = "محمد الأمين حسان",
                        Phone = "0771234567",
                        Email = "<EMAIL>",
                        Address = "حي بوعنان، قسنطينة",
                        CustomerType = "عميل تجاري",
                        CreditLimit = 100000.00m,
                        Notes = "عميل تجاري كبير - شروط دفع مرنة"
                    },
                    new Customer
                    {
                        Name = "خديجة بوعلام",
                        Phone = "0551234568",
                        Email = "<EMAIL>",
                        Address = "حي المقام، سطيف",
                        CustomerType = "عميل دائم",
                        CreditLimit = 30000.00m,
                        Notes = "عميل منتظم - دفع نقدي"
                    },
                    new Customer
                    {
                        Name = "يوسف بن صالح",
                        Phone = "0661234568",
                        Email = "<EMAIL>",
                        Address = "حي الأندلس، عنابة",
                        CustomerType = "عميل موسمي",
                        CreditLimit = 15000.00m,
                        Notes = "عميل موسمي - نشاط صيفي"
                    }
                };

                foreach (var customer in customers)
                {
                    // التحقق من وجود العميل بالاسم
                    var allCustomers = await _customerService.GetAllCustomersAsync();
                    var existing = allCustomers.FirstOrDefault(c => c.Name == customer.Name);
                    if (existing == null)
                    {
                        await _customerService.AddCustomerAsync(customer);
                        LoggingService.LogInfo($"تم إنشاء العميل التجريبي: {customer.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء العملاء التجريبيين");
            }
        }

        private async Task CreateSampleSuppliersAsync()
        {
            try
            {
                var suppliers = new[]
                {
                    new Supplier
                    {
                        Name = "شركة التقنيات المتقدمة",
                        Phone = "0213123456",
                        Email = "<EMAIL>",
                        Address = "المنطقة الصناعية، الجزائر العاصمة",
                        Balance = 0.00m,
                        CreatedAt = DateTime.Now
                    },
                    new Supplier
                    {
                        Name = "مؤسسة الأثاث العصري",
                        Phone = "0213234567",
                        Email = "<EMAIL>",
                        Address = "المنطقة الصناعية، وهران",
                        Balance = 0.00m,
                        CreatedAt = DateTime.Now
                    },
                    new Supplier
                    {
                        Name = "شركة المواد الغذائية الطازجة",
                        Phone = "0213345678",
                        Email = "<EMAIL>",
                        Address = "السوق المركزي، قسنطينة",
                        Balance = 0.00m,
                        CreatedAt = DateTime.Now
                    }
                };

                foreach (var supplier in suppliers)
                {
                    // التحقق من وجود المورد بالاسم
                    var allSuppliers = await _supplierService.GetAllSuppliersAsync();
                    var existing = allSuppliers.FirstOrDefault(s => s.Name == supplier.Name);
                    if (existing == null)
                    {
                        await _supplierService.AddSupplierAsync(supplier);
                        LoggingService.LogInfo($"تم إنشاء المورد التجريبي: {supplier.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء الموردين التجريبيين");
            }
        }

        private async Task CreateSamplePurchaseInvoicesAsync()
        {
            try
            {
                // الحصول على الموردين والمنتجات
                var suppliers = await _supplierService.GetAllSuppliersAsync();
                var products = await _productService.GetAllProductsAsync();

                if (!suppliers.Any() || !products.Any())
                {
                    LoggingService.LogWarning("لا توجد موردين أو منتجات لإنشاء فواتير الشراء");
                    return;
                }

                var supplier1 = suppliers.First();
                var supplier2 = suppliers.Skip(1).FirstOrDefault() ?? supplier1;

                // إنشاء فواتير شراء تجريبية
                var purchaseInvoices = new[]
                {
                    new Invoice
                    {
                        InvoiceNumber = "PUR-001",
                        InvoiceType = InvoiceType.Purchase,
                        Status = InvoiceStatus.Paid,
                        SupplierId = supplier1.Id,
                        InvoiceDate = DateTime.Now.AddDays(-30),
                        DueDate = DateTime.Now.AddDays(-15),
                        Subtotal = 15000.00m,
                        TaxAmount = 1500.00m,
                        DiscountAmount = 500.00m,
                        TotalAmount = 16000.00m,
                        PaidAmount = 16000.00m,
                        RemainingAmount = 0.00m,
                        PaymentMethod = "تحويل بنكي",
                        Terms = "30 يوم",
                        Notes = "فاتورة شراء مدفوعة - تجريبية",
                        CreatedBy = "admin",
                        CreatedAt = DateTime.Now.AddDays(-30),
                        Items = new ObservableCollection<InvoiceItem>
                        {
                            new InvoiceItem
                            {
                                ProductId = products.First().Id,
                                ProductName = products.First().Name,
                                Description = "شراء مخزون أولي",
                                Quantity = 10,
                                UnitPrice = 1200.00m,
                                Discount = 200.00m,
                                Total = 11800.00m
                            },
                            new InvoiceItem
                            {
                                ProductId = products.Skip(1).First().Id,
                                ProductName = products.Skip(1).First().Name,
                                Description = "شراء مخزون أولي",
                                Quantity = 5,
                                UnitPrice = 700.00m,
                                Discount = 300.00m,
                                Total = 3200.00m
                            }
                        }
                    }
                };

                foreach (var invoice in purchaseInvoices)
                {
                    await _invoiceService.AddInvoiceAsync(invoice);
                    LoggingService.LogInfo($"تم إنشاء فاتورة الشراء التجريبية: {invoice.InvoiceNumber}");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء فواتير الشراء التجريبية");
            }
        }

        private async Task CreateSampleSalesInvoicesAsync()
        {
            try
            {
                // الحصول على العملاء والمنتجات
                var customers = await _customerService.GetAllCustomersAsync();
                var products = await _productService.GetAllProductsAsync();

                if (!customers.Any() || !products.Any())
                {
                    LoggingService.LogWarning("لا توجد عملاء أو منتجات لإنشاء فواتير البيع");
                    return;
                }

                var customer1 = customers.First();
                var customer2 = customers.Skip(1).FirstOrDefault() ?? customer1;
                var customer3 = customers.Skip(2).FirstOrDefault() ?? customer1;

                // إنشاء فواتير بيع تجريبية (مدفوعة وبديون)
                var salesInvoices = new[]
                {
                    // فاتورة مدفوعة بالكامل
                    new Invoice
                    {
                        InvoiceNumber = "SAL-001",
                        InvoiceType = InvoiceType.Sales,
                        Status = InvoiceStatus.Paid,
                        CustomerId = customer1.Id,
                        Customer = customer1.Name,
                        InvoiceDate = DateTime.Now.AddDays(-20),
                        DueDate = DateTime.Now.AddDays(-5),
                        Subtotal = 12000.00m,
                        TaxAmount = 1200.00m,
                        DiscountAmount = 600.00m,
                        TotalAmount = 12600.00m,
                        PaidAmount = 12600.00m,
                        RemainingAmount = 0.00m,
                        PaymentMethod = "نقدي",
                        Terms = "فوري",
                        Notes = "فاتورة بيع مدفوعة - تجريبية",
                        CreatedBy = "admin",
                        CreatedAt = DateTime.Now.AddDays(-20),
                        Items = new ObservableCollection<InvoiceItem>
                        {
                            new InvoiceItem
                            {
                                ProductId = products.First().Id,
                                ProductName = products.First().Name,
                                Description = "بيع للعميل",
                                Quantity = 2,
                                UnitPrice = 5000.00m,
                                Discount = 300.00m,
                                Total = 9700.00m
                            },
                            new InvoiceItem
                            {
                                ProductId = products.Skip(1).First().Id,
                                ProductName = products.Skip(1).First().Name,
                                Description = "بيع للعميل",
                                Quantity = 1,
                                UnitPrice = 2600.00m,
                                Discount = 300.00m,
                                Total = 2300.00m
                            }
                        }
                    },
                    // فاتورة مدفوعة جزئياً (بدين)
                    new Invoice
                    {
                        InvoiceNumber = "SAL-002",
                        InvoiceType = InvoiceType.Sales,
                        Status = InvoiceStatus.Sent,
                        CustomerId = customer2.Id,
                        Customer = customer2.Name,
                        InvoiceDate = DateTime.Now.AddDays(-10),
                        DueDate = DateTime.Now.AddDays(20),
                        Subtotal = 8500.00m,
                        TaxAmount = 850.00m,
                        DiscountAmount = 200.00m,
                        TotalAmount = 9150.00m,
                        PaidAmount = 4000.00m,
                        RemainingAmount = 5150.00m,
                        PaymentMethod = "بطاقة ائتمان",
                        Terms = "30 يوم",
                        Notes = "فاتورة بيع مدفوعة جزئياً - دين 5150 دج",
                        CreatedBy = "admin",
                        CreatedAt = DateTime.Now.AddDays(-10),
                        Items = new ObservableCollection<InvoiceItem>
                        {
                            new InvoiceItem
                            {
                                ProductId = products.Skip(2).First().Id,
                                ProductName = products.Skip(2).First().Name,
                                Description = "بيع بالدين",
                                Quantity = 5,
                                UnitPrice = 1500.00m,
                                Discount = 100.00m,
                                Total = 7400.00m
                            },
                            new InvoiceItem
                            {
                                ProductId = products.Last().Id,
                                ProductName = products.Last().Name,
                                Description = "بيع بالدين",
                                Quantity = 8,
                                UnitPrice = 250.00m,
                                Discount = 100.00m,
                                Total = 1900.00m
                            }
                        }
                    },
                    // فاتورة غير مدفوعة (دين كامل)
                    new Invoice
                    {
                        InvoiceNumber = "SAL-003",
                        InvoiceType = InvoiceType.Sales,
                        Status = InvoiceStatus.Draft,
                        CustomerId = customer3.Id,
                        Customer = customer3.Name,
                        InvoiceDate = DateTime.Now.AddDays(-5),
                        DueDate = DateTime.Now.AddDays(25),
                        Subtotal = 6200.00m,
                        TaxAmount = 620.00m,
                        DiscountAmount = 150.00m,
                        TotalAmount = 6670.00m,
                        PaidAmount = 0.00m,
                        RemainingAmount = 6670.00m,
                        PaymentMethod = "تحويل بنكي",
                        Terms = "30 يوم",
                        Notes = "فاتورة بيع غير مدفوعة - دين كامل",
                        CreatedBy = "admin",
                        CreatedAt = DateTime.Now.AddDays(-5),
                        Items = new ObservableCollection<InvoiceItem>
                        {
                            new InvoiceItem
                            {
                                ProductId = products.First().Id,
                                ProductName = products.First().Name,
                                Description = "بيع بالدين",
                                Quantity = 1,
                                UnitPrice = 4500.00m,
                                Discount = 100.00m,
                                Total = 4400.00m
                            },
                            new InvoiceItem
                            {
                                ProductId = products.Skip(3).First().Id,
                                ProductName = products.Skip(3).First().Name,
                                Description = "بيع بالدين",
                                Quantity = 10,
                                UnitPrice = 200.00m,
                                Discount = 50.00m,
                                Total = 1950.00m
                            }
                        }
                    }
                };

                foreach (var invoice in salesInvoices)
                {
                    await _invoiceService.AddInvoiceAsync(invoice);
                    LoggingService.LogInfo($"تم إنشاء فاتورة البيع التجريبية: {invoice.InvoiceNumber} - المبلغ: {invoice.TotalAmount} دج - الدين: {invoice.RemainingAmount} دج");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء فواتير البيع التجريبية");
            }
        }

        private async Task CreateSampleQuickSalesAsync()
        {
            try
            {
                var products = await _productService.GetAllProductsAsync();
                if (!products.Any())
                {
                    LoggingService.LogWarning("لا توجد منتجات لإنشاء عمليات البيع السريع");
                    return;
                }

                // إنشاء عمليات بيع سريع تجريبية
                var quickSales = new[]
                {
                    new
                    {
                        InvoiceNumber = "QS-20241225-001",
                        CustomerId = 1,
                        Date = DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd HH:mm:ss"),
                        Subtotal = 3500.00m,
                        Discount = 100.00m,
                        Tax = 0.00m,
                        Total = 3400.00m,
                        PaymentMethod = "نقدي",
                        PaymentStatus = "مدفوع",
                        Notes = "بيع سريع تجريبي - مدفوع",
                        CreatedAt = DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd HH:mm:ss"),
                        Items = new[]
                        {
                            new { ProductId = products.First().Id, Quantity = 1, UnitPrice = 2500.00m, Discount = 50.00m, Total = 2450.00m },
                            new { ProductId = products.Skip(1).First().Id, Quantity = 1, UnitPrice = 1000.00m, Discount = 50.00m, Total = 950.00m }
                        }
                    },
                    new
                    {
                        InvoiceNumber = "QS-20241225-002",
                        CustomerId = 1,
                        Date = DateTime.Now.AddDays(-3).ToString("yyyy-MM-dd HH:mm:ss"),
                        Subtotal = 1800.00m,
                        Discount = 0.00m,
                        Tax = 0.00m,
                        Total = 1800.00m,
                        PaymentMethod = "بطاقة ائتمان",
                        PaymentStatus = "مدفوع",
                        Notes = "بيع سريع تجريبي - مدفوع",
                        CreatedAt = DateTime.Now.AddDays(-3).ToString("yyyy-MM-dd HH:mm:ss"),
                        Items = new[]
                        {
                            new { ProductId = products.Skip(2).First().Id, Quantity = 4, UnitPrice = 400.00m, Discount = 0.00m, Total = 1600.00m },
                            new { ProductId = products.Last().Id, Quantity = 1, UnitPrice = 200.00m, Discount = 0.00m, Total = 200.00m }
                        }
                    },
                    new
                    {
                        InvoiceNumber = "QS-20241225-003",
                        CustomerId = 1,
                        Date = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd HH:mm:ss"),
                        Subtotal = 2200.00m,
                        Discount = 50.00m,
                        Tax = 0.00m,
                        Total = 2150.00m,
                        PaymentMethod = "نقدي",
                        PaymentStatus = "مدفوع",
                        Notes = "بيع سريع تجريبي - مدفوع",
                        CreatedAt = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd HH:mm:ss"),
                        Items = new[]
                        {
                            new { ProductId = products.Skip(1).First().Id, Quantity = 1, UnitPrice = 2200.00m, Discount = 50.00m, Total = 2150.00m }
                        }
                    }
                };

                foreach (var sale in quickSales)
                {
                    try
                    {
                        // إدراج البيع في جدول Sales
                        const string salesSql = @"
                            INSERT INTO Sales (
                                InvoiceNumber, CustomerId, Date, Subtotal, Discount, Tax, Total,
                                PaymentMethod, PaymentStatus, Notes, CreatedAt
                            ) VALUES (
                                @InvoiceNumber, @CustomerId, @Date, @Subtotal, @Discount, @Tax, @Total,
                                @PaymentMethod, @PaymentStatus, @Notes, @CreatedAt
                            );
                            SELECT last_insert_rowid();";

                        var saleId = await _dbService.QuerySingleAsync<int>(salesSql, sale);

                        // إضافة عناصر البيع
                        foreach (var item in sale.Items)
                        {
                            const string itemSql = @"
                                INSERT INTO SaleItems (
                                    SaleId, ProductId, Quantity, UnitPrice, Discount, Total
                                ) VALUES (
                                    @SaleId, @ProductId, @Quantity, @UnitPrice, @Discount, @Total
                                )";

                            var itemParams = new
                            {
                                SaleId = saleId,
                                item.ProductId,
                                item.Quantity,
                                item.UnitPrice,
                                item.Discount,
                                item.Total
                            };

                            await _dbService.ExecuteAsync(itemSql, itemParams);
                        }

                        LoggingService.LogInfo($"تم إنشاء البيع السريع التجريبي: {sale.InvoiceNumber} بمبلغ {sale.Total} دج");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في إنشاء البيع السريع التجريبي {sale.InvoiceNumber}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء عمليات البيع السريع التجريبية");
            }
        }

        private async Task CreateSampleDebtsAndPayablesAsync()
        {
            try
            {
                // إنشاء ديون العملاء (من الفواتير غير المدفوعة)
                const string customerDebtsSql = @"
                    INSERT OR IGNORE INTO CustomerDebts (CustomerId, InvoiceId, Amount, DueDate, Status, Notes, CreatedAt)
                    SELECT
                        i.CustomerId,
                        i.Id,
                        i.RemainingAmount,
                        i.DueDate,
                        CASE
                            WHEN i.RemainingAmount > 0 THEN 'مستحق'
                            ELSE 'مدفوع'
                        END,
                        'دين من فاتورة ' || i.InvoiceNumber,
                        i.CreatedAt
                    FROM Invoices i
                    WHERE i.InvoiceType = 'Sales' AND i.RemainingAmount > 0";

                await _dbService.ExecuteAsync(customerDebtsSql);

                // إنشاء مستحقات الموردين (من فواتير الشراء غير المدفوعة)
                const string supplierPayablesSql = @"
                    INSERT OR IGNORE INTO SupplierPayables (SupplierId, InvoiceId, Amount, DueDate, Status, Notes, CreatedAt)
                    SELECT
                        i.SupplierId,
                        i.Id,
                        i.RemainingAmount,
                        i.DueDate,
                        CASE
                            WHEN i.RemainingAmount > 0 THEN 'مستحق'
                            ELSE 'مدفوع'
                        END,
                        'مستحق من فاتورة ' || i.InvoiceNumber,
                        i.CreatedAt
                    FROM Invoices i
                    WHERE i.InvoiceType = 'Purchase' AND i.RemainingAmount > 0";

                await _dbService.ExecuteAsync(supplierPayablesSql);

                LoggingService.LogInfo("تم إنشاء الديون والمستحقات التجريبية");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء الديون والمستحقات التجريبية");
            }
        }

        public async Task<bool> ResetSampleDataAsync()
        {
            try
            {
                LoggingService.LogInfo("بدء حذف جميع البيانات التجريبية...");

                // حذف البيانات بالترتيب الصحيح (من الأطفال إلى الآباء)
                await _dbService.ExecuteAsync("DELETE FROM SaleItems");
                await _dbService.ExecuteAsync("DELETE FROM Sales");
                await _dbService.ExecuteAsync("DELETE FROM InvoiceItems");
                await _dbService.ExecuteAsync("DELETE FROM Invoices");
                await _dbService.ExecuteAsync("DELETE FROM CustomerDebts");
                await _dbService.ExecuteAsync("DELETE FROM SupplierPayables");
                await _dbService.ExecuteAsync("DELETE FROM Products");
                await _dbService.ExecuteAsync("DELETE FROM Categories");
                await _dbService.ExecuteAsync("DELETE FROM Warehouses");
                await _dbService.ExecuteAsync("DELETE FROM Customers WHERE Id > 1"); // الاحتفاظ بالعميل الافتراضي
                await _dbService.ExecuteAsync("DELETE FROM Suppliers");

                // إعادة تعيين العدادات التلقائية
                await _dbService.ExecuteAsync("DELETE FROM sqlite_sequence WHERE name IN ('Products', 'Categories', 'Warehouses', 'Customers', 'Suppliers', 'Invoices', 'Sales')");

                LoggingService.LogInfo("تم حذف جميع البيانات التجريبية بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في حذف البيانات التجريبية");
                return false;
            }
        }

        public async Task<bool> CreateSampleDataIfNeededAsync()
        {
            try
            {
                var hasData = await HasExistingDataAsync();
                if (!hasData)
                {
                    await CreateCompleteSampleDataAsync();
                    return true;
                }
                else
                {
                    LoggingService.LogInfo("البيانات التجريبية موجودة مسبقاً");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في التحقق من البيانات التجريبية");
                return false;
            }
        }
    }
}
