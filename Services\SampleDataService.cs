using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class SampleDataService
    {
        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private readonly CategoryService _categoryService;
        private readonly WarehouseService _warehouseService;

        public SampleDataService(DatabaseService dbService)
        {
            _dbService = dbService;
            _productService = new ProductService(dbService);
            _categoryService = new CategoryService(dbService);

            var settingsService = new SettingsService(dbService);
            var notificationService = new NotificationService(dbService, settingsService);
            _warehouseService = new WarehouseService(dbService, notificationService);
        }

        public async Task<bool> CreateSampleProductsAsync()
        {
            try
            {
                // التحقق من وجود منتجات مسبقاً
                var existingProducts = await _productService.GetAllProductsAsync();
                if (existingProducts.Any())
                {
                    LoggingService.LogInfo("المنتجات الافتراضية موجودة مسبقاً");
                    return true;
                }

                // إنشاء فئات افتراضية
                await CreateSampleCategoriesAsync();

                // إنشاء مخازن افتراضية
                await CreateSampleWarehousesAsync();

                // الحصول على الفئات والمخازن
                var categories = await _categoryService.GetAllCategoriesAsync();
                var warehouses = await _warehouseService.GetAllWarehousesAsync();

                var category1 = categories.FirstOrDefault(c => c.Name == "إلكترونيات");
                var category2 = categories.FirstOrDefault(c => c.Name == "ملابس");
                var category3 = categories.FirstOrDefault(c => c.Name == "أطعمة");
                var warehouse = warehouses.FirstOrDefault();

                // إنشاء 4 منتجات افتراضية
                var sampleProducts = new List<Product>
                {
                    new Product
                    {
                        Code = "ELEC001",
                        Name = "هاتف ذكي سامسونج",
                        Description = "هاتف ذكي حديث بمواصفات عالية",
                        CategoryId = category1?.Id,

                        PurchasePrice = 800.00m,
                        SalePrice = 1200.00m,
                        SalePrice2 = 1150.00m,
                        Unit = "قطعة",
                        Barcode = "1234567890123",
                        Quantity = 50,
                        MinQuantity = 10,
                        WarehouseId = warehouse?.Id,

                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    },
                    new Product
                    {
                        Code = "ELEC002",
                        Name = "لابتوب ديل",
                        Description = "لابتوب للأعمال والدراسة",
                        CategoryId = category1?.Id,

                        PurchasePrice = 1500.00m,
                        SalePrice = 2200.00m,
                        SalePrice2 = 2100.00m,
                        Unit = "قطعة",
                        Barcode = "2345678901234",
                        Quantity = 25,
                        MinQuantity = 5,
                        WarehouseId = warehouse?.Id,

                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    },
                    new Product
                    {
                        Code = "CLOTH001",
                        Name = "قميص قطني رجالي",
                        Description = "قميص قطني عالي الجودة",
                        CategoryId = category2?.Id,

                        PurchasePrice = 25.00m,
                        SalePrice = 45.00m,
                        SalePrice2 = 40.00m,
                        Unit = "قطعة",
                        Barcode = "3456789012345",
                        Quantity = 100,
                        MinQuantity = 20,
                        WarehouseId = warehouse?.Id,

                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    },
                    new Product
                    {
                        Code = "FOOD001",
                        Name = "أرز بسمتي",
                        Description = "أرز بسمتي فاخر 5 كيلو",
                        CategoryId = category3?.Id,

                        PurchasePrice = 15.00m,
                        SalePrice = 25.00m,
                        SalePrice2 = 23.00m,
                        Unit = "كيس",
                        Barcode = "4567890123456",
                        Quantity = 200,
                        MinQuantity = 50,
                        WarehouseId = warehouse?.Id,

                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    }
                };

                // حفظ المنتجات
                foreach (var product in sampleProducts)
                {
                    var result = await _productService.AddProductAsync(product);
                    if (result != null)
                    {
                        LoggingService.LogInfo($"تم إنشاء المنتج الافتراضي: {product.Name}");
                    }
                    else
                    {
                        LoggingService.LogError($"فشل في إنشاء المنتج الافتراضي: {product.Name}");
                    }
                }

                LoggingService.LogInfo("تم إنشاء جميع المنتجات الافتراضية بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء المنتجات الافتراضية");
                return false;
            }
        }

        private async Task CreateSampleCategoriesAsync()
        {
            try
            {
                var categories = new List<Category>
                {
                    new Category { Name = "إلكترونيات", Description = "أجهزة إلكترونية متنوعة" },
                    new Category { Name = "ملابس", Description = "ملابس رجالية ونسائية" },
                    new Category { Name = "أطعمة", Description = "مواد غذائية متنوعة" },
                    new Category { Name = "أدوات منزلية", Description = "أدوات ومستلزمات منزلية" }
                };

                foreach (var category in categories)
                {
                    var existing = await _categoryService.GetCategoryByNameAsync(category.Name);
                    if (existing == null)
                    {
                        await _categoryService.AddCategoryAsync(category);
                        LoggingService.LogInfo($"تم إنشاء الفئة الافتراضية: {category.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء الفئات الافتراضية");
            }
        }

        private async Task CreateSampleWarehousesAsync()
        {
            try
            {
                var warehouses = new List<Warehouse>
                {
                    new Warehouse
                    {
                        Name = "المخزن الرئيسي",
                        Address = "الطابق الأرضي",
                        Description = "المخزن الرئيسي للمتجر",
                        Status = WarehouseStatus.Active,
                        Type = WarehouseType.Main,
                        IsDefault = true,
                        CreatedAt = DateTime.Now
                    },
                    new Warehouse
                    {
                        Name = "مخزن الإلكترونيات",
                        Address = "الطابق الثاني",
                        Description = "مخزن مخصص للأجهزة الإلكترونية",
                        Status = WarehouseStatus.Active,
                        Type = WarehouseType.Branch,
                        IsDefault = false,
                        CreatedAt = DateTime.Now
                    }
                };

                foreach (var warehouse in warehouses)
                {
                    var existing = await _warehouseService.GetWarehouseByNameAsync(warehouse.Name);
                    if (existing == null)
                    {
                        await _warehouseService.AddWarehouseAsync(warehouse);
                        LoggingService.LogInfo($"تم إنشاء المخزن الافتراضي: {warehouse.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء المخازن الافتراضية");
            }
        }

        public async Task<bool> ResetSampleDataAsync()
        {
            try
            {
                // حذف جميع المنتجات
                await _dbService.ExecuteAsync("DELETE FROM Products");
                await _dbService.ExecuteAsync("DELETE FROM Categories");
                await _dbService.ExecuteAsync("DELETE FROM Warehouses");

                // إعادة إنشاء البيانات الافتراضية
                return await CreateSampleProductsAsync();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إعادة تعيين البيانات الافتراضية");
                return false;
            }
        }
    }
}
