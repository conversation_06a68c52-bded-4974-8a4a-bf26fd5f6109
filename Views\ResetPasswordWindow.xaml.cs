using System;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// نافذة إعادة تعيين كلمة المرور
    /// </summary>
    public partial class ResetPasswordWindow : Window
    {
        private readonly LoginAuthService _authService;
        private readonly LoginAuthService.SystemUser _user;
        public string? NewPassword { get; private set; }

        public ResetPasswordWindow(LoginAuthService.SystemUser user)
        {
            InitializeComponent();
            _authService = LoginAuthService.Instance;
            _user = user;
            
            LoadUserInfo();
            
            // تركيز على حقل كلمة المرور
            Loaded += (s, e) => NewPasswordBox.Focus();
        }

        // تحميل معلومات المستخدم
        private void LoadUserInfo()
        {
            UserNameLabel.Text = _user.FullName;
            UserRoleLabel.Text = $"الدور: {_user.Role}";
        }

        // تغيير كلمة المرور الجديدة
        private void NewPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            UpdatePasswordStrength();
            ValidatePasswords();
        }

        // تغيير تأكيد كلمة المرور
        private void ConfirmPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            ValidatePasswords();
        }

        // تحديث مؤشر قوة كلمة المرور
        private void UpdatePasswordStrength()
        {
            var password = NewPasswordBox.Password;
            var strength = CalculatePasswordStrength(password);
            
            PasswordStrengthBar.Value = strength.Score;
            PasswordStrengthLabel.Text = strength.Label;
            PasswordStrengthLabel.Foreground = new SolidColorBrush(strength.Color);
            
            // تحديث لون شريط التقدم
            PasswordStrengthBar.Foreground = new SolidColorBrush(strength.Color);
        }

        // حساب قوة كلمة المرور
        private (int Score, string Label, Color Color) CalculatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return (0, "غير محددة", Colors.Gray);

            int score = 0;
            
            // الطول
            if (password.Length >= 4) score += 20;
            if (password.Length >= 6) score += 20;
            if (password.Length >= 8) score += 20;
            
            // الأحرف
            if (password.Any(char.IsLetter)) score += 15;
            if (password.Any(char.IsDigit)) score += 15;
            if (password.Any(char.IsUpper)) score += 10;
            
            return score switch
            {
                < 30 => (score, "ضعيفة جداً", Colors.Red),
                < 50 => (score, "ضعيفة", Colors.Orange),
                < 70 => (score, "متوسطة", Colors.Yellow),
                < 90 => (score, "قوية", Colors.LightGreen),
                _ => (score, "قوية جداً", Colors.Green)
            };
        }

        // التحقق من صحة كلمات المرور
        private void ValidatePasswords()
        {
            var newPassword = NewPasswordBox.Password;
            var confirmPassword = ConfirmPasswordBox.Password;
            
            // إخفاء رسالة الخطأ
            ErrorMessage.Visibility = Visibility.Collapsed;
            
            // التحقق من الطول الأدنى
            if (string.IsNullOrEmpty(newPassword))
            {
                ResetButton.IsEnabled = false;
                return;
            }
            
            if (newPassword.Length < 4)
            {
                ShowError("كلمة المرور يجب أن تكون 4 أحرف على الأقل");
                ResetButton.IsEnabled = false;
                return;
            }
            
            // التحقق من تطابق كلمات المرور
            if (newPassword != confirmPassword)
            {
                if (!string.IsNullOrEmpty(confirmPassword))
                {
                    ShowError("كلمات المرور غير متطابقة");
                }
                ResetButton.IsEnabled = false;
                return;
            }
            
            // تفعيل الزر إذا كانت البيانات صحيحة
            ResetButton.IsEnabled = true;
        }

        // توليد كلمة مرور عشوائية
        private void Generate_Click(object sender, RoutedEventArgs e)
        {
            var generatedPassword = GenerateRandomPassword();
            NewPasswordBox.Password = generatedPassword;
            ConfirmPasswordBox.Password = generatedPassword;
            
            MessageBox.Show($"تم توليد كلمة المرور: {generatedPassword}\n\nيرجى حفظها في مكان آمن", 
                "كلمة مرور مولدة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // توليد كلمة مرور عشوائية
        private string GenerateRandomPassword()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            var password = new StringBuilder();
            
            // ضمان وجود حرف كبير وصغير ورقم
            password.Append(chars[random.Next(0, 26)]); // حرف كبير
            password.Append(chars[random.Next(26, 52)]); // حرف صغير
            password.Append(chars[random.Next(52, 62)]); // رقم
            
            // إضافة أحرف عشوائية للوصول لطول 8
            for (int i = 3; i < 8; i++)
            {
                password.Append(chars[random.Next(chars.Length)]);
            }
            
            // خلط الأحرف
            return new string(password.ToString().OrderBy(x => random.Next()).ToArray());
        }

        // تعيين كلمة المرور الجديدة
        private void Reset_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var newPassword = NewPasswordBox.Password;
                
                if (string.IsNullOrEmpty(newPassword) || newPassword.Length < 4)
                {
                    ShowError("يرجى إدخال كلمة مرور صحيحة");
                    return;
                }
                
                if (newPassword != ConfirmPasswordBox.Password)
                {
                    ShowError("كلمات المرور غير متطابقة");
                    return;
                }
                
                // تأكيد العملية
                var result = MessageBox.Show(
                    $"هل تريد تعيين كلمة المرور الجديدة للمستخدم '{_user.FullName}'؟",
                    "تأكيد إعادة تعيين كلمة المرور", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    if (_authService.UpdatePassword(_user.Username, newPassword))
                    {
                        NewPassword = newPassword;
                        DialogResult = true;
                        this.Close();
                    }
                    else
                    {
                        ShowError("فشل في تحديث كلمة المرور");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تعيين كلمة المرور: {ex.Message}");
            }
        }

        // عرض رسالة خطأ
        private void ShowError(string message)
        {
            ErrorMessage.Text = message;
            ErrorMessage.Visibility = Visibility.Visible;
        }

        // إلغاء العملية
        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            this.Close();
        }
    }
}
