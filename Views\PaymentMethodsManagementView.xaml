<UserControl x:Class="SalesManagementSystem.Views.PaymentMethodsManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- تحويل نوع الدفع إلى أيقونة -->
        <Style x:Key="PaymentTypeIcon" TargetType="materialDesign:PackIcon">
            <Setter Property="Kind" Value="Payment"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding PaymentType}" Value="CreditCard">
                    <Setter Property="Kind" Value="CreditCard"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding PaymentType}" Value="DebitCard">
                    <Setter Property="Kind" Value="CreditCard"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding PaymentType}" Value="BankTransfer">
                    <Setter Property="Kind" Value="Bank"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding PaymentType}" Value="DigitalWallet">
                    <Setter Property="Kind" Value="Wallet"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding PaymentType}" Value="CashOnDelivery">
                    <Setter Property="Kind" Value="Cash"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding PaymentType}" Value="Installments">
                    <Setter Property="Kind" Value="Calculator"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding PaymentType}" Value="Cryptocurrency">
                    <Setter Property="Kind" Value="Bitcoin"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل حالة طريقة الدفع إلى لون -->
        <Style x:Key="PaymentMethodStatusCard" TargetType="Border">
            <Setter Property="Background" Value="#E8F5E8"/>
            <Setter Property="BorderBrush" Value="#4CAF50"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsActive}" Value="False">
                    <Setter Property="Background" Value="#FAFAFA"/>
                    <Setter Property="BorderBrush" Value="#9E9E9E"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding IsTestMode}" Value="True">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CreditCard" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="إدارة طرق الدفع الإلكترونية" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding AddPaymentMethodCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة طريقة دفع"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding TestPaymentGatewayCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="TestTube" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="اختبار البوابة"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات سريعة -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي طرق الدفع -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CreditCard" Width="28" Height="28" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalPaymentMethods}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي الطرق" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- الطرق النشطة -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CheckCircle" Width="28" Height="28" 
                                           Foreground="Green"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ActivePaymentMethods}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Green"/>
                    <TextBlock Text="نشطة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- إجمالي المعاملات -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="SwapHorizontal" Width="28" Height="28" 
                                           Foreground="Blue"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalTransactions}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Blue"/>
                    <TextBlock Text="إجمالي المعاملات" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- المعاملات الناجحة -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CheckCircleOutline" Width="28" Height="28" 
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding SuccessfulTransactions}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="معاملات ناجحة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- معدل النجاح -->
                <StackPanel Grid.Column="4" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="TrendingUp" Width="28" Height="28" 
                                           Foreground="Purple"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding SuccessRate, StringFormat='{}{0:F1}%'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Purple"/>
                    <TextBlock Text="معدل النجاح" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- قائمة طرق الدفع -->
        <ScrollViewer Grid.Row="2" Margin="16,8,16,16" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding PaymentMethods}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="2" Margin="0,0,0,16"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <materialDesign:Card Margin="8" 
                                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
                            <Border BorderThickness="2" CornerRadius="4" Style="{StaticResource PaymentMethodStatusCard}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- رأس البطاقة -->
                                    <Grid Grid.Row="0" Margin="16,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- أيقونة نوع الدفع -->
                                        <materialDesign:PackIcon Grid.Column="0" 
                                                               Style="{StaticResource PaymentTypeIcon}"
                                                               Width="24" Height="24"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,12,0"/>

                                        <!-- اسم طريقة الدفع -->
                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding Name}" 
                                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                     FontWeight="Bold"/>
                                            <TextBlock Text="{Binding Code}" 
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     Opacity="0.7"/>
                                        </StackPanel>

                                        <!-- مؤشر الحالة -->
                                        <materialDesign:Chip Grid.Column="2"
                                                           Content="{Binding IsActive, Converter={StaticResource BooleanToActiveStatusConverter}}"
                                                           VerticalAlignment="Center"
                                                           FontSize="10"
                                                           Margin="8,0"/>

                                        <!-- وضع الاختبار -->
                                        <materialDesign:Chip Grid.Column="3"
                                                           Content="اختبار"
                                                           VerticalAlignment="Center"
                                                           FontSize="10"
                                                           Background="Orange"
                                                           Visibility="{Binding IsTestMode, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                    </Grid>

                                    <!-- محتوى البطاقة -->
                                    <Grid Grid.Row="1" Margin="16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- معلومات أساسية -->
                                        <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                            <TextBlock Text="نوع الدفع" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding PaymentTypeDisplay}" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,8"/>
                                            
                                            <TextBlock Text="مقدم الخدمة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding ProviderDisplay}" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,8"/>
                                            
                                            <TextBlock Text="رسوم المعاملة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding FormattedTransactionFee}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                        </StackPanel>

                                        <!-- معلومات إضافية -->
                                        <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                            <TextBlock Text="الحد الأدنى" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding FormattedMinimumAmount}" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,8"/>
                                            
                                            <TextBlock Text="الحد الأقصى" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding FormattedMaximumAmount}" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,8"/>
                                            
                                            <TextBlock Text="العملات المدعومة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding SupportedCurrencies}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- الوصف -->
                                    <TextBlock Grid.Row="2" 
                                             Text="{Binding Description}"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                                             TextWrapping="Wrap"
                                             Margin="16,0,16,8"
                                             Opacity="0.8"
                                             Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}"/>

                                    <!-- أزرار الإجراءات -->
                                    <Grid Grid.Row="3" Margin="16,8,16,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- زر التفعيل/التعطيل -->
                                        <Button Grid.Column="1" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.TogglePaymentMethodCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="{Binding IsActive, Converter={StaticResource BooleanToToggleTooltipConverter}}">
                                            <materialDesign:PackIcon Kind="{Binding IsActive, Converter={StaticResource BooleanToToggleIconConverter}}" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر الاختبار -->
                                        <Button Grid.Column="2" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.TestPaymentMethodCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="اختبار طريقة الدفع">
                                            <materialDesign:PackIcon Kind="TestTube" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر التحرير -->
                                        <Button Grid.Column="3" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.EditPaymentMethodCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="تحرير">
                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر المعاملات -->
                                        <Button Grid.Column="4" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.ViewTransactionsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="عرض المعاملات">
                                            <materialDesign:PackIcon Kind="History" Width="18" Height="18"/>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Border>
                        </materialDesign:Card>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Grid Grid.Row="2" 
              Background="White" 
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري التحميل..." 
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
