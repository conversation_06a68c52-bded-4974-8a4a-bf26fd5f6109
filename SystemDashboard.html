<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم نظام إدارة المبيعات - التقارير المتقدمة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 16px 24px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .navbar-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: #2196F3;
        }

        .nav-links {
            display: flex;
            gap: 24px;
        }

        .nav-link {
            text-decoration: none;
            color: #495057;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: #2196F3;
            color: white;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }

        .hero-section {
            text-align: center;
            color: white;
            margin-bottom: 48px;
        }

        .hero-section h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .hero-section p {
            font-size: 20px;
            opacity: 0.9;
            margin-bottom: 32px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-bottom: 48px;
        }

        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #2196F3, #21CBF3);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .feature-icon .material-icons {
            font-size: 32px;
            color: white;
        }

        .feature-card h3 {
            font-size: 24px;
            font-weight: 600;
            color: #212529;
            margin-bottom: 12px;
        }

        .feature-card p {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .feature-btn {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .feature-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(33, 150, 243, 0.3);
        }

        .stats-section {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 48px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
        }

        .stat-item {
            text-align: center;
            padding: 24px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #2196F3;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 500;
        }

        .demo-section {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }

        .demo-section h2 {
            font-size: 32px;
            color: #212529;
            margin-bottom: 16px;
        }

        .demo-section p {
            color: #6c757d;
            font-size: 18px;
            margin-bottom: 32px;
        }

        .demo-buttons {
            display: flex;
            justify-content: center;
            gap: 16px;
            flex-wrap: wrap;
        }

        .demo-btn {
            background: linear-gradient(135deg, #4CAF50, #388E3C);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 12px;
        }

        .demo-btn.secondary {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }

        .demo-btn:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.2);
        }

        .footer {
            text-align: center;
            color: white;
            padding: 32px 0;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .hero-section h1 {
                font-size: 36px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                display: none;
            }
            
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="navbar-content">
            <div class="logo">
                <span class="material-icons">analytics</span>
                نظام إدارة المبيعات
            </div>
            <div class="nav-links">
                <a href="#features" class="nav-link">الميزات</a>
                <a href="#stats" class="nav-link">الإحصائيات</a>
                <a href="#demo" class="nav-link">التجربة</a>
                <a href="#contact" class="nav-link">التواصل</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Hero Section -->
        <div class="hero-section">
            <h1>نظام التقارير المتقدم</h1>
            <p>حلول شاملة لإدارة المبيعات والتقارير التفاعلية مع واجهة عربية حديثة</p>
        </div>

        <!-- Features Grid -->
        <div class="features-grid" id="features">
            <div class="feature-card">
                <div class="feature-icon">
                    <span class="material-icons">assessment</span>
                </div>
                <h3>تقارير شاملة</h3>
                <p>إنشاء تقارير مفصلة للمبيعات، المخزون، العملاء، والأرباح مع إمكانيات تصدير متقدمة</p>
                <a href="ReportsSystemPreview.html" class="feature-btn" target="_blank">
                    <span class="material-icons">launch</span>
                    عرض التقارير
                </a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <span class="material-icons">bar_chart</span>
                </div>
                <h3>رسوم بيانية تفاعلية</h3>
                <p>مخططات ورسوم بيانية متقدمة لتحليل البيانات بصرياً مع دعم أنواع مختلفة من الرسوم</p>
                <a href="ReportsSystemPreview.html#chart" class="feature-btn" target="_blank">
                    <span class="material-icons">show_chart</span>
                    عرض الرسوم
                </a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <span class="material-icons">file_download</span>
                </div>
                <h3>تصدير متقدم</h3>
                <p>تصدير التقارير بصيغ متعددة: Excel، PDF، CSV مع تنسيق احترافي ودعم اللغة العربية</p>
                <a href="#" class="feature-btn" onclick="showExportDemo()">
                    <span class="material-icons">get_app</span>
                    تجربة التصدير
                </a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <span class="material-icons">timeline</span>
                </div>
                <h3>تحليل الاتجاهات</h3>
                <p>تحليل متقدم للاتجاهات والنمو مع مقارنات زمنية وتوقعات مستقبلية</p>
                <a href="#" class="feature-btn" onclick="showTrendDemo()">
                    <span class="material-icons">trending_up</span>
                    عرض التحليل
                </a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <span class="material-icons">security</span>
                </div>
                <h3>نظام أمان متقدم</h3>
                <p>حماية شاملة للبيانات مع نظام صلاحيات متدرج وتشفير متقدم</p>
                <a href="TestAuthenticationSystem.html" class="feature-btn" target="_blank">
                    <span class="material-icons">verified_user</span>
                    اختبار الأمان
                </a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <span class="material-icons">bug_report</span>
                </div>
                <h3>نظام اختبار شامل</h3>
                <p>أدوات اختبار متقدمة لضمان جودة النظام وموثوقية العمليات</p>
                <a href="TestReportsSystemPreview.html" class="feature-btn" target="_blank">
                    <span class="material-icons">play_arrow</span>
                    بدء الاختبار
                </a>
            </div>
        </div>

        <!-- Statistics Section -->
        <div class="stats-section" id="stats">
            <h2 style="text-align: center; margin-bottom: 32px; color: #212529;">إحصائيات النظام</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">8+</div>
                    <div class="stat-label">أنواع التقارير</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">صيغ التصدير</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">5+</div>
                    <div class="stat-label">أنواع الرسوم البيانية</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">دعم اللغة العربية</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">مراقبة النظام</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">معدل التوفر</div>
                </div>
            </div>
        </div>

        <!-- Demo Section -->
        <div class="demo-section" id="demo">
            <h2>جرب النظام الآن</h2>
            <p>استكشف جميع ميزات نظام التقارير المتقدم من خلال العروض التفاعلية</p>
            <div class="demo-buttons">
                <a href="ReportsSystemPreview.html" class="demo-btn" target="_blank">
                    <span class="material-icons">dashboard</span>
                    لوحة التقارير الرئيسية
                </a>
                <a href="TestReportsSystemPreview.html" class="demo-btn secondary" target="_blank">
                    <span class="material-icons">science</span>
                    نظام الاختبار المتقدم
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>&copy; 2024 نظام إدارة المبيعات - التقارير المتقدمة. جميع الحقوق محفوظة.</p>
    </div>

    <script>
        function showExportDemo() {
            alert('🎉 ميزة التصدير المتقدم:\n\n✅ تصدير Excel مع تنسيق احترافي\n✅ تصدير PDF مع دعم العربية\n✅ تصدير CSV للبيانات الخام\n✅ طباعة مباشرة للتقارير\n\nجرب النظام الكامل لرؤية هذه الميزات!');
        }

        function showTrendDemo() {
            alert('📈 تحليل الاتجاهات المتقدم:\n\n📊 تحليل نمو المبيعات\n📈 مقارنة الفترات الزمنية\n🔮 توقعات مستقبلية\n📉 تحليل الانخفاضات\n📋 تقارير الأداء\n\nافتح نظام التقارير لرؤية التحليل الكامل!');
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });

        // Welcome message
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                console.log('🎉 مرحباً بك في نظام التقارير المتقدم!');
                console.log('📊 استكشف جميع الميزات من خلال الروابط أعلاه');
                console.log('🚀 النظام جاهز للاستخدام!');
            }, 1000);
        });
    </script>
</body>
</html>
