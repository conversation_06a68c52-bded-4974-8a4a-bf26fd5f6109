using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج المتجر الإلكتروني
    /// </summary>
    public class ECommerceStore : INotifyPropertyChanged
    {
        private int _id;
        private string _storeName = string.Empty;
        private string _storeCode = string.Empty;
        private string _description = string.Empty;
        private string _logo = string.Empty;
        private string _banner = string.Empty;
        private StoreStatus _status = StoreStatus.Active;
        private StoreType _storeType = StoreType.B2C;
        private string _domain = string.Empty;
        private string _customDomain = string.Empty;
        private string _theme = "Default";
        private string _primaryColor = "#2196F3";
        private string _secondaryColor = "#FFC107";
        private string _currency = "SAR";
        private string _language = "ar";
        private string _timezone = "Asia/Riyadh";
        private bool _isMultiLanguage;
        private bool _isMultiCurrency;
        private bool _allowGuestCheckout = true;
        private bool _requireEmailVerification;
        private bool _enableWishlist = true;
        private bool _enableReviews = true;
        private bool _enableInventoryTracking = true;
        private bool _enableCoupons = true;
        private bool _enableTaxCalculation = true;
        private decimal _taxRate = 15.0m;
        private string _taxNumber = string.Empty;
        private string _contactEmail = string.Empty;
        private string _contactPhone = string.Empty;
        private string _supportEmail = string.Empty;
        private string _address = string.Empty;
        private string _city = string.Empty;
        private string _country = "Saudi Arabia";
        private string _postalCode = string.Empty;
        private string _socialMediaLinks = string.Empty;
        private string _seoTitle = string.Empty;
        private string _seoDescription = string.Empty;
        private string _seoKeywords = string.Empty;
        private string _analyticsCode = string.Empty;
        private string _pixelCode = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private string _createdBy = string.Empty;
        private ObservableCollection<ProductCategory> _categories = new();
        private ObservableCollection<OnlineProduct> _products = new();
        private ObservableCollection<OnlineOrder> _orders = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string StoreName
        {
            get => _storeName;
            set
            {
                if (_storeName != value)
                {
                    _storeName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string StoreCode
        {
            get => _storeCode;
            set
            {
                if (_storeCode != value)
                {
                    _storeCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Logo
        {
            get => _logo;
            set
            {
                if (_logo != value)
                {
                    _logo = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Banner
        {
            get => _banner;
            set
            {
                if (_banner != value)
                {
                    _banner = value;
                    OnPropertyChanged();
                }
            }
        }

        public StoreStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        public StoreType StoreType
        {
            get => _storeType;
            set
            {
                if (_storeType != value)
                {
                    _storeType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StoreTypeDisplay));
                }
            }
        }

        public string Domain
        {
            get => _domain;
            set
            {
                if (_domain != value)
                {
                    _domain = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StoreUrl));
                }
            }
        }

        public string CustomDomain
        {
            get => _customDomain;
            set
            {
                if (_customDomain != value)
                {
                    _customDomain = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StoreUrl));
                }
            }
        }

        public string Theme
        {
            get => _theme;
            set
            {
                if (_theme != value)
                {
                    _theme = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PrimaryColor
        {
            get => _primaryColor;
            set
            {
                if (_primaryColor != value)
                {
                    _primaryColor = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SecondaryColor
        {
            get => _secondaryColor;
            set
            {
                if (_secondaryColor != value)
                {
                    _secondaryColor = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Currency
        {
            get => _currency;
            set
            {
                if (_currency != value)
                {
                    _currency = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Language
        {
            get => _language;
            set
            {
                if (_language != value)
                {
                    _language = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Timezone
        {
            get => _timezone;
            set
            {
                if (_timezone != value)
                {
                    _timezone = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsMultiLanguage
        {
            get => _isMultiLanguage;
            set
            {
                if (_isMultiLanguage != value)
                {
                    _isMultiLanguage = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsMultiCurrency
        {
            get => _isMultiCurrency;
            set
            {
                if (_isMultiCurrency != value)
                {
                    _isMultiCurrency = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool AllowGuestCheckout
        {
            get => _allowGuestCheckout;
            set
            {
                if (_allowGuestCheckout != value)
                {
                    _allowGuestCheckout = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool RequireEmailVerification
        {
            get => _requireEmailVerification;
            set
            {
                if (_requireEmailVerification != value)
                {
                    _requireEmailVerification = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableWishlist
        {
            get => _enableWishlist;
            set
            {
                if (_enableWishlist != value)
                {
                    _enableWishlist = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableReviews
        {
            get => _enableReviews;
            set
            {
                if (_enableReviews != value)
                {
                    _enableReviews = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableInventoryTracking
        {
            get => _enableInventoryTracking;
            set
            {
                if (_enableInventoryTracking != value)
                {
                    _enableInventoryTracking = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableCoupons
        {
            get => _enableCoupons;
            set
            {
                if (_enableCoupons != value)
                {
                    _enableCoupons = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableTaxCalculation
        {
            get => _enableTaxCalculation;
            set
            {
                if (_enableTaxCalculation != value)
                {
                    _enableTaxCalculation = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal TaxRate
        {
            get => _taxRate;
            set
            {
                if (_taxRate != value)
                {
                    _taxRate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTaxRate));
                }
            }
        }

        public string TaxNumber
        {
            get => _taxNumber;
            set
            {
                if (_taxNumber != value)
                {
                    _taxNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ContactEmail
        {
            get => _contactEmail;
            set
            {
                if (_contactEmail != value)
                {
                    _contactEmail = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ContactPhone
        {
            get => _contactPhone;
            set
            {
                if (_contactPhone != value)
                {
                    _contactPhone = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SupportEmail
        {
            get => _supportEmail;
            set
            {
                if (_supportEmail != value)
                {
                    _supportEmail = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Address
        {
            get => _address;
            set
            {
                if (_address != value)
                {
                    _address = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string City
        {
            get => _city;
            set
            {
                if (_city != value)
                {
                    _city = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string Country
        {
            get => _country;
            set
            {
                if (_country != value)
                {
                    _country = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string PostalCode
        {
            get => _postalCode;
            set
            {
                if (_postalCode != value)
                {
                    _postalCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SocialMediaLinks
        {
            get => _socialMediaLinks;
            set
            {
                if (_socialMediaLinks != value)
                {
                    _socialMediaLinks = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SeoTitle
        {
            get => _seoTitle;
            set
            {
                if (_seoTitle != value)
                {
                    _seoTitle = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SeoDescription
        {
            get => _seoDescription;
            set
            {
                if (_seoDescription != value)
                {
                    _seoDescription = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SeoKeywords
        {
            get => _seoKeywords;
            set
            {
                if (_seoKeywords != value)
                {
                    _seoKeywords = value;
                    OnPropertyChanged();
                }
            }
        }

        public string AnalyticsCode
        {
            get => _analyticsCode;
            set
            {
                if (_analyticsCode != value)
                {
                    _analyticsCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PixelCode
        {
            get => _pixelCode;
            set
            {
                if (_pixelCode != value)
                {
                    _pixelCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<ProductCategory> Categories
        {
            get => _categories;
            set
            {
                if (_categories != value)
                {
                    _categories = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CategoriesCount));
                }
            }
        }

        public ObservableCollection<OnlineProduct> Products
        {
            get => _products;
            set
            {
                if (_products != value)
                {
                    _products = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ProductsCount));
                    OnPropertyChanged(nameof(ActiveProductsCount));
                }
            }
        }

        public ObservableCollection<OnlineOrder> Orders
        {
            get => _orders;
            set
            {
                if (_orders != value)
                {
                    _orders = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(OrdersCount));
                    OnPropertyChanged(nameof(TotalRevenue));
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsActive => Status == StoreStatus.Active;
        public string StoreUrl => !string.IsNullOrEmpty(CustomDomain) ? CustomDomain : Domain;
        public string FullAddress => $"{Address}, {City}, {Country}".Trim(' ', ',');
        public int CategoriesCount => Categories?.Count ?? 0;
        public int ProductsCount => Products?.Count ?? 0;
        public int ActiveProductsCount => Products?.Count(p => p.IsActive) ?? 0;
        public int OrdersCount => Orders?.Count ?? 0;
        public decimal TotalRevenue => Orders?.Sum(o => o.TotalAmount) ?? 0;

        // Display Properties
        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    StoreStatus.Active => "نشط",
                    StoreStatus.Inactive => "غير نشط",
                    StoreStatus.Maintenance => "صيانة",
                    StoreStatus.Suspended => "معلق",
                    _ => "غير محدد"
                };
            }
        }

        public string StoreTypeDisplay
        {
            get
            {
                return StoreType switch
                {
                    StoreType.B2C => "تجارة إلكترونية للمستهلكين",
                    StoreType.B2B => "تجارة إلكترونية للشركات",
                    StoreType.Marketplace => "سوق إلكتروني",
                    StoreType.Subscription => "اشتراكات",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    StoreStatus.Active => "Green",
                    StoreStatus.Inactive => "Gray",
                    StoreStatus.Maintenance => "Orange",
                    StoreStatus.Suspended => "Red",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedTaxRate => $"{TaxRate:F1}%";
        public string FormattedTotalRevenue => TotalRevenue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        public void Activate()
        {
            Status = StoreStatus.Active;
            UpdatedAt = DateTime.Now;
        }

        public void Deactivate()
        {
            Status = StoreStatus.Inactive;
            UpdatedAt = DateTime.Now;
        }

        public void SetMaintenance()
        {
            Status = StoreStatus.Maintenance;
            UpdatedAt = DateTime.Now;
        }

        public void Suspend()
        {
            Status = StoreStatus.Suspended;
            UpdatedAt = DateTime.Now;
        }

        public void UpdateTheme(string theme, string primaryColor, string secondaryColor)
        {
            Theme = theme;
            PrimaryColor = primaryColor;
            SecondaryColor = secondaryColor;
            UpdatedAt = DateTime.Now;
        }

        public void UpdateSeoSettings(string title, string description, string keywords)
        {
            SeoTitle = title;
            SeoDescription = description;
            SeoKeywords = keywords;
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Enums

    public enum StoreStatus
    {
        Active,         // نشط
        Inactive,       // غير نشط
        Maintenance,    // صيانة
        Suspended       // معلق
    }

    public enum StoreType
    {
        B2C,            // تجارة إلكترونية للمستهلكين
        B2B,            // تجارة إلكترونية للشركات
        Marketplace,    // سوق إلكتروني
        Subscription    // اشتراكات
    }

    #endregion

    #region Validation

    public class ECommerceStoreValidator : AbstractValidator<ECommerceStore>
    {
        public ECommerceStoreValidator()
        {
            RuleFor(s => s.StoreName)
                .NotEmpty().WithMessage("اسم المتجر مطلوب")
                .MaximumLength(100).WithMessage("اسم المتجر لا يمكن أن يتجاوز 100 حرف");

            RuleFor(s => s.StoreCode)
                .NotEmpty().WithMessage("كود المتجر مطلوب")
                .MaximumLength(20).WithMessage("كود المتجر لا يمكن أن يتجاوز 20 حرف");

            RuleFor(s => s.Domain)
                .NotEmpty().WithMessage("نطاق المتجر مطلوب")
                .Matches(@"^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]$").WithMessage("نطاق المتجر غير صحيح");

            RuleFor(s => s.ContactEmail)
                .EmailAddress().When(s => !string.IsNullOrEmpty(s.ContactEmail))
                .WithMessage("البريد الإلكتروني غير صحيح");

            RuleFor(s => s.SupportEmail)
                .EmailAddress().When(s => !string.IsNullOrEmpty(s.SupportEmail))
                .WithMessage("بريد الدعم الفني غير صحيح");

            RuleFor(s => s.TaxRate)
                .InclusiveBetween(0, 100).WithMessage("معدل الضريبة يجب أن يكون بين 0 و 100");
        }
    }

    #endregion
}
