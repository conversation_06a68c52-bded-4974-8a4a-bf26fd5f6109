using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.Views.Dialogs;

namespace SalesManagementSystem.ViewModels
{
    public class SuppliersViewModel : BaseViewModel
    {
        #region Services

        private readonly SupplierService _supplierService;

        #endregion

        #region Properties

        private ObservableCollection<Supplier> _suppliers = new();
        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set => SetProperty(ref _suppliers, value);
        }

        private Supplier? _selectedSupplier;
        public Supplier? SelectedSupplier
        {
            get => _selectedSupplier;
            set
            {
                if (SetProperty(ref _selectedSupplier, value))
                {
                    UpdateCommandStates();
                }
            }
        }

        private string _searchText = string.Empty;
        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
            }
        }

        #endregion

        #region Commands

        private RelayCommand? _addSupplierCommand;
        public RelayCommand AddSupplierCommand => _addSupplierCommand ??= new RelayCommand(AddSupplier);

        private RelayCommand? _editSupplierCommand;
        public RelayCommand EditSupplierCommand => _editSupplierCommand ??= new RelayCommand(EditSupplier, CanEditSupplier);

        private RelayCommand? _deleteSupplierCommand;
        public RelayCommand DeleteSupplierCommand => _deleteSupplierCommand ??= new RelayCommand(DeleteSupplier, CanDeleteSupplier);

        private RelayCommand? _searchCommand;
        public RelayCommand SearchCommand => _searchCommand ??= new RelayCommand(SearchSuppliers, CanSearch);

        private new RelayCommand? _refreshCommand;
        public new RelayCommand RefreshCommand => _refreshCommand ??= new RelayCommand(RefreshSuppliers);

        #endregion

        #region Constructor

        public SuppliersViewModel()
        {
            var dbService = new DatabaseService();
            _supplierService = new SupplierService(dbService);

            // Load suppliers on initialization
            _ = LoadSuppliersAsync();
        }

        #endregion

        #region Methods

        private async Task LoadSuppliersAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تحميل الموردين...";
                ClearError();

                var suppliers = await _supplierService.GetAllSuppliersAsync();
                Suppliers = new ObservableCollection<Supplier>(suppliers);

                LoggingService.LogInfo($"تم تحميل {Suppliers.Count} مورد");
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل الموردين: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تحميل الموردين");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void AddSupplier()
        {
            try
            {
                var dialog = new SupplierDialog();
                var result = dialog.ShowDialog();

                if (result == true && dialog.Result != null)
                {
                    // Add to collection
                    Suppliers.Add(dialog.Result);
                    SelectedSupplier = dialog.Result;

                    LoggingService.LogInfo($"تم إضافة المورد: {dialog.Result.Name}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المورد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                LoggingService.LogError(ex, "خطأ في إضافة المورد");
            }
        }

        private async void EditSupplier()
        {
            if (SelectedSupplier == null) return;

            try
            {
                var dialog = new SupplierDialog(SelectedSupplier);
                var result = dialog.ShowDialog();

                if (result == true && dialog.Result != null)
                {
                    // Update the supplier in the collection
                    var index = Suppliers.IndexOf(SelectedSupplier);
                    if (index >= 0)
                    {
                        Suppliers[index] = dialog.Result;
                        SelectedSupplier = dialog.Result;
                    }

                    LoggingService.LogInfo($"تم تعديل المورد: {dialog.Result.Name}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المورد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                LoggingService.LogError(ex, "خطأ في تعديل المورد");
            }
        }

        private bool CanEditSupplier()
        {
            return SelectedSupplier != null;
        }

        private async void DeleteSupplier()
        {
            if (SelectedSupplier == null) return;

            try
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المورد '{SelectedSupplier.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    IsLoading = true;
                    LoadingMessage = "جاري حذف المورد...";

                    var success = await _supplierService.DeleteSupplierAsync(SelectedSupplier.Id);
                    if (success)
                    {
                        Suppliers.Remove(SelectedSupplier);
                        SelectedSupplier = null;

                        MessageBox.Show("تم حذف المورد بنجاح!", "نجح الحذف",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        LoggingService.LogInfo($"تم حذف المورد: {SelectedSupplier?.Name}");
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المورد", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                LoggingService.LogError(ex, "خطأ في حذف المورد");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanDeleteSupplier()
        {
            return SelectedSupplier != null;
        }

        private async void SearchSuppliers()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري البحث...";
                ClearError();

                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    await LoadSuppliersAsync();
                }
                else
                {
                    var suppliers = await _supplierService.SearchSuppliersAsync(SearchText);
                    Suppliers = new ObservableCollection<Supplier>(suppliers);
                }

                LoggingService.LogInfo($"تم العثور على {Suppliers.Count} مورد");
            }
            catch (Exception ex)
            {
                SetError($"خطأ في البحث: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في البحث عن الموردين");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanSearch()
        {
            return !IsLoading;
        }

        private async void RefreshSuppliers()
        {
            SearchText = string.Empty;
            await LoadSuppliersAsync();
        }

        private void UpdateCommandStates()
        {
            // RelayCommand automatically handles CanExecute changes
            OnPropertyChanged(nameof(EditSupplierCommand));
            OnPropertyChanged(nameof(DeleteSupplierCommand));
        }

        #endregion
    }
}
