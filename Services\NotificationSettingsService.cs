using System;
using System.IO;
using System.Text.Json;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إعدادات الإشعارات
    /// </summary>
    public class NotificationSettingsService
    {
        private static NotificationSettingsService? _instance;
        private NotificationSettings _currentSettings = new();
        private readonly string _settingsFilePath;

        public static NotificationSettingsService Instance => _instance ??= new NotificationSettingsService();

        private NotificationSettingsService()
        {
            _settingsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "SalesManagementSystem", "NotificationSettings.json");

            // إنشاء المجلد إذا لم يكن موجوداً
            var directory = Path.GetDirectoryName(_settingsFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            LoadSettings();
        }

        // نموذج إعدادات الإشعارات
        public class NotificationSettings
        {
            // إشعارات المبيعات
            public bool SaleCompleted { get; set; } = true;
            public bool SaleCancelled { get; set; } = true;
            public bool DailySalesTarget { get; set; } = false;
            public bool LargeSales { get; set; } = true;

            // إشعارات المخزون
            public bool StockOut { get; set; } = true;
            public bool LowStock { get; set; } = true;
            public bool NewProduct { get; set; } = false;
            public bool PriceUpdate { get; set; } = false;

            // إشعارات النظام
            public bool UserLogin { get; set; } = true;
            public bool BackupCreated { get; set; } = true;
            public bool SystemError { get; set; } = true;

            // إشعارات الأمان
            public bool FailedLogin { get; set; } = true;
            public bool PasswordChange { get; set; } = true;
            public bool NewUser { get; set; } = true;

            // إعدادات عامة
            public bool NotificationSounds { get; set; } = true;
            public bool SaveNotificationLog { get; set; } = true;
            public int NotificationDuration { get; set; } = 5;

            // معلومات إضافية
            public DateTime LastModified { get; set; } = DateTime.Now;
            public string ModifiedBy { get; set; } = "النظام";
        }

        // تحميل الإعدادات
        private void LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsFilePath))
                {
                    var json = File.ReadAllText(_settingsFilePath);
                    _currentSettings = JsonSerializer.Deserialize<NotificationSettings>(json) ?? new NotificationSettings();
                }
                else
                {
                    _currentSettings = new NotificationSettings();
                    SaveSettings(_currentSettings); // حفظ الإعدادات الافتراضية
                }
            }
            catch
            {
                _currentSettings = new NotificationSettings();
            }
        }

        // حفظ الإعدادات
        public bool SaveSettings(NotificationSettings settings)
        {
            try
            {
                settings.LastModified = DateTime.Now;
                settings.ModifiedBy = CurrentUserService.Instance.CurrentUserName;

                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_settingsFilePath, json);

                _currentSettings = settings;
                return true;
            }
            catch
            {
                return false;
            }
        }

        // الحصول على الإعدادات الحالية
        public NotificationSettings GetCurrentSettings()
        {
            return _currentSettings;
        }

        // إعادة تعيين إلى القيم الافتراضية
        public void ResetToDefaults()
        {
            _currentSettings = new NotificationSettings();
            SaveSettings(_currentSettings);
        }

        // التحقق من تفعيل إشعار معين
        public bool IsNotificationEnabled(string notificationType)
        {
            return notificationType.ToLower() switch
            {
                "sale_completed" => _currentSettings.SaleCompleted,
                "sale_cancelled" => _currentSettings.SaleCancelled,
                "daily_sales_target" => _currentSettings.DailySalesTarget,
                "large_sales" => _currentSettings.LargeSales,
                "stock_out" => _currentSettings.StockOut,
                "low_stock" => _currentSettings.LowStock,
                "new_product" => _currentSettings.NewProduct,
                "price_update" => _currentSettings.PriceUpdate,
                "user_login" => _currentSettings.UserLogin,
                "backup_created" => _currentSettings.BackupCreated,
                "system_error" => _currentSettings.SystemError,
                "failed_login" => _currentSettings.FailedLogin,
                "password_change" => _currentSettings.PasswordChange,
                "new_user" => _currentSettings.NewUser,
                _ => false
            };
        }

        // تفعيل أو إلغاء تفعيل إشعار معين
        public void SetNotificationEnabled(string notificationType, bool enabled)
        {
            switch (notificationType.ToLower())
            {
                case "sale_completed":
                    _currentSettings.SaleCompleted = enabled;
                    break;
                case "sale_cancelled":
                    _currentSettings.SaleCancelled = enabled;
                    break;
                case "daily_sales_target":
                    _currentSettings.DailySalesTarget = enabled;
                    break;
                case "large_sales":
                    _currentSettings.LargeSales = enabled;
                    break;
                case "stock_out":
                    _currentSettings.StockOut = enabled;
                    break;
                case "low_stock":
                    _currentSettings.LowStock = enabled;
                    break;
                case "new_product":
                    _currentSettings.NewProduct = enabled;
                    break;
                case "price_update":
                    _currentSettings.PriceUpdate = enabled;
                    break;
                case "user_login":
                    _currentSettings.UserLogin = enabled;
                    break;
                case "backup_created":
                    _currentSettings.BackupCreated = enabled;
                    break;
                case "system_error":
                    _currentSettings.SystemError = enabled;
                    break;
                case "failed_login":
                    _currentSettings.FailedLogin = enabled;
                    break;
                case "password_change":
                    _currentSettings.PasswordChange = enabled;
                    break;
                case "new_user":
                    _currentSettings.NewUser = enabled;
                    break;
            }

            SaveSettings(_currentSettings);
        }

        // الحصول على إحصائيات الإشعارات
        public NotificationStats GetNotificationStats()
        {
            var totalNotifications = 14; // إجمالي أنواع الإشعارات المتاحة
            var enabledNotifications = 0;

            // حساب الإشعارات المفعلة
            if (_currentSettings.SaleCompleted) enabledNotifications++;
            if (_currentSettings.SaleCancelled) enabledNotifications++;
            if (_currentSettings.DailySalesTarget) enabledNotifications++;
            if (_currentSettings.LargeSales) enabledNotifications++;
            if (_currentSettings.StockOut) enabledNotifications++;
            if (_currentSettings.LowStock) enabledNotifications++;
            if (_currentSettings.NewProduct) enabledNotifications++;
            if (_currentSettings.PriceUpdate) enabledNotifications++;
            if (_currentSettings.UserLogin) enabledNotifications++;
            if (_currentSettings.BackupCreated) enabledNotifications++;
            if (_currentSettings.SystemError) enabledNotifications++;
            if (_currentSettings.FailedLogin) enabledNotifications++;
            if (_currentSettings.PasswordChange) enabledNotifications++;
            if (_currentSettings.NewUser) enabledNotifications++;

            return new NotificationStats
            {
                TotalNotifications = totalNotifications,
                EnabledNotifications = enabledNotifications,
                DisabledNotifications = totalNotifications - enabledNotifications,
                SoundsEnabled = _currentSettings.NotificationSounds,
                LoggingEnabled = _currentSettings.SaveNotificationLog,
                NotificationDuration = _currentSettings.NotificationDuration,
                LastModified = _currentSettings.LastModified,
                ModifiedBy = _currentSettings.ModifiedBy
            };
        }

        // نموذج إحصائيات الإشعارات
        public class NotificationStats
        {
            public int TotalNotifications { get; set; }
            public int EnabledNotifications { get; set; }
            public int DisabledNotifications { get; set; }
            public bool SoundsEnabled { get; set; }
            public bool LoggingEnabled { get; set; }
            public int NotificationDuration { get; set; }
            public DateTime LastModified { get; set; }
            public string ModifiedBy { get; set; } = "";
        }

        // تسجيل إشعار في السجل (إذا كان مفعلاً)
        public void LogNotification(string notificationType, string message)
        {
            try
            {
                if (!_currentSettings.SaveNotificationLog)
                    return;

                var logEntry = new
                {
                    Timestamp = DateTime.Now,
                    Type = notificationType,
                    Message = message,
                    User = CurrentUserService.Instance.CurrentUserName
                };

                var logPath = Path.Combine(Path.GetDirectoryName(_settingsFilePath) ?? "", "NotificationLog.txt");
                var logText = $"{logEntry.Timestamp:yyyy-MM-dd HH:mm:ss} | {logEntry.Type} | {logEntry.User} | {logEntry.Message}\n";

                File.AppendAllText(logPath, logText);
            }
            catch
            {
                // تجاهل أخطاء التسجيل لتجنب تعطيل النظام
            }
        }
    }
}
