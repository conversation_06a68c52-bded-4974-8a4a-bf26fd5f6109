using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة المستخدم الحالي
    /// </summary>
    public class CurrentUserService : INotifyPropertyChanged
    {
        private static CurrentUserService? _instance;
        private string _currentUserName;
        private string _currentUserRole;
        private DateTime _loginTime;

        public static CurrentUserService Instance => _instance ??= new CurrentUserService();

        private CurrentUserService()
        {
            // لا يوجد مستخدم مسجل دخول في البداية
            _currentUserName = "ضيف";
            _currentUserRole = "زائر";
            _loginTime = DateTime.Now;
        }

        public string CurrentUserName
        {
            get => _currentUserName;
            set
            {
                if (_currentUserName != value)
                {
                    _currentUserName = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(WelcomeMessage));
                    OnPropertyChanged(nameof(UserDisplayName));
                }
            }
        }

        public string CurrentUserRole
        {
            get => _currentUserRole;
            set
            {
                if (_currentUserRole != value)
                {
                    _currentUserRole = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(UserDisplayName));
                }
            }
        }

        public DateTime LoginTime
        {
            get => _loginTime;
            set
            {
                if (_loginTime != value)
                {
                    _loginTime = value;
                    OnPropertyChanged();
                }
            }
        }

        // رسالة الترحيب مع اسم المستخدم
        public string WelcomeMessage => $"مرحباً بك {_currentUserName} في نظام إدارة المبيعات المتكامل";

        // عرض اسم المستخدم مع الدور
        public string UserDisplayName => $"{_currentUserName} ({_currentUserRole})";

        // قائمة المستخدمين المتاحين (للتجربة)
        public static class Users
        {
            public static readonly (string Name, string Role)[] AvailableUsers = new[]
            {
                ("أحمد محمد", "مدير النظام"),
                ("فاطمة علي", "محاسبة"),
                ("محمد حسن", "موظف مبيعات"),
                ("سارة أحمد", "مدير المخزون"),
                ("عمر خالد", "موظف استقبال"),
                ("نور الدين", "مدير فرع"),
                ("ليلى محمود", "محاسبة مساعدة"),
                ("يوسف إبراهيم", "موظف مبيعات"),
                ("زينب حسام", "مديرة العملاء"),
                ("المدير العام", "مدير النظام")
            };
        }

        // تسجيل دخول مستخدم جديد
        public void LoginUser(string userName, string userRole)
        {
            CurrentUserName = userName;
            CurrentUserRole = userRole;
            LoginTime = DateTime.Now;

            // إشعار بتسجيل الدخول
            UserLoggedIn?.Invoke(this, new UserLoginEventArgs(userName, userRole, LoginTime));
        }

        // تسجيل خروج المستخدم
        public void LogoutUser()
        {
            CurrentUserName = "ضيف";
            CurrentUserRole = "زائر";
            LoginTime = DateTime.Now;
        }

        // التحقق من صلاحيات المستخدم
        public bool HasPermission(string permission)
        {
            return CurrentUserRole switch
            {
                "مدير النظام" => true, // جميع الصلاحيات
                "مدير فرع" => permission != "system_settings", // كل شيء عدا إعدادات النظام
                "محاسبة" => permission is "sales" or "reports" or "customers", // المبيعات والتقارير والعملاء
                "موظف مبيعات" => permission is "sales" or "customers" or "products", // المبيعات والعملاء والمنتجات
                "مدير المخزون" => permission is "products" or "inventory" or "purchases", // المنتجات والمخزون والمشتريات
                "موظف استقبال" => permission is "customers" or "sales", // العملاء والمبيعات فقط
                _ => false // لا توجد صلاحيات للأدوار الأخرى
            };
        }

        // الأحداث
        public event EventHandler<UserLoginEventArgs>? UserLoggedIn;
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // فئات الأحداث
    public class UserLoginEventArgs : EventArgs
    {
        public string UserName { get; }
        public string UserRole { get; }
        public DateTime LoginTime { get; }

        public UserLoginEventArgs(string userName, string userRole, DateTime loginTime)
        {
            UserName = userName;
            UserRole = userRole;
            LoginTime = loginTime;
        }
    }
}
