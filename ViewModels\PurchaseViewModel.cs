using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class PurchaseViewModel : BaseViewModel
    {
        #region Services

        private readonly DatabaseService _dbService;
        private readonly PurchaseService _purchaseService;
        private readonly ProductService _productService;
        private readonly SupplierService _supplierService;

        #endregion

        #region Properties

        private ObservableCollection<Purchase> _purchases = new();
        public ObservableCollection<Purchase> Purchases
        {
            get => _purchases;
            set => SetProperty(ref _purchases, value);
        }

        private ObservableCollection<Purchase> _filteredPurchases = new();
        public ObservableCollection<Purchase> FilteredPurchases
        {
            get => _filteredPurchases;
            set => SetProperty(ref _filteredPurchases, value);
        }

        private ObservableCollection<Supplier> _suppliers = new();
        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set => SetProperty(ref _suppliers, value);
        }

        private Purchase? _selectedPurchase;
        public Purchase? SelectedPurchase
        {
            get => _selectedPurchase;
            set
            {
                if (SetProperty(ref _selectedPurchase, value))
                {
                    EditPurchaseCommand.RaiseCanExecuteChanged();
                    DeletePurchaseCommand.RaiseCanExecuteChanged();
                    ViewPurchaseCommand.RaiseCanExecuteChanged();
                }
            }
        }

        private string _searchText = string.Empty;
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    ApplyFilters();
                }
            }
        }

        private Supplier? _selectedSupplier;
        public Supplier? SelectedSupplier
        {
            get => _selectedSupplier;
            set
            {
                if (SetProperty(ref _selectedSupplier, value))
                {
                    ApplyFilters();
                }
            }
        }

        private DateTime _startDate = DateTime.Now.AddMonths(-1);
        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (SetProperty(ref _startDate, value))
                {
                    ApplyFilters();
                }
            }
        }

        private DateTime _endDate = DateTime.Now;
        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                if (SetProperty(ref _endDate, value))
                {
                    ApplyFilters();
                }
            }
        }

        private string _selectedPaymentStatus = "الكل";
        public string SelectedPaymentStatus
        {
            get => _selectedPaymentStatus;
            set
            {
                if (SetProperty(ref _selectedPaymentStatus, value))
                {
                    ApplyFilters();
                }
            }
        }

        // Statistics
        private int _totalPurchases;
        public int TotalPurchases
        {
            get => _totalPurchases;
            set => SetProperty(ref _totalPurchases, value);
        }

        private decimal _totalAmount;
        public decimal TotalAmount
        {
            get => _totalAmount;
            set => SetProperty(ref _totalAmount, value);
        }

        private decimal _paidAmount;
        public decimal PaidAmount
        {
            get => _paidAmount;
            set => SetProperty(ref _paidAmount, value);
        }

        private decimal _unpaidAmount;
        public decimal UnpaidAmount
        {
            get => _unpaidAmount;
            set => SetProperty(ref _unpaidAmount, value);
        }

        // Available Options
        public ObservableCollection<string> PaymentStatuses { get; } = new()
        {
            "الكل",
            "مدفوع",
            "غير مدفوع",
            "مدفوع جزئياً",
            "ملغي"
        };

        #endregion

        #region Commands

        private DelegateCommand? _addPurchaseCommand;
        public DelegateCommand AddPurchaseCommand => _addPurchaseCommand ??= new DelegateCommand(AddPurchase);

        private DelegateCommand? _editPurchaseCommand;
        public DelegateCommand EditPurchaseCommand => _editPurchaseCommand ??= new DelegateCommand(EditPurchase, CanEditPurchase);

        private DelegateCommand? _deletePurchaseCommand;
        public DelegateCommand DeletePurchaseCommand => _deletePurchaseCommand ??= new DelegateCommand(DeletePurchase, CanDeletePurchase);

        private DelegateCommand? _viewPurchaseCommand;
        public DelegateCommand ViewPurchaseCommand => _viewPurchaseCommand ??= new DelegateCommand(ViewPurchase, CanViewPurchase);

        private DelegateCommand? _clearFiltersCommand;
        public DelegateCommand ClearFiltersCommand => _clearFiltersCommand ??= new DelegateCommand(ClearFilters);

        private DelegateCommand? _exportPurchasesCommand;
        public DelegateCommand ExportPurchasesCommand => _exportPurchasesCommand ??= new DelegateCommand(ExportPurchases);

        private DelegateCommand? _printPurchaseCommand;
        public DelegateCommand PrintPurchaseCommand => _printPurchaseCommand ??= new DelegateCommand(PrintPurchase, CanPrintPurchase);

        #endregion

        #region Constructor

        public PurchaseViewModel()
        {
            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);
            _supplierService = new SupplierService(_dbService);
            _purchaseService = new PurchaseService(_dbService, _productService, _supplierService);

            _ = LoadDataAsync();
        }

        #endregion

        #region Methods

        protected override async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                ClearError();

                await LoadPurchasesAsync();
                await LoadSuppliersAsync();
                CalculateStatistics();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل البيانات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تحميل بيانات المشتريات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadPurchasesAsync()
        {
            var purchases = await _purchaseService.GetAllPurchasesAsync();
            Purchases.Clear();

            foreach (var purchase in purchases)
            {
                Purchases.Add(purchase);
            }
        }

        private async Task LoadSuppliersAsync()
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync();
            Suppliers.Clear();
            Suppliers.Add(new Supplier { Id = 0, Name = "جميع الموردين" });

            foreach (var supplier in suppliers)
            {
                Suppliers.Add(supplier);
            }

            SelectedSupplier = Suppliers.FirstOrDefault();
        }

        private void CalculateStatistics()
        {
            TotalPurchases = Purchases.Count;
            TotalAmount = Purchases.Sum(p => p.Total);
            PaidAmount = Purchases.Where(p => p.PaymentStatus == "مدفوع").Sum(p => p.Total);
            UnpaidAmount = Purchases.Where(p => p.PaymentStatus == "غير مدفوع").Sum(p => p.Total);
        }

        private void ApplyFilters()
        {
            var filtered = Purchases.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                filtered = filtered.Where(p =>
                    p.InvoiceNumber.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    (p.SupplierName?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                    (p.Notes?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false));
            }

            // Apply supplier filter
            if (SelectedSupplier != null && SelectedSupplier.Id > 0)
            {
                filtered = filtered.Where(p => p.SupplierId == SelectedSupplier.Id);
            }

            // Apply date range filter
            filtered = filtered.Where(p =>
            {
                if (DateTime.TryParse(p.Date.ToString(), out DateTime purchaseDate))
                {
                    return purchaseDate.Date >= StartDate.Date && purchaseDate.Date <= EndDate.Date;
                }
                return true;
            });

            // Apply payment status filter
            if (SelectedPaymentStatus != "الكل")
            {
                filtered = filtered.Where(p => p.PaymentStatus == SelectedPaymentStatus);
            }

            FilteredPurchases.Clear();
            foreach (var purchase in filtered.OrderByDescending(p => p.Date))
            {
                FilteredPurchases.Add(purchase);
            }
        }

        #endregion

        #region Command Handlers

        private void AddPurchase()
        {
            PurchaseDialogRequested?.Invoke(null);
        }

        private void EditPurchase()
        {
            if (SelectedPurchase != null)
            {
                PurchaseDialogRequested?.Invoke(SelectedPurchase);
            }
        }

        private bool CanEditPurchase()
        {
            return SelectedPurchase != null;
        }

        private async void DeletePurchase()
        {
            if (SelectedPurchase == null) return;

            try
            {
                var result = System.Windows.MessageBox.Show(
                    $"هل أنت متأكد من حذف فاتورة الشراء '{SelectedPurchase.InvoiceNumber}'؟",
                    "تأكيد الحذف",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    IsLoading = true;
                    await _purchaseService.DeletePurchaseAsync(SelectedPurchase.Id);

                    LoggingService.LogPurchaseTransaction(SelectedPurchase.InvoiceNumber, SelectedPurchase.Total, "حذف فاتورة شراء");

                    await LoadDataAsync();

                    System.Windows.MessageBox.Show("تم حذف فاتورة الشراء بنجاح", "نجح",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حذف فاتورة الشراء: {ex.Message}");
                LoggingService.LogError(ex, $"خطأ في حذف فاتورة الشراء: {SelectedPurchase.InvoiceNumber}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanDeletePurchase()
        {
            return SelectedPurchase != null;
        }

        private void ViewPurchase()
        {
            if (SelectedPurchase != null)
            {
                PurchaseViewRequested?.Invoke(SelectedPurchase);
            }
        }

        private bool CanViewPurchase()
        {
            return SelectedPurchase != null;
        }

        private void ClearFilters()
        {
            SearchText = string.Empty;
            SelectedSupplier = Suppliers.FirstOrDefault();
            StartDate = DateTime.Now.AddMonths(-1);
            EndDate = DateTime.Now;
            SelectedPaymentStatus = "الكل";
        }

        private async void ExportPurchases()
        {
            try
            {
                IsLoading = true;

                // إنشاء مسار الملف للتصدير
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "ملفات إكسل (*.xlsx)|*.xlsx|ملفات CSV (*.csv)|*.csv|ملفات PDF (*.pdf)|*.pdf",
                    Title = "تصدير المشتريات",
                    FileName = $"المشتريات_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (dialog.ShowDialog() == true)
                {
                    // الحصول على المشتريات المفلترة للتصدير
                    var purchases = FilteredPurchases.ToList();
                    if (!purchases.Any())
                    {
                        System.Windows.MessageBox.Show("لا توجد مشتريات للتصدير", "تنبيه",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                        return;
                    }

                    var exportService = new SimpleExportService();
                    string extension = System.IO.Path.GetExtension(dialog.FileName).ToLower();
                    bool success = false;

                    if (extension == ".xlsx")
                    {
                        // تصدير إلى ملف إكسل
                        success = await exportService.ExportToExcelAsync(purchases, dialog.FileName, "المشتريات");
                    }
                    else if (extension == ".csv")
                    {
                        // تصدير إلى ملف CSV
                        success = await exportService.ExportToCsvAsync(purchases, dialog.FileName);
                    }
                    else if (extension == ".pdf")
                    {
                        // تصدير إلى ملف PDF
                        success = await exportService.ExportToPdfAsync(purchases, "تقرير المشتريات", dialog.FileName);
                    }

                    if (success)
                    {
                        System.Windows.MessageBox.Show("تم تصدير المشتريات بنجاح", "تم",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تصدير المشتريات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تصدير المشتريات");
                System.Windows.MessageBox.Show($"حدث خطأ أثناء تصدير المشتريات: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void PrintPurchase()
        {
            if (SelectedPurchase != null)
            {
                try
                {
                    var printService = new Services.PrintService(new Services.SettingsService(new Services.DatabaseService()),
                                                                new Services.BarcodeService());
                    var success = await printService.PrintPurchaseInvoiceAsync(SelectedPurchase);

                    if (success)
                    {
                        System.Windows.MessageBox.Show("تم طباعة الفاتورة بنجاح", "نجحت الطباعة",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        private bool CanPrintPurchase()
        {
            return SelectedPurchase != null;
        }

        #endregion

        #region Events

        public event Action<Purchase?>? PurchaseDialogRequested;
        public event Action<Purchase>? PurchaseViewRequested;

        #endregion

        #region Public Methods

        public async Task OnPurchaseSaved()
        {
            await LoadDataAsync();
        }

        #endregion
    }
}
