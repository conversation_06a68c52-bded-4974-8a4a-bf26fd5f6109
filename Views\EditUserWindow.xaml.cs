using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// نافذة تعديل المستخدم
    /// </summary>
    public partial class EditUserWindow : Window
    {
        private readonly LoginAuthService _authService;
        private readonly EnhancedNotificationService _notificationService;
        private LoginAuthService.SystemUser _originalUser;
        public bool IsModified { get; private set; }

        public EditUserWindow(LoginAuthService.SystemUser user)
        {
            InitializeComponent();
            _authService = LoginAuthService.Instance;
            _notificationService = EnhancedNotificationService.Instance;
            _originalUser = user;

            LoadUserData();
        }

        // تحميل بيانات المستخدم
        private void LoadUserData()
        {
            try
            {
                UsernameTextBox.Text = _originalUser.Username;
                FullNameTextBox.Text = _originalUser.FullName;
                IsActiveCheckBox.IsChecked = _originalUser.IsActive;

                // تحديد الدور في القائمة
                foreach (ComboBoxItem item in RoleComboBox.Items)
                {
                    if (item.Content.ToString() == _originalUser.Role)
                    {
                        RoleComboBox.SelectedItem = item;
                        break;
                    }
                }

                // عرض المعلومات الإضافية
                CreatedAtLabel.Text = $"تاريخ الإنشاء: {_originalUser.CreatedAt:yyyy/MM/dd HH:mm}";
                LastLoginLabel.Text = _originalUser.LastLogin == DateTime.MinValue
                    ? "آخر تسجيل دخول: لم يسجل دخول"
                    : $"آخر تسجيل دخول: {_originalUser.LastLogin:yyyy/MM/dd HH:mm}";
                PermissionsCountLabel.Text = $"عدد الصلاحيات: {_originalUser.Permissions.Count}";

                // منع تعديل المدير العام
                if (_originalUser.Username == "admin")
                {
                    RoleComboBox.IsEnabled = false;
                    IsActiveCheckBox.IsEnabled = false;
                    Title = "عرض بيانات المدير العام (للقراءة فقط)";
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات المستخدم: {ex.Message}");
            }
        }

        // حفظ التغييرات
        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                var fullName = FullNameTextBox.Text.Trim();
                var role = (RoleComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "";
                var isActive = IsActiveCheckBox.IsChecked ?? true;

                // التحقق من وجود تغييرات
                if (_originalUser.FullName == fullName &&
                    _originalUser.Role == role &&
                    _originalUser.IsActive == isActive)
                {
                    MessageBox.Show("لم يتم إجراء أي تغييرات", "معلومات",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // تحديث بيانات المستخدم
                if (UpdateUser(fullName, role, isActive))
                {
                    IsModified = true;

                    _notificationService.AddNotification(
                        "تعديل مستخدم",
                        $"تم تعديل بيانات المستخدم {fullName} بنجاح",
                        Models.EnhancedNotificationType.Success,
                        Models.EnhancedNotificationPriority.Normal,
                        "إدارة المستخدمين");

                    DialogResult = true;
                    this.Close();
                }
                else
                {
                    ShowError("فشل في تحديث بيانات المستخدم");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ التغييرات: {ex.Message}");
            }
        }

        // تحديث بيانات المستخدم
        private bool UpdateUser(string fullName, string role, bool isActive)
        {
            try
            {
                return _authService.UpdateUser(_originalUser.Username, fullName, role, isActive);
            }
            catch
            {
                return false;
            }
        }

        // التحقق من صحة البيانات
        private bool ValidateInput()
        {
            var fullName = FullNameTextBox.Text.Trim();

            if (string.IsNullOrEmpty(fullName))
            {
                ShowError("يرجى إدخال الاسم الكامل");
                FullNameTextBox.Focus();
                return false;
            }

            if (RoleComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار الدور");
                RoleComboBox.Focus();
                return false;
            }

            // إخفاء رسالة الخطأ إذا كانت البيانات صحيحة
            ErrorMessage.Visibility = Visibility.Collapsed;
            return true;
        }

        // تحديد الصلاحيات حسب الدور
        private List<string> GetPermissionsForRole(string role)
        {
            return role switch
            {
                "مدير النظام" => new List<string>
                {
                    "sales", "purchases", "products", "customers", "reports", "settings",
                    "inventory", "suppliers", "expenses", "backup", "user_management",
                    "system_settings", "quick_sale", "crm", "analytics", "view_products",
                    "view_reports", "manage_users", "delete_users", "create_users"
                },
                "محاسبة" => new List<string>
                {
                    "sales", "reports", "customers", "view_products", "expenses"
                },
                "موظف مبيعات" => new List<string>
                {
                    "sales", "customers", "products", "quick_sale"
                },
                "مدير المخزون" => new List<string>
                {
                    "products", "inventory", "purchases", "suppliers"
                },
                "موظف استقبال" => new List<string>
                {
                    "customers", "sales", "quick_sale"
                },
                "مدير فرع" => new List<string>
                {
                    "sales", "customers", "products", "reports", "expenses"
                },
                "محاسبة مساعدة" => new List<string>
                {
                    "sales", "customers", "view_reports"
                },
                "مديرة العملاء" => new List<string>
                {
                    "customers", "sales", "reports", "crm"
                },
                _ => new List<string> { "sales", "customers" }
            };
        }

        // إعادة تعيين كلمة المرور
        private void ResetPassword_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var resetPasswordWindow = new ResetPasswordWindow(_originalUser);
                if (resetPasswordWindow.ShowDialog() == true)
                {
                    var newPassword = resetPasswordWindow.NewPassword;

                    MessageBox.Show($"تم تعيين كلمة المرور الجديدة للمستخدم '{_originalUser.FullName}' بنجاح",
                        "تم بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                    _notificationService.AddNotification(
                        "إعادة تعيين كلمة المرور",
                        $"تم تعيين كلمة مرور جديدة للمستخدم {_originalUser.FullName}",
                        Models.EnhancedNotificationType.Info,
                        Models.EnhancedNotificationPriority.Normal,
                        "إدارة المستخدمين");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}");
            }
        }

        // عرض رسالة خطأ
        private void ShowError(string message)
        {
            ErrorMessage.Text = message;
            ErrorMessage.Visibility = Visibility.Visible;
        }

        // إلغاء العملية
        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            this.Close();
        }
    }
}
