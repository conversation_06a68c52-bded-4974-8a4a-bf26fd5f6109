<UserControl x:Class="SalesManagementSystem.Views.MarketingCampaignView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- تحويل حالة الحملة إلى لون -->
        <Style x:Key="CampaignStatusCard" TargetType="Border">
            <Setter Property="Background" Value="#FAFAFA"/>
            <Setter Property="BorderBrush" Value="#9E9E9E"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Draft">
                    <Setter Property="Background" Value="#FAFAFA"/>
                    <Setter Property="BorderBrush" Value="#9E9E9E"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Planned">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Active">
                    <Setter Property="Background" Value="#E8F5E8"/>
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Paused">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Completed">
                    <Setter Property="Background" Value="#F3E5F5"/>
                    <Setter Property="BorderBrush" Value="#9C27B0"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Cancelled">
                    <Setter Property="Background" Value="#FFEBEE"/>
                    <Setter Property="BorderBrush" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل نوع الحملة إلى أيقونة -->
        <Style x:Key="CampaignTypeIcon" TargetType="materialDesign:PackIcon">
            <Setter Property="Kind" Value="Bullhorn"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Type}" Value="Email">
                    <Setter Property="Kind" Value="Email"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="SocialMedia">
                    <Setter Property="Kind" Value="Facebook"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="PPC">
                    <Setter Property="Kind" Value="GoogleAds"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="SEO">
                    <Setter Property="Kind" Value="Magnify"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="ContentMarketing">
                    <Setter Property="Kind" Value="FileDocument"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Event">
                    <Setter Property="Kind" Value="Calendar"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Print">
                    <Setter Property="Kind" Value="Printer"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Radio">
                    <Setter Property="Kind" Value="Radio"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="TV">
                    <Setter Property="Kind" Value="Television"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Outdoor">
                    <Setter Property="Kind" Value="Billboard"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Bullhorn" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="إدارة الحملات التسويقية" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding CreateCampaignCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="حملة جديدة"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding CampaignTemplatesCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileTemplate" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="قوالب"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding CampaignAnalyticsCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartLine" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحليلات"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات الحملات -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي الحملات -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Bullhorn" Width="28" Height="28" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalCampaigns}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي الحملات" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- الحملات النشطة -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="PlayCircle" Width="28" Height="28" 
                                           Foreground="Green"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ActiveCampaigns}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Green"/>
                    <TextBlock Text="حملات نشطة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- إجمالي الميزانية -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CurrencyUsd" Width="28" Height="28" 
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalBudget, StringFormat='{}{0:C}'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي الميزانية" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- إجمالي الوصول -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AccountGroup" Width="28" Height="28" 
                                           Foreground="Orange"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalReach, StringFormat='{}{0:N0}'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Orange"/>
                    <TextBlock Text="إجمالي الوصول" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- إجمالي التحويلات -->
                <StackPanel Grid.Column="4" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="TrendingUp" Width="28" Height="28" 
                                           Foreground="Purple"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalConversions, StringFormat='{}{0:N0}'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Purple"/>
                    <TextBlock Text="إجمالي التحويلات" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- العائد على الاستثمار -->
                <StackPanel Grid.Column="5" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ChartLine" Width="28" Height="28" 
                                           Foreground="Teal"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding AverageROI, StringFormat='{}{0:F1}%'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Teal"/>
                    <TextBlock Text="متوسط العائد" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط البحث والفلترة -->
        <materialDesign:Card Grid.Row="2" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- مربع البحث -->
                <TextBox Grid.Column="0" 
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="البحث في الحملات..."
                         materialDesign:TextFieldAssist.HasLeadingIcon="True"
                         materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,8,0"/>

                <!-- فلتر النوع -->
                <ComboBox Grid.Column="1" 
                          SelectedItem="{Binding SelectedType}"
                          ItemsSource="{Binding CampaignTypes}"
                          materialDesign:HintAssist.Hint="نوع الحملة"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Display}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- فلتر الحالة -->
                <ComboBox Grid.Column="2" 
                          SelectedItem="{Binding SelectedStatus}"
                          ItemsSource="{Binding CampaignStatuses}"
                          materialDesign:HintAssist.Hint="حالة الحملة"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Display}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- فلتر الأولوية -->
                <ComboBox Grid.Column="3" 
                          SelectedItem="{Binding SelectedPriority}"
                          ItemsSource="{Binding CampaignPriorities}"
                          materialDesign:HintAssist.Hint="الأولوية"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Display}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- أزرار الفلترة -->
                <StackPanel Grid.Column="4" Orientation="Horizontal" Margin="8,0,0,0">
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            Command="{Binding ApplyFiltersCommand}"
                            ToolTip="تطبيق الفلاتر">
                        <materialDesign:PackIcon Kind="FilterVariant" Width="20" Height="20"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            Command="{Binding ClearFiltersCommand}"
                            ToolTip="مسح الفلاتر">
                        <materialDesign:PackIcon Kind="FilterVariantRemove" Width="20" Height="20"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- قائمة الحملات -->
        <ScrollViewer Grid.Row="3" Margin="16,8,16,16" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding FilteredCampaigns}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="2" Margin="0,0,0,16"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <materialDesign:Card Margin="8" 
                                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
                            <Border BorderThickness="2" CornerRadius="4" Style="{StaticResource CampaignStatusCard}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- رأس البطاقة -->
                                    <Grid Grid.Row="0" Margin="16,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- أيقونة نوع الحملة -->
                                        <materialDesign:PackIcon Grid.Column="0" 
                                                               Style="{StaticResource CampaignTypeIcon}"
                                                               Width="24" Height="24"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,12,0"/>

                                        <!-- اسم الحملة -->
                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding Name}" 
                                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                     FontWeight="Bold"/>
                                            <TextBlock Text="{Binding CampaignCode}" 
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     Opacity="0.7"/>
                                        </StackPanel>

                                        <!-- مؤشر الأولوية -->
                                        <materialDesign:Chip Grid.Column="2"
                                                           Content="{Binding PriorityDisplay}"
                                                           VerticalAlignment="Center"
                                                           FontSize="10"
                                                           Background="{Binding PriorityColor}"
                                                           Margin="8,0"/>

                                        <!-- مؤشر الحالة -->
                                        <materialDesign:Chip Grid.Column="3"
                                                           Content="{Binding StatusDisplay}"
                                                           VerticalAlignment="Center"
                                                           FontSize="10"
                                                           Background="{Binding StatusColor}"/>
                                    </Grid>

                                    <!-- محتوى البطاقة -->
                                    <Grid Grid.Row="1" Margin="16">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- الوصف -->
                                        <TextBlock Grid.Row="0" 
                                                 Text="{Binding Description}"
                                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                 TextWrapping="Wrap"
                                                 MaxHeight="60"
                                                 Margin="0,0,0,8"/>

                                        <!-- الميزانية والتكلفة -->
                                        <Grid Grid.Row="1" Margin="0,0,0,8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                <TextBlock Text="الميزانية" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding FormattedBudget}" 
                                                         Style="{StaticResource MaterialDesignBody1TextBlock}" 
                                                         Foreground="{DynamicResource SecondaryHueMidBrush}"
                                                         FontWeight="Bold"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                <TextBlock Text="التكلفة الفعلية" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding FormattedActualCost}" 
                                                         Style="{StaticResource MaterialDesignBody1TextBlock}" 
                                                         FontWeight="Bold"/>
                                            </StackPanel>
                                        </Grid>

                                        <!-- الوصول والتحويلات -->
                                        <Grid Grid.Row="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                <TextBlock Text="الوصول" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding FormattedActualReach}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                                    <TextBlock Text=" / " Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                                    <TextBlock Text="{Binding FormattedTargetReach}" Style="{StaticResource MaterialDesignBody2TextBlock}" Opacity="0.7"/>
                                                </StackPanel>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                <TextBlock Text="التحويلات" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding FormattedConversions}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                                    <TextBlock Text=" (" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                                    <TextBlock Text="{Binding FormattedConversionRate}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                                    <TextBlock Text=")" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Grid>
                                    </Grid>

                                    <!-- شريط التقدم الزمني -->
                                    <Grid Grid.Row="2" Margin="16,0,16,8">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="SpaceBetween" Margin="0,0,0,4">
                                            <TextBlock Text="{Binding FormattedStartDate}" Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                            <TextBlock Text="{Binding FormattedEndDate}" Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                        </StackPanel>

                                        <ProgressBar Grid.Row="1" 
                                                   Value="{Binding DaysElapsed}" 
                                                   Maximum="{Binding Duration}" 
                                                   Height="4"/>
                                    </Grid>

                                    <!-- أزرار الإجراءات -->
                                    <Grid Grid.Row="3" Margin="16,8,16,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- نوع الحملة -->
                                        <materialDesign:Chip Grid.Column="0" 
                                                           Content="{Binding TypeDisplay}" 
                                                           FontSize="10"
                                                           VerticalAlignment="Center"/>

                                        <!-- زر الإطلاق/الإيقاف -->
                                        <Button Grid.Column="1" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.ToggleCampaignCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="{Binding IsActive, Converter={StaticResource BooleanToToggleTooltipConverter}}">
                                            <materialDesign:PackIcon Kind="{Binding IsActive, Converter={StaticResource BooleanToToggleIconConverter}}" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر التحليلات -->
                                        <Button Grid.Column="2" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.ViewCampaignAnalyticsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="عرض التحليلات">
                                            <materialDesign:PackIcon Kind="ChartLine" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر التحرير -->
                                        <Button Grid.Column="3" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.EditCampaignCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="تحرير">
                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر النسخ -->
                                        <Button Grid.Column="4" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.DuplicateCampaignCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="نسخ الحملة">
                                            <materialDesign:PackIcon Kind="ContentCopy" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر الحذف -->
                                        <Button Grid.Column="5" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.DeleteCampaignCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="حذف">
                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Border>
                        </materialDesign:Card>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Grid Grid.RowSpan="4" 
              Background="White" 
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري التحميل..." 
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
