using System;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج العنوان للشحن والفوترة
    /// </summary>
    public class Address : INotifyPropertyChanged
    {
        private int _id;
        private int _customerId;
        private AddressType _addressType = AddressType.Billing;
        private string _firstName = string.Empty;
        private string _lastName = string.Empty;
        private string _company = string.Empty;
        private string _addressLine1 = string.Empty;
        private string _addressLine2 = string.Empty;
        private string _city = string.Empty;
        private string _state = string.Empty;
        private string _postalCode = string.Empty;
        private string _country = "Saudi Arabia";
        private string _phone = string.Empty;
        private string _email = string.Empty;
        private bool _isDefault;
        private bool _isActive = true;
        private string _notes = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public AddressType AddressType
        {
            get => _addressType;
            set
            {
                if (_addressType != value)
                {
                    _addressType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(AddressTypeDisplay));
                }
            }
        }

        public string FirstName
        {
            get => _firstName;
            set
            {
                if (_firstName != value)
                {
                    _firstName = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullName));
                }
            }
        }

        public string LastName
        {
            get => _lastName;
            set
            {
                if (_lastName != value)
                {
                    _lastName = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullName));
                }
            }
        }

        public string Company
        {
            get => _company;
            set
            {
                if (_company != value)
                {
                    _company = value;
                    OnPropertyChanged();
                }
            }
        }

        public string AddressLine1
        {
            get => _addressLine1;
            set
            {
                if (_addressLine1 != value)
                {
                    _addressLine1 = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string AddressLine2
        {
            get => _addressLine2;
            set
            {
                if (_addressLine2 != value)
                {
                    _addressLine2 = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string City
        {
            get => _city;
            set
            {
                if (_city != value)
                {
                    _city = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string State
        {
            get => _state;
            set
            {
                if (_state != value)
                {
                    _state = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string PostalCode
        {
            get => _postalCode;
            set
            {
                if (_postalCode != value)
                {
                    _postalCode = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string Country
        {
            get => _country;
            set
            {
                if (_country != value)
                {
                    _country = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string Phone
        {
            get => _phone;
            set
            {
                if (_phone != value)
                {
                    _phone = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Email
        {
            get => _email;
            set
            {
                if (_email != value)
                {
                    _email = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsDefault
        {
            get => _isDefault;
            set
            {
                if (_isDefault != value)
                {
                    _isDefault = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public string FullName => $"{FirstName} {LastName}".Trim();

        public string FullAddress
        {
            get
            {
                var parts = new[]
                {
                    AddressLine1,
                    AddressLine2,
                    City,
                    State,
                    PostalCode,
                    Country
                }.Where(p => !string.IsNullOrWhiteSpace(p));

                return string.Join(", ", parts);
            }
        }

        public string AddressTypeDisplay
        {
            get
            {
                return AddressType switch
                {
                    AddressType.Billing => "عنوان الفوترة",
                    AddressType.Shipping => "عنوان الشحن",
                    AddressType.Both => "الفوترة والشحن",
                    _ => "غير محدد"
                };
            }
        }

        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        public Address Clone()
        {
            return new Address
            {
                CustomerId = CustomerId,
                AddressType = AddressType,
                FirstName = FirstName,
                LastName = LastName,
                Company = Company,
                AddressLine1 = AddressLine1,
                AddressLine2 = AddressLine2,
                City = City,
                State = State,
                PostalCode = PostalCode,
                Country = Country,
                Phone = Phone,
                Email = Email,
                Notes = Notes
            };
        }

        public void CopyFrom(Address other)
        {
            if (other == null) return;

            FirstName = other.FirstName;
            LastName = other.LastName;
            Company = other.Company;
            AddressLine1 = other.AddressLine1;
            AddressLine2 = other.AddressLine2;
            City = other.City;
            State = other.State;
            PostalCode = other.PostalCode;
            Country = other.Country;
            Phone = other.Phone;
            Email = other.Email;
            Notes = other.Notes;
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// عنوان الفوترة
    /// </summary>
    public class BillingAddress : Address
    {
        public BillingAddress()
        {
            AddressType = AddressType.Billing;
        }
    }

    /// <summary>
    /// عنوان الشحن
    /// </summary>
    public class ShippingAddress : Address
    {
        private string _deliveryInstructions = string.Empty;
        private bool _requireSignature;
        private bool _leaveAtDoor;

        public ShippingAddress()
        {
            AddressType = AddressType.Shipping;
        }

        public string DeliveryInstructions
        {
            get => _deliveryInstructions;
            set
            {
                if (_deliveryInstructions != value)
                {
                    _deliveryInstructions = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool RequireSignature
        {
            get => _requireSignature;
            set
            {
                if (_requireSignature != value)
                {
                    _requireSignature = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool LeaveAtDoor
        {
            get => _leaveAtDoor;
            set
            {
                if (_leaveAtDoor != value)
                {
                    _leaveAtDoor = value;
                    OnPropertyChanged();
                }
            }
        }
    }

    #endregion

    #region Enums

    public enum AddressType
    {
        Billing,        // عنوان الفوترة
        Shipping,       // عنوان الشحن
        Both           // كلاهما
    }

    #endregion

    #region Validation

    public class AddressValidator : AbstractValidator<Address>
    {
        public AddressValidator()
        {
            RuleFor(a => a.FirstName)
                .NotEmpty().WithMessage("الاسم الأول مطلوب")
                .MaximumLength(50).WithMessage("الاسم الأول لا يمكن أن يتجاوز 50 حرف");

            RuleFor(a => a.LastName)
                .NotEmpty().WithMessage("اسم العائلة مطلوب")
                .MaximumLength(50).WithMessage("اسم العائلة لا يمكن أن يتجاوز 50 حرف");

            RuleFor(a => a.AddressLine1)
                .NotEmpty().WithMessage("العنوان مطلوب")
                .MaximumLength(200).WithMessage("العنوان لا يمكن أن يتجاوز 200 حرف");

            RuleFor(a => a.City)
                .NotEmpty().WithMessage("المدينة مطلوبة")
                .MaximumLength(100).WithMessage("المدينة لا يمكن أن تتجاوز 100 حرف");

            RuleFor(a => a.Country)
                .NotEmpty().WithMessage("البلد مطلوب")
                .MaximumLength(100).WithMessage("البلد لا يمكن أن يتجاوز 100 حرف");

            RuleFor(a => a.Phone)
                .NotEmpty().WithMessage("رقم الهاتف مطلوب")
                .Matches(@"^[\+]?[0-9\-\(\)\s]+$").WithMessage("رقم الهاتف غير صحيح");

            RuleFor(a => a.Email)
                .EmailAddress().When(a => !string.IsNullOrEmpty(a.Email))
                .WithMessage("البريد الإلكتروني غير صحيح");

            RuleFor(a => a.PostalCode)
                .MaximumLength(20).WithMessage("الرمز البريدي لا يمكن أن يتجاوز 20 حرف");
        }
    }

    #endregion
}
