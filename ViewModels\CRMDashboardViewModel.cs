using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel للوحة تحكم CRM
    /// </summary>
    public class CRMDashboardViewModel : INotifyPropertyChanged
    {
        private readonly CRMService _crmService;
        private readonly CRMReportsService _reportsService;
        private readonly NotificationService _notificationService;

        private bool _isLoading;
        private CRMDashboardSummary _dashboardSummary = new();
        private ObservableCollection<CrmCustomer> _recentCustomers = new();
        private ObservableCollection<SalesOpportunity> _recentOpportunities = new();
        private ObservableCollection<CustomerActivity> _recentActivities = new();
        private ObservableCollection<SalesOpportunity> _upcomingFollowUps = new();

        // Statistics Properties
        private int _totalCustomers;
        private int _activeCustomers;
        private int _openOpportunities;
        private decimal _totalOpportunityValue;
        private int _activeCampaigns;
        private decimal _conversionRate;

        public CRMDashboardViewModel(
            CRMService crmService,
            CRMReportsService reportsService,
            NotificationService notificationService)
        {
            _crmService = crmService ?? throw new ArgumentNullException(nameof(crmService));
            _reportsService = reportsService ?? throw new ArgumentNullException(nameof(reportsService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));

            InitializeCommands();
            _ = Task.Run(async () => await LoadDashboardDataAsync());
        }

        #region Properties

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }

        public CRMDashboardSummary DashboardSummary
        {
            get => _dashboardSummary;
            set
            {
                if (_dashboardSummary != value)
                {
                    _dashboardSummary = value;
                    OnPropertyChanged();
                    UpdateStatistics();
                }
            }
        }

        public ObservableCollection<CrmCustomer> RecentCustomers
        {
            get => _recentCustomers;
            set
            {
                if (_recentCustomers != value)
                {
                    _recentCustomers = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<SalesOpportunity> RecentOpportunities
        {
            get => _recentOpportunities;
            set
            {
                if (_recentOpportunities != value)
                {
                    _recentOpportunities = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<CustomerActivity> RecentActivities
        {
            get => _recentActivities;
            set
            {
                if (_recentActivities != value)
                {
                    _recentActivities = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<SalesOpportunity> UpcomingFollowUps
        {
            get => _upcomingFollowUps;
            set
            {
                if (_upcomingFollowUps != value)
                {
                    _upcomingFollowUps = value;
                    OnPropertyChanged();
                }
            }
        }

        // Statistics Properties
        public int TotalCustomers
        {
            get => _totalCustomers;
            set
            {
                if (_totalCustomers != value)
                {
                    _totalCustomers = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ActiveCustomers
        {
            get => _activeCustomers;
            set
            {
                if (_activeCustomers != value)
                {
                    _activeCustomers = value;
                    OnPropertyChanged();
                }
            }
        }

        public int OpenOpportunities
        {
            get => _openOpportunities;
            set
            {
                if (_openOpportunities != value)
                {
                    _openOpportunities = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal TotalOpportunityValue
        {
            get => _totalOpportunityValue;
            set
            {
                if (_totalOpportunityValue != value)
                {
                    _totalOpportunityValue = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ActiveCampaigns
        {
            get => _activeCampaigns;
            set
            {
                if (_activeCampaigns != value)
                {
                    _activeCampaigns = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal ConversionRate
        {
            get => _conversionRate;
            set
            {
                if (_conversionRate != value)
                {
                    _conversionRate = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Commands

        public ICommand AddCustomerCommand { get; private set; } = null!;
        public ICommand AddOpportunityCommand { get; private set; } = null!;
        public ICommand CreateCampaignCommand { get; private set; } = null!;
        public ICommand ViewCustomersCommand { get; private set; } = null!;
        public ICommand ViewOpportunitiesCommand { get; private set; } = null!;
        public ICommand ViewCampaignsCommand { get; private set; } = null!;
        public ICommand ViewReportsCommand { get; private set; } = null!;
        public ICommand ViewActivitiesCommand { get; private set; } = null!;
        public ICommand ViewCustomerCommand { get; private set; } = null!;
        public ICommand ContactCustomerCommand { get; private set; } = null!;
        public ICommand ViewAllCustomersCommand { get; private set; } = null!;
        public ICommand ViewAllOpportunitiesCommand { get; private set; } = null!;
        public ICommand RefreshCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            AddCustomerCommand = new RelayCommand(async () => await AddCustomerAsync());
            AddOpportunityCommand = new RelayCommand(async () => await AddOpportunityAsync());
            CreateCampaignCommand = new RelayCommand(async () => await CreateCampaignAsync());
            ViewCustomersCommand = new RelayCommand(() => NavigateToCustomers());
            ViewOpportunitiesCommand = new RelayCommand(() => NavigateToOpportunities());
            ViewCampaignsCommand = new RelayCommand(() => NavigateToCampaigns());
            ViewReportsCommand = new RelayCommand(() => NavigateToReports());
            ViewActivitiesCommand = new RelayCommand(() => NavigateToActivities());
            ViewCustomerCommand = new RelayCommand<CrmCustomer>(customer => ViewCustomerDetails(customer!));
            ContactCustomerCommand = new RelayCommand<CrmCustomer>(customer => _ = ContactCustomer(customer!));
            ViewAllCustomersCommand = new RelayCommand(() => NavigateToCustomers());
            ViewAllOpportunitiesCommand = new RelayCommand(() => NavigateToOpportunities());
            RefreshCommand = new RelayCommand(async () => await RefreshDashboardAsync());
        }

        #endregion

        #region Methods

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                IsLoading = true;

                // تحميل ملخص لوحة التحكم
                DashboardSummary = await _reportsService.GetDashboardSummaryAsync();

                // تحميل العملاء الجدد (آخر 10)
                var allCustomers = await _crmService.GetAllCustomersAsync();
                RecentCustomers = new ObservableCollection<CrmCustomer>(
                    allCustomers.OrderByDescending(c => c.CreatedAt).Take(10)
                );

                // تحميل الفرص الحديثة (آخر 10)
                var allOpportunities = await _crmService.GetAllOpportunitiesAsync();
                RecentOpportunities = new ObservableCollection<SalesOpportunity>(
                    allOpportunities.OrderByDescending(o => o.CreatedAt).Take(10)
                );

                // تحميل الأنشطة الحديثة
                await LoadRecentActivitiesAsync();

                // تحميل المتابعات القادمة
                UpcomingFollowUps = new ObservableCollection<SalesOpportunity>(
                    allOpportunities.Where(o => o.NextFollowUpDate.HasValue &&
                                              o.NextFollowUpDate.Value <= DateTime.Now.AddDays(7))
                                   .OrderBy(o => o.NextFollowUpDate)
                                   .Take(10)
                );
            }
            catch (Exception ex)
            {
                await _notificationService.ShowErrorAsync("خطأ في تحميل بيانات لوحة التحكم", ex.Message);
                LoggingService.LogError(ex, "خطأ في تحميل بيانات لوحة التحكم");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadRecentActivitiesAsync()
        {
            try
            {
                // هذا مثال - في التطبيق الحقيقي ستحتاج إلى استعلام قاعدة البيانات
                await Task.Delay(100); // محاكاة عملية تحميل البيانات
                var activities = new List<CustomerActivity>();

                // إضافة أنشطة وهمية للعرض
                activities.Add(new CustomerActivity
                {
                    ActivityType = "اتصال هاتفي",
                    Description = "تم الاتصال بالعميل لمتابعة العرض",
                    ActivityDate = DateTime.Now.AddHours(-2),
                    PerformedBy = "أحمد محمد"
                });

                activities.Add(new CustomerActivity
                {
                    ActivityType = "إرسال عرض",
                    Description = "تم إرسال عرض سعر جديد للعميل",
                    ActivityDate = DateTime.Now.AddHours(-4),
                    PerformedBy = "فاطمة علي"
                });

                activities.Add(new CustomerActivity
                {
                    ActivityType = "اجتماع",
                    Description = "اجتماع مع العميل لمناقشة المتطلبات",
                    ActivityDate = DateTime.Now.AddHours(-6),
                    PerformedBy = "محمد سالم"
                });

                RecentActivities = new ObservableCollection<CustomerActivity>(activities);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل الأنشطة الحديثة");
            }
        }

        private void UpdateStatistics()
        {
            if (DashboardSummary != null)
            {
                TotalCustomers = DashboardSummary.TotalCustomers;
                ActiveCustomers = DashboardSummary.ActiveCustomers;
                OpenOpportunities = DashboardSummary.OpenOpportunities;
                TotalOpportunityValue = DashboardSummary.PipelineValue;
                ActiveCampaigns = DashboardSummary.ActiveCampaigns;
                ConversionRate = DashboardSummary.LeadConversionRate;
            }
        }

        private async Task AddCustomerAsync()
        {
            try
            {
                // فتح نافذة إضافة عميل جديد
                var customerDialog = new CustomerEditDialog();
                var result = customerDialog.ShowDialog();

                if (result == true && customerDialog.Customer != null)
                {
                    await _crmService.CreateCustomerAsync(customerDialog.Customer);
                    await RefreshDashboardAsync();

                    await _notificationService.ShowSuccessAsync(
                        "تم إضافة العميل بنجاح",
                        $"تم إضافة العميل {customerDialog.Customer.DisplayName} بنجاح"
                    );
                }
            }
            catch (Exception ex)
            {
                await _notificationService.ShowErrorAsync("خطأ في إضافة العميل", ex.Message);
                LoggingService.LogError(ex, "خطأ في إضافة عميل جديد");
            }
        }

        private async Task AddOpportunityAsync()
        {
            try
            {
                // فتح نافذة إضافة فرصة تجارية جديدة
                var opportunityDialog = new OpportunityEditDialog();
                var result = opportunityDialog.ShowDialog();

                if (result == true && opportunityDialog.Opportunity != null)
                {
                    await _crmService.CreateOpportunityAsync(opportunityDialog.Opportunity);
                    await RefreshDashboardAsync();

                    await _notificationService.ShowSuccessAsync(
                        "تم إضافة الفرصة التجارية بنجاح",
                        $"تم إضافة الفرصة {opportunityDialog.Opportunity.Title} بنجاح"
                    );
                }
            }
            catch (Exception ex)
            {
                await _notificationService.ShowErrorAsync("خطأ في إضافة الفرصة التجارية", ex.Message);
                LoggingService.LogError(ex, "خطأ في إضافة فرصة تجارية جديدة");
            }
        }

        private async Task CreateCampaignAsync()
        {
            try
            {
                // فتح نافذة إنشاء حملة تسويقية جديدة
                var campaignDialog = new CampaignEditDialog();
                var result = campaignDialog.ShowDialog();

                if (result == true && campaignDialog.Campaign != null)
                {
                    await _crmService.CreateCampaignAsync(campaignDialog.Campaign);
                    await RefreshDashboardAsync();

                    await _notificationService.ShowSuccessAsync(
                        "تم إنشاء الحملة التسويقية بنجاح",
                        $"تم إنشاء الحملة {campaignDialog.Campaign.Name} بنجاح"
                    );
                }
            }
            catch (Exception ex)
            {
                await _notificationService.ShowErrorAsync("خطأ في إنشاء الحملة التسويقية", ex.Message);
                LoggingService.LogError(ex, "خطأ في إنشاء حملة تسويقية جديدة");
            }
        }

        private void NavigateToCustomers()
        {
            // التنقل إلى صفحة إدارة العملاء
            NavigationService.NavigateTo("CustomerManagement");
        }

        private void NavigateToOpportunities()
        {
            // التنقل إلى صفحة إدارة الفرص التجارية
            NavigationService.NavigateTo("OpportunityManagement");
        }

        private void NavigateToCampaigns()
        {
            // التنقل إلى صفحة إدارة الحملات التسويقية
            NavigationService.NavigateTo("MarketingCampaigns");
        }

        private void NavigateToReports()
        {
            // التنقل إلى صفحة التقارير
            NavigationService.NavigateTo("CRMReports");
        }

        private void NavigateToActivities()
        {
            // التنقل إلى صفحة الأنشطة
            NavigationService.NavigateTo("Activities");
        }

        private void ViewCustomerDetails(CrmCustomer customer)
        {
            if (customer != null)
            {
                // فتح نافذة تفاصيل العميل
                var customerDetailsDialog = new CustomerDetailsDialog(customer);
                customerDetailsDialog.ShowDialog();
            }
        }

        private async Task ContactCustomer(CrmCustomer customer)
        {
            if (customer != null)
            {
                try
                {
                    // فتح نافذة الاتصال بالعميل
                    var contactDialog = new ContactCustomerDialog(customer);
                    var result = contactDialog.ShowDialog();

                    if (result == true)
                    {
                        // إضافة نشاط الاتصال
                        await _crmService.AddCustomerActivityAsync(
                            customer.Id,
                            "اتصال",
                            contactDialog.ContactNotes,
                            Environment.UserName
                        );

                        await RefreshDashboardAsync();
                    }
                }
                catch (Exception ex)
                {
                    await _notificationService.ShowErrorAsync("خطأ في تسجيل الاتصال", ex.Message);
                    LoggingService.LogError(ex, $"خطأ في تسجيل اتصال مع العميل {customer.Id}");
                }
            }
        }

        private async Task RefreshDashboardAsync()
        {
            await LoadDashboardDataAsync();
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Helper Classes

    // هذه الفئات ستحتاج إلى تطوير منفصل
    public class CustomerEditDialog
    {
        public CrmCustomer? Customer { get; set; }
        public bool? ShowDialog() => true; // Placeholder
    }

    public class OpportunityEditDialog
    {
        public SalesOpportunity? Opportunity { get; set; }
        public bool? ShowDialog() => true; // Placeholder
    }

    public class CampaignEditDialog
    {
        public MarketingCampaign? Campaign { get; set; }
        public bool? ShowDialog() => true; // Placeholder
    }

    public class CustomerDetailsDialog
    {
        public CustomerDetailsDialog(CrmCustomer customer) { }
        public bool? ShowDialog() => true; // Placeholder
    }

    public class ContactCustomerDialog
    {
        public string ContactNotes { get; set; } = string.Empty;
        public ContactCustomerDialog(CrmCustomer customer) { }
        public bool? ShowDialog() => true; // Placeholder
    }

    public static class NavigationService
    {
        public static void NavigateTo(string page) { } // Placeholder
    }

    #endregion
}
