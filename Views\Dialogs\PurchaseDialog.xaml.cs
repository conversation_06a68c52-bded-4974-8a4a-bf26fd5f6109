using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views.Dialogs
{
    public partial class PurchaseDialog : Window
    {
        private readonly PurchaseDialogViewModel _viewModel;

        public Purchase? Result { get; private set; }

        public PurchaseDialog(Purchase? existingPurchase = null)
        {
            InitializeComponent();
            
            _viewModel = existingPurchase != null 
                ? new PurchaseDialogViewModel(existingPurchase) 
                : new PurchaseDialogViewModel();
            
            DataContext = _viewModel;
            
            // Subscribe to events
            _viewModel.PurchaseSaved += OnPurchaseSaved;
            _viewModel.RequestClose += OnRequestClose;
        }

        #region Event Handlers

        private void OnPurchaseSaved(Purchase purchase)
        {
            Result = purchase;
            DialogResult = true;
        }

        private void OnRequestClose(bool result)
        {
            DialogResult = result;
            Close();
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // Unsubscribe from events
            _viewModel.PurchaseSaved -= OnPurchaseSaved;
            _viewModel.RequestClose -= OnRequestClose;
            
            base.OnClosed(e);
        }

        #endregion
    }
}
