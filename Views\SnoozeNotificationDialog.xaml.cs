using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// نافذة تأجيل الإشعار
    /// </summary>
    public partial class SnoozeNotificationDialog : Window, INotifyPropertyChanged
    {
        private readonly NotificationService _notificationService;
        private Notification _notification = null!;
        
        private bool _isSelected5Minutes;
        private bool _isSelected15Minutes;
        private bool _isSelected30Minutes;
        private bool _isSelected1Hour;
        private bool _isSelected3Hours;
        private bool _isSelected1Day;
        private bool _isCustomTime;
        private DateTime _customDate = DateTime.Today.AddDays(1);
        private TimeSpan _customTime = new TimeSpan(9, 0, 0);
        private string _calculatedSnoozeTime = string.Empty;

        public SnoozeNotificationDialog()
        {
            InitializeComponent();
            DataContext = this;
            
            var dbService = new DatabaseService();
            var settingsService = new SettingsService(dbService);
            _notificationService = new NotificationService(dbService, settingsService);
            
            InitializeCommands();
            
            // تحديد الخيار الافتراضي
            IsSelected15Minutes = true;
        }

        public SnoozeNotificationDialog(Notification notification) : this()
        {
            Notification = notification;
        }

        #region Properties

        public Notification Notification
        {
            get => _notification;
            set
            {
                if (_notification != value)
                {
                    _notification = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsSelected5Minutes
        {
            get => _isSelected5Minutes;
            set
            {
                if (_isSelected5Minutes != value)
                {
                    _isSelected5Minutes = value;
                    OnPropertyChanged();
                    if (value) ClearOtherSelections(nameof(IsSelected5Minutes));
                    UpdateCalculatedTime();
                }
            }
        }

        public bool IsSelected15Minutes
        {
            get => _isSelected15Minutes;
            set
            {
                if (_isSelected15Minutes != value)
                {
                    _isSelected15Minutes = value;
                    OnPropertyChanged();
                    if (value) ClearOtherSelections(nameof(IsSelected15Minutes));
                    UpdateCalculatedTime();
                }
            }
        }

        public bool IsSelected30Minutes
        {
            get => _isSelected30Minutes;
            set
            {
                if (_isSelected30Minutes != value)
                {
                    _isSelected30Minutes = value;
                    OnPropertyChanged();
                    if (value) ClearOtherSelections(nameof(IsSelected30Minutes));
                    UpdateCalculatedTime();
                }
            }
        }

        public bool IsSelected1Hour
        {
            get => _isSelected1Hour;
            set
            {
                if (_isSelected1Hour != value)
                {
                    _isSelected1Hour = value;
                    OnPropertyChanged();
                    if (value) ClearOtherSelections(nameof(IsSelected1Hour));
                    UpdateCalculatedTime();
                }
            }
        }

        public bool IsSelected3Hours
        {
            get => _isSelected3Hours;
            set
            {
                if (_isSelected3Hours != value)
                {
                    _isSelected3Hours = value;
                    OnPropertyChanged();
                    if (value) ClearOtherSelections(nameof(IsSelected3Hours));
                    UpdateCalculatedTime();
                }
            }
        }

        public bool IsSelected1Day
        {
            get => _isSelected1Day;
            set
            {
                if (_isSelected1Day != value)
                {
                    _isSelected1Day = value;
                    OnPropertyChanged();
                    if (value) ClearOtherSelections(nameof(IsSelected1Day));
                    UpdateCalculatedTime();
                }
            }
        }

        public bool IsCustomTime
        {
            get => _isCustomTime;
            set
            {
                if (_isCustomTime != value)
                {
                    _isCustomTime = value;
                    OnPropertyChanged();
                    if (value) ClearOtherSelections(nameof(IsCustomTime));
                    UpdateCalculatedTime();
                }
            }
        }

        public DateTime CustomDate
        {
            get => _customDate;
            set
            {
                if (_customDate != value)
                {
                    _customDate = value;
                    OnPropertyChanged();
                    UpdateCalculatedTime();
                }
            }
        }

        public TimeSpan CustomTime
        {
            get => _customTime;
            set
            {
                if (_customTime != value)
                {
                    _customTime = value;
                    OnPropertyChanged();
                    UpdateCalculatedTime();
                }
            }
        }

        public string CalculatedSnoozeTime
        {
            get => _calculatedSnoozeTime;
            set
            {
                if (_calculatedSnoozeTime != value)
                {
                    _calculatedSnoozeTime = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Commands

        public ICommand SnoozeCommand { get; private set; } = null!;
        public ICommand CancelCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            SnoozeCommand = new RelayCommand(async () => await SnoozeNotificationAsync(), () => CanSnooze());
            CancelCommand = new RelayCommand(() => Close());
        }

        #endregion

        #region Methods

        private void ClearOtherSelections(string currentSelection)
        {
            if (currentSelection != nameof(IsSelected5Minutes)) _isSelected5Minutes = false;
            if (currentSelection != nameof(IsSelected15Minutes)) _isSelected15Minutes = false;
            if (currentSelection != nameof(IsSelected30Minutes)) _isSelected30Minutes = false;
            if (currentSelection != nameof(IsSelected1Hour)) _isSelected1Hour = false;
            if (currentSelection != nameof(IsSelected3Hours)) _isSelected3Hours = false;
            if (currentSelection != nameof(IsSelected1Day)) _isSelected1Day = false;
            if (currentSelection != nameof(IsCustomTime)) _isCustomTime = false;

            // إثارة أحداث التغيير
            OnPropertyChanged(nameof(IsSelected5Minutes));
            OnPropertyChanged(nameof(IsSelected15Minutes));
            OnPropertyChanged(nameof(IsSelected30Minutes));
            OnPropertyChanged(nameof(IsSelected1Hour));
            OnPropertyChanged(nameof(IsSelected3Hours));
            OnPropertyChanged(nameof(IsSelected1Day));
            OnPropertyChanged(nameof(IsCustomTime));
        }

        private void UpdateCalculatedTime()
        {
            try
            {
                DateTime snoozeTime;

                if (IsSelected5Minutes)
                    snoozeTime = DateTime.Now.AddMinutes(5);
                else if (IsSelected15Minutes)
                    snoozeTime = DateTime.Now.AddMinutes(15);
                else if (IsSelected30Minutes)
                    snoozeTime = DateTime.Now.AddMinutes(30);
                else if (IsSelected1Hour)
                    snoozeTime = DateTime.Now.AddHours(1);
                else if (IsSelected3Hours)
                    snoozeTime = DateTime.Now.AddHours(3);
                else if (IsSelected1Day)
                    snoozeTime = DateTime.Now.AddDays(1);
                else if (IsCustomTime)
                    snoozeTime = CustomDate.Date.Add(CustomTime);
                else
                    snoozeTime = DateTime.Now.AddMinutes(15); // افتراضي

                CalculatedSnoozeTime = snoozeTime.ToString("dd/MM/yyyy HH:mm");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في حساب وقت التأجيل");
                CalculatedSnoozeTime = "خطأ في الحساب";
            }
        }

        private bool CanSnooze()
        {
            if (IsCustomTime)
            {
                var customDateTime = CustomDate.Date.Add(CustomTime);
                return customDateTime > DateTime.Now;
            }

            return IsSelected5Minutes || IsSelected15Minutes || IsSelected30Minutes ||
                   IsSelected1Hour || IsSelected3Hours || IsSelected1Day;
        }

        private async System.Threading.Tasks.Task SnoozeNotificationAsync()
        {
            try
            {
                if (Notification == null) return;

                TimeSpan snoozeTime;

                if (IsSelected5Minutes)
                    snoozeTime = TimeSpan.FromMinutes(5);
                else if (IsSelected15Minutes)
                    snoozeTime = TimeSpan.FromMinutes(15);
                else if (IsSelected30Minutes)
                    snoozeTime = TimeSpan.FromMinutes(30);
                else if (IsSelected1Hour)
                    snoozeTime = TimeSpan.FromHours(1);
                else if (IsSelected3Hours)
                    snoozeTime = TimeSpan.FromHours(3);
                else if (IsSelected1Day)
                    snoozeTime = TimeSpan.FromDays(1);
                else if (IsCustomTime)
                {
                    var customDateTime = CustomDate.Date.Add(CustomTime);
                    snoozeTime = customDateTime - DateTime.Now;
                    
                    if (snoozeTime.TotalMinutes <= 0)
                    {
                        MessageBox.Show("الوقت المحدد يجب أن يكون في المستقبل", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }
                else
                {
                    snoozeTime = TimeSpan.FromMinutes(15); // افتراضي
                }

                // تأجيل الإشعار
                await _notificationService.SnoozeNotificationAsync(Notification.Id, snoozeTime);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تأجيل الإشعار: {Notification?.Id}");
                MessageBox.Show($"خطأ في تأجيل الإشعار: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
