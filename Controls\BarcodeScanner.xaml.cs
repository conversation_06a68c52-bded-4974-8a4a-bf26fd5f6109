using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Win32;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Controls
{
    /// <summary>
    /// عنصر تحكم ماسح الباركود
    /// </summary>
    public partial class BarcodeScanner : UserControl, INotifyPropertyChanged
    {
        private readonly BarcodeService _barcodeService;
        private readonly ProductService _productService;
        
        private string _barcodeText = string.Empty;
        private Product? _foundProduct;
        private bool _isScanning;
        private bool _hasScanResult;
        private bool _productNotFound;

        public BarcodeScanner()
        {
            InitializeComponent();
            DataContext = this;
            
            _barcodeService = new BarcodeService();
            _productService = new ProductService(new DatabaseService());
            
            InitializeCommands();
        }

        #region Properties

        public string BarcodeText
        {
            get => _barcodeText;
            set
            {
                if (_barcodeText != value)
                {
                    _barcodeText = value;
                    OnPropertyChanged();
                }
            }
        }

        public Product? FoundProduct
        {
            get => _foundProduct;
            set
            {
                if (_foundProduct != value)
                {
                    _foundProduct = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsScanning
        {
            get => _isScanning;
            set
            {
                if (_isScanning != value)
                {
                    _isScanning = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool HasScanResult
        {
            get => _hasScanResult;
            set
            {
                if (_hasScanResult != value)
                {
                    _hasScanResult = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool ProductNotFound
        {
            get => _productNotFound;
            set
            {
                if (_productNotFound != value)
                {
                    _productNotFound = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Commands

        public ICommand ScanBarcodeCommand { get; private set; } = null!;
        public ICommand ScanFromFileCommand { get; private set; } = null!;
        public ICommand StartCameraScanCommand { get; private set; } = null!;
        public ICommand AddToSaleCommand { get; private set; } = null!;
        public ICommand ViewProductDetailsCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            ScanBarcodeCommand = new RelayCommand(async () => await ScanBarcodeAsync(), () => !string.IsNullOrWhiteSpace(BarcodeText));
            ScanFromFileCommand = new RelayCommand(async () => await ScanFromFileAsync());
            StartCameraScanCommand = new RelayCommand(() => StartCameraScan());
            AddToSaleCommand = new RelayCommand(() => AddToSale(), () => FoundProduct != null);
            ViewProductDetailsCommand = new RelayCommand(() => ViewProductDetails(), () => FoundProduct != null);
        }

        #endregion

        #region Events

        public event EventHandler<ProductFoundEventArgs>? ProductFound;
        public event EventHandler<ProductSelectedEventArgs>? ProductSelected;
        public event PropertyChangedEventHandler? PropertyChanged;

        #endregion

        #region Methods

        /// <summary>
        /// مسح الباركود والبحث عن المنتج
        /// </summary>
        private async Task ScanBarcodeAsync()
        {
            if (string.IsNullOrWhiteSpace(BarcodeText))
                return;

            try
            {
                IsScanning = true;
                HasScanResult = false;
                ProductNotFound = false;
                FoundProduct = null;

                // البحث عن المنتج بالباركود
                var product = await _productService.GetProductByBarcodeAsync(BarcodeText);

                if (product != null)
                {
                    FoundProduct = product;
                    ProductNotFound = false;
                    
                    // إثارة حدث العثور على المنتج
                    ProductFound?.Invoke(this, new ProductFoundEventArgs(product));
                }
                else
                {
                    FoundProduct = null;
                    ProductNotFound = true;
                }

                HasScanResult = true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في مسح الباركود: {BarcodeText}");
                MessageBox.Show($"خطأ في مسح الباركود: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsScanning = false;
            }
        }

        /// <summary>
        /// مسح الباركود من ملف صورة
        /// </summary>
        private async Task ScanFromFileAsync()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختيار صورة الباركود",
                    Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif|All Files|*.*",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    IsScanning = true;
                    HasScanResult = false;
                    ProductNotFound = false;
                    FoundProduct = null;

                    // قراءة الباركود من الصورة
                    var barcodeText = _barcodeService.ReadBarcodeFromImage(openFileDialog.FileName);

                    if (!string.IsNullOrEmpty(barcodeText))
                    {
                        BarcodeText = barcodeText;
                        await ScanBarcodeAsync();
                    }
                    else
                    {
                        MessageBox.Show("لم يتم العثور على باركود في الصورة المحددة", "تنبيه", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        HasScanResult = true;
                        ProductNotFound = true;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في مسح الباركود من الملف");
                MessageBox.Show($"خطأ في مسح الباركود من الملف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsScanning = false;
            }
        }

        /// <summary>
        /// تشغيل مسح الكاميرا (للتنفيذ المستقبلي)
        /// </summary>
        private void StartCameraScan()
        {
            MessageBox.Show("ميزة مسح الكاميرا ستكون متاحة في التحديث القادم", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// إضافة المنتج للفاتورة
        /// </summary>
        private void AddToSale()
        {
            if (FoundProduct != null)
            {
                ProductSelected?.Invoke(this, new ProductSelectedEventArgs(FoundProduct, ProductSelectionAction.AddToSale));
            }
        }

        /// <summary>
        /// عرض تفاصيل المنتج
        /// </summary>
        private void ViewProductDetails()
        {
            if (FoundProduct != null)
            {
                ProductSelected?.Invoke(this, new ProductSelectedEventArgs(FoundProduct, ProductSelectionAction.ViewDetails));
            }
        }

        /// <summary>
        /// مسح النتائج
        /// </summary>
        public void ClearResults()
        {
            BarcodeText = string.Empty;
            FoundProduct = null;
            HasScanResult = false;
            ProductNotFound = false;
            IsScanning = false;
        }

        #endregion

        #region Event Handlers

        private async void BarcodeTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !string.IsNullOrWhiteSpace(BarcodeText))
            {
                await ScanBarcodeAsync();
            }
        }

        #endregion

        #region INotifyPropertyChanged

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Event Args Classes

    public class ProductFoundEventArgs : EventArgs
    {
        public Product Product { get; }

        public ProductFoundEventArgs(Product product)
        {
            Product = product;
        }
    }

    public class ProductSelectedEventArgs : EventArgs
    {
        public Product Product { get; }
        public ProductSelectionAction Action { get; }

        public ProductSelectedEventArgs(Product product, ProductSelectionAction action)
        {
            Product = product;
            Action = action;
        }
    }

    public enum ProductSelectionAction
    {
        AddToSale,
        ViewDetails
    }

    #endregion
}
