using System;
using System.Globalization;
using System.Threading;
using System.Windows;
using System.Windows.Markup;
using SalesManagementSystem.Services;
using SalesManagementSystem.Views;

namespace SalesManagementSystem
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        public static bool IsArabic { get; private set; } = false;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Set application culture and UI direction
            // استخدام الثقافة الإنجليزية للأرقام مع الاتجاه العربي للواجهة
            SetApplicationCulture("en-US");

            // Initialize services
            InitializeServices();
        }

        public static void SetApplicationCulture(string cultureName)
        {
            // استخدام الثقافة الإنجليزية للأرقام مع الحفاظ على الاتجاه العربي
            IsArabic = true; // الحفاظ على الاتجاه العربي

            // Set the culture for the current thread - استخدام الإنجليزية للأرقام
            Thread.CurrentThread.CurrentCulture = new CultureInfo("en-US");
            Thread.CurrentThread.CurrentUICulture = new CultureInfo("en-US");

            // Set FlowDirection for XAML - الحفاظ على الاتجاه العربي
            FrameworkElement.FlowDirectionProperty.OverrideMetadata(
                typeof(FrameworkElement),
                new FrameworkPropertyMetadata(FlowDirection.RightToLeft));

            // Set language for XAML - استخدام الإنجليزية
            XmlLanguage language = XmlLanguage.GetLanguage("en-US");
            FrameworkElement.LanguageProperty.OverrideMetadata(
                typeof(FrameworkElement),
                new FrameworkPropertyMetadata(language));

            // Reload resources - استخدام الملف العربي للنصوص
            ResourceDictionary resourceDictionary = new ResourceDictionary
            {
                Source = new Uri("/Resources/Localization/ar-SA.xaml", UriKind.Relative)
            };

            // Replace the localization dictionary
            ResourceDictionary? oldDictionary = null;
            foreach (ResourceDictionary dict in Current.Resources.MergedDictionaries)
            {
                if (dict.Source != null && dict.Source.OriginalString.Contains("/Resources/Localization/"))
                {
                    oldDictionary = dict;
                    break;
                }
            }

            if (oldDictionary != null)
            {
                int index = Current.Resources.MergedDictionaries.IndexOf(oldDictionary);
                Current.Resources.MergedDictionaries.Remove(oldDictionary);
                Current.Resources.MergedDictionaries.Insert(index, resourceDictionary);
            }
            else
            {
                Current.Resources.MergedDictionaries.Add(resourceDictionary);
            }
        }

        private async void InitializeServices()
        {
            try
            {
                // تهيئة نظام التسجيل
                LoggingService.LogSystemEvent("بدء التطبيق", "تم بدء تشغيل نظام إدارة المبيعات");

                // إصلاح قاعدة البيانات أولاً (مع إعادة إنشاء للتأكد من التحديث)
                DatabaseFixer.FixDatabase();

                // تهيئة خدمة قاعدة البيانات
                var dbService = new DatabaseService();
                await dbService.InitializeDatabaseAsync();

                // تهيئة الإعدادات الافتراضية
                var settingsService = new SettingsService(dbService);
                await settingsService.InitializeDefaultSettingsAsync();

                // تهيئة الفئات الافتراضية
                var categoryService = new CategoryService(dbService);
                await categoryService.InitializeDefaultCategoriesAsync();

                // تنظيف ملفات السجل القديمة
                LoggingService.CleanOldLogs(30);

                LoggingService.LogSystemEvent("تهيئة الخدمات", "تم تهيئة جميع الخدمات بنجاح");

                // إظهار نافذة تسجيل الدخول
                ShowLoginWindow();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تهيئة الخدمات");

                // محاولة تشغيل النظام في وضع العرض التوضيحي
                try
                {
                    MessageBox.Show("سيتم تشغيل النظام في وضع العرض التوضيحي", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    ShowLoginWindow();
                }
                catch
                {
                    MessageBox.Show($"خطأ في تهيئة الخدمات: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown();
                }
            }
        }

        private void ShowLoginWindow()
        {
            try
            {
                // إظهار نافذة تسجيل الدخول الرئيسية
                var loginWindow = new LoginWindow();
                loginWindow.Show();

                LoggingService.LogSystemEvent("بدء تسجيل الدخول", "تم عرض نافذة تسجيل الدخول");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في عرض نافذة تسجيل الدخول");
                MessageBox.Show($"خطأ في عرض نافذة تسجيل الدخول: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }
    }
}