<Window x:Class="SalesManagementSystem.Views.SalesReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تقرير المبيعات"
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <Border Grid.Row="0" Background="Green">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="📊" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="تقرير المبيعات التفصيلي" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <Border Grid.Row="1" Background="LightGray" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <DatePicker Width="120" Margin="0,0,10,0"/>
                <TextBlock Text="إلى تاريخ:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <DatePicker Width="120" Margin="0,0,20,0"/>
                <ComboBox Width="120" Margin="0,0,10,0">
                    <ComboBoxItem Content="جميع العملاء"/>
                    <ComboBoxItem Content="أحمد محمد"/>
                </ComboBox>
                <Button Content="📊 إنشاء التقرير" Width="120" Background="Green" Foreground="White"/>
            </StackPanel>
        </Border>

        <TabControl Grid.Row="2" Margin="10">
            <TabItem Header="الإحصائيات">
                <Grid Margin="20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Grid.Column="0" Background="LightBlue" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي المبيعات" FontSize="16" FontWeight="Bold"/>
                            <TextBlock Text="125750.50 دج" FontSize="24" Foreground="Green"/>
                        </StackPanel>
                    </Border>

                    <Border Grid.Row="0" Grid.Column="1" Background="LightGreen" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="عدد الفواتير" FontSize="16" FontWeight="Bold"/>
                            <TextBlock Text="342" FontSize="24" Foreground="Blue"/>
                        </StackPanel>
                    </Border>

                    <Border Grid.Row="1" Grid.Column="0" Background="LightYellow" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="متوسط الفاتورة" FontSize="16" FontWeight="Bold"/>
                            <TextBlock Text="367.69 دج" FontSize="24" Foreground="Orange"/>
                        </StackPanel>
                    </Border>

                    <Border Grid.Row="1" Grid.Column="1" Background="LightPink" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي الربح" FontSize="16" FontWeight="Bold"/>
                            <TextBlock Text="45250.25 دج" FontSize="24" Foreground="Purple"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </TabItem>

            <TabItem Header="التفاصيل">
                <DataGrid Margin="10" AutoGenerateColumns="False" CanUserAddRows="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="التاريخ" Width="100"/>
                        <DataGridTextColumn Header="رقم الفاتورة" Width="100"/>
                        <DataGridTextColumn Header="العميل" Width="150"/>
                        <DataGridTextColumn Header="المبلغ" Width="100"/>
                        <DataGridTextColumn Header="الربح" Width="100"/>
                        <DataGridTextColumn Header="طريقة الدفع" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
        </TabControl>

        <Border Grid.Row="3" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="🖨️ طباعة" Width="100" Height="35" Margin="10"
                       Background="Green" Foreground="White" FontWeight="Bold"/>
                <Button Content="📤 تصدير Excel" Width="120" Height="35" Margin="10"
                       Background="Blue" Foreground="White" FontWeight="Bold"/>
                <Button Content="📧 إرسال بالبريد" Width="130" Height="35" Margin="10"
                       Background="Orange" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إغلاق" Width="100" Height="35" Margin="10"
                       Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
