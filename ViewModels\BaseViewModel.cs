using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using Prism.Commands;

namespace SalesManagementSystem.ViewModels
{
    /// <summary>
    /// Base class for all ViewModels providing INotifyPropertyChanged implementation
    /// </summary>
    public abstract class BaseViewModel : INotifyPropertyChanged
    {
        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion

        #region Loading State

        private bool _isLoading;
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        private string _loadingMessage = "جاري التحميل...";
        public string LoadingMessage
        {
            get => _loadingMessage;
            set => SetProperty(ref _loadingMessage, value);
        }

        #endregion

        #region Error Handling

        private string? _errorMessage;
        public string? ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        private bool _hasError;
        public bool HasError
        {
            get => _hasError;
            set => SetProperty(ref _hasError, value);
        }

        protected void SetError(string message)
        {
            ErrorMessage = message;
            HasError = true;
        }

        protected void ClearError()
        {
            ErrorMessage = null;
            HasError = false;
        }

        #endregion

        #region Common Commands

        private ICommand? _refreshCommand;
        public ICommand RefreshCommand => _refreshCommand ??= new RelayCommand(async () => await RefreshAsync());

        protected virtual async Task RefreshAsync()
        {
            // Override in derived classes
            await Task.CompletedTask;
        }

        #endregion

        #region Validation

        private readonly Dictionary<string, List<string>> _validationErrors = new();

        public bool HasValidationErrors => _validationErrors.Any();

        public IEnumerable<string> GetValidationErrors(string propertyName)
        {
            return _validationErrors.ContainsKey(propertyName)
                ? _validationErrors[propertyName]
                : Enumerable.Empty<string>();
        }

        protected void AddValidationError(string propertyName, string error)
        {
            if (!_validationErrors.ContainsKey(propertyName))
                _validationErrors[propertyName] = new List<string>();

            if (!_validationErrors[propertyName].Contains(error))
            {
                _validationErrors[propertyName].Add(error);
                OnPropertyChanged(nameof(HasValidationErrors));
            }
        }

        protected void RemoveValidationError(string propertyName, string error)
        {
            if (_validationErrors.ContainsKey(propertyName))
            {
                _validationErrors[propertyName].Remove(error);
                if (!_validationErrors[propertyName].Any())
                    _validationErrors.Remove(propertyName);

                OnPropertyChanged(nameof(HasValidationErrors));
            }
        }

        protected void ClearValidationErrors(string propertyName)
        {
            if (_validationErrors.ContainsKey(propertyName))
            {
                _validationErrors.Remove(propertyName);
                OnPropertyChanged(nameof(HasValidationErrors));
            }
        }

        protected void ClearAllValidationErrors()
        {
            _validationErrors.Clear();
            OnPropertyChanged(nameof(HasValidationErrors));
        }

        #endregion

        #region Disposal

        private bool _disposed;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                }
                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion
    }
}
