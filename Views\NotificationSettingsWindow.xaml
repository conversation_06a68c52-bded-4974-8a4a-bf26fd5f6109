<Window x:Class="SalesManagementSystem.Views.NotificationSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الإشعارات - نظام إدارة المبيعات"
        Height="750" Width="950"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                               Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Toggle Switch Style -->
        <Style x:Key="ToggleSwitchStyle" TargetType="CheckBox">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="CheckBox">
                        <Grid>
                            <Border x:Name="SwitchTrack" Width="50" Height="25" CornerRadius="12.5"
                                   Background="#FFCCCCCC" BorderThickness="1" BorderBrush="#FFAAAAAA"/>
                            <Border x:Name="SwitchThumb" Width="21" Height="21" CornerRadius="10.5"
                                   Background="White" HorizontalAlignment="Left" Margin="2,2,0,2">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" BlurRadius="3" ShadowDepth="1" Opacity="0.3"/>
                                </Border.Effect>
                            </Border>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="SwitchTrack" Property="Background" Value="#FF2196F3"/>
                                <Setter TargetName="SwitchThumb" Property="HorizontalAlignment" Value="Right"/>
                                <Setter TargetName="SwitchThumb" Property="Margin" Value="0,2,2,2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Category Header Style -->
        <Style x:Key="CategoryHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#FF2196F3"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- Notification Item Style -->
        <Style x:Key="NotificationItemStyle" TargetType="Grid">
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
    </Window.Resources>

    <Grid Background="#FFF5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#FF2196F3">
            <Border.Effect>
                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
            </Border.Effect>
            <Grid Margin="20,0">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🔔" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة الإشعارات والتنبيهات" FontSize="18"
                              FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center">
                    <TextBlock Text="إجمالي الإشعارات النشطة: " FontSize="12" Foreground="LightBlue"/>
                    <TextBlock x:Name="ActiveNotificationsCount" Text="0" FontSize="12" FontWeight="Bold" Foreground="Yellow"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="15"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- العمود الأيسر -->
                <StackPanel Grid.Column="0">

                    <!-- إشعارات المبيعات -->
                    <Border Background="White" CornerRadius="10" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="📊 إشعارات المبيعات" Style="{StaticResource CategoryHeaderStyle}"/>

                            <StackPanel Grid.Row="1">
                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند إتمام عملية بيع جديدة" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="SaleCompletedCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند إلغاء عملية بيع" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="SaleCancelledCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند تحقيق هدف مبيعات يومي" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="DailySalesTargetCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="False"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند المبيعات الكبيرة (أكثر من مبلغ محدد)" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="LargeSalesCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- إشعارات المخزون -->
                    <Border Background="White" CornerRadius="10" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="📦 إشعارات المخزون" Style="{StaticResource CategoryHeaderStyle}"/>

                            <StackPanel Grid.Row="1">
                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند نفاد المخزون" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="StockOutCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند انخفاض المخزون (أقل من الحد الأدنى)" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="LowStockCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند إضافة منتج جديد" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="NewProductCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="False"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند تحديث أسعار المنتجات" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="PriceUpdateCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="False"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>
                </StackPanel>

                <!-- العمود الأيمن -->
                <StackPanel Grid.Column="2">

                    <!-- إشعارات النظام -->
                    <Border Background="White" CornerRadius="10" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="⚙️ إشعارات النظام" Style="{StaticResource CategoryHeaderStyle}"/>

                            <StackPanel Grid.Row="1">
                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند تسجيل دخول مستخدم جديد" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="UserLoginCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند إنشاء نسخة احتياطية" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="BackupCreatedCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند حدوث خطأ في النظام" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="SystemErrorCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- إشعارات الأمان -->
                    <Border Background="White" CornerRadius="10" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="🔐 إشعارات الأمان" Style="{StaticResource CategoryHeaderStyle}"/>

                            <StackPanel Grid.Row="1">
                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند محاولة دخول فاشلة" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="FailedLoginCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند تغيير كلمة المرور" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="PasswordChangeCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="إشعار عند إضافة مستخدم جديد" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="NewUserCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- إعدادات عامة -->
                    <Border Background="White" CornerRadius="10">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="🔧 إعدادات عامة" Style="{StaticResource CategoryHeaderStyle}"/>

                            <StackPanel Grid.Row="1">
                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="تفعيل الأصوات للإشعارات" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="NotificationSoundsCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="حفظ سجل الإشعارات" FontSize="12" VerticalAlignment="Center"/>
                                    <CheckBox x:Name="SaveNotificationLogCheckBox" Grid.Column="1" Style="{StaticResource ToggleSwitchStyle}"
                                             VerticalAlignment="Center" IsChecked="True"/>
                                </Grid>

                                <Grid Style="{StaticResource NotificationItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="مدة عرض الإشعار (بالثواني):" FontSize="12" VerticalAlignment="Center"/>
                                    <TextBox x:Name="NotificationDurationTextBox" Grid.Column="1" Text="5" Width="50" Height="25"
                                            TextAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- شريط الأزرار -->
        <Border Grid.Row="2" Background="White">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button x:Name="SaveButton" Content="💾 حفظ الإعدادات"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF4CAF50" Click="Save_Click" Width="130"/>

                <Button x:Name="SelectAllButton" Content="✅ تحديد الكل"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF2196F3" Click="SelectAll_Click" Width="110"/>

                <Button x:Name="DeselectAllButton" Content="❌ إلغاء تحديد الكل"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FFFF9800" Click="DeselectAll_Click" Width="140"/>

                <Button x:Name="CloseButton" Content="🚪 إغلاق"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF607D8B" Click="Close_Click" Width="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
