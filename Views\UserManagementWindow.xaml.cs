using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// نافذة إدارة المستخدمين
    /// </summary>
    public partial class UserManagementWindow : Window
    {
        private readonly LoginAuthService _authService;
        private readonly EnhancedNotificationService _notificationService;
        private List<UserDisplayModel> _users = new();

        public UserManagementWindow()
        {
            InitializeComponent();
            _authService = LoginAuthService.Instance;
            _notificationService = EnhancedNotificationService.Instance;

            LoadUsers();
        }

        // نموذج عرض المستخدم
        public class UserDisplayModel
        {
            public string Username { get; set; } = string.Empty;
            public string FullName { get; set; } = string.Empty;
            public string Role { get; set; } = string.Empty;
            public bool IsActive { get; set; }
            public DateTime LastLogin { get; set; }
            public List<string> Permissions { get; set; } = new();

            public string StatusText => IsActive ? "نشط" : "غير نشط";
            public string LastLoginText => LastLogin == DateTime.MinValue ? "لم يسجل دخول" : LastLogin.ToString("yyyy/MM/dd HH:mm");
        }

        // تحميل قائمة المستخدمين
        private void LoadUsers()
        {
            try
            {
                var systemUsers = _authService.GetAllUsers();
                _users = systemUsers.Select(u => new UserDisplayModel
                {
                    Username = u.Username,
                    FullName = u.FullName,
                    Role = u.Role,
                    IsActive = u.IsActive,
                    LastLogin = u.LastLogin,
                    Permissions = u.Permissions
                }).ToList();

                UsersDataGrid.ItemsSource = _users;
                UpdateStatistics();
                StatusLabel.Text = $"تم تحميل {_users.Count} مستخدم بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusLabel.Text = "خطأ في تحميل البيانات";
            }
        }

        // تحديث الإحصائيات
        private void UpdateStatistics()
        {
            var adminCount = _users.Count(u => u.Role == "مدير النظام");
            var activeCount = _users.Count(u => u.IsActive);
            var inactiveCount = _users.Count(u => !u.IsActive);

            AdminCountLabel.Text = $"المديرين: {adminCount}";
            ActiveUsersLabel.Text = $"المستخدمين النشطين: {activeCount}";
            InactiveUsersLabel.Text = $"المستخدمين غير النشطين: {inactiveCount}";
            UserCountLabel.Text = $"({_users.Count} مستخدمين)";
        }

        // تحديد مستخدم في الجدول
        private void UsersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var selectedUser = UsersDataGrid.SelectedItem as UserDisplayModel;

            if (selectedUser != null)
            {
                SelectedUserName.Text = selectedUser.FullName;
                SelectedUserRole.Text = $"الدور: {selectedUser.Role} | الحالة: {selectedUser.StatusText}";

                // تفعيل أزرار الإجراءات
                EditUserButton.IsEnabled = true;
                ResetPasswordButton.IsEnabled = true;

                // منع حذف المدير العام
                DeleteUserButton.IsEnabled = selectedUser.Username != "admin";
            }
            else
            {
                SelectedUserName.Text = "لم يتم تحديد مستخدم";
                SelectedUserRole.Text = "";

                // تعطيل أزرار الإجراءات
                EditUserButton.IsEnabled = false;
                DeleteUserButton.IsEnabled = false;
                ResetPasswordButton.IsEnabled = false;
            }
        }

        // إضافة مستخدم جديد
        private void AddUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addUserWindow = new AddUserWindow();
                if (addUserWindow.ShowDialog() == true)
                {
                    LoadUsers(); // إعادة تحميل القائمة

                    _notificationService.AddNotification(
                        "مستخدم جديد",
                        $"تم إضافة المستخدم {addUserWindow.NewUser?.FullName} بنجاح",
                        Models.EnhancedNotificationType.Success,
                        Models.EnhancedNotificationPriority.Normal,
                        "إدارة المستخدمين");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // تعديل مستخدم
        private void EditUser_Click(object sender, RoutedEventArgs e)
        {
            var selectedUser = UsersDataGrid.SelectedItem as UserDisplayModel;
            if (selectedUser == null) return;

            try
            {
                // العثور على المستخدم الأصلي
                var systemUsers = _authService.GetAllUsers();
                var originalUser = systemUsers.FirstOrDefault(u => u.Username == selectedUser.Username);

                if (originalUser == null)
                {
                    MessageBox.Show("لم يتم العثور على المستخدم", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var editWindow = new EditUserWindow(originalUser);
                if (editWindow.ShowDialog() == true && editWindow.IsModified)
                {
                    LoadUsers(); // إعادة تحميل القائمة
                    StatusLabel.Text = $"تم تعديل المستخدم {selectedUser.FullName} بنجاح";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // حذف مستخدم
        private void DeleteUser_Click(object sender, RoutedEventArgs e)
        {
            var selectedUser = UsersDataGrid.SelectedItem as UserDisplayModel;
            if (selectedUser == null) return;

            if (selectedUser.Username == "admin")
            {
                MessageBox.Show("لا يمكن حذف المدير العام", "تحذير",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المستخدم '{selectedUser.FullName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    if (_authService.DeleteUser(selectedUser.Username))
                    {
                        LoadUsers(); // إعادة تحميل القائمة

                        _notificationService.AddNotification(
                            "حذف مستخدم",
                            $"تم حذف المستخدم {selectedUser.FullName} بنجاح",
                            Models.EnhancedNotificationType.Warning,
                            Models.EnhancedNotificationPriority.Normal,
                            "إدارة المستخدمين");

                        StatusLabel.Text = $"تم حذف المستخدم {selectedUser.FullName}";
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المستخدم", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // إعادة تعيين كلمة المرور
        private void ResetPassword_Click(object sender, RoutedEventArgs e)
        {
            var selectedUser = UsersDataGrid.SelectedItem as UserDisplayModel;
            if (selectedUser == null) return;

            try
            {
                // العثور على المستخدم الأصلي
                var systemUsers = _authService.GetAllUsers();
                var originalUser = systemUsers.FirstOrDefault(u => u.Username == selectedUser.Username);

                if (originalUser == null)
                {
                    MessageBox.Show("لم يتم العثور على المستخدم", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var resetPasswordWindow = new ResetPasswordWindow(originalUser);
                if (resetPasswordWindow.ShowDialog() == true)
                {
                    MessageBox.Show($"تم تعيين كلمة المرور الجديدة للمستخدم '{selectedUser.FullName}' بنجاح",
                        "تم بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                    _notificationService.AddNotification(
                        "إعادة تعيين كلمة المرور",
                        $"تم تعيين كلمة مرور جديدة للمستخدم {selectedUser.FullName}",
                        Models.EnhancedNotificationType.Info,
                        Models.EnhancedNotificationPriority.Normal,
                        "إدارة المستخدمين");

                    StatusLabel.Text = $"تم تعيين كلمة مرور جديدة للمستخدم {selectedUser.FullName}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // إغلاق النافذة
        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
