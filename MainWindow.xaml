<Window x:Class="SalesManagementSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام إدارة المبيعات"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                               Background="{TemplateBinding Background}"
                               CornerRadius="8"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" BlurRadius="8" ShadowDepth="3" Opacity="0.3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                                <Setter TargetName="border" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" BlurRadius="12" ShadowDepth="5" Opacity="0.4"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                                <Setter TargetName="border" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Sales Button Style -->
        <Style x:Key="SalesButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF2196F3"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF42A5F5"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#FF1976D2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Products Button Style -->
        <Style x:Key="ProductsButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FFFF9800"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FFFFA726"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#FFF57C00"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Customers Button Style -->
        <Style x:Key="CustomersButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF9C27B0"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FFBA68C8"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#FF7B1FA2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Reports Button Style -->
        <Style x:Key="ReportsButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FFF44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FFEF5350"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#FFD32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <!-- شريط القوائم -->
    <DockPanel>
        <Grid DockPanel.Dock="Top" Height="30" Background="LightBlue">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- القوائم الرئيسية -->
            <Menu Grid.Column="0" Background="Transparent" Height="30">
                <MenuItem x:Name="FileMenuItem" Header="ملف">
                    <MenuItem x:Name="NewMenuItem" Header="جديد" Click="NewFile_Click"/>
                    <MenuItem x:Name="OpenMenuItem" Header="فتح" Click="OpenFile_Click"/>
                    <MenuItem x:Name="SaveMenuItem" Header="حفظ" Click="SaveFile_Click"/>
                    <Separator/>
                    <MenuItem x:Name="ExitMenuItem" Header="خروج" Click="Exit_Click"/>
                </MenuItem>
                <MenuItem x:Name="SalesMenuItem" Header="المبيعات">
                    <MenuItem x:Name="NewSaleMenuItem" Header="فاتورة جديدة" Click="NewSale_Click"/>
                    <MenuItem x:Name="QuickSaleMenuItem" Header="⚡ بيع سريع - مسح الكودبار" Click="QuickSale_Click"/>
                    <MenuItem x:Name="ViewSalesMenuItem" Header="عرض المبيعات" Click="ViewSales_Click"/>
                </MenuItem>
                <MenuItem x:Name="PurchasesMenuItem" Header="المشتريات">
                    <MenuItem x:Name="NewPurchaseMenuItem" Header="فاتورة شراء جديدة" Click="NewPurchase_Click"/>
                    <MenuItem x:Name="ViewPurchasesMenuItem" Header="عرض المشتريات" Click="ViewPurchases_Click"/>
                    <MenuItem x:Name="SuppliersMenuItem" Header="إدارة الموردين" Click="ManageSuppliers_Click"/>
                </MenuItem>
                <MenuItem x:Name="ProductsMenuItem" Header="المنتجات">
                    <MenuItem x:Name="AddProductMenuItem" Header="إضافة منتج" Click="AddProduct_Click"/>
                    <MenuItem x:Name="ViewProductsMenuItem" Header="عرض المنتجات" Click="ViewProducts_Click"/>
                    <MenuItem x:Name="InventoryMenuItem" Header="إدارة المخزون" Click="ManageInventory_Click"/>
                </MenuItem>
                <MenuItem x:Name="CustomersMenuItem" Header="العملاء">
                    <MenuItem x:Name="AddCustomerMenuItem" Header="إضافة عميل" Click="AddCustomer_Click"/>
                    <MenuItem x:Name="ViewCustomersMenuItem" Header="عرض العملاء" Click="ViewCustomers_Click"/>
                </MenuItem>
                <MenuItem x:Name="ReportsMenuItem" Header="التقارير">
                    <MenuItem x:Name="SalesReportMenuItem" Header="تقرير المبيعات" Click="SalesReport_Click"/>
                    <MenuItem x:Name="InventoryReportMenuItem" Header="تقرير المخزون" Click="InventoryReport_Click"/>
                </MenuItem>
                <MenuItem x:Name="SettingsMenuItem" Header="الإعدادات">
                    <MenuItem x:Name="SystemSettingsMenuItem" Header="إعدادات النظام" Click="Settings_Click"/>
                    <MenuItem x:Name="BackupMenuItem" Header="النسخ الاحتياطي" Click="Backup_Click"/>
                    <Separator/>
                    <MenuItem x:Name="UserManagementMenuItem" Header="👥 إدارة المستخدمين" Click="UserManagement_Click"/>
                    <MenuItem x:Name="SecuritySettingsMenuItem" Header="🔐 إعدادات الأمان" Click="SecuritySettings_Click"/>
                    <Separator/>
                    <MenuItem x:Name="CurrencyMenuItem" Header="🇩🇿 عرض الدينار الجزائري" Click="ShowArabicNumbers_Click"/>
                </MenuItem>
            </Menu>

            <!-- أزرار الثيم واللغة في أقصى اليمين -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                <!-- زر اللغة -->
                <Button x:Name="LanguageToggleButton" Click="LanguageToggle_Click"
                       ToolTip="التبديل بين العربية والإنجليزية"
                       Background="Transparent" BorderThickness="0"
                       Width="35" Height="25" Margin="2,0"
                       Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#33FFFFFF"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#66FFFFFF"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <TextBlock x:Name="LanguageIcon" Text="🇬🇧" FontSize="16"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Button>

                <!-- زر الوضع الليلي -->
                <Button x:Name="ThemeToggleButton" Click="ThemeToggle_Click"
                       ToolTip="التبديل بين الوضع العادي والوضع الليلي"
                       Background="Transparent" BorderThickness="0"
                       Width="35" Height="25" Margin="2,0"
                       Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#33FFFFFF"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#66FFFFFF"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <TextBlock x:Name="ThemeIcon" Text="🌙" FontSize="16"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Button>
            </StackPanel>
        </Grid>

        <!-- المحتوى الرئيسي -->
        <Grid Background="White">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="30"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Border Grid.Row="0" Background="DarkBlue">
                <Border.Effect>
                    <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                </Border.Effect>
                <Grid Margin="20,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="🏪" FontSize="24" Foreground="White" Margin="0,0,10,0">
                            <TextBlock.Effect>
                                <DropShadowEffect Color="Yellow" BlurRadius="5" ShadowDepth="0" Opacity="0.7"/>
                            </TextBlock.Effect>
                        </TextBlock>
                        <TextBlock Text="نظام إدارة المبيعات المتكامل" FontSize="18"
                                  FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0,0,0">
                            <TextBlock x:Name="WelcomeMessage" Text="مرحباً بك في النظام" FontSize="12"
                                      Foreground="LightBlue" VerticalAlignment="Center"/>
                            <TextBlock Text=" | المستخدم الحالي: " FontSize="12"
                                      Foreground="#FFE3F2FD" VerticalAlignment="Center" Margin="15,0,5,0"/>
                            <TextBlock x:Name="CurrentUserDisplay" Text="المدير العام" FontSize="12"
                                      Foreground="LightGreen" FontWeight="SemiBold" VerticalAlignment="Center"/>
                            <TextBlock Text=" (" FontSize="12"
                                      Foreground="#FFE3F2FD" VerticalAlignment="Center" Margin="3,0,0,0"/>
                            <TextBlock x:Name="CurrentUserRole" Text="مدير النظام" FontSize="12"
                                      Foreground="Orange" FontWeight="Medium" VerticalAlignment="Center"/>
                            <TextBlock Text=")" FontSize="12"
                                      Foreground="#FFE3F2FD" VerticalAlignment="Center"/>
                        </StackPanel>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,10,0">
                        <!-- أيقونة الإشعارات المحسنة -->
                        <Border x:Name="NotificationIcon" Background="Transparent"
                               CornerRadius="25" Width="50" Height="50"
                               Margin="0,0,20,0" Cursor="Hand"
                               MouseEnter="NotificationIcon_MouseEnter"
                               MouseLeave="NotificationIcon_MouseLeave"
                               MouseLeftButtonUp="NotificationIcon_Click">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#33FFFFFF"/>
                                            <Setter Property="RenderTransform">
                                                <Setter.Value>
                                                    <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                                </Setter.Value>
                                            </Setter>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>

                            <Grid>
                                <!-- أيقونة الجرس -->
                                <TextBlock Text="🔔" FontSize="22"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"
                                          Foreground="White">
                                    <TextBlock.RenderTransform>
                                        <RotateTransform x:Name="BellRotateTransform" Angle="0"
                                                        CenterX="11" CenterY="11"/>
                                    </TextBlock.RenderTransform>
                                </TextBlock>

                                <!-- شارة العدد المحسنة -->
                                <Border x:Name="NotificationBadge" Background="#FFE53E3E" CornerRadius="10"
                                       Width="20" Height="20"
                                       HorizontalAlignment="Right" VerticalAlignment="Top"
                                       Margin="0,-5,-5,0" Visibility="Collapsed">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Black" BlurRadius="3" ShadowDepth="1" Opacity="0.5"/>
                                    </Border.Effect>
                                    <TextBlock x:Name="NotificationCountText" Text="0"
                                              FontSize="10" FontWeight="Bold" Foreground="White"
                                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </Grid>
                        </Border>

                        <!-- فاصل بصري -->
                        <Rectangle Width="2" Height="35" Fill="#66FFFFFF" Margin="0,0,20,0"/>

                        <!-- الوقت والتاريخ المحسن -->
                        <StackPanel Orientation="Vertical" HorizontalAlignment="Right">
                            <TextBlock x:Name="CurrentTimeLabel" Text="00:00:00" FontSize="16"
                                      Foreground="LightGreen" FontWeight="Bold" FontFamily="Consolas"
                                      HorizontalAlignment="Right"/>
                            <TextBlock x:Name="CurrentDateLabel" Text="2024/01/01" FontSize="13"
                                      Foreground="LightBlue" FontWeight="Medium"
                                      HorizontalAlignment="Right" Margin="0,2,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- المحتوى الرئيسي -->
            <TabControl Grid.Row="1" Margin="10">
                <TabItem Header="لوحة التحكم">
                    <Grid Background="LightGray">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="🎉 مرحباً بك في نظام إدارة المبيعات!"
                                      FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,20"/>

                            <TextBlock Text="النظام جاهز للاستخدام بجميع الميزات التالية:"
                                      FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,20"/>

                            <!-- الصف الأول من الأزرار -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                                <Button Content="📊 المبيعات" Width="140" Height="50"
                                       Style="{StaticResource SalesButtonStyle}"
                                       Click="Sales_Click" ToolTip="إدارة المبيعات والفواتير"/>
                                <Button Content="📦 المنتجات" Width="140" Height="50"
                                       Style="{StaticResource ProductsButtonStyle}"
                                       Click="Products_Click" ToolTip="إدارة المنتجات والمخزون"/>
                                <Button Content="👥 العملاء" Width="140" Height="50"
                                       Style="{StaticResource CustomersButtonStyle}"
                                       Click="Customers_Click" ToolTip="إدارة العملاء والموردين"/>
                                <Button Content="📈 التقارير" Width="140" Height="50"
                                       Style="{StaticResource ReportsButtonStyle}"
                                       Click="Reports_Click" ToolTip="التقارير والإحصائيات"/>
                            </StackPanel>

                            <!-- الصف الثاني من الأزرار -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                                <Button Content="⚡ بيع سريع" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF4CAF50"
                                       Click="QuickSale_Click" ToolTip="بيع سريع بمسح الكودبار"/>
                                <Button Content="🛒 مشتريات" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF607D8B"
                                       Click="Purchases_Click" ToolTip="إدارة المشتريات"/>
                                <Button Content="💳 الديون والمستحقات" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FFE91E63"
                                       Click="DebtsPayments_Click" ToolTip="إدارة ديون العملاء ومستحقات الموردين"/>
                                <Button Content="💰 المصاريف" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FFFF5722"
                                       Click="Expenses_Click" ToolTip="إدارة المصاريف"/>
                            </StackPanel>

                            <!-- الصف الثاني المكمل -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                                <Button Content="🏪 المستودعات" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF795548"
                                       Click="WarehouseManagement_Click" ToolTip="إدارة المستودعات المتعددة"/>
                                <Button Content="⚙️ الإعدادات" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF607D8B"
                                       Click="Settings_Click" ToolTip="إعدادات النظام"/>
                            </StackPanel>

                            <!-- الصف الثالث من الأزرار -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="💾 نسخ احتياطي" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF009688"
                                       Click="Backup_Click" ToolTip="النسخ الاحتياطي"/>
                                <Button Content="🔢 الأرقام العربية" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF3F51B5"
                                       Click="ShowArabicNumbers_Click" ToolTip="عرض الأرقام العربية"/>
                                <Button Content="💱 إعدادات العملة" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FFFF9800"
                                       Click="ShowCurrencySettings_Click" ToolTip="إعدادات العملة"/>
                                <Button Content="📈 الأرباح" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF4CAF50"
                                       Click="Profits_Click" ToolTip="إدارة الأرباح والتحليل المالي"/>
                            </StackPanel>

                            <!-- الصف الرابع من الأزرار - خاص بالمدير فقط -->
                            <StackPanel x:Name="AdminOnlyPanel" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                                <Button x:Name="UserManagementButton" Content="👥 إدارة المستخدمين" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF9C27B0"
                                       Click="UserManagement_Click" ToolTip="إدارة المستخدمين والصلاحيات"/>
                                <Button Content="🔐 إعدادات الأمان" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF795548"
                                       Click="SecuritySettings_Click" ToolTip="إعدادات الأمان والحماية"/>
                                <Button Content="🔔 إدارة الإشعارات" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF2196F3"
                                       Click="NotificationSettings_Click" ToolTip="إعدادات وإدارة الإشعارات"/>
                                <Button Content="📊 سجل النظام" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF607D8B"
                                       Click="SystemLog_Click" ToolTip="سجل أحداث النظام"/>
                            </StackPanel>

                            <!-- الصف الخامس من الأزرار - إعدادات متقدمة -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                                <Button Content="🔧 إعدادات متقدمة" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF455A64"
                                       Click="AdvancedSettings_Click" ToolTip="الإعدادات المتقدمة"/>
                                <Button Content="🎲 بيانات تجريبية" Width="140" Height="50"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF8BC34A"
                                       Click="SampleData_Click" ToolTip="إنشاء أو حذف البيانات التجريبية المترابطة"/>
                            </StackPanel>

                            <TextBlock Text="جميع الميزات متاحة من خلال شريط القوائم أعلاه"
                                      FontSize="14" HorizontalAlignment="Center" Margin="0,30,0,0"
                                      Foreground="Gray"/>
                        </StackPanel>
                    </Grid>
                </TabItem>

                <TabItem Header="الميزات المتقدمة">
                    <Grid Background="LightYellow">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="🚀 الميزات المتقدمة المتاحة:"
                                      FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,20"/>

                            <StackPanel>
                                <TextBlock Text="🤖 نظام الذكاء الاصطناعي والتحليلات المتقدمة" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="🏪 إدارة المستودعات المتعددة" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="👥 نظام CRM المتكامل" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="🛒 نظام التجارة الإلكترونية" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="💾 النسخ الاحتياطي المتقدم" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="🔐 نظام الأمان والمصادقة المحسن" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="📊 التقارير والتحليلات التفاعلية" FontSize="14" Margin="0,5"/>
                                <TextBlock Text="🔔 نظام الإشعارات المحسن" FontSize="14" Margin="0,5"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </TabItem>
            </TabControl>

            <!-- شريط الحالة -->
            <Border Grid.Row="2" Background="Gray">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <TextBlock Text="الحالة: متصل" Foreground="White" FontSize="12"/>
                    <TextBlock Text=" | " Foreground="White" FontSize="12"/>
                    <TextBlock Text="المستخدم: مدير النظام" Foreground="White" FontSize="12"/>
                    <TextBlock Text=" | " Foreground="White" FontSize="12"/>
                    <TextBlock Text="الإصدار: 1.0.0" Foreground="White" FontSize="12"/>
                </StackPanel>
            </Border>
        </Grid>
    </DockPanel>
</Window>