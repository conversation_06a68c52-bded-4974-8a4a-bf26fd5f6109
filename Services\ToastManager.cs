using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using SalesManagementSystem.Controls;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// مدير إشعارات Toast - إدارة عرض وإخفاء إشعارات Toast
    /// </summary>
    public class ToastManager
    {
        private readonly List<ToastNotification> _activeToasts = new();
        private readonly Panel _toastContainer;
        private readonly int _maxToasts = 5;
        private readonly int _defaultDuration = 5;

        public ToastManager(Panel toastContainer)
        {
            _toastContainer = toastContainer ?? throw new ArgumentNullException(nameof(toastContainer));
        }

        /// <summary>
        /// عرض إشعار Toast
        /// </summary>
        /// <param name="notification">الإشعار</param>
        /// <param name="duration">مدة العرض بالثواني (0 = لا يختفي تلقائياً)</param>
        public void ShowToast(Notification notification, int duration = 0)
        {
            try
            {
                if (notification == null) return;

                Application.Current.Dispatcher.Invoke(() =>
                {
                    // التحقق من الحد الأقصى للإشعارات
                    if (_activeToasts.Count >= _maxToasts)
                    {
                        // إزالة أقدم إشعار
                        var oldestToast = _activeToasts.FirstOrDefault();
                        if (oldestToast != null)
                        {
                            RemoveToast(oldestToast);
                        }
                    }

                    // إنشاء Toast جديد
                    var toastDuration = duration > 0 ? duration : GetDefaultDuration(notification);
                    var toast = new ToastNotification(notification, toastDuration);
                    
                    // ربط الأحداث
                    toast.Dismissed += Toast_Dismissed;
                    toast.ActionRequested += Toast_ActionRequested;
                    toast.SnoozeRequested += Toast_SnoozeRequested;

                    // إضافة للحاوية
                    _toastContainer.Children.Add(toast);
                    _activeToasts.Add(toast);

                    // تحديد الموضع
                    UpdateToastPositions();

                    // عرض Toast
                    toast.Show();

                    LoggingService.LogInfo($"تم عرض Toast: {notification.Title}");
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في عرض Toast: {notification?.Title}");
            }
        }

        /// <summary>
        /// عرض إشعار Toast سريع
        /// </summary>
        public void ShowQuickToast(string title, string message, NotificationType type = NotificationType.Info, int duration = 3)
        {
            var notification = new Notification
            {
                Title = title,
                Message = message,
                Type = type.ToString(),
                CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                IsRead = false
            };

            ShowToast(notification, duration);
        }

        /// <summary>
        /// عرض إشعار نجاح
        /// </summary>
        public void ShowSuccess(string title, string message, int duration = 3)
        {
            ShowQuickToast(title, message, NotificationType.Success, duration);
        }

        /// <summary>
        /// عرض إشعار خطأ
        /// </summary>
        public void ShowError(string title, string message, int duration = 0)
        {
            ShowQuickToast(title, message, NotificationType.Error, duration);
        }

        /// <summary>
        /// عرض إشعار تحذير
        /// </summary>
        public void ShowWarning(string title, string message, int duration = 5)
        {
            ShowQuickToast(title, message, NotificationType.Warning, duration);
        }

        /// <summary>
        /// عرض إشعار معلومات
        /// </summary>
        public void ShowInfo(string title, string message, int duration = 4)
        {
            ShowQuickToast(title, message, NotificationType.Info, duration);
        }

        /// <summary>
        /// إزالة جميع إشعارات Toast
        /// </summary>
        public void ClearAllToasts()
        {
            try
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var toastsToRemove = _activeToasts.ToList();
                    foreach (var toast in toastsToRemove)
                    {
                        RemoveToast(toast);
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في مسح جميع Toast notifications");
            }
        }

        /// <summary>
        /// إزالة Toast محدد
        /// </summary>
        private void RemoveToast(ToastNotification toast)
        {
            try
            {
                if (toast == null) return;

                // إلغاء ربط الأحداث
                toast.Dismissed -= Toast_Dismissed;
                toast.ActionRequested -= Toast_ActionRequested;
                toast.SnoozeRequested -= Toast_SnoozeRequested;

                // إزالة من الحاوية والقائمة
                _toastContainer.Children.Remove(toast);
                _activeToasts.Remove(toast);

                // تنظيف الموارد
                toast.Dispose();

                // تحديث المواضع
                UpdateToastPositions();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إزالة Toast notification");
            }
        }

        /// <summary>
        /// تحديث مواضع إشعارات Toast
        /// </summary>
        private void UpdateToastPositions()
        {
            try
            {
                for (int i = 0; i < _activeToasts.Count; i++)
                {
                    var toast = _activeToasts[i];
                    
                    // تحديد الموضع من الأعلى
                    Canvas.SetTop(toast, i * 120 + 20);
                    Canvas.SetRight(toast, 20);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحديث مواضع Toast notifications");
            }
        }

        /// <summary>
        /// الحصول على المدة الافتراضية حسب نوع الإشعار
        /// </summary>
        private int GetDefaultDuration(Notification notification)
        {
            if (notification.Type == null) return _defaultDuration;

            return notification.Type.ToLower() switch
            {
                "error" => 0, // لا يختفي تلقائياً
                "warning" => 7,
                "success" => 3,
                "info" => 5,
                "lowstock" => 8,
                "outofstock" => 0, // لا يختفي تلقائياً
                "paymentdue" => 10,
                "system" => 6,
                _ => _defaultDuration
            };
        }

        /// <summary>
        /// التحقق من وجود Toast للإشعار
        /// </summary>
        public bool HasToastForNotification(int notificationId)
        {
            return _activeToasts.Any(t => t.Notification?.Id == notificationId);
        }

        /// <summary>
        /// إزالة Toast للإشعار المحدد
        /// </summary>
        public void RemoveToastForNotification(int notificationId)
        {
            var toast = _activeToasts.FirstOrDefault(t => t.Notification?.Id == notificationId);
            if (toast != null)
            {
                RemoveToast(toast);
            }
        }

        #region Event Handlers

        private void Toast_Dismissed(object? sender, EventArgs e)
        {
            if (sender is ToastNotification toast)
            {
                RemoveToast(toast);
            }
        }

        private void Toast_ActionRequested(object? sender, EventArgs e)
        {
            if (sender is ToastNotification toast && toast.Notification != null)
            {
                try
                {
                    // تنفيذ الإجراء المطلوب
                    if (!string.IsNullOrEmpty(toast.Notification.ActionUrl))
                    {
                        // فتح الرابط أو تنفيذ الإجراء
                        ActionRequested?.Invoke(this, new ToastActionEventArgs(toast.Notification, ToastAction.View));
                    }
                }
                catch (Exception ex)
                {
                    LoggingService.LogError(ex, $"خطأ في تنفيذ إجراء Toast: {toast.Notification.Id}");
                }
            }
        }

        private void Toast_SnoozeRequested(object? sender, EventArgs e)
        {
            if (sender is ToastNotification toast && toast.Notification != null)
            {
                try
                {
                    // إثارة حدث التأجيل
                    ActionRequested?.Invoke(this, new ToastActionEventArgs(toast.Notification, ToastAction.Snooze));
                }
                catch (Exception ex)
                {
                    LoggingService.LogError(ex, $"خطأ في تأجيل Toast: {toast.Notification.Id}");
                }
            }
        }

        #endregion

        #region Events

        public event EventHandler<ToastActionEventArgs>? ActionRequested;

        #endregion

        #region Cleanup

        public void Dispose()
        {
            ClearAllToasts();
        }

        #endregion
    }

    /// <summary>
    /// أحداث إجراءات Toast
    /// </summary>
    public class ToastActionEventArgs : EventArgs
    {
        public Notification Notification { get; }
        public ToastAction Action { get; }

        public ToastActionEventArgs(Notification notification, ToastAction action)
        {
            Notification = notification;
            Action = action;
        }
    }

    /// <summary>
    /// أنواع إجراءات Toast
    /// </summary>
    public enum ToastAction
    {
        View,
        Snooze,
        Dismiss
    }

    /// <summary>
    /// أنواع الإشعارات
    /// </summary>
    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error,
        LowStock,
        OutOfStock,
        PaymentDue,
        System
    }
}
