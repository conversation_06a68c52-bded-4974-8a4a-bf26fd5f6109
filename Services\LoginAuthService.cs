using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة المصادقة وإدارة المستخدمين
    /// </summary>
    public class LoginAuthService
    {
        private static LoginAuthService? _instance;
        private readonly List<SystemUser> _users;

        public static LoginAuthService Instance => _instance ??= new LoginAuthService();

        private LoginAuthService()
        {
            _users = InitializeDefaultUsers();
        }

        // نموذج المستخدم
        public class SystemUser
        {
            public string Username { get; set; } = string.Empty;
            public string PasswordHash { get; set; } = string.Empty;
            public string FullName { get; set; } = string.Empty;
            public string Role { get; set; } = string.Empty;
            public bool IsActive { get; set; } = true;
            public DateTime CreatedAt { get; set; } = DateTime.Now;
            public DateTime LastLogin { get; set; }
            public List<string> Permissions { get; set; } = new();
        }

        // نتيجة تسجيل الدخول
        public class LoginResult
        {
            public bool IsSuccess { get; set; }
            public SystemUser? User { get; set; }
            public string ErrorMessage { get; set; } = string.Empty;
        }

        // تهيئة المستخدمين الافتراضيين
        private List<SystemUser> InitializeDefaultUsers()
        {
            return new List<SystemUser>
            {
                // المدير العام (admin)
                new SystemUser
                {
                    Username = "admin",
                    PasswordHash = HashPassword("0000"),
                    FullName = "المدير العام",
                    Role = "مدير النظام",
                    Permissions = GetAllPermissions()
                },

                // مدير النظام
                new SystemUser
                {
                    Username = "ahmed",
                    PasswordHash = HashPassword("1234"),
                    FullName = "أحمد محمد",
                    Role = "مدير النظام",
                    Permissions = GetAllPermissions()
                },

                // محاسبة
                new SystemUser
                {
                    Username = "fatima",
                    PasswordHash = HashPassword("1234"),
                    FullName = "فاطمة علي",
                    Role = "محاسبة",
                    Permissions = new List<string> { "sales", "reports", "customers", "view_products" }
                },

                // موظف مبيعات
                new SystemUser
                {
                    Username = "mohamed",
                    PasswordHash = HashPassword("1234"),
                    FullName = "محمد حسن",
                    Role = "موظف مبيعات",
                    Permissions = new List<string> { "sales", "customers", "products", "quick_sale" }
                },

                // مدير المخزون
                new SystemUser
                {
                    Username = "sara",
                    PasswordHash = HashPassword("1234"),
                    FullName = "سارة أحمد",
                    Role = "مدير المخزون",
                    Permissions = new List<string> { "products", "inventory", "purchases", "suppliers" }
                },

                // موظف استقبال
                new SystemUser
                {
                    Username = "omar",
                    PasswordHash = HashPassword("1234"),
                    FullName = "عمر خالد",
                    Role = "موظف استقبال",
                    Permissions = new List<string> { "customers", "sales", "quick_sale" }
                },

                // مدير فرع
                new SystemUser
                {
                    Username = "nour",
                    PasswordHash = HashPassword("1234"),
                    FullName = "نور الدين",
                    Role = "مدير فرع",
                    Permissions = new List<string> { "sales", "customers", "products", "reports", "expenses" }
                },

                // محاسبة مساعدة
                new SystemUser
                {
                    Username = "layla",
                    PasswordHash = HashPassword("1234"),
                    FullName = "ليلى محمود",
                    Role = "محاسبة مساعدة",
                    Permissions = new List<string> { "sales", "customers", "view_reports" }
                },

                // موظف مبيعات 2
                new SystemUser
                {
                    Username = "youssef",
                    PasswordHash = HashPassword("1234"),
                    FullName = "يوسف إبراهيم",
                    Role = "موظف مبيعات",
                    Permissions = new List<string> { "sales", "customers", "products", "quick_sale" }
                },

                // مديرة العملاء
                new SystemUser
                {
                    Username = "zeinab",
                    PasswordHash = HashPassword("1234"),
                    FullName = "زينب حسام",
                    Role = "مديرة العملاء",
                    Permissions = new List<string> { "customers", "sales", "reports", "crm" }
                }
            };
        }

        // جميع الصلاحيات المتاحة
        private List<string> GetAllPermissions()
        {
            return new List<string>
            {
                "sales", "purchases", "products", "customers", "reports", "settings",
                "inventory", "suppliers", "expenses", "backup", "user_management",
                "system_settings", "quick_sale", "crm", "analytics", "view_products",
                "view_reports", "manage_users", "delete_users", "create_users"
            };
        }

        // تسجيل الدخول
        public LoginResult Login(string username, string password)
        {
            try
            {
                var user = _users.FirstOrDefault(u =>
                    u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) && u.IsActive);

                if (user == null)
                {
                    return new LoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "اسم المستخدم غير موجود"
                    };
                }

                if (!VerifyPassword(password, user.PasswordHash))
                {
                    return new LoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "كلمة المرور غير صحيحة"
                    };
                }

                // تحديث وقت آخر تسجيل دخول
                user.LastLogin = DateTime.Now;

                return new LoginResult
                {
                    IsSuccess = true,
                    User = user
                };
            }
            catch (Exception ex)
            {
                return new LoginResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"خطأ في النظام: {ex.Message}"
                };
            }
        }

        // التحقق من الصلاحيات
        public bool HasPermission(string username, string permission)
        {
            var user = _users.FirstOrDefault(u =>
                u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) && u.IsActive);

            return user?.Permissions.Contains(permission) ?? false;
        }

        // الحصول على جميع المستخدمين (للمدير فقط)
        public List<SystemUser> GetAllUsers()
        {
            return _users.ToList();
        }

        // إضافة مستخدم جديد (للمدير فقط)
        public bool AddUser(SystemUser newUser)
        {
            if (_users.Any(u => u.Username.Equals(newUser.Username, StringComparison.OrdinalIgnoreCase)))
            {
                return false; // اسم المستخدم موجود بالفعل
            }

            newUser.PasswordHash = HashPassword(newUser.PasswordHash); // تشفير كلمة المرور
            _users.Add(newUser);
            return true;
        }

        // تحديث مستخدم (للمدير فقط)
        public bool UpdateUser(string username, string fullName, string role, bool isActive)
        {
            var user = _users.FirstOrDefault(u =>
                u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));

            if (user != null)
            {
                user.FullName = fullName;
                user.Role = role;
                user.IsActive = isActive;
                user.Permissions = GetPermissionsForRole(role);
                return true;
            }
            return false;
        }

        // تحديث كلمة المرور (للمدير فقط)
        public bool UpdatePassword(string username, string newPassword)
        {
            // التحقق من قوة كلمة المرور حسب إعدادات الأمان
            var securityService = SecuritySettingsService.Instance;
            var validation = securityService.ValidatePassword(newPassword);

            if (!validation.IsValid)
            {
                throw new ArgumentException(validation.ErrorMessage);
            }

            var user = _users.FirstOrDefault(u =>
                u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));

            if (user != null)
            {
                user.PasswordHash = HashPassword(newPassword);

                // تسجيل حدث تغيير كلمة المرور
                securityService.LogSecurityEvent("password_change",
                    $"تم تغيير كلمة المرور للمستخدم {username}", username);

                return true;
            }
            return false;
        }

        // الحصول على مستخدم بالاسم
        public SystemUser? GetUserByUsername(string username)
        {
            return _users.FirstOrDefault(u =>
                u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
        }

        // حذف مستخدم (للمدير فقط)
        public bool DeleteUser(string username)
        {
            var user = _users.FirstOrDefault(u =>
                u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));

            if (user != null && user.Username != "admin") // لا يمكن حذف المدير العام
            {
                _users.Remove(user);
                return true;
            }
            return false;
        }

        // تحديد الصلاحيات حسب الدور (دالة مساعدة)
        private List<string> GetPermissionsForRole(string role)
        {
            return role switch
            {
                "مدير النظام" => GetAllPermissions(),
                "محاسبة" => new List<string> { "sales", "reports", "customers", "view_products", "expenses" },
                "موظف مبيعات" => new List<string> { "sales", "customers", "products", "quick_sale" },
                "مدير المخزون" => new List<string> { "products", "inventory", "purchases", "suppliers" },
                "موظف استقبال" => new List<string> { "customers", "sales", "quick_sale" },
                "مدير فرع" => new List<string> { "sales", "customers", "products", "reports", "expenses" },
                "محاسبة مساعدة" => new List<string> { "sales", "customers", "view_reports" },
                "مديرة العملاء" => new List<string> { "customers", "sales", "reports", "crm" },
                _ => new List<string> { "sales", "customers" }
            };
        }

        // تشفير كلمة المرور
        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "SalesSystem2024"));
            return Convert.ToBase64String(hashedBytes);
        }

        // التحقق من كلمة المرور
        private bool VerifyPassword(string password, string hash)
        {
            return HashPassword(password) == hash;
        }
    }
}
