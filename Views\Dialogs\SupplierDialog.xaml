<Window x:Class="SalesManagementSystem.Views.Dialogs.SupplierDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding WindowTitle}"
        Height="600" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="20,20,20,10" Padding="20">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="TruckDelivery" Width="32" Height="32" 
                                       Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                       VerticalAlignment="Center" Margin="0,0,15,0"/>
                <TextBlock Text="{Binding WindowTitle}" FontSize="20" FontWeight="Bold" 
                         VerticalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="20,0,20,10" VerticalScrollBarVisibility="Auto">
            <materialDesign:Card Padding="30">
                <StackPanel>
                    <!-- اسم المورد -->
                    <TextBox materialDesign:HintAssist.Hint="اسم المورد *"
                           Text="{Binding Supplier.Name, UpdateSourceTrigger=PropertyChanged}"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,0,20"/>

                    <!-- رقم الهاتف -->
                    <TextBox materialDesign:HintAssist.Hint="رقم الهاتف"
                           Text="{Binding Supplier.Phone, UpdateSourceTrigger=PropertyChanged}"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,0,20"/>

                    <!-- البريد الإلكتروني -->
                    <TextBox materialDesign:HintAssist.Hint="البريد الإلكتروني"
                           Text="{Binding Supplier.Email, UpdateSourceTrigger=PropertyChanged}"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,0,20"/>

                    <!-- العنوان -->
                    <TextBox materialDesign:HintAssist.Hint="العنوان"
                           Text="{Binding Supplier.Address, UpdateSourceTrigger=PropertyChanged}"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Height="80"
                           TextWrapping="Wrap"
                           AcceptsReturn="True"
                           VerticalScrollBarVisibility="Auto"
                           Margin="0,0,0,20"/>

                    <!-- الرصيد -->
                    <TextBox materialDesign:HintAssist.Hint="الرصيد الحالي"
                           Text="{Binding Supplier.Balance, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,0,20"/>

                    <!-- معلومات إضافية -->
                    <Expander Header="معلومات إضافية" Margin="0,10,0,0">
                        <StackPanel Margin="0,10,0,0">
                            <!-- تاريخ الإنشاء -->
                            <TextBlock Text="{Binding Supplier.CreatedAt, StringFormat='تاريخ الإنشاء: {0:yyyy/MM/dd HH:mm}'}"
                                     Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                     Margin="0,0,0,10"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                            <!-- تاريخ آخر تحديث -->
                            <TextBlock Text="{Binding Supplier.UpdatedAt, StringFormat='آخر تحديث: {0:yyyy/MM/dd HH:mm}'}"
                                     Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                     Margin="0,0,0,10"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        </StackPanel>
                    </Expander>
                </StackPanel>
            </materialDesign:Card>
        </ScrollViewer>

        <!-- Footer -->
        <materialDesign:Card Grid.Row="2" Margin="20,10,20,20" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Error Message -->
                <Border Grid.Row="0" Background="#FFEBEE" CornerRadius="4" Padding="10" Margin="0,0,0,10"
                      Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AlertCircle" Foreground="#F44336" 
                                               VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding ErrorMessage}" Foreground="#F44336" 
                                 VerticalAlignment="Center" TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>

                <!-- Buttons -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                          Command="{Binding SaveCommand}"
                          IsDefault="True"
                          Margin="0,0,10,0"
                          Width="100">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Margin="0,0,5,0"/>
                            <TextBlock Text="حفظ"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                          Command="{Binding CancelCommand}"
                          IsCancel="True"
                          Width="100">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Cancel" Margin="0,0,5,0"/>
                            <TextBlock Text="إلغاء"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3" Background="#80000000" 
            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center" Padding="40">
                <StackPanel HorizontalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                               IsIndeterminate="True"
                               Width="50" Height="50"
                               Margin="0,0,0,20"/>
                    <TextBlock Text="{Binding LoadingMessage}" HorizontalAlignment="Center" 
                             FontSize="14" Foreground="{DynamicResource MaterialDesignBody}"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </Grid>
</Window>
