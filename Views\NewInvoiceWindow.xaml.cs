using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class NewInvoiceWindow : Window
    {
        private readonly ProductService _productService;
        private readonly CustomerService _customerService;
        private readonly InvoiceService _invoiceService;
        private List<Product> _allProducts;
        private List<Customer> _allCustomers;
        private List<Product> _filteredProducts;
        private ObservableCollection<InvoiceItem> _invoiceItems;
        private bool _isSelectingFromSuggestions = false;
        private Invoice _currentInvoice;

        public NewInvoiceWindow()
        {
            InitializeComponent();

            var databaseService = new DatabaseService();
            _productService = new ProductService(databaseService);
            _customerService = new CustomerService(databaseService);
            _invoiceService = new InvoiceService(databaseService, new NotificationService(), new PaymentService());

            _allProducts = new List<Product>();
            _allCustomers = new List<Customer>();
            _filteredProducts = new List<Product>();
            _invoiceItems = new ObservableCollection<InvoiceItem>();

            _currentInvoice = new Invoice
            {
                InvoiceType = InvoiceType.Sales,
                InvoiceDate = DateTime.Now,
                DueDate = DateTime.Now.AddDays(30)
            };

            InvoiceItemsDataGrid.ItemsSource = _invoiceItems;

            LoadDataAsync();
            UpdateSummary();
        }

        private async void LoadDataAsync()
        {
            try
            {
                // تحميل المنتجات
                _allProducts = (await _productService.GetAllProductsAsync()).ToList();

                // تحميل العملاء
                _allCustomers = (await _customerService.GetAllCustomersAsync()).ToList();
                CustomerComboBox.ItemsSource = _allCustomers;

                // تحديد تاريخ الاستحقاق الافتراضي
                DueDatePicker.SelectedDate = DateTime.Now.AddDays(30);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ProductSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isSelectingFromSuggestions)
                return;

            string searchText = ProductSearchTextBox.Text.Trim();

            if (string.IsNullOrEmpty(searchText))
            {
                HideProductSuggestions();
                return;
            }

            // تحديد ما إذا كان النص يبدو كباركود
            bool looksLikeBarcode = IsLikelyBarcode(searchText);

            if (looksLikeBarcode)
            {
                // معالجة فورية للكودبار
                HideProductSuggestions();
                // لا نضيف المنتج تلقائياً، ننتظر الضغط على إضافة أو Enter
            }
            else if (searchText.Length >= 2)
            {
                // إظهار الاقتراحات للبحث بالاسم
                ShowProductSuggestions(searchText);
            }
            else
            {
                HideProductSuggestions();
            }
        }

        private void ProductSearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (ProductSuggestionsPopup.IsOpen && _filteredProducts.Count > 0)
                {
                    // اختيار أول اقتراح
                    ProductSuggestionsListBox.SelectedIndex = 0;
                    SelectProductSuggestion();
                }
                else
                {
                    // إضافة المنتج مباشرة
                    AddProduct_Click(sender, e);
                }
                e.Handled = true;
            }
            else if (e.Key == Key.Down && ProductSuggestionsPopup.IsOpen)
            {
                ProductSuggestionsListBox.Focus();
                if (ProductSuggestionsListBox.Items.Count > 0)
                {
                    ProductSuggestionsListBox.SelectedIndex = 0;
                }
                e.Handled = true;
            }
            else if (e.Key == Key.Escape)
            {
                HideProductSuggestions();
                e.Handled = true;
            }
        }

        private bool IsLikelyBarcode(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            if (text.Length < 4 || text.Length > 30)
                return false;

            if (text.Contains(' '))
                return false;

            int digitCount = text.Count(char.IsDigit);
            double digitPercentage = (double)digitCount / text.Length;

            bool allDigits = text.All(char.IsDigit) && text.Length >= 4;
            bool alphanumeric = text.All(c => char.IsLetterOrDigit(c)) && digitPercentage >= 0.3;

            return digitPercentage >= 0.7 || allDigits || alphanumeric;
        }

        private void ShowProductSuggestions(string searchText)
        {
            if (_allProducts == null || _allProducts.Count == 0)
                return;

            _filteredProducts = _allProducts.Where(p =>
                p.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (!string.IsNullOrEmpty(p.Code) && p.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase)) ||
                (!string.IsNullOrEmpty(p.Description) && p.Description.Contains(searchText, StringComparison.OrdinalIgnoreCase))
            ).Take(10).ToList();

            if (_filteredProducts.Count > 0)
            {
                ProductSuggestionsListBox.ItemsSource = _filteredProducts;
                ProductSuggestionsPopup.IsOpen = true;
            }
            else
            {
                HideProductSuggestions();
            }
        }

        private void HideProductSuggestions()
        {
            ProductSuggestionsPopup.IsOpen = false;
            ProductSuggestionsListBox.ItemsSource = null;
        }

        private void ProductSuggestionsListBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            SelectProductSuggestion();
        }

        private void SelectProductSuggestion()
        {
            if (ProductSuggestionsListBox.SelectedItem is Product selectedProduct)
            {
                _isSelectingFromSuggestions = true;
                ProductSearchTextBox.Text = selectedProduct.Name;
                _isSelectingFromSuggestions = false;

                HideProductSuggestions();
                AddProductToInvoice(selectedProduct);
            }
        }

        private void AddProduct_Click(object sender, RoutedEventArgs e)
        {
            string searchText = ProductSearchTextBox.Text.Trim();
            if (string.IsNullOrEmpty(searchText))
                return;

            // البحث عن المنتج
            Product? foundProduct = null;

            // البحث بالباركود أولاً
            foundProduct = _allProducts.FirstOrDefault(p =>
                !string.IsNullOrEmpty(p.Barcode) && p.Barcode == searchText);

            // إذا لم يوجد، ابحث بالكود
            if (foundProduct == null)
            {
                foundProduct = _allProducts.FirstOrDefault(p =>
                    !string.IsNullOrEmpty(p.Code) && p.Code == searchText);
            }

            // إذا لم يوجد، ابحث بالاسم
            if (foundProduct == null)
            {
                foundProduct = _allProducts.FirstOrDefault(p =>
                    p.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase));
            }

            if (foundProduct != null)
            {
                AddProductToInvoice(foundProduct);
            }
            else
            {
                MessageBox.Show($"لم يتم العثور على منتج: {searchText}", "منتج غير موجود",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void AddProductToInvoice(Product product)
        {
            try
            {
                if (!int.TryParse(QuantityTextBox.Text, out int quantity) || quantity <= 0)
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // البحث عن المنتج في القائمة الموجودة
                var existingItem = _invoiceItems.FirstOrDefault(item => item.ProductId == product.Id);

                if (existingItem != null)
                {
                    // زيادة الكمية
                    existingItem.Quantity += quantity;
                }
                else
                {
                    // إضافة منتج جديد
                    var newItem = new InvoiceItem
                    {
                        ProductId = product.Id,
                        ProductName = product.Name,
                        Description = product.Description ?? "",
                        Quantity = quantity,
                        UnitPrice = product.SalePrice,
                        Discount = 0
                    };

                    _invoiceItems.Add(newItem);
                }

                // مسح حقل البحث والكمية
                ProductSearchTextBox.Clear();
                QuantityTextBox.Text = "1";
                ProductSearchTextBox.Focus();

                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateSummary()
        {
            var subTotal = _invoiceItems.Sum(item => item.Total);
            var discount = _invoiceItems.Sum(item => item.Discount);
            var tax = 0m; // لا توجد ضريبة في النموذج الحالي
            var total = subTotal - discount + tax;

            SubTotalLabel.Text = $"{subTotal:F2} دج";
            DiscountLabel.Text = $"{discount:F2} دج";
            TaxLabel.Text = $"{tax:F2} دج";
            TotalLabel.Text = $"{total:F2} دج";
            HeaderTotalLabel.Text = $"{total:F2} دج";

            // تحديث الفاتورة الحالية
            _currentInvoice.Subtotal = subTotal;
            _currentInvoice.DiscountAmount = discount;
            _currentInvoice.TaxAmount = tax;
            _currentInvoice.TotalAmount = total;
        }

        private void ClearSearch_Click(object sender, RoutedEventArgs e)
        {
            ProductSearchTextBox.Clear();
            QuantityTextBox.Text = "1";
            HideProductSuggestions();
            ProductSearchTextBox.Focus();
        }

        private void CustomerComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CustomerComboBox.SelectedItem is Customer selectedCustomer)
            {
                _currentInvoice.CustomerId = selectedCustomer.Id;
            }
        }



        private void AddNewCustomer_Click(object sender, RoutedEventArgs e)
        {
            // فتح نافذة إضافة عميل جديد
            MessageBox.Show("سيتم تطوير نافذة إضافة عميل جديد قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CustomerDetails_Click(object sender, RoutedEventArgs e)
        {
            if (CustomerComboBox.SelectedItem is Customer selectedCustomer)
            {
                MessageBox.Show($"تفاصيل العميل:\nالاسم: {selectedCustomer.Name}\nالهاتف: {selectedCustomer.Phone}\nالعنوان: {selectedCustomer.Address}",
                    "تفاصيل العميل", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("يرجى اختيار عميل أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void EditItem_Click(object sender, RoutedEventArgs e)
        {
            if (InvoiceItemsDataGrid.SelectedItem is InvoiceItem item)
            {
                // فتح نافذة تعديل العنصر
                MessageBox.Show("سيتم تطوير نافذة تعديل العنصر قريباً", "قيد التطوير",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteItem_Click(object sender, RoutedEventArgs e)
        {
            if (InvoiceItemsDataGrid.SelectedItem is InvoiceItem item)
            {
                var result = MessageBox.Show($"هل تريد حذف {item.ProductName}؟", "تأكيد الحذف",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _invoiceItems.Remove(item);
                    UpdateSummary();
                }
            }
        }

        private async void SaveDraft_Click(object sender, RoutedEventArgs e)
        {
            await SaveInvoice(InvoiceStatus.Draft);
        }

        private async void SaveAndPay_Click(object sender, RoutedEventArgs e)
        {
            await SaveInvoice(InvoiceStatus.Paid);
        }

        private async Task SaveInvoice(InvoiceStatus status)
        {
            try
            {
                if (_currentInvoice.CustomerId == null)
                {
                    MessageBox.Show("يرجى اختيار عميل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (_invoiceItems.Count == 0)
                {
                    MessageBox.Show("يرجى إضافة منتجات للفاتورة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                _currentInvoice.InvoiceDate = InvoiceDatePicker.SelectedDate ?? DateTime.Now;
                _currentInvoice.DueDate = DueDatePicker.SelectedDate;
                _currentInvoice.Notes = NotesTextBox.Text;
                _currentInvoice.Status = status;

                if (status == InvoiceStatus.Paid)
                {
                    _currentInvoice.PaidAmount = _currentInvoice.TotalAmount;
                }

                // إضافة العناصر للفاتورة
                _currentInvoice.Items.Clear();
                foreach (var item in _invoiceItems)
                {
                    _currentInvoice.Items.Add(item);
                }

                var savedInvoice = await _invoiceService.AddInvoiceAsync(_currentInvoice);

                MessageBox.Show($"تم حفظ الفاتورة بنجاح!\nرقم الفاتورة: {savedInvoice.InvoiceNumber}",
                    "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Print_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير وظيفة الطباعة قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            if (_invoiceItems.Count > 0)
            {
                var result = MessageBox.Show("هناك عناصر في الفاتورة. هل تريد الإغلاق؟", "تأكيد الإغلاق",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                    return;
            }

            DialogResult = false;
            Close();
        }
    }
}
