using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Views
{
    public partial class AddExpenseWindow : Window
    {
        public Expense? NewExpense { get; private set; }
        private readonly bool _isEditMode = false;

        public AddExpenseWindow()
        {
            InitializeComponent();
            InitializeForm();
        }

        public AddExpenseWindow(Expense expenseToEdit) : this()
        {
            _isEditMode = true;
            HeaderTitle.Text = "تعديل المصروف";
            Title = "✏️ تعديل المصروف";
            LoadExpenseData(expenseToEdit);
        }

        private void InitializeForm()
        {
            // تحديد التاريخ الحالي
            DatePicker.SelectedDate = DateTime.Now;

            // إضافة تصنيفات المصاريف
            var categories = new List<ExpenseCategoryItem>
            {
                new ExpenseCategoryItem { Id = 1, Name = "👨‍💼 راتب موظف", Icon = "👨‍💼" },
                new ExpenseCategoryItem { Id = 2, Name = "📅 منتج منتهي الصلاحية", Icon = "📅" },
                new ExpenseCategoryItem { Id = 3, Name = "💔 منتج تالف", Icon = "💔" },
                new ExpenseCategoryItem { Id = 4, Name = "💡 فواتير ومرافق", Icon = "💡" },
                new ExpenseCategoryItem { Id = 5, Name = "🏢 إيجار", Icon = "🏢" },
                new ExpenseCategoryItem { Id = 6, Name = "🔧 صيانة", Icon = "🔧" },
                new ExpenseCategoryItem { Id = 7, Name = "📢 تسويق وإعلان", Icon = "📢" },
                new ExpenseCategoryItem { Id = 8, Name = "🚚 نقل ومواصلات", Icon = "🚚" },
                new ExpenseCategoryItem { Id = 9, Name = "📋 مصاريف مكتبية", Icon = "📋" },
                new ExpenseCategoryItem { Id = 10, Name = "💰 مصاريف أخرى", Icon = "💰" }
            };

            CategoryComboBox.ItemsSource = categories;
            CategoryComboBox.DisplayMemberPath = "Name";
            CategoryComboBox.SelectedValuePath = "Id";

            // ربط الأحداث
            TitleTextBox.TextChanged += UpdatePreview;
            AmountTextBox.TextChanged += UpdatePreview;
            CategoryComboBox.SelectionChanged += UpdatePreview;
        }

        private void LoadExpenseData(Expense expense)
        {
            TitleTextBox.Text = expense.Title;
            CategoryComboBox.SelectedValue = expense.CategoryId;
            AmountTextBox.Text = expense.Amount.ToString("F2");
            DatePicker.SelectedDate = expense.Date;
            EmployeeTextBox.Text = expense.EmployeeName;
            ProductTextBox.Text = expense.ProductName;
            DescriptionTextBox.Text = expense.Description;
            NotesTextBox.Text = expense.Notes;
        }

        private void CategoryComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CategoryComboBox.SelectedItem is ExpenseCategoryItem selectedCategory)
            {
                // إظهار/إخفاء الحقول حسب التصنيف
                bool showEmployee = selectedCategory.Id == 1; // راتب موظف
                bool showProduct = selectedCategory.Id == 2 || selectedCategory.Id == 3; // منتج منتهي الصلاحية أو تالف

                EmployeeLabel.Visibility = showEmployee ? Visibility.Visible : Visibility.Collapsed;
                EmployeeTextBox.Visibility = showEmployee ? Visibility.Visible : Visibility.Collapsed;

                ProductLabel.Visibility = showProduct ? Visibility.Visible : Visibility.Collapsed;
                ProductTextBox.Visibility = showProduct ? Visibility.Visible : Visibility.Collapsed;

                // تحديث أيقونة المعاينة
                PreviewIcon.Text = selectedCategory.Icon;
            }

            UpdatePreview(sender, EventArgs.Empty);
        }

        private void UpdatePreview(object sender, EventArgs e)
        {
            var selectedCategory = CategoryComboBox.SelectedItem as ExpenseCategoryItem;
            var title = TitleTextBox.Text.Trim();
            var amountText = AmountTextBox.Text.Trim();

            if (selectedCategory != null && !string.IsNullOrEmpty(title))
            {
                PreviewText.Text = $"{selectedCategory.Name}: {title}";
                PreviewIcon.Text = selectedCategory.Icon;
            }
            else
            {
                PreviewText.Text = "لم يتم تحديد المصروف بعد";
                PreviewIcon.Text = "💰";
            }

            if (decimal.TryParse(amountText, out decimal amount))
            {
                PreviewAmount.Text = $"المبلغ: {amount:F2} دج";
            }
            else
            {
                PreviewAmount.Text = "المبلغ: 0.00 دج";
            }
        }

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                var selectedCategory = CategoryComboBox.SelectedItem as ExpenseCategoryItem;

                NewExpense = new Expense
                {
                    Title = TitleTextBox.Text.Trim(),
                    CategoryId = selectedCategory!.Id,
                    CategoryName = selectedCategory.Name.Substring(2), // إزالة الأيقونة
                    Amount = decimal.Parse(AmountTextBox.Text.Trim()),
                    Date = DatePicker.SelectedDate!.Value,
                    EmployeeName = EmployeeTextBox.Text.Trim(),
                    ProductName = ProductTextBox.Text.Trim(),
                    Description = DescriptionTextBox.Text.Trim(),
                    Notes = NotesTextBox.Text.Trim()
                };

                DialogResult = true;
                Close();
            }
        }

        private bool ValidateForm()
        {
            // التحقق من العنوان
            if (string.IsNullOrWhiteSpace(TitleTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان المصروف", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                TitleTextBox.Focus();
                return false;
            }

            // التحقق من التصنيف
            if (CategoryComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار تصنيف المصروف", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                CategoryComboBox.Focus();
                return false;
            }

            // التحقق من المبلغ
            if (!decimal.TryParse(AmountTextBox.Text.Trim(), out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح أكبر من صفر", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                AmountTextBox.Focus();
                return false;
            }

            // التحقق من التاريخ
            if (DatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ المصروف", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                DatePicker.Focus();
                return false;
            }

            // التحقق من الحقول الإضافية حسب التصنيف
            var selectedCategory = CategoryComboBox.SelectedItem as ExpenseCategoryItem;

            if (selectedCategory!.Id == 1 && string.IsNullOrWhiteSpace(EmployeeTextBox.Text)) // راتب موظف
            {
                MessageBox.Show("يرجى إدخال اسم الموظف", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                EmployeeTextBox.Focus();
                return false;
            }

            if ((selectedCategory.Id == 2 || selectedCategory.Id == 3) && string.IsNullOrWhiteSpace(ProductTextBox.Text)) // منتج
            {
                MessageBox.Show("يرجى إدخال اسم المنتج", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                ProductTextBox.Focus();
                return false;
            }

            return true;
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class ExpenseCategoryItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }
}
