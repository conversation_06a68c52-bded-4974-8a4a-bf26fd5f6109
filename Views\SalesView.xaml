<UserControl x:Class="SalesManagementSystem.Views.SalesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="0" Padding="15" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox x:Name="SearchTextBox" Grid.Column="0" 
                        materialDesign:HintAssist.Hint="البحث في المبيعات..."
                        materialDesign:TextFieldAssist.HasClearButton="True"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        Margin="0,0,15,0"/>

                <!-- Add Sale Button -->
                <Button x:Name="AddSaleButton" Grid.Column="1" 
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Click="AddSaleButton_Click"
                       Margin="0,0,10,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Margin="0,0,5,0"/>
                        <TextBlock Text="فاتورة جديدة"/>
                    </StackPanel>
                </Button>

                <!-- Print Button -->
                <Button x:Name="PrintButton" Grid.Column="2" 
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Click="PrintButton_Click"
                       Margin="0,0,10,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Printer" Margin="0,0,5,0"/>
                        <TextBlock Text="طباعة"/>
                    </StackPanel>
                </Button>

                <!-- Refresh Button -->
                <Button x:Name="RefreshButton" Grid.Column="3" 
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Click="RefreshButton_Click">
                    <materialDesign:PackIcon Kind="Refresh"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Sales DataGrid -->
        <materialDesign:Card Grid.Row="1" Padding="0">
            <DataGrid x:Name="SalesDataGrid" 
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     materialDesign:DataGridAssist.CellPadding="8"
                     materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                
                <DataGrid.Columns>
                    <!-- Invoice Number -->
                    <DataGridTextColumn Header="رقم الفاتورة" 
                                      Binding="{Binding InvoiceNumber}" 
                                      Width="120"/>
                    
                    <!-- Date -->
                    <DataGridTextColumn Header="التاريخ" 
                                      Binding="{Binding Date, StringFormat=yyyy-MM-dd}" 
                                      Width="100"/>
                    
                    <!-- Customer -->
                    <DataGridTextColumn Header="العميل" 
                                      Binding="{Binding CustomerName}" 
                                      Width="*"/>
                    
                    <!-- Items Count -->
                    <DataGridTextColumn Header="عدد الأصناف" 
                                      Binding="{Binding ItemsCount}" 
                                      Width="100"/>
                    
                    <!-- Subtotal -->
                    <DataGridTextColumn Header="المجموع الفرعي" 
                                      Binding="{Binding Subtotal, StringFormat=C}" 
                                      Width="120"/>
                    
                    <!-- Tax -->
                    <DataGridTextColumn Header="الضريبة" 
                                      Binding="{Binding Tax, StringFormat=C}" 
                                      Width="100"/>
                    
                    <!-- Total -->
                    <DataGridTextColumn Header="الإجمالي" 
                                      Binding="{Binding Total, StringFormat=C}" 
                                      Width="120"/>
                    
                    <!-- Actions -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="180">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Style="{StaticResource MaterialDesignIconButton}" 
                                           ToolTip="عرض"
                                           Click="ViewSale_Click"
                                           Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Eye" Foreground="{StaticResource PrimaryHueMidBrush}"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}" 
                                           ToolTip="طباعة"
                                           Click="PrintSale_Click"
                                           Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Printer" Foreground="#4CAF50"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}" 
                                           ToolTip="تعديل"
                                           Click="EditSale_Click"
                                           Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Pencil" Foreground="#FF9800"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}" 
                                           ToolTip="حذف"
                                           Click="DeleteSale_Click"
                                           Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Delete" Foreground="#F44336"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
