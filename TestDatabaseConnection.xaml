<Window x:Class="SalesManagementSystem.TestDatabaseConnection"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختبار قاعدة البيانات والمنتجات" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="اختبار قاعدة البيانات والمنتجات"
                   FontSize="24" FontWeight="Bold"
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- Test Buttons -->
        <WrapPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <Button Name="TestDatabaseButton" Content="اختبار قاعدة البيانات"
                    Click="TestDatabaseButton_Click" Margin="5" Padding="10,5"/>
            <Button Name="RepairDatabaseButton" Content="إصلاح قاعدة البيانات"
                    Click="RepairDatabaseButton_Click" Margin="5" Padding="10,5"
                    Background="Orange" Foreground="White"/>
            <Button Name="ShowTableStructureButton" Content="عرض هيكل الجداول"
                    Click="ShowTableStructureButton_Click" Margin="5" Padding="10,5"
                    Background="Blue" Foreground="White"/>
            <Button Name="CreateWarehousesButton" Content="إنشاء جدول المخازن"
                    Click="CreateWarehousesButton_Click" Margin="5" Padding="10,5"
                    Background="Purple" Foreground="White"/>
            <Button Name="CreateCustomersButton" Content="إنشاء جدول العملاء"
                    Click="CreateCustomersButton_Click" Margin="5" Padding="10,5"
                    Background="Teal" Foreground="White"/>
            <Button Name="CreateSampleDataButton" Content="إنشاء البيانات الافتراضية"
                    Click="CreateSampleDataButton_Click" Margin="5" Padding="10,5"/>
            <Button Name="LoadProductsButton" Content="تحميل المنتجات"
                    Click="LoadProductsButton_Click" Margin="5" Padding="10,5"/>
            <Button Name="TestAddProductButton" Content="اختبار إضافة منتج"
                    Click="TestAddProductButton_Click" Margin="5" Padding="10,5"/>
            <Button Name="TestCustomersButton" Content="اختبار العملاء"
                    Click="TestCustomersButton_Click" Margin="5" Padding="10,5"
                    Background="DarkCyan" Foreground="White"/>
            <Button Name="OpenProductDialogButton" Content="فتح نافذة إضافة منتج"
                    Click="OpenProductDialogButton_Click" Margin="5" Padding="10,5"/>
            <Button Name="OpenCustomersWindowButton" Content="فتح نافذة العملاء"
                    Click="OpenCustomersWindowButton_Click" Margin="5" Padding="10,5"
                    Background="SeaGreen" Foreground="White"/>
            <Button Name="ClearButton" Content="مسح النتائج"
                    Click="ClearButton_Click" Margin="5" Padding="10,5"/>
        </WrapPanel>

        <!-- Results -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <TextBox Name="ResultsTextBox"
                     IsReadOnly="True"
                     TextWrapping="Wrap"
                     VerticalAlignment="Stretch"
                     FontFamily="Consolas"
                     FontSize="12"
                     Background="Black"
                     Foreground="LightGreen"
                     Padding="10"/>
        </ScrollViewer>
    </Grid>
</Window>
