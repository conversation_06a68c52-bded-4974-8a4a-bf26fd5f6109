using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using LiveCharts;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class ReportsViewModel : BaseViewModel
    {
        #region Services

        private readonly SimpleReportService _reportService;
        private readonly SimpleExportService _exportService;
        private readonly SimpleChartService _chartService;

        #endregion

        #region Properties

        private DateTime _startDate = DateTime.Today.AddMonths(-1);
        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (SetProperty(ref _startDate, value))
                {
                    RefreshReportsCommand.RaiseCanExecuteChanged();
                }
            }
        }

        private DateTime _endDate = DateTime.Today;
        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                if (SetProperty(ref _endDate, value))
                {
                    RefreshReportsCommand.RaiseCanExecuteChanged();
                }
            }
        }

        private string _selectedReportType = "المبيعات";
        public string SelectedReportType
        {
            get => _selectedReportType;
            set
            {
                if (SetProperty(ref _selectedReportType, value))
                {
                    OnReportTypeChanged();
                }
            }
        }

        private ObservableCollection<string> _reportTypes = new()
        {
            "المبيعات", "المخزون", "العملاء", "المنتجات", "الأرباح والخسائر",
            "أداء الموظفين", "مقارنة الفترات", "تحليل الاتجاهات"
        };
        public ObservableCollection<string> ReportTypes
        {
            get => _reportTypes;
            set => SetProperty(ref _reportTypes, value);
        }

        // Sales Report Data
        private SalesReport? _salesReport;
        public SalesReport? SalesReport
        {
            get => _salesReport;
            set => SetProperty(ref _salesReport, value);
        }

        private ObservableCollection<SaleReportItem> _salesData = new();
        public ObservableCollection<SaleReportItem> SalesData
        {
            get => _salesData;
            set => SetProperty(ref _salesData, value);
        }

        // Product Sales Data
        private ObservableCollection<SimpleProductSalesReport> _productSalesData = new();
        public ObservableCollection<SimpleProductSalesReport> ProductSalesData
        {
            get => _productSalesData;
            set => SetProperty(ref _productSalesData, value);
        }

        // Customer Sales Data
        private ObservableCollection<SimpleCustomerSalesReport> _customerSalesData = new();
        public ObservableCollection<SimpleCustomerSalesReport> CustomerSalesData
        {
            get => _customerSalesData;
            set => SetProperty(ref _customerSalesData, value);
        }

        // Inventory Data
        private ObservableCollection<SimpleInventoryReportItem> _inventoryData = new();
        public ObservableCollection<SimpleInventoryReportItem> InventoryData
        {
            get => _inventoryData;
            set => SetProperty(ref _inventoryData, value);
        }

        // Chart Data
        private SeriesCollection? _chartData;
        public SeriesCollection? ChartData
        {
            get => _chartData;
            set => SetProperty(ref _chartData, value);
        }

        private string[] _chartLabels = Array.Empty<string>();
        public string[] ChartLabels
        {
            get => _chartLabels;
            set => SetProperty(ref _chartLabels, value);
        }

        private Func<double, string>? _chartFormatter;
        public Func<double, string>? ChartFormatter
        {
            get => _chartFormatter;
            set => SetProperty(ref _chartFormatter, value);
        }

        // Summary Data
        private decimal _totalSales;
        public decimal TotalSales
        {
            get => _totalSales;
            set => SetProperty(ref _totalSales, value);
        }

        private decimal _totalProfit;
        public decimal TotalProfit
        {
            get => _totalProfit;
            set => SetProperty(ref _totalProfit, value);
        }

        private int _totalOrders;
        public int TotalOrders
        {
            get => _totalOrders;
            set => SetProperty(ref _totalOrders, value);
        }

        private decimal _averageOrderValue;
        public decimal AverageOrderValue
        {
            get => _averageOrderValue;
            set => SetProperty(ref _averageOrderValue, value);
        }

        public bool CanRefreshReports => !IsLoading && StartDate <= EndDate;

        #endregion

        #region Commands

        private DelegateCommand? _refreshReportsCommand;
        public DelegateCommand RefreshReportsCommand => _refreshReportsCommand ??= new DelegateCommand(RefreshReports, () => CanRefreshReports);

        private DelegateCommand? _exportToExcelCommand;
        public DelegateCommand ExportToExcelCommand => _exportToExcelCommand ??= new DelegateCommand(ExportToExcel);

        private DelegateCommand? _exportToPdfCommand;
        public DelegateCommand ExportToPdfCommand => _exportToPdfCommand ??= new DelegateCommand(ExportToPdf);

        private DelegateCommand? _exportToCsvCommand;
        public DelegateCommand ExportToCsvCommand => _exportToCsvCommand ??= new DelegateCommand(ExportToCsv);

        private DelegateCommand? _printReportCommand;
        public DelegateCommand PrintReportCommand => _printReportCommand ??= new DelegateCommand(PrintReport);

        #endregion

        #region Constructor

        public ReportsViewModel()
        {
            var dbService = new DatabaseService();
            _reportService = new SimpleReportService(dbService);
            _exportService = new SimpleExportService();
            _chartService = new SimpleChartService(_reportService);

            // Load initial data
            _ = LoadInitialDataAsync();
        }

        #endregion

        #region Methods

        private async Task LoadInitialDataAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تحميل البيانات الأولية...";

                await RefreshCurrentReport();
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل البيانات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تحميل البيانات الأولية للتقارير");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void RefreshReports()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تحديث التقارير...";
                ClearError();

                await RefreshCurrentReport();
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحديث التقارير: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تحديث التقارير");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task RefreshCurrentReport()
        {
            switch (SelectedReportType)
            {
                case "المبيعات":
                    await LoadSalesReportAsync();
                    break;
                case "المخزون":
                    await LoadInventoryReportAsync();
                    break;
                case "العملاء":
                    await LoadCustomerSalesReportAsync();
                    break;
                case "المنتجات":
                    await LoadProductSalesReportAsync();
                    break;
                case "الأرباح والخسائر":
                    await LoadProfitLossReportAsync();
                    break;
                case "أداء الموظفين":
                    await LoadEmployeePerformanceReportAsync();
                    break;
                case "مقارنة الفترات":
                    await LoadPeriodComparisonReportAsync();
                    break;
                case "تحليل الاتجاهات":
                    await LoadTrendAnalysisReportAsync();
                    break;
            }
        }

        private async Task LoadSalesReportAsync()
        {
            var report = await _reportService.GetSalesReportAsync(StartDate, EndDate);
            SalesReport = report;

            SalesData.Clear();
            foreach (var sale in report.SalesByMonth)
            {
                // Convert SaleSummaryByMonth to SaleReportItem
                SalesData.Add(new SaleReportItem
                {
                    SaleDate = DateTime.Parse(sale.Month + "-01"),
                    TotalAmount = sale.TotalAmount,
                    NetAmount = sale.TotalAmount,
                    CustomerName = "مجموع الشهر",
                    SalespersonName = "متعدد"
                });
            }

            TotalSales = report.TotalSales;
            TotalOrders = (int)report.TotalSales; // Temporary conversion
            AverageOrderValue = report.TotalSales / Math.Max(1, report.SalesByMonth.Count);

            // Load chart data
            ChartData = await _chartService.GetMonthlySalesChartAsync(DateTime.Now.Year);
            ChartLabels = _chartService.GetMonthLabels();
            ChartFormatter = _chartService.GetCurrencyFormatter();
        }

        private async Task LoadInventoryReportAsync()
        {
            var inventoryReport = await _reportService.GetInventoryReportAsync();

            InventoryData.Clear();
            foreach (var item in inventoryReport.Where(i => i.StockStatus == "منخفض"))
            {
                InventoryData.Add(item);
            }
        }

        private async Task LoadCustomerSalesReportAsync()
        {
            var customerSales = await _reportService.GetCustomerSalesReportAsync(StartDate, EndDate);

            CustomerSalesData.Clear();
            foreach (var customer in customerSales)
            {
                CustomerSalesData.Add(customer);
            }
        }

        private async Task LoadProductSalesReportAsync()
        {
            var productSales = await _reportService.GetProductSalesReportAsync(StartDate, EndDate);

            ProductSalesData.Clear();
            foreach (var product in productSales)
            {
                ProductSalesData.Add(product);
            }

            // Load pie chart for top products
            ChartData = await _chartService.GetTopProductsPieChartAsync(StartDate, EndDate);
        }

        private async Task LoadProfitLossReportAsync()
        {
            var profitLoss = await _reportService.GetProfitLossReportAsync(StartDate, EndDate);
            TotalSales = profitLoss.TotalRevenue;
            TotalProfit = profitLoss.NetProfit;

            // Load profit/loss chart
            ChartData = await _chartService.GetProfitLossChartAsync(DateTime.Now.Year);
            ChartLabels = _chartService.GetMonthLabels();
            ChartFormatter = _chartService.GetCurrencyFormatter();
        }

        private async Task LoadEmployeePerformanceReportAsync()
        {
            var employeePerformance = await _reportService.GetEmployeePerformanceReportAsync(StartDate, EndDate);

            // Load employee performance chart
            ChartData = _chartService.GetEmployeePerformanceChart(employeePerformance);
            ChartLabels = _chartService.GetEmployeeLabels(employeePerformance);
            ChartFormatter = _chartService.GetCurrencyFormatter();
        }

        private async Task LoadPeriodComparisonReportAsync()
        {
            var period1Start = StartDate.AddYears(-1);
            var period1End = EndDate.AddYears(-1);

            var comparison = await _reportService.GetPeriodComparisonReportAsync(
                period1Start, period1End, StartDate, EndDate);

            ChartData = _chartService.GetPeriodComparisonChart(comparison);
            ChartLabels = _chartService.GetPeriodComparisonLabels();
            ChartFormatter = _chartService.GetCurrencyFormatter();
        }

        private async Task LoadTrendAnalysisReportAsync()
        {
            var trendAnalysis = await _reportService.GetTrendAnalysisReportAsync(12);

            ChartData = _chartService.GetTrendAnalysisChart(trendAnalysis);
            ChartLabels = trendAnalysis.SalesTrend.Select(t => t.MonthName).ToArray();
            ChartFormatter = _chartService.GetCurrencyFormatter();
        }

        private void OnReportTypeChanged()
        {
            _ = RefreshCurrentReport();
        }

        #endregion

        #region Export Methods

        private async void ExportToExcel()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري التصدير إلى Excel...";

                bool success = false;
                switch (SelectedReportType)
                {
                    case "المبيعات":
                        success = await _exportService.ExportSalesReportToExcelAsync(SalesReport!);
                        break;
                    case "المخزون":
                        success = await _exportService.ExportToCsvAsync(InventoryData, "تقرير المخزون");
                        break;
                    // Add more export cases as needed
                }

                if (success)
                {
                    System.Windows.MessageBox.Show("تم التصدير بنجاح!", "نجح",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في التصدير: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تصدير التقرير إلى Excel");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void ExportToPdf()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري التصدير إلى PDF...";

                var success = await _exportService.ExportToPdfAsync(SalesReport ?? new SalesReport(), $"تقرير {SelectedReportType}");

                if (success)
                {
                    System.Windows.MessageBox.Show("تم التصدير بنجاح!", "نجح",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في التصدير: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تصدير التقرير إلى PDF");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void ExportToCsv()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري التصدير إلى CSV...";

                bool success = false;
                switch (SelectedReportType)
                {
                    case "المبيعات":
                        success = await _exportService.ExportToCsvAsync(SalesData, "تقرير المبيعات");
                        break;
                    case "المخزون":
                        success = await _exportService.ExportToCsvAsync(InventoryData, "تقرير المخزون");
                        break;
                    case "العملاء":
                        success = await _exportService.ExportToCsvAsync(CustomerSalesData, "تقرير العملاء");
                        break;
                    case "المنتجات":
                        success = await _exportService.ExportToCsvAsync(ProductSalesData, "تقرير المنتجات");
                        break;
                }

                if (success)
                {
                    System.Windows.MessageBox.Show("تم التصدير بنجاح!", "نجح",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في التصدير: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تصدير التقرير إلى CSV");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void PrintReport()
        {
            try
            {
                // Implement print functionality
                System.Windows.MessageBox.Show("وظيفة الطباعة قيد التطوير", "معلومات",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                SetError($"خطأ في الطباعة: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في طباعة التقرير");
            }
        }

        #endregion
    }
}
