<Window x:Class="SalesManagementSystem.Views.SnoozeNotificationDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تأجيل الإشعار"
        Height="400"
        Width="450"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="ClockOutline"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="تأجيل الإشعار"
                      Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                      VerticalAlignment="Center"
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Notification Info -->
        <GroupBox Grid.Row="1"
                 Header="معلومات الإشعار"
                 Style="{DynamicResource MaterialDesignCardGroupBox}"
                 Margin="0,0,0,16">
            <StackPanel Margin="16">
                <TextBlock Text="{Binding Notification.Title}"
                          Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                          Margin="0,0,0,8"/>
                <TextBlock Text="{Binding Notification.Message}"
                          Style="{DynamicResource MaterialDesignBody2TextBlock}"
                          TextWrapping="Wrap"
                          Opacity="0.7"/>
            </StackPanel>
        </GroupBox>

        <!-- Snooze Options -->
        <GroupBox Grid.Row="2"
                 Header="خيارات التأجيل"
                 Style="{DynamicResource MaterialDesignCardGroupBox}">
            <StackPanel Margin="16">
                
                <!-- Quick Options -->
                <TextBlock Text="خيارات سريعة"
                          Style="{DynamicResource MaterialDesignSubtitle2TextBlock}"
                          Margin="0,0,0,12"/>
                
                <UniformGrid Columns="2" Margin="0,0,0,16">
                    <RadioButton Content="5 دقائق"
                               IsChecked="{Binding IsSelected5Minutes}"
                               Style="{DynamicResource MaterialDesignRadioButton}"
                               Margin="0,4"/>
                    <RadioButton Content="15 دقيقة"
                               IsChecked="{Binding IsSelected15Minutes}"
                               Style="{DynamicResource MaterialDesignRadioButton}"
                               Margin="0,4"/>
                    <RadioButton Content="30 دقيقة"
                               IsChecked="{Binding IsSelected30Minutes}"
                               Style="{DynamicResource MaterialDesignRadioButton}"
                               Margin="0,4"/>
                    <RadioButton Content="ساعة واحدة"
                               IsChecked="{Binding IsSelected1Hour}"
                               Style="{DynamicResource MaterialDesignRadioButton}"
                               Margin="0,4"/>
                    <RadioButton Content="3 ساعات"
                               IsChecked="{Binding IsSelected3Hours}"
                               Style="{DynamicResource MaterialDesignRadioButton}"
                               Margin="0,4"/>
                    <RadioButton Content="يوم واحد"
                               IsChecked="{Binding IsSelected1Day}"
                               Style="{DynamicResource MaterialDesignRadioButton}"
                               Margin="0,4"/>
                </UniformGrid>

                <!-- Custom Time -->
                <RadioButton Content="وقت مخصص"
                           IsChecked="{Binding IsCustomTime}"
                           Style="{DynamicResource MaterialDesignRadioButton}"
                           Margin="0,8,0,12"/>

                <Grid IsEnabled="{Binding IsCustomTime}" Margin="16,0,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="8"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="التاريخ"
                                  Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                  Margin="0,0,0,4"/>
                        <DatePicker SelectedDate="{Binding CustomDate}"
                                   Style="{DynamicResource MaterialDesignDatePicker}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الوقت"
                                  Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                  Margin="0,0,0,4"/>
                        <materialDesign:TimePicker SelectedTime="{Binding CustomTime}"
                                                 Style="{DynamicResource MaterialDesignTimePicker}"/>
                    </StackPanel>
                </Grid>

                <!-- Calculated Snooze Time -->
                <Border Background="{DynamicResource MaterialDesignSelection}"
                       CornerRadius="4"
                       Padding="12"
                       Margin="0,16,0,0">
                    <StackPanel>
                        <TextBlock Text="سيتم تذكيرك في:"
                                  Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                  Opacity="0.7"/>
                        <TextBlock Text="{Binding CalculatedSnoozeTime}"
                                  Style="{DynamicResource MaterialDesignSubtitle2TextBlock}"
                                  FontWeight="Medium"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </GroupBox>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right"
                   Margin="0,24,0,0">
            
            <Button Command="{Binding SnoozeCommand}"
                   Style="{DynamicResource MaterialDesignRaisedButton}"
                   Content="تأجيل"
                   Margin="0,0,8,0"/>
            
            <Button Command="{Binding CancelCommand}"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Content="إلغاء"/>
        </StackPanel>
    </Grid>
</Window>
