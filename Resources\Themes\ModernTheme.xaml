<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Modern Design System Colors -->
    
    <!-- Primary Colors -->
    <Color x:Key="Primary50">#E3F2FD</Color>
    <Color x:Key="Primary100">#BBDEFB</Color>
    <Color x:Key="Primary200">#90CAF9</Color>
    <Color x:Key="Primary300">#64B5F6</Color>
    <Color x:Key="Primary400">#42A5F5</Color>
    <Color x:Key="Primary500">#2196F3</Color>
    <Color x:Key="Primary600">#1E88E5</Color>
    <Color x:Key="Primary700">#1976D2</Color>
    <Color x:Key="Primary800">#1565C0</Color>
    <Color x:Key="Primary900">#0D47A1</Color>
    
    <!-- Secondary Colors -->
    <Color x:Key="Secondary50">#F3E5F5</Color>
    <Color x:Key="Secondary100">#E1BEE7</Color>
    <Color x:Key="Secondary200">#CE93D8</Color>
    <Color x:Key="Secondary300">#BA68C8</Color>
    <Color x:Key="Secondary400">#AB47BC</Color>
    <Color x:Key="Secondary500">#9C27B0</Color>
    <Color x:Key="Secondary600">#8E24AA</Color>
    <Color x:Key="Secondary700">#7B1FA2</Color>
    <Color x:Key="Secondary800">#6A1B9A</Color>
    <Color x:Key="Secondary900">#4A148C</Color>
    
    <!-- Accent Colors -->
    <Color x:Key="Accent50">#FFF3E0</Color>
    <Color x:Key="Accent100">#FFE0B2</Color>
    <Color x:Key="Accent200">#FFCC80</Color>
    <Color x:Key="Accent300">#FFB74D</Color>
    <Color x:Key="Accent400">#FFA726</Color>
    <Color x:Key="Accent500">#FF9800</Color>
    <Color x:Key="Accent600">#FB8C00</Color>
    <Color x:Key="Accent700">#F57C00</Color>
    <Color x:Key="Accent800">#EF6C00</Color>
    <Color x:Key="Accent900">#E65100</Color>
    
    <!-- Neutral Colors -->
    <Color x:Key="Neutral50">#FAFAFA</Color>
    <Color x:Key="Neutral100">#F5F5F5</Color>
    <Color x:Key="Neutral200">#EEEEEE</Color>
    <Color x:Key="Neutral300">#E0E0E0</Color>
    <Color x:Key="Neutral400">#BDBDBD</Color>
    <Color x:Key="Neutral500">#9E9E9E</Color>
    <Color x:Key="Neutral600">#757575</Color>
    <Color x:Key="Neutral700">#616161</Color>
    <Color x:Key="Neutral800">#424242</Color>
    <Color x:Key="Neutral900">#212121</Color>
    
    <!-- Semantic Colors -->
    <Color x:Key="Success50">#E8F5E8</Color>
    <Color x:Key="Success500">#4CAF50</Color>
    <Color x:Key="Success700">#388E3C</Color>
    
    <Color x:Key="Warning50">#FFF8E1</Color>
    <Color x:Key="Warning500">#FF9800</Color>
    <Color x:Key="Warning700">#F57C00</Color>
    
    <Color x:Key="Error50">#FFEBEE</Color>
    <Color x:Key="Error500">#F44336</Color>
    <Color x:Key="Error700">#D32F2F</Color>
    
    <Color x:Key="Info50">#E3F2FD</Color>
    <Color x:Key="Info500">#2196F3</Color>
    <Color x:Key="Info700">#1976D2</Color>
    
    <!-- Surface Colors -->
    <Color x:Key="Surface">#FFFFFF</Color>
    <Color x:Key="Background">#FAFAFA</Color>
    <Color x:Key="Paper">#FFFFFF</Color>
    
    <!-- Text Colors -->
    <Color x:Key="TextPrimary">#212121</Color>
    <Color x:Key="TextSecondary">#757575</Color>
    <Color x:Key="TextDisabled">#BDBDBD</Color>
    <Color x:Key="TextHint">#9E9E9E</Color>
    
    <!-- Border and Divider -->
    <Color x:Key="Border">#E0E0E0</Color>
    <Color x:Key="Divider">#EEEEEE</Color>
    
    <!-- Elevation Shadows -->
    <Color x:Key="Shadow">#1A000000</Color>
    <Color x:Key="ShadowMedium">#33000000</Color>
    <Color x:Key="ShadowHigh">#4D000000</Color>

    <!-- Modern Brushes -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource Primary500}" />
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource Primary300}" />
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource Primary700}" />
    
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource Secondary500}" />
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="{StaticResource Secondary300}" />
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="{StaticResource Secondary700}" />
    
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource Accent500}" />
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource Accent300}" />
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource Accent700}" />
    
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource Background}" />
    <SolidColorBrush x:Key="PaperBrush" Color="{StaticResource Paper}" />
    
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimary}" />
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondary}" />
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource TextDisabled}" />
    
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource Success500}" />
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource Warning500}" />
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource Error500}" />
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource Info500}" />
    
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource Border}" />
    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource Divider}" />

    <!-- Material Design Compatibility -->
    <SolidColorBrush x:Key="PrimaryHueMidBrush" Color="{StaticResource Primary500}" />
    <SolidColorBrush x:Key="PrimaryHueLightBrush" Color="{StaticResource Primary300}" />
    <SolidColorBrush x:Key="PrimaryHueDarkBrush" Color="{StaticResource Primary700}" />
    <SolidColorBrush x:Key="AccentColorBrush" Color="{StaticResource Accent500}" />
    <SolidColorBrush x:Key="MaterialDesignBackground" Color="{StaticResource Background}" />
    <SolidColorBrush x:Key="MaterialDesignPaper" Color="{StaticResource Paper}" />
    <SolidColorBrush x:Key="MaterialDesignCardBackground" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="MaterialDesignBody" Color="{StaticResource TextPrimary}" />
    <SolidColorBrush x:Key="MaterialDesignBodyLight" Color="{StaticResource TextSecondary}" />
    <SolidColorBrush x:Key="MaterialDesignDivider" Color="{StaticResource Divider}" />
    <SolidColorBrush x:Key="ValidationErrorBrush" Color="{StaticResource Error500}" />

    <!-- Typography Scale -->
    <FontFamily x:Key="PrimaryFont">Segoe UI, Tahoma, Arial</FontFamily>
    <FontFamily x:Key="ArabicFont">Segoe UI, Tahoma, Arial</FontFamily>
    
    <!-- Font Sizes -->
    <sys:Double x:Key="FontSizeCaption" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>
    <sys:Double x:Key="FontSizeBody2" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
    <sys:Double x:Key="FontSizeBody1" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="FontSizeSubtitle2" xmlns:sys="clr-namespace:System;assembly=mscorlib">18</sys:Double>
    <sys:Double x:Key="FontSizeSubtitle1" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    <sys:Double x:Key="FontSizeHeadline6" xmlns:sys="clr-namespace:System;assembly=mscorlib">22</sys:Double>
    <sys:Double x:Key="FontSizeHeadline5" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>
    <sys:Double x:Key="FontSizeHeadline4" xmlns:sys="clr-namespace:System;assembly=mscorlib">28</sys:Double>
    <sys:Double x:Key="FontSizeHeadline3" xmlns:sys="clr-namespace:System;assembly=mscorlib">32</sys:Double>
    <sys:Double x:Key="FontSizeHeadline2" xmlns:sys="clr-namespace:System;assembly=mscorlib">36</sys:Double>
    <sys:Double x:Key="FontSizeHeadline1" xmlns:sys="clr-namespace:System;assembly=mscorlib">40</sys:Double>

    <!-- Spacing Scale -->
    <sys:Double x:Key="SpacingXS" xmlns:sys="clr-namespace:System;assembly=mscorlib">4</sys:Double>
    <sys:Double x:Key="SpacingSM" xmlns:sys="clr-namespace:System;assembly=mscorlib">8</sys:Double>
    <sys:Double x:Key="SpacingMD" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="SpacingLG" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>
    <sys:Double x:Key="SpacingXL" xmlns:sys="clr-namespace:System;assembly=mscorlib">32</sys:Double>
    <sys:Double x:Key="SpacingXXL" xmlns:sys="clr-namespace:System;assembly=mscorlib">48</sys:Double>

    <!-- Border Radius -->
    <CornerRadius x:Key="RadiusXS">2</CornerRadius>
    <CornerRadius x:Key="RadiusSM">4</CornerRadius>
    <CornerRadius x:Key="RadiusMD">8</CornerRadius>
    <CornerRadius x:Key="RadiusLG">12</CornerRadius>
    <CornerRadius x:Key="RadiusXL">16</CornerRadius>
    <CornerRadius x:Key="RadiusRound">50</CornerRadius>

</ResourceDictionary>
