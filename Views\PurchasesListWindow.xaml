<Window x:Class="SalesManagementSystem.Views.PurchasesListWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="قائمة المشتريات"
        Height="700" Width="1100"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="DarkBlue">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="📦" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="قائمة فواتير المشتريات" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Background="LightGray" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBox Width="150" Margin="0,0,10,0" Text="البحث في المشتريات..."/>
                <Button Content="🔍 بحث" Width="80" Margin="0,0,10,0" Background="Blue" Foreground="White"/>

                <TextBlock Text="من تاريخ:" VerticalAlignment="Center" Margin="20,0,5,0"/>
                <DatePicker Width="120" Margin="0,0,10,0"/>

                <TextBlock Text="المورد:" VerticalAlignment="Center" Margin="20,0,5,0"/>
                <ComboBox Width="150" Margin="0,0,10,0">
                    <ComboBoxItem Content="جميع الموردين"/>
                    <ComboBoxItem Content="شركة التقنية المتقدمة"/>
                    <ComboBoxItem Content="مؤسسة الحاسوب"/>
                </ComboBox>

                <Button Content="🔄 تحديث" Width="80" Margin="10,0,0,0" Background="Green" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Purchases DataGrid -->
        <DataGrid Grid.Row="2" Margin="10" AutoGenerateColumns="False" CanUserAddRows="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الفاتورة" Width="100"/>
                <DataGridTextColumn Header="التاريخ" Width="100"/>
                <DataGridTextColumn Header="المورد" Width="150"/>
                <DataGridTextColumn Header="عدد الأصناف" Width="100"/>
                <DataGridTextColumn Header="إجمالي التكلفة" Width="120"/>
                <DataGridTextColumn Header="طريقة الدفع" Width="100"/>
                <DataGridTextColumn Header="تاريخ الاستحقاق" Width="120"/>
                <DataGridTextColumn Header="الحالة" Width="80"/>
                <DataGridTemplateColumn Header="الإجراءات" Width="200">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Content="👁️" Width="30" Height="25" Margin="2"
                                       Background="Blue" Foreground="White" ToolTip="عرض"/>
                                <Button Content="✏️" Width="30" Height="25" Margin="2"
                                       Background="Orange" Foreground="White" ToolTip="تعديل"/>
                                <Button Content="🖨️" Width="30" Height="25" Margin="2"
                                       Background="Green" Foreground="White" ToolTip="طباعة"/>
                                <Button Content="💰" Width="30" Height="25" Margin="2"
                                       Background="Purple" Foreground="White" ToolTip="دفع"/>
                                <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                       Background="Red" Foreground="White" ToolTip="حذف"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Footer -->
        <Border Grid.Row="3" Background="LightGray">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="إجمالي المشتريات: " FontWeight="Bold"/>
                    <TextBlock Text="15" Foreground="Blue" FontWeight="Bold" Margin="0,0,20,0"/>

                    <TextBlock Text="إجمالي التكلفة: " FontWeight="Bold"/>
                    <TextBlock Text="85250.75 دج" Foreground="DarkBlue" FontWeight="Bold" Margin="0,0,20,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="➕ شراء جديد" Width="120" Height="35" Margin="5"
                           Background="DarkBlue" Foreground="White" FontWeight="Bold"/>
                    <Button Content="📊 تقرير المشتريات" Width="130" Height="35" Margin="5"
                           Background="Blue" Foreground="White" FontWeight="Bold"/>
                    <Button Content="❌ إغلاق" Width="80" Height="35" Margin="5"
                           Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
