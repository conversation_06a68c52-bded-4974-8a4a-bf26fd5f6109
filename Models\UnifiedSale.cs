using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج موحد لعرض جميع أنواع المبيعات (فواتير + بيع سريع)
    /// </summary>
    public class UnifiedSale : INotifyPropertyChanged
    {
        private int _id;
        private string _saleNumber = string.Empty;
        private string _saleType = string.Empty;
        private int _customerId;
        private string _customerName = string.Empty;
        private DateTime _saleDate;
        private decimal _subtotal;
        private decimal _discount;
        private decimal _tax;
        private decimal _previousDebt;
        private decimal _totalAmount;
        private decimal _paidAmount;
        private decimal _remainingAmount;
        private string _paymentMethod = string.Empty;
        private string _paymentStatus = string.Empty;
        private string _notes = string.Empty;
        private DateTime _createdAt;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SaleNumber
        {
            get => _saleNumber;
            set
            {
                if (_saleNumber != value)
                {
                    _saleNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SaleType
        {
            get => _saleType;
            set
            {
                if (_saleType != value)
                {
                    _saleType = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CustomerName
        {
            get => _customerName;
            set
            {
                if (_customerName != value)
                {
                    _customerName = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime SaleDate
        {
            get => _saleDate;
            set
            {
                if (_saleDate != value)
                {
                    _saleDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(SaleDateFormatted));
                }
            }
        }

        public decimal Subtotal
        {
            get => _subtotal;
            set
            {
                if (_subtotal != value)
                {
                    _subtotal = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(SubtotalFormatted));
                }
            }
        }

        public decimal Discount
        {
            get => _discount;
            set
            {
                if (_discount != value)
                {
                    _discount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(DiscountFormatted));
                }
            }
        }

        public decimal Tax
        {
            get => _tax;
            set
            {
                if (_tax != value)
                {
                    _tax = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TaxFormatted));
                }
            }
        }

        public decimal PreviousDebt
        {
            get => _previousDebt;
            set
            {
                if (_previousDebt != value)
                {
                    _previousDebt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PreviousDebtFormatted));
                }
            }
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set
            {
                if (_totalAmount != value)
                {
                    _totalAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TotalAmountFormatted));
                }
            }
        }

        public decimal PaidAmount
        {
            get => _paidAmount;
            set
            {
                if (_paidAmount != value)
                {
                    _paidAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PaidAmountFormatted));
                    OnPropertyChanged(nameof(RemainingAmount));
                    OnPropertyChanged(nameof(RemainingAmountFormatted));
                }
            }
        }

        public decimal RemainingAmount
        {
            get => _remainingAmount;
            set
            {
                if (_remainingAmount != value)
                {
                    _remainingAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(RemainingAmountFormatted));
                }
            }
        }

        public string PaymentMethod
        {
            get => _paymentMethod;
            set
            {
                if (_paymentMethod != value)
                {
                    _paymentMethod = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PaymentStatus
        {
            get => _paymentStatus;
            set
            {
                if (_paymentStatus != value)
                {
                    _paymentStatus = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CreatedAtFormatted));
                }
            }
        }

        // خصائص منسقة للعرض
        public string SaleDateFormatted => SaleDate.ToString("yyyy/MM/dd HH:mm");
        public string CreatedAtFormatted => CreatedAt.ToString("yyyy/MM/dd HH:mm");
        public string SubtotalFormatted => Subtotal.ToString("N2");
        public string DiscountFormatted => Discount.ToString("N2");
        public string TaxFormatted => Tax.ToString("N2");
        public string PreviousDebtFormatted => PreviousDebt.ToString("N2");
        public string TotalAmountFormatted => TotalAmount.ToString("N2");
        public string PaidAmountFormatted => PaidAmount.ToString("N2");
        public string RemainingAmountFormatted => RemainingAmount.ToString("N2");

        // خصائص إضافية للعرض
        public string SaleTypeIcon => SaleType == "فاتورة" ? "📄" : "⚡";
        public string PaymentStatusIcon => PaymentStatus == "مدفوع" ? "✅" : 
                                          PaymentStatus == "جزئي" ? "⚠️" : "❌";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
