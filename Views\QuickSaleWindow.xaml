<Window x:Class="SalesManagementSystem.Views.QuickSaleWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🛒 بيع سريع - مسح الكودبار"
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        KeyDown="Window_KeyDown">

    <Window.Resources>
        <!-- Digital Glow Effect for Numbers -->
        <DropShadowEffect x:Key="DigitalGlowEffect"
                         Color="Lime"
                         BlurRadius="12"
                         ShadowDepth="0"
                         Opacity="0.9"/>

        <!-- Strong Digital Glow Effect -->
        <DropShadowEffect x:Key="StrongDigitalGlowEffect"
                         Color="Lime"
                         BlurRadius="25"
                         ShadowDepth="0"
                         Opacity="1.0"/>

        <!-- Digital Number Style -->
        <Style x:Key="DigitalNumberStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas, 'Courier New', monospace"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="Lime"/>
            <Setter Property="Effect" Value="{StaticResource DigitalGlowEffect}"/>
            <Setter Property="Background" Value="Black"/>
            <Setter Property="Padding" Value="8"/>
        </Style>

        <!-- Header Digital Style -->
        <Style x:Key="HeaderDigitalStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas, 'Courier New', monospace"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="Lime"/>
            <Setter Property="Effect" Value="{StaticResource StrongDigitalGlowEffect}"/>
            <Setter Property="FontSize" Value="32"/>
        </Style>
    </Window.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="80"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="DarkGreen">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Title Section -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🛒" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="بيع سريع - مسح الكودبار" FontSize="18"
                              FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock Text="⚡" FontSize="20" Foreground="Yellow" Margin="10,0,0,0"/>
                </StackPanel>

                <!-- Total Amount Section -->
                <Border Grid.Column="1" Background="Black" CornerRadius="15" Padding="25,12"
                       BorderBrush="Lime" BorderThickness="3" MinWidth="200">
                    <Border.Effect>
                        <DropShadowEffect Color="Lime" BlurRadius="15" ShadowDepth="0" Opacity="0.5"/>
                    </Border.Effect>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="💰" FontSize="24" Foreground="Lime" Margin="0,0,15,0" VerticalAlignment="Center">
                            <TextBlock.Effect>
                                <DropShadowEffect Color="Yellow" BlurRadius="8" ShadowDepth="0" Opacity="0.7"/>
                            </TextBlock.Effect>
                        </TextBlock>
                        <TextBlock x:Name="HeaderTotalLabel" Text="0.00 دج"
                                  FontSize="32" FontWeight="Bold" Foreground="Lime"
                                  FontFamily="Consolas, 'Courier New', monospace"
                                  VerticalAlignment="Center"
                                  Effect="{StaticResource StrongDigitalGlowEffect}"
                                  Padding="10,6">
                            <TextBlock.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#002200" Offset="0"/>
                                    <GradientStop Color="#000000" Offset="1"/>
                                </LinearGradientBrush>
                            </TextBlock.Background>
                        </TextBlock>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Barcode Scanner Section -->
        <Border Grid.Row="1" Background="LightGreen" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="🔍 البحث/الكودبار:" FontSize="16" FontWeight="Bold"
                          VerticalAlignment="Center" Margin="0,0,10,0"/>

                <TextBox Grid.Column="1" x:Name="SearchTextBox" FontSize="16" Height="35"
                        Margin="0,0,10,0" Background="White" BorderBrush="DarkGreen" BorderThickness="2"
                        TextChanged="SearchTextBox_TextChanged" KeyDown="SearchTextBox_KeyDown"
                        ToolTip="امسح الكودبار أو ابحث باسم المنتج..."/>

                <Button Grid.Column="2" Content="🔍 بحث يدوي" Width="100" Height="35" Margin="0,0,10,0"
                       Background="Blue" Foreground="White" FontWeight="Bold" Click="ManualSearch_Click"/>

                <Button Grid.Column="3" Content="🗑️ مسح" Width="80" Height="35"
                       Background="Orange" Foreground="White" FontWeight="Bold" Click="ClearBarcode_Click"/>
            </Grid>
        </Border>

        <!-- Products DataGrid -->
        <DataGrid Grid.Row="2" x:Name="ProductsDataGrid" Margin="10" AutoGenerateColumns="False"
                 CanUserAddRows="False" GridLinesVisibility="All" AlternatingRowBackground="LightGray">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الكودبار" Binding="{Binding Barcode}" Width="120" IsReadOnly="True"/>
                <DataGridTextColumn Header="اسم المنتج" Binding="{Binding ProductName}" Width="*" IsReadOnly="True"/>
                <DataGridTextColumn Header="سعر الوحدة" Binding="{Binding UnitPrice}" Width="100" IsReadOnly="True"/>
                <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                <DataGridTextColumn Header="الإجمالي" Binding="{Binding Total}" Width="100" IsReadOnly="True"/>
                <DataGridTemplateColumn Header="الإجراءات" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Content="➕" Width="30" Height="25" Margin="2"
                                       Background="Green" Foreground="White" ToolTip="زيادة الكمية"
                                       Click="IncreaseQuantity_Click"/>
                                <Button Content="➖" Width="30" Height="25" Margin="2"
                                       Background="Orange" Foreground="White" ToolTip="تقليل الكمية"
                                       Click="DecreaseQuantity_Click"/>
                                <Button Content="✏️" Width="30" Height="25" Margin="2"
                                       Background="Blue" Foreground="White" ToolTip="تعديل الكمية"
                                       Click="EditQuantity_Click"/>
                                <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                       Background="Red" Foreground="White" ToolTip="حذف المنتج"
                                       Click="RemoveProduct_Click"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Summary Section -->
        <Border Grid.Row="3" Background="LightBlue" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="عدد الأصناف" FontWeight="Bold" FontSize="14"/>
                    <TextBlock x:Name="ItemsCountLabel" Text="0" FontSize="20" Foreground="Blue" FontWeight="Bold"/>
                </StackPanel>

                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي الكمية" FontWeight="Bold" FontSize="14"/>
                    <TextBlock x:Name="TotalQuantityLabel" Text="0" FontSize="20" Foreground="Green" FontWeight="Bold"/>
                </StackPanel>

                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="المبلغ الإجمالي" FontWeight="Bold" FontSize="14"/>
                    <TextBlock x:Name="TotalAmountLabel" Text="0.00 دج" FontSize="20" Foreground="DarkGreen" FontWeight="Bold"/>
                </StackPanel>

                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="آخر عملية" FontWeight="Bold" FontSize="14"/>
                    <TextBlock x:Name="LastActionLabel" Text="جاهز للمسح" FontSize="16" Foreground="Purple" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Row="4" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💳 دفع نقدي" Width="120" Height="50" Margin="10"
                       Background="DarkGreen" Foreground="White" FontWeight="Bold" FontSize="14"
                       Click="CashPayment_Click"/>
                <Button Content="💰 دفع بالبطاقة" Width="120" Height="50" Margin="10"
                       Background="Blue" Foreground="White" FontWeight="Bold" FontSize="14"
                       Click="CardPayment_Click"/>
                <Button Content="📋 حفظ مؤقت" Width="120" Height="50" Margin="10"
                       Background="Orange" Foreground="White" FontWeight="Bold" FontSize="14"
                       Click="SaveDraft_Click"/>
                <Button Content="🗑️ مسح الكل" Width="120" Height="50" Margin="10"
                       Background="Red" Foreground="White" FontWeight="Bold" FontSize="14"
                       Click="ClearAll_Click"/>
                <Button Content="❌ إغلاق" Width="100" Height="50" Margin="10"
                       Background="Gray" Foreground="White" FontWeight="Bold" FontSize="14"
                       Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
