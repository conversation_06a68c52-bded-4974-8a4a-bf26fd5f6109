using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using OfficeOpenXml;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class SimpleImportService
    {
        public SimpleImportService()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public async Task<List<Product>> ImportProductsFromExcelAsync(string filePath)
        {
            var products = new List<Product>();
            await Task.CompletedTask; // To satisfy async requirement

            try
            {
                using var package = new ExcelPackage(new FileInfo(filePath));
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                    return products;

                // Skip header row, start from row 2
                for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
                {
                    try
                    {
                        var product = new Product
                        {
                            Name = worksheet.Cells[row, 1].Value?.ToString() ?? "",
                            Code = worksheet.Cells[row, 2].Value?.ToString() ?? "",
                            Barcode = worksheet.Cells[row, 3].Value?.ToString() ?? "",
                            CategoryName = worksheet.Cells[row, 4].Value?.ToString() ?? "",
                            Unit = worksheet.Cells[row, 5].Value?.ToString() ?? "قطعة",
                            PurchasePrice = decimal.TryParse(worksheet.Cells[row, 6].Value?.ToString(), out var purchasePrice) ? purchasePrice : 0,
                            SalePrice = decimal.TryParse(worksheet.Cells[row, 7].Value?.ToString(), out var salePrice) ? salePrice : 0,
                            Quantity = int.TryParse(worksheet.Cells[row, 8].Value?.ToString(), out var quantity) ? quantity : 0,
                            MinQuantity = int.TryParse(worksheet.Cells[row, 9].Value?.ToString(), out var minQuantity) ? minQuantity : 0,
                            Description = worksheet.Cells[row, 10].Value?.ToString() ?? ""
                        };

                        if (!string.IsNullOrEmpty(product.Name))
                        {
                            products.Add(product);
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في قراءة الصف {row}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في استيراد المنتجات من Excel");
                throw;
            }

            return products;
        }

        public async Task<List<Product>> ImportProductsFromCsvAsync(string filePath)
        {
            var products = new List<Product>();

            try
            {
                var lines = await File.ReadAllLinesAsync(filePath);

                // Skip header line
                for (int i = 1; i < lines.Length; i++)
                {
                    try
                    {
                        var values = lines[i].Split(',');

                        if (values.Length >= 8)
                        {
                            var product = new Product
                            {
                                Name = values[0].Trim('"'),
                                Code = values[1].Trim('"'),
                                Barcode = values[2].Trim('"'),
                                CategoryName = values[3].Trim('"'),
                                Unit = values[4].Trim('"'),
                                PurchasePrice = decimal.TryParse(values[5], out var purchasePrice) ? purchasePrice : 0,
                                SalePrice = decimal.TryParse(values[6], out var salePrice) ? salePrice : 0,
                                Quantity = int.TryParse(values[7], out var quantity) ? quantity : 0,
                                MinQuantity = values.Length > 8 && int.TryParse(values[8], out var minQuantity) ? minQuantity : 0,
                                Description = values.Length > 9 ? values[9].Trim('"') : ""
                            };

                            if (!string.IsNullOrEmpty(product.Name))
                            {
                                products.Add(product);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في قراءة السطر {i + 1}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في استيراد المنتجات من CSV");
                throw;
            }

            return products;
        }

        public async Task<List<Customer>> ImportCustomersFromExcelAsync(string filePath)
        {
            var customers = new List<Customer>();
            await Task.CompletedTask; // To satisfy async requirement

            try
            {
                using var package = new ExcelPackage(new FileInfo(filePath));
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                    return customers;

                for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
                {
                    try
                    {
                        var customer = new Customer
                        {
                            Name = worksheet.Cells[row, 1].Value?.ToString() ?? "",
                            Phone = worksheet.Cells[row, 2].Value?.ToString() ?? "",
                            Email = worksheet.Cells[row, 3].Value?.ToString() ?? "",
                            Address = worksheet.Cells[row, 4].Value?.ToString() ?? "",
                            Notes = worksheet.Cells[row, 5].Value?.ToString() ?? ""
                        };

                        if (!string.IsNullOrEmpty(customer.Name))
                        {
                            customers.Add(customer);
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في قراءة الصف {row}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في استيراد العملاء من Excel");
                throw;
            }

            return customers;
        }

        public async Task<List<Customer>> ImportCustomersFromCsvAsync(string filePath)
        {
            var customers = new List<Customer>();

            try
            {
                var lines = await File.ReadAllLinesAsync(filePath);

                for (int i = 1; i < lines.Length; i++)
                {
                    try
                    {
                        var values = lines[i].Split(',');

                        if (values.Length >= 4)
                        {
                            var customer = new Customer
                            {
                                Name = values[0].Trim('"'),
                                Phone = values[1].Trim('"'),
                                Email = values[2].Trim('"'),
                                Address = values[3].Trim('"'),
                                Notes = values.Length > 4 ? values[4].Trim('"') : ""
                            };

                            if (!string.IsNullOrEmpty(customer.Name))
                            {
                                customers.Add(customer);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في قراءة السطر {i + 1}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في استيراد العملاء من CSV");
                throw;
            }

            return customers;
        }
    }
}
