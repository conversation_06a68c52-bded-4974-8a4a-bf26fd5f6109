<Window x:Class="SalesManagementSystem.Views.NotificationsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🔔 مركز الإشعارات"
        Height="600" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="3"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                               Background="{TemplateBinding Background}"
                               CornerRadius="3"
                               BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Notification Item Style -->
        <Style x:Key="NotificationItemStyle" TargetType="Border">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="LightGray"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F0F0F0"/>
                    <Setter Property="BorderBrush" Value="Gray"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF673AB7">
            <Grid Margin="15,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🔔" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Text="مركز الإشعارات" FontSize="16" 
                                  FontWeight="Bold" Foreground="White"/>
                        <TextBlock x:Name="NotificationCountLabel" Text="0 إشعار جديد" FontSize="11" 
                                  Foreground="LightGray" Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>
                
                <!-- Unread Count Badge -->
                <Border Grid.Column="1" x:Name="UnreadBadge" Background="Red" CornerRadius="12" 
                       MinWidth="24" Height="24" VerticalAlignment="Center"
                       Visibility="Collapsed">
                    <TextBlock x:Name="UnreadCountText" Text="0" 
                              FontSize="12" FontWeight="Bold" Foreground="White"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
            </Grid>
        </Border>

        <!-- Action Bar -->
        <Border Grid.Row="1" Background="LightGray" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="✅ تحديد الكل كمقروء" Width="130" Height="30" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF2196F3" Click="MarkAllAsRead_Click"/>
                <Button Content="🗑️ مسح القديمة" Width="100" Height="30" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FFFF9800" Click="ClearOld_Click"/>
                <Button Content="🔄 تحديث" Width="70" Height="30" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF9C27B0" Click="Refresh_Click"/>
            </StackPanel>
        </Border>

        <!-- Notifications List -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Margin="5">
            <ItemsControl x:Name="NotificationsItemsControl">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{StaticResource NotificationItemStyle}"
                               MouseLeftButtonUp="NotificationItem_Click"
                               Tag="{Binding Id}">
                            <Border.Background>
                                <Binding Path="TypeBackground" />
                            </Border.Background>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Type Icon -->
                                <TextBlock Grid.Column="0" Grid.Row="0" Grid.RowSpan="2"
                                          Text="{Binding TypeIcon}" FontSize="20" 
                                          VerticalAlignment="Center" Margin="0,0,10,0"/>

                                <!-- Title and Priority -->
                                <StackPanel Grid.Column="1" Grid.Row="0" Orientation="Horizontal">
                                    <TextBlock Text="{Binding Title}" FontSize="13" FontWeight="{Binding FontWeight}"
                                              VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding PriorityIcon}" FontSize="12" 
                                              Margin="5,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Message -->
                                <TextBlock Grid.Column="1" Grid.Row="1" 
                                          Text="{Binding Message}" FontSize="11" 
                                          Foreground="Gray" TextWrapping="Wrap" Margin="0,3,0,0"/>

                                <!-- Time and Category -->
                                <StackPanel Grid.Column="1" Grid.Row="2" Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="{Binding TimeAgo}" FontSize="10" 
                                              Foreground="DarkGray"/>
                                    <TextBlock Text=" • " FontSize="10" Foreground="DarkGray"/>
                                    <TextBlock Text="{Binding Category}" FontSize="10" 
                                              Foreground="DarkGray"/>
                                </StackPanel>

                                <!-- Read Status and Actions -->
                                <StackPanel Grid.Column="2" Grid.Row="0" Grid.RowSpan="3" 
                                           VerticalAlignment="Center" Margin="10,0,0,0">
                                    <TextBlock Text="{Binding ReadStatusIcon}" FontSize="8" 
                                              HorizontalAlignment="Center"/>
                                    <Button Content="❌" Width="20" Height="20" FontSize="8"
                                           Background="Transparent" BorderThickness="0"
                                           Foreground="Gray" Cursor="Hand" Margin="0,5,0,0"
                                           Click="RemoveNotification_Click" Tag="{Binding Id}"
                                           ToolTip="حذف الإشعار"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- Empty State -->
        <StackPanel Grid.Row="2" x:Name="EmptyStatePanel" 
                   HorizontalAlignment="Center" VerticalAlignment="Center"
                   Visibility="Collapsed">
            <TextBlock Text="📭" FontSize="48" HorizontalAlignment="Center" 
                      Foreground="LightGray" Margin="0,0,0,10"/>
            <TextBlock Text="لا توجد إشعارات" FontSize="16" 
                      HorizontalAlignment="Center" Foreground="Gray"/>
            <TextBlock Text="ستظهر الإشعارات الجديدة هنا" FontSize="12" 
                      HorizontalAlignment="Center" Foreground="LightGray" Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="DarkGray" Height="40">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                <TextBlock Text="📊" FontSize="12" Foreground="White" Margin="0,0,5,0"/>
                <TextBlock x:Name="StatusLabel" Text="جاهز" FontSize="12" Foreground="White" Margin="0,0,20,0"/>
                <TextBlock Text="⏰" FontSize="12" Foreground="White" Margin="0,0,5,0"/>
                <TextBlock x:Name="LastUpdateLabel" Text="آخر تحديث: الآن" FontSize="12" Foreground="LightGray"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
