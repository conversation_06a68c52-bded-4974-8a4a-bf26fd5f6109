<UserControl x:Class="SalesManagementSystem.Views.CRMReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ChartBox" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="تقارير وتحليلات CRM" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding ExportReportCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExport" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير التقرير"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding ScheduleReportCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ClockOutline" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="جدولة التقرير"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshDataCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث البيانات"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- فلاتر التقرير -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- تاريخ البداية -->
                <DatePicker Grid.Column="0" 
                           SelectedDate="{Binding FromDate}"
                           materialDesign:HintAssist.Hint="من تاريخ"
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Margin="0,0,8,0"/>

                <!-- تاريخ النهاية -->
                <DatePicker Grid.Column="1" 
                           SelectedDate="{Binding ToDate}"
                           materialDesign:HintAssist.Hint="إلى تاريخ"
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Margin="8,0"/>

                <!-- نوع التقرير -->
                <ComboBox Grid.Column="2" 
                          SelectedItem="{Binding SelectedReportType}"
                          ItemsSource="{Binding ReportTypes}"
                          materialDesign:HintAssist.Hint="نوع التقرير"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Display}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- زر التطبيق -->
                <Button Grid.Column="3" 
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding ApplyFiltersCommand}"
                        Margin="8,0,0,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FilterVariant" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="تطبيق"/>
                    </StackPanel>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- محتوى التقارير -->
        <ScrollViewer Grid.Row="2" Margin="16,8,16,16" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- ملخص الأداء -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Speedometer" Width="24" Height="24" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="ملخص الأداء العام" 
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- مؤشرات الأداء الرئيسية -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- إجمالي العملاء -->
                            <Border Grid.Column="0" 
                                  Background="#E3F2FD" 
                                  CornerRadius="8" 
                                  Padding="16" 
                                  Margin="0,0,8,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="AccountGroup" Width="32" Height="32" 
                                                           Foreground="#1976D2" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding Summary.TotalCustomers}" 
                                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                             HorizontalAlignment="Center"
                                             Foreground="#1976D2"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="إجمالي العملاء" 
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- الفرص المفتوحة -->
                            <Border Grid.Column="1" 
                                  Background="#FFF3E0" 
                                  CornerRadius="8" 
                                  Padding="16" 
                                  Margin="8,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="TrendingUp" Width="32" Height="32" 
                                                           Foreground="#F57C00" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding Summary.OpenOpportunities}" 
                                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                             HorizontalAlignment="Center"
                                             Foreground="#F57C00"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="فرص مفتوحة" 
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- قيمة الأنبوب -->
                            <Border Grid.Column="2" 
                                  Background="#E8F5E8" 
                                  CornerRadius="8" 
                                  Padding="16" 
                                  Margin="8,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="CurrencyUsd" Width="32" Height="32" 
                                                           Foreground="#388E3C" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding Summary.PipelineValue, StringFormat='{}{0:C}'}" 
                                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                             HorizontalAlignment="Center"
                                             Foreground="#388E3C"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="قيمة الأنبوب" 
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- معدل النجاح -->
                            <Border Grid.Column="3" 
                                  Background="#F3E5F5" 
                                  CornerRadius="8" 
                                  Padding="16" 
                                  Margin="8,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Target" Width="32" Height="32" 
                                                           Foreground="#7B1FA2" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding Summary.WinRate, StringFormat='{}{0:F1}%'}" 
                                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                             HorizontalAlignment="Center"
                                             Foreground="#7B1FA2"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="معدل النجاح" 
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- الحملات النشطة -->
                            <Border Grid.Column="4" 
                                  Background="#E0F2F1" 
                                  CornerRadius="8" 
                                  Padding="16" 
                                  Margin="8,0,0,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Bullhorn" Width="32" Height="32" 
                                                           Foreground="#00796B" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding Summary.ActiveCampaigns}" 
                                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                             HorizontalAlignment="Center"
                                             Foreground="#00796B"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="حملات نشطة" 
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Grid>
                </materialDesign:Card>

                <!-- الرسوم البيانية -->
                <Grid Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- رسم بياني لنمو العملاء -->
                    <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" Padding="16">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="300"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="نمو العملاء الشهري" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <lvc:CartesianChart Grid.Row="1" Series="{Binding CustomerGrowthSeries}" 
                                              AxisX="{Binding CustomerGrowthAxisX}"
                                              AxisY="{Binding CustomerGrowthAxisY}"
                                              LegendLocation="Bottom"/>
                        </Grid>
                    </materialDesign:Card>

                    <!-- رسم بياني لمراحل الفرص -->
                    <materialDesign:Card Grid.Column="1" Margin="8,0,0,0" Padding="16">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="300"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="ChartPie" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="توزيع مراحل الفرص" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <lvc:PieChart Grid.Row="1" Series="{Binding OpportunityStagesSeries}" 
                                        LegendLocation="Right"
                                        InnerRadius="40"/>
                        </Grid>
                    </materialDesign:Card>
                </Grid>

                <!-- جداول التحليلات -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- أفضل العملاء -->
                    <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" Padding="16">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="Star" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="أفضل العملاء أداءً" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <DataGrid Grid.Row="1" 
                                    ItemsSource="{Binding TopCustomers}"
                                    AutoGenerateColumns="False"
                                    IsReadOnly="True"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    HeadersVisibility="Column"
                                    GridLinesVisibility="Horizontal"
                                    MaxHeight="300">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="اسم العميل" Binding="{Binding Name}" Width="*"/>
                                    <DataGridTextColumn Header="إجمالي المشتريات" Binding="{Binding TotalPurchases, StringFormat='{}{0:C}'}" Width="Auto"/>
                                    <DataGridTextColumn Header="عدد الطلبات" Binding="{Binding TotalOrders}" Width="Auto"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </materialDesign:Card>

                    <!-- أفضل الفرص -->
                    <materialDesign:Card Grid.Column="1" Margin="8,0,0,0" Padding="16">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="TrendingUp" Width="20" Height="20" 
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="أفضل الفرص قيمة" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <DataGrid Grid.Row="1" 
                                    ItemsSource="{Binding TopOpportunities}"
                                    AutoGenerateColumns="False"
                                    IsReadOnly="True"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    HeadersVisibility="Column"
                                    GridLinesVisibility="Horizontal"
                                    MaxHeight="300">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="عنوان الفرصة" Binding="{Binding Title}" Width="*"/>
                                    <DataGridTextColumn Header="القيمة المقدرة" Binding="{Binding EstimatedValue, StringFormat='{}{0:C}'}" Width="Auto"/>
                                    <DataGridTextColumn Header="الاحتمالية" Binding="{Binding ProbabilityPercentage, StringFormat='{}{0}%'}" Width="Auto"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </materialDesign:Card>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Grid Grid.RowSpan="3" 
              Background="White" 
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري تحميل التقارير..." 
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
