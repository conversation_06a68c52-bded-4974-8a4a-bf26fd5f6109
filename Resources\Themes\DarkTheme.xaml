<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Dark Theme Colors -->
    <Color x:Key="PrimaryColor">#FF1976D2</Color>
    <Color x:Key="PrimaryLightColor">#FF42A5F5</Color>
    <Color x:Key="PrimaryDarkColor">#FF0D47A1</Color>
    <Color x:Key="AccentColor">#FFFF5722</Color>
    <Color x:Key="AccentLightColor">#FFFF8A65</Color>
    <Color x:Key="AccentDarkColor">#FFD84315</Color>
    
    <!-- Dark Background Colors -->
    <Color x:Key="BackgroundColor">#FF121212</Color>
    <Color x:Key="SurfaceColor">#FF1E1E1E</Color>
    <Color x:Key="CardColor">#FF2D2D2D</Color>
    <Color x:Key="DialogColor">#FF424242</Color>
    
    <!-- Dark Text Colors -->
    <Color x:Key="OnPrimaryColor">#FFFFFFFF</Color>
    <Color x:Key="OnSecondaryColor">#FFFFFFFF</Color>
    <Color x:Key="OnBackgroundColor">#FFFFFFFF</Color>
    <Color x:Key="OnSurfaceColor">#FFFFFFFF</Color>
    <Color x:Key="OnErrorColor">#FFFFFFFF</Color>
    
    <!-- Status Colors -->
    <Color x:Key="SuccessColor">#FF4CAF50</Color>
    <Color x:Key="WarningColor">#FFFF9800</Color>
    <Color x:Key="ErrorColor">#FFF44336</Color>
    <Color x:Key="InfoColor">#FF2196F3</Color>
    
    <!-- Border and Divider Colors -->
    <Color x:Key="BorderColor">#FF616161</Color>
    <Color x:Key="DividerColor">#FF424242</Color>
    <Color x:Key="DisabledColor">#FF757575</Color>
    
    <!-- Brushes -->
    <SolidColorBrush x:Key="PrimaryHueMidBrush" Color="{StaticResource PrimaryColor}" />
    <SolidColorBrush x:Key="PrimaryHueLightBrush" Color="{StaticResource PrimaryLightColor}" />
    <SolidColorBrush x:Key="PrimaryHueDarkBrush" Color="{StaticResource PrimaryDarkColor}" />
    <SolidColorBrush x:Key="AccentColorBrush" Color="{StaticResource AccentColor}" />
    <SolidColorBrush x:Key="AccentColorBrush2" Color="{StaticResource AccentLightColor}" />
    <SolidColorBrush x:Key="AccentColorBrush3" Color="{StaticResource AccentDarkColor}" />
    
    <SolidColorBrush x:Key="MaterialDesignBackground" Color="{StaticResource BackgroundColor}" />
    <SolidColorBrush x:Key="MaterialDesignPaper" Color="{StaticResource SurfaceColor}" />
    <SolidColorBrush x:Key="MaterialDesignCardBackground" Color="{StaticResource CardColor}" />
    <SolidColorBrush x:Key="MaterialDesignToolBarBackground" Color="{StaticResource SurfaceColor}" />
    <SolidColorBrush x:Key="MaterialDesignBody" Color="{StaticResource OnBackgroundColor}" />
    <SolidColorBrush x:Key="MaterialDesignBodyLight" Color="#FFBDBDBD" />
    <SolidColorBrush x:Key="MaterialDesignColumnHeader" Color="#FF9E9E9E" />
    <SolidColorBrush x:Key="MaterialDesignCheckBoxOff" Color="#FF757575" />
    <SolidColorBrush x:Key="MaterialDesignCheckBoxDisabled" Color="#FF424242" />
    <SolidColorBrush x:Key="MaterialDesignTextBoxBorder" Color="{StaticResource BorderColor}" />
    <SolidColorBrush x:Key="MaterialDesignDivider" Color="{StaticResource DividerColor}" />
    <SolidColorBrush x:Key="MaterialDesignSelection" Color="#FF424242" />
    
    <!-- Status Brushes -->
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}" />
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}" />
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}" />
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}" />
    
    <!-- Button Styles -->
    <Style x:Key="DarkButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryHueMidBrush}" />
        <Setter Property="Foreground" Value="{StaticResource OnPrimaryColor}" />
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryHueMidBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="16,8" />
        <Setter Property="FontWeight" Value="Medium" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryHueLightBrush}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryHueDarkBrush}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource DisabledColor}" />
                            <Setter Property="Foreground" Value="#FF9E9E9E" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- TextBox Styles -->
    <Style x:Key="DarkTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="{StaticResource MaterialDesignPaper}" />
        <Setter Property="Foreground" Value="{StaticResource MaterialDesignBody}" />
        <Setter Property="BorderBrush" Value="{StaticResource MaterialDesignTextBoxBorder}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="8" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Margin="{TemplateBinding Padding}"
                                    VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryHueMidBrush}" />
                            <Setter Property="BorderThickness" Value="2" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- DataGrid Styles -->
    <Style x:Key="DarkDataGridStyle" TargetType="DataGrid">
        <Setter Property="Background" Value="{StaticResource MaterialDesignPaper}" />
        <Setter Property="Foreground" Value="{StaticResource MaterialDesignBody}" />
        <Setter Property="BorderBrush" Value="{StaticResource MaterialDesignDivider}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="GridLinesVisibility" Value="Horizontal" />
        <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource MaterialDesignDivider}" />
        <Setter Property="RowBackground" Value="Transparent" />
        <Setter Property="AlternatingRowBackground" Value="#FF2A2A2A" />
        <Setter Property="HeadersVisibility" Value="Column" />
        <Setter Property="AutoGenerateColumns" Value="False" />
        <Setter Property="CanUserAddRows" Value="False" />
        <Setter Property="CanUserDeleteRows" Value="False" />
        <Setter Property="IsReadOnly" Value="True" />
        <Setter Property="SelectionMode" Value="Single" />
        <Setter Property="SelectionUnit" Value="FullRow" />
    </Style>

</ResourceDictionary>
