using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج المدفوعات المتقدم
    /// </summary>
    public class Payment : INotifyPropertyChanged
    {
        private int _id;
        private string _paymentNumber = string.Empty;
        private decimal _amount;
        private PaymentType _paymentType = PaymentType.Cash;
        private PaymentStatus _status = PaymentStatus.Pending;
        private PaymentDirection _direction = PaymentDirection.Incoming;
        private int? _customerId;
        private int? _supplierId;
        private int? _invoiceId;
        private int? _saleId;
        private string _description = string.Empty;
        private string _reference = string.Empty;
        private DateTime _paymentDate = DateTime.Now;
        private DateTime? _dueDate;
        private string _notes = string.Empty;
        private string _createdBy = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private bool _isRecurring;
        private RecurringType? _recurringType;
        private int? _recurringInterval;
        private DateTime? _nextPaymentDate;
        private decimal _paidAmount;
        private decimal _remainingAmount;

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PaymentNumber
        {
            get => _paymentNumber;
            set
            {
                if (_paymentNumber != value)
                {
                    _paymentNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Amount
        {
            get => _amount;
            set
            {
                if (_amount != value)
                {
                    _amount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAmount));
                    UpdateRemainingAmount();
                }
            }
        }

        public PaymentType PaymentType
        {
            get => _paymentType;
            set
            {
                if (_paymentType != value)
                {
                    _paymentType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PaymentTypeDisplay));
                    OnPropertyChanged(nameof(PaymentTypeIcon));
                }
            }
        }

        public PaymentStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(CanEdit));
                    OnPropertyChanged(nameof(CanCancel));
                }
            }
        }

        public PaymentDirection Direction
        {
            get => _direction;
            set
            {
                if (_direction != value)
                {
                    _direction = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(DirectionDisplay));
                    OnPropertyChanged(nameof(DirectionIcon));
                    OnPropertyChanged(nameof(DirectionColor));
                }
            }
        }

        public int? CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? SupplierId
        {
            get => _supplierId;
            set
            {
                if (_supplierId != value)
                {
                    _supplierId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? InvoiceId
        {
            get => _invoiceId;
            set
            {
                if (_invoiceId != value)
                {
                    _invoiceId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? SaleId
        {
            get => _saleId;
            set
            {
                if (_saleId != value)
                {
                    _saleId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Reference
        {
            get => _reference;
            set
            {
                if (_reference != value)
                {
                    _reference = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime PaymentDate
        {
            get => _paymentDate;
            set
            {
                if (_paymentDate != value)
                {
                    _paymentDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedPaymentDate));
                    OnPropertyChanged(nameof(IsOverdue));
                }
            }
        }

        public DateTime? DueDate
        {
            get => _dueDate;
            set
            {
                if (_dueDate != value)
                {
                    _dueDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDueDate));
                    OnPropertyChanged(nameof(IsOverdue));
                    OnPropertyChanged(nameof(DaysUntilDue));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsRecurring
        {
            get => _isRecurring;
            set
            {
                if (_isRecurring != value)
                {
                    _isRecurring = value;
                    OnPropertyChanged();
                }
            }
        }

        public RecurringType? RecurringType
        {
            get => _recurringType;
            set
            {
                if (_recurringType != value)
                {
                    _recurringType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(RecurringTypeDisplay));
                }
            }
        }

        public int? RecurringInterval
        {
            get => _recurringInterval;
            set
            {
                if (_recurringInterval != value)
                {
                    _recurringInterval = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? NextPaymentDate
        {
            get => _nextPaymentDate;
            set
            {
                if (_nextPaymentDate != value)
                {
                    _nextPaymentDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedNextPaymentDate));
                }
            }
        }

        public decimal PaidAmount
        {
            get => _paidAmount;
            set
            {
                if (_paidAmount != value)
                {
                    _paidAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedPaidAmount));
                    UpdateRemainingAmount();
                }
            }
        }

        public decimal RemainingAmount
        {
            get => _remainingAmount;
            set
            {
                if (_remainingAmount != value)
                {
                    _remainingAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedRemainingAmount));
                    OnPropertyChanged(nameof(IsFullyPaid));
                    OnPropertyChanged(nameof(PaymentProgress));
                }
            }
        }

        #endregion

        #region Calculated Properties

        public string FormattedAmount => Amount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedPaidAmount => PaidAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedRemainingAmount => RemainingAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedPaymentDate => PaymentDate.ToString("dd/MM/yyyy");
        public string FormattedDueDate => DueDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedNextPaymentDate => NextPaymentDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");

        public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.Now && Status != PaymentStatus.Paid;
        public bool IsFullyPaid => RemainingAmount <= 0;
        public bool CanEdit => Status == PaymentStatus.Pending || Status == PaymentStatus.PartiallyPaid;
        public bool CanCancel => Status != PaymentStatus.Paid && Status != PaymentStatus.Cancelled;

        public int DaysUntilDue
        {
            get
            {
                if (!DueDate.HasValue) return 0;
                return (int)(DueDate.Value - DateTime.Now).TotalDays;
            }
        }

        public double PaymentProgress
        {
            get
            {
                if (Amount == 0) return 0;
                return (double)(PaidAmount / Amount) * 100;
            }
        }

        public string PaymentTypeDisplay
        {
            get
            {
                return PaymentType switch
                {
                    PaymentType.Cash => "نقدي",
                    PaymentType.Card => "بطاقة",
                    PaymentType.BankTransfer => "تحويل بنكي",
                    PaymentType.Check => "شيك",
                    PaymentType.Credit => "آجل",
                    PaymentType.Digital => "محفظة رقمية",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    PaymentStatus.Pending => "معلق",
                    PaymentStatus.Paid => "مدفوع",
                    PaymentStatus.PartiallyPaid => "مدفوع جزئياً",
                    PaymentStatus.Overdue => "متأخر",
                    PaymentStatus.Cancelled => "ملغي",
                    PaymentStatus.Refunded => "مسترد",
                    _ => "غير محدد"
                };
            }
        }

        public string DirectionDisplay
        {
            get
            {
                return Direction switch
                {
                    PaymentDirection.Incoming => "وارد",
                    PaymentDirection.Outgoing => "صادر",
                    _ => "غير محدد"
                };
            }
        }

        public string RecurringTypeDisplay
        {
            get
            {
                return RecurringType switch
                {
                    Models.RecurringType.Daily => "يومي",
                    Models.RecurringType.Weekly => "أسبوعي",
                    Models.RecurringType.Monthly => "شهري",
                    Models.RecurringType.Quarterly => "ربع سنوي",
                    Models.RecurringType.Yearly => "سنوي",
                    _ => "غير محدد"
                };
            }
        }

        public string PaymentTypeIcon
        {
            get
            {
                return PaymentType switch
                {
                    PaymentType.Cash => "Cash",
                    PaymentType.Card => "CreditCard",
                    PaymentType.BankTransfer => "Bank",
                    PaymentType.Check => "CheckboxMarked",
                    PaymentType.Credit => "ClockOutline",
                    PaymentType.Digital => "Wallet",
                    _ => "CurrencyUsd"
                };
            }
        }

        public string DirectionIcon
        {
            get
            {
                return Direction switch
                {
                    PaymentDirection.Incoming => "ArrowDown",
                    PaymentDirection.Outgoing => "ArrowUp",
                    _ => "SwapHorizontal"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    PaymentStatus.Paid => "Green",
                    PaymentStatus.PartiallyPaid => "Orange",
                    PaymentStatus.Pending => "Blue",
                    PaymentStatus.Overdue => "Red",
                    PaymentStatus.Cancelled => "Gray",
                    PaymentStatus.Refunded => "Purple",
                    _ => "Gray"
                };
            }
        }

        public string DirectionColor
        {
            get
            {
                return Direction switch
                {
                    PaymentDirection.Incoming => "Green",
                    PaymentDirection.Outgoing => "Red",
                    _ => "Gray"
                };
            }
        }

        #endregion

        #region Methods

        private void UpdateRemainingAmount()
        {
            RemainingAmount = Amount - PaidAmount;
        }

        public void MarkAsPaid()
        {
            PaidAmount = Amount;
            Status = PaymentStatus.Paid;
            UpdatedAt = DateTime.Now;
        }

        public void AddPartialPayment(decimal amount)
        {
            PaidAmount += amount;
            if (PaidAmount >= Amount)
            {
                PaidAmount = Amount;
                Status = PaymentStatus.Paid;
            }
            else
            {
                Status = PaymentStatus.PartiallyPaid;
            }
            UpdatedAt = DateTime.Now;
        }

        public void Cancel()
        {
            Status = PaymentStatus.Cancelled;
            UpdatedAt = DateTime.Now;
        }

        public void Refund(decimal amount = 0)
        {
            if (amount == 0) amount = PaidAmount;
            PaidAmount -= amount;
            if (PaidAmount <= 0)
            {
                PaidAmount = 0;
                Status = PaymentStatus.Refunded;
            }
            else
            {
                Status = PaymentStatus.PartiallyPaid;
            }
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Enums

    public enum PaymentType
    {
        Cash,           // نقدي
        Card,           // بطاقة
        BankTransfer,   // تحويل بنكي
        Check,          // شيك
        Credit,         // آجل
        Digital         // محفظة رقمية
    }

    public enum PaymentStatus
    {
        Pending,        // معلق
        Paid,           // مدفوع
        PartiallyPaid,  // مدفوع جزئياً
        Overdue,        // متأخر
        Cancelled,      // ملغي
        Refunded        // مسترد
    }

    public enum PaymentDirection
    {
        Incoming,       // وارد (من العملاء)
        Outgoing        // صادر (للموردين)
    }

    public enum RecurringType
    {
        Daily,          // يومي
        Weekly,         // أسبوعي
        Monthly,        // شهري
        Quarterly,      // ربع سنوي
        Yearly          // سنوي
    }

    #endregion

    #region Validation

    public class PaymentValidator : AbstractValidator<Payment>
    {
        public PaymentValidator()
        {
            RuleFor(p => p.Amount)
                .GreaterThan(0).WithMessage("المبلغ يجب أن يكون أكبر من صفر");

            RuleFor(p => p.PaymentNumber)
                .NotEmpty().WithMessage("رقم المدفوعة مطلوب")
                .MaximumLength(50).WithMessage("رقم المدفوعة لا يمكن أن يتجاوز 50 حرف");

            RuleFor(p => p.Description)
                .NotEmpty().WithMessage("الوصف مطلوب")
                .MaximumLength(500).WithMessage("الوصف لا يمكن أن يتجاوز 500 حرف");

            RuleFor(p => p.PaymentDate)
                .NotEmpty().WithMessage("تاريخ الدفع مطلوب");

            RuleFor(p => p.DueDate)
                .GreaterThanOrEqualTo(p => p.PaymentDate)
                .When(p => p.DueDate.HasValue)
                .WithMessage("تاريخ الاستحقاق يجب أن يكون بعد تاريخ الدفع");

            RuleFor(p => p.PaidAmount)
                .GreaterThanOrEqualTo(0).WithMessage("المبلغ المدفوع لا يمكن أن يكون سالب")
                .LessThanOrEqualTo(p => p.Amount).WithMessage("المبلغ المدفوع لا يمكن أن يتجاوز المبلغ الإجمالي");
        }
    }

    #endregion
}
