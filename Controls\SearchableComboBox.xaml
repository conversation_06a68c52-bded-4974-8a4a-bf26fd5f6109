<UserControl x:Class="SalesManagementSystem.Controls.SearchableComboBox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
        
        <Style x:Key="SearchComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="IsEditable" Value="True"/>
            <Setter Property="IsTextSearchEnabled" Value="False"/>
            <Setter Property="StaysOpenOnEdit" Value="True"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <ComboBox x:Name="MainComboBox"
                 Grid.Column="0"
                 Style="{StaticResource SearchComboBoxStyle}"
                 ItemsSource="{Binding FilteredItems, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 SelectedItem="{Binding SelectedItem, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 Text="{Binding SearchText, RelativeSource={RelativeSource AncestorType=UserControl}, UpdateSourceTrigger=PropertyChanged}"
                 materialDesign:HintAssist.Hint="{Binding Hint, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 DisplayMemberPath="{Binding DisplayMemberPath, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 TextSearch.TextPath="{Binding DisplayMemberPath, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 PreviewKeyDown="MainComboBox_PreviewKeyDown"
                 SelectionChanged="MainComboBox_SelectionChanged"
                 DropDownOpened="MainComboBox_DropDownOpened"
                 DropDownClosed="MainComboBox_DropDownClosed">
            
            <ComboBox.ItemTemplate>
                <DataTemplate>
                    <Grid Margin="4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Main content -->
                        <StackPanel Grid.Column="0">
                            <TextBlock x:Name="PrimaryText" 
                                      Text="{Binding Name}" 
                                      FontWeight="Medium"
                                      TextTrimming="CharacterEllipsis"/>
                            <TextBlock x:Name="SecondaryText"
                                      Text="{Binding Code}"
                                      Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                      Opacity="0.7"
                                      Visibility="{Binding Code, Converter={StaticResource BoolToVisConverter}}"/>
                        </StackPanel>
                        
                        <!-- Additional info -->
                        <StackPanel Grid.Column="1" 
                                   Orientation="Horizontal"
                                   VerticalAlignment="Center">
                            <!-- Stock indicator for products -->
                            <Border Background="{DynamicResource PrimaryHueLightBrush}"
                                   CornerRadius="10"
                                   Padding="6,2"
                                   Margin="4,0"
                                   Visibility="{Binding Quantity, Converter={StaticResource BoolToVisConverter}}">
                                <TextBlock Text="{Binding Quantity}"
                                          Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                          Foreground="White"
                                          FontSize="10"/>
                            </Border>
                            
                            <!-- Balance indicator for customers -->
                            <Border Background="{DynamicResource SecondaryHueLightBrush}"
                                   CornerRadius="10"
                                   Padding="6,2"
                                   Margin="4,0"
                                   Visibility="{Binding Balance, Converter={StaticResource BoolToVisConverter}}">
                                <TextBlock Text="{Binding Balance, StringFormat=F2}"
                                          Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                          Foreground="White"
                                          FontSize="10"/>
                            </Border>
                        </StackPanel>
                    </Grid>
                    
                    <DataTemplate.Triggers>
                        <!-- Highlight search matches -->
                        <DataTrigger Binding="{Binding IsHighlighted}" Value="True">
                            <Setter TargetName="PrimaryText" Property="Background" Value="{DynamicResource PrimaryHueLightBrush}"/>
                            <Setter TargetName="PrimaryText" Property="Foreground" Value="White"/>
                        </DataTrigger>
                    </DataTemplate.Triggers>
                </DataTemplate>
            </ComboBox.ItemTemplate>
        </ComboBox>

        <!-- Clear/Add button -->
        <Button x:Name="ActionButton"
               Grid.Column="1"
               Style="{DynamicResource MaterialDesignIconButton}"
               Width="40" Height="40"
               VerticalAlignment="Center"
               Margin="8,0,0,0"
               Click="ActionButton_Click"
               ToolTip="{Binding ActionButtonTooltip, RelativeSource={RelativeSource AncestorType=UserControl}}">
            <materialDesign:PackIcon x:Name="ActionIcon"
                                   Kind="{Binding ActionButtonIcon, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                   Width="20" Height="20"/>
        </Button>

        <!-- Loading indicator -->
        <ProgressBar Grid.Column="0"
                    Style="{DynamicResource MaterialDesignLinearProgressBar}"
                    IsIndeterminate="True"
                    Height="2"
                    VerticalAlignment="Bottom"
                    Visibility="{Binding IsLoading, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BoolToVisConverter}}"/>

        <!-- No results message -->
        <Border Grid.Column="0"
               Background="{DynamicResource MaterialDesignSelection}"
               CornerRadius="4"
               Padding="12,8"
               Margin="0,60,0,0"
               VerticalAlignment="Top"
               Visibility="{Binding ShowNoResults, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BoolToVisConverter}}">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Information" 
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"
                                       Opacity="0.7"/>
                <TextBlock Text="لا توجد نتائج مطابقة للبحث"
                          Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
