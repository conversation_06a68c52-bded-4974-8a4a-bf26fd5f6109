using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.ViewModels;
using SalesManagementSystem.Controls;

namespace SalesManagementSystem.Views.Dialogs
{
    public partial class SaleDialog : Window
    {
        private readonly SaleDialogViewModel _viewModel;

        public Sale? Result { get; private set; }

        public SaleDialog(Sale? existingSale = null)
        {
            InitializeComponent();

            _viewModel = existingSale != null
                ? new SaleDialogViewModel(existingSale)
                : new SaleDialogViewModel();

            DataContext = _viewModel;

            // Subscribe to events
            _viewModel.SaleSaved += OnSaleSaved;
            _viewModel.RequestClose += OnRequestClose;
        }

        #region Event Handlers

        private void OnSaleSaved(Sale sale)
        {
            Result = sale;
            DialogResult = true;
        }

        private void OnRequestClose(bool result)
        {
            DialogResult = result;
            Close();
        }

        private void CustomerComboBox_AddNewRequested(object sender, object e)
        {
            _viewModel.AddCustomerCommand.Execute();
        }

        private void ProductComboBox_AddNewRequested(object sender, object e)
        {
            _viewModel.AddProductCommand.Execute();
        }

        private void BarcodeScanner_ProductFound(object sender, ProductFoundEventArgs e)
        {
            // تحديد المنتج المعثور عليه في ComboBox
            _viewModel.SelectedProduct = e.Product;
        }

        private void BarcodeScanner_ProductSelected(object sender, ProductSelectedEventArgs e)
        {
            if (e.Action == ProductSelectionAction.AddToSale)
            {
                // تحديد المنتج وإضافته للفاتورة
                _viewModel.SelectedProduct = e.Product;
                _viewModel.ItemQuantity = 1; // كمية افتراضية
                _viewModel.AddItemCommand.Execute();

                // مسح نتائج الماسح
                BarcodeScanner.ClearResults();
            }
            else if (e.Action == ProductSelectionAction.ViewDetails)
            {
                // عرض تفاصيل المنتج (يمكن تنفيذه لاحقاً)
                MessageBox.Show($"تفاصيل المنتج:\nالاسم: {e.Product.Name}\nالكود: {e.Product.Code}\nالسعر: {e.Product.SalePrice:F2}",
                    "تفاصيل المنتج", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // Unsubscribe from events
            _viewModel.SaleSaved -= OnSaleSaved;
            _viewModel.RequestClose -= OnRequestClose;

            base.OnClosed(e);
        }

        #endregion
    }
}
