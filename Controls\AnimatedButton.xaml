<UserControl x:Class="SalesManagementSystem.Controls.AnimatedButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <UserControl.Resources>
        <!-- Button Animations -->
        <Storyboard x:Key="HoverInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1.05" Duration="0:0:0.15">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1.05" Duration="0:0:0.15">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.BlurRadius)"
                           To="20" Duration="0:0:0.15"/>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.ShadowDepth)"
                           To="6" Duration="0:0:0.15"/>
        </Storyboard>

        <Storyboard x:Key="HoverOutAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1" Duration="0:0:0.15">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1" Duration="0:0:0.15">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.BlurRadius)"
                           To="12" Duration="0:0:0.15"/>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.ShadowDepth)"
                           To="3" Duration="0:0:0.15"/>
        </Storyboard>

        <Storyboard x:Key="PressAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="0.98" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="0.98" Duration="0:0:0.1"/>
        </Storyboard>

        <Storyboard x:Key="ReleaseAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1.05" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1.05" Duration="0:0:0.1"/>
        </Storyboard>

        <!-- Loading Animation -->
        <Storyboard x:Key="LoadingAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                           From="0" To="360" Duration="0:0:1"/>
        </Storyboard>

        <!-- Success Animation -->
        <Storyboard x:Key="SuccessAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1" To="1.1" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1" To="1.1" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1.1" To="1" Duration="0:0:0.2" BeginTime="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1.1" To="1" Duration="0:0:0.2" BeginTime="0:0:0.1"/>
        </Storyboard>
    </UserControl.Resources>

    <Border x:Name="ButtonBorder"
            Background="{Binding ButtonBackground, RelativeSource={RelativeSource AncestorType=UserControl}}"
            CornerRadius="{Binding CornerRadius, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Padding="{Binding ButtonPadding, RelativeSource={RelativeSource AncestorType=UserControl}}"
            MinWidth="{Binding MinWidth, RelativeSource={RelativeSource AncestorType=UserControl}}"
            MinHeight="{Binding MinHeight, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Cursor="Hand"
            RenderTransformOrigin="0.5,0.5">

        <Border.RenderTransform>
            <ScaleTransform ScaleX="1" ScaleY="1"/>
        </Border.RenderTransform>

        <Border.Effect>
            <DropShadowEffect BlurRadius="12"
                            ShadowDepth="3"
                            Direction="270"
                            Opacity="0.2"
                            Color="#000000"/>
        </Border.Effect>

        <Grid>
            <!-- Normal Content -->
            <StackPanel x:Name="NormalContent"
                       Orientation="{Binding ContentOrientation, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center">

                <!-- Icon -->
                <materialDesign:PackIcon x:Name="ButtonIcon"
                                       Kind="{Binding IconKind, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Width="{Binding IconSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Height="{Binding IconSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Foreground="{Binding ButtonForeground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Margin="{Binding IconMargin, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Visibility="{Binding IconKind, RelativeSource={RelativeSource AncestorType=UserControl},
                                                  Converter={StaticResource NullToVisibilityConverter}}"/>

                <!-- Text -->
                <TextBlock x:Name="ButtonTextBlock"
                          Text="{Binding ButtonText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          FontSize="{Binding FontSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          FontWeight="{Binding FontWeight, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          Foreground="{Binding ButtonForeground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Visibility="{Binding ButtonText, RelativeSource={RelativeSource AncestorType=UserControl},
                                     Converter={StaticResource NullToVisibilityConverter}}"/>
            </StackPanel>

            <!-- Loading Content -->
            <StackPanel x:Name="LoadingContent"
                       Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Visibility="Collapsed">

                <materialDesign:PackIcon Kind="Loading"
                                       Width="20" Height="20"
                                       Foreground="{Binding ButtonForeground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       RenderTransformOrigin="0.5,0.5">
                    <materialDesign:PackIcon.RenderTransform>
                        <RotateTransform Angle="0"/>
                    </materialDesign:PackIcon.RenderTransform>
                </materialDesign:PackIcon>

                <TextBlock Text="{Binding LoadingText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          FontSize="{Binding FontSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          Foreground="{Binding ButtonForeground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          Margin="8,0,0,0"
                          Visibility="{Binding LoadingText, RelativeSource={RelativeSource AncestorType=UserControl},
                                     Converter={StaticResource NullToVisibilityConverter}}"/>
            </StackPanel>

            <!-- Success Content -->
            <StackPanel x:Name="SuccessContent"
                       Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Visibility="Collapsed">

                <materialDesign:PackIcon Kind="Check"
                                       Width="20" Height="20"
                                       Foreground="{DynamicResource SuccessBrush}"
                                       RenderTransformOrigin="0.5,0.5">
                    <materialDesign:PackIcon.RenderTransform>
                        <ScaleTransform ScaleX="1" ScaleY="1"/>
                    </materialDesign:PackIcon.RenderTransform>
                </materialDesign:PackIcon>

                <TextBlock Text="{Binding SuccessText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          FontSize="{Binding FontSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          Foreground="{DynamicResource SuccessBrush}"
                          Margin="8,0,0,0"
                          Visibility="{Binding SuccessText, RelativeSource={RelativeSource AncestorType=UserControl},
                                     Converter={StaticResource NullToVisibilityConverter}}"/>
            </StackPanel>

            <!-- Error Content -->
            <StackPanel x:Name="ErrorContent"
                       Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Visibility="Collapsed">

                <materialDesign:PackIcon Kind="AlertCircle"
                                       Width="20" Height="20"
                                       Foreground="{DynamicResource ErrorBrush}"/>

                <TextBlock Text="{Binding ErrorText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          FontSize="{Binding FontSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          Foreground="{DynamicResource ErrorBrush}"
                          Margin="8,0,0,0"
                          Visibility="{Binding ErrorText, RelativeSource={RelativeSource AncestorType=UserControl},
                                     Converter={StaticResource NullToVisibilityConverter}}"/>
            </StackPanel>

            <!-- Ripple Effect -->
            <Border x:Name="RippleContainer"
                    Background="Transparent"
                    CornerRadius="{Binding CornerRadius, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    ClipToBounds="True">
                <Ellipse x:Name="RippleEllipse"
                         Fill="{Binding RippleColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                         Opacity="0"
                         RenderTransformOrigin="0.5,0.5">
                    <Ellipse.RenderTransform>
                        <ScaleTransform ScaleX="0" ScaleY="0"/>
                    </Ellipse.RenderTransform>
                </Ellipse>
            </Border>
        </Grid>
    </Border>

</UserControl>
