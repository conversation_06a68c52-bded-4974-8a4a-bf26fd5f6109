using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views.Dialogs
{
    public partial class ChangePasswordDialog : Window
    {
        private readonly ChangePasswordViewModel _viewModel;

        public bool PasswordChanged { get; private set; }

        public ChangePasswordDialog(User user)
        {
            InitializeComponent();
            
            _viewModel = new ChangePasswordViewModel(user);
            DataContext = _viewModel;
            
            // Subscribe to events
            _viewModel.PasswordChanged += OnPasswordChanged;
            _viewModel.RequestClose += OnRequestClose;
            
            // Focus on current password if required
            Loaded += (s, e) => 
            {
                if (_viewModel.RequireCurrentPassword)
                    CurrentPasswordBox.Focus();
                else
                    NewPasswordBox.Focus();
            };
        }

        #region Event Handlers

        private void OnPasswordChanged()
        {
            PasswordChanged = true;
            DialogResult = true;
        }

        private void OnRequestClose(bool result)
        {
            DialogResult = result;
            Close();
        }

        private void CurrentPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox)
            {
                _viewModel.CurrentPassword = passwordBox.Password;
            }
        }

        private void NewPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox)
            {
                _viewModel.NewPassword = passwordBox.Password;
            }
        }

        private void ConfirmPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox)
            {
                _viewModel.ConfirmPassword = passwordBox.Password;
            }
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // Unsubscribe from events
            _viewModel.PasswordChanged -= OnPasswordChanged;
            _viewModel.RequestClose -= OnRequestClose;
            
            base.OnClosed(e);
        }

        #endregion
    }
}
