<Window x:Class="SalesManagementSystem.Views.InventoryReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تقرير المخزون"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <Border Grid.Row="0" Background="Orange">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="📦" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="تقرير المخزون التفصيلي" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <TabControl Grid.Row="1" Margin="10">
            <TabItem Header="ملخص المخزون">
                <Grid Margin="20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Grid.Column="0" Background="LightBlue" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي المنتجات" FontWeight="Bold"/>
                            <TextBlock Text="1250" FontSize="20" Foreground="Blue"/>
                        </StackPanel>
                    </Border>

                    <Border Grid.Row="0" Grid.Column="1" Background="LightGreen" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="قيمة المخزون" FontWeight="Bold"/>
                            <TextBlock Text="325750.50 دج" FontSize="20" Foreground="Green"/>
                        </StackPanel>
                    </Border>

                    <Border Grid.Row="0" Grid.Column="2" Background="LightYellow" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="تحت الحد الأدنى" FontWeight="Bold"/>
                            <TextBlock Text="25" FontSize="20" Foreground="Orange"/>
                        </StackPanel>
                    </Border>

                    <Border Grid.Row="1" Grid.Column="0" Background="LightPink" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="نفد المخزون" FontWeight="Bold"/>
                            <TextBlock Text="8" FontSize="20" Foreground="Red"/>
                        </StackPanel>
                    </Border>

                    <Border Grid.Row="1" Grid.Column="1" Background="LightGray" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="منتجات راكدة" FontWeight="Bold"/>
                            <TextBlock Text="12" FontSize="20" Foreground="Gray"/>
                        </StackPanel>
                    </Border>

                    <Border Grid.Row="1" Grid.Column="2" Background="LightCyan" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="منتجات جديدة" FontWeight="Bold"/>
                            <TextBlock Text="35" FontSize="20" Foreground="Teal"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </TabItem>

            <TabItem Header="تفاصيل المخزون">
                <DataGrid Margin="10" AutoGenerateColumns="False" CanUserAddRows="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="المنتج" Width="*"/>
                        <DataGridTextColumn Header="الكمية المتاحة" Width="100"/>
                        <DataGridTextColumn Header="الحد الأدنى" Width="100"/>
                        <DataGridTextColumn Header="سعر الشراء" Width="100"/>
                        <DataGridTextColumn Header="سعر البيع" Width="100"/>
                        <DataGridTextColumn Header="قيمة المخزون" Width="120"/>
                        <DataGridTextColumn Header="الحالة" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
        </TabControl>

        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="🖨️ طباعة" Width="100" Height="35" Margin="10"
                       Background="Orange" Foreground="White" FontWeight="Bold"/>
                <Button Content="📤 تصدير" Width="100" Height="35" Margin="10"
                       Background="Blue" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إغلاق" Width="100" Height="35" Margin="10"
                       Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
