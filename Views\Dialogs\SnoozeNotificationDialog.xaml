<Window x:Class="SalesManagementSystem.Views.Dialogs.SnoozeNotificationDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تأجيل الإشعار" Height="300" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Style="{StaticResource MaterialDesignWindow}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0"
                   Text="تأجيل الإشعار"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- Content -->
        <StackPanel Grid.Row="1" Orientation="Vertical">
            <TextBlock Text="اختر مدة التأجيل:"
                       Style="{StaticResource MaterialDesignBody1TextBlock}"/>

            <ComboBox x:Name="SnoozeDurationComboBox"
                      materialDesign:HintAssist.Hint="مدة التأجيل"
                      Style="{StaticResource MaterialDesignComboBox}"
                      Margin="0,15,0,0">
                <ComboBoxItem Content="5 دقائق" Tag="5"/>
                <ComboBoxItem Content="15 دقيقة" Tag="15"/>
                <ComboBoxItem Content="30 دقيقة" Tag="30"/>
                <ComboBoxItem Content="ساعة واحدة" Tag="60"/>
                <ComboBoxItem Content="ساعتان" Tag="120"/>
                <ComboBoxItem Content="يوم واحد" Tag="1440"/>
            </ComboBox>

            <TextBox x:Name="CustomMinutesTextBox"
                     materialDesign:HintAssist.Hint="أو أدخل عدد الدقائق"
                     Style="{StaticResource MaterialDesignTextBox}"
                     Margin="0,10,0,0"/>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,20,0,0">
            <Button x:Name="CancelButton"
                    Content="إلغاء"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0"
                    Click="CancelButton_Click"/>

            <Button x:Name="SnoozeButton"
                    Content="تأجيل"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Click="SnoozeButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
