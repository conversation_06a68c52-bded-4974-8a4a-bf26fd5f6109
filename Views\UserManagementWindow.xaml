<Window x:Class="SalesManagementSystem.Views.UserManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المستخدمين - نظام إدارة المبيعات"
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                               Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#FFF5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#FF1976D2">
            <Border.Effect>
                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
            </Border.Effect>
            <Grid Margin="20,0">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="👥" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة المستخدمين والصلاحيات" FontSize="18"
                              FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة المستخدمين -->
            <Border Grid.Column="0" Background="White" CornerRadius="10">
                <Border.Effect>
                    <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                </Border.Effect>
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان القائمة -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="📋 قائمة المستخدمين" FontSize="16" FontWeight="Bold"
                                  Foreground="#FF1976D2" VerticalAlignment="Center"/>
                        <TextBlock x:Name="UserCountLabel" Text="(10 مستخدمين)" FontSize="12"
                                  Foreground="Gray" VerticalAlignment="Center" Margin="10,0,0,0"/>
                    </StackPanel>

                    <!-- جدول المستخدمين -->
                    <DataGrid x:Name="UsersDataGrid" Grid.Row="1"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             SelectionChanged="UsersDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="120"/>
                            <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="150"/>
                            <DataGridTextColumn Header="الدور" Binding="{Binding Role}" Width="120"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80"/>
                            <DataGridTextColumn Header="آخر دخول" Binding="{Binding LastLoginText}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>

            <!-- لوحة التحكم -->
            <Border Grid.Column="2" Background="White" CornerRadius="10">
                <Border.Effect>
                    <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
                </Border.Effect>
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان لوحة التحكم -->
                    <TextBlock Grid.Row="0" Text="🔧 لوحة التحكم" FontSize="16" FontWeight="Bold"
                              Foreground="#FF1976D2" Margin="0,0,0,20"/>

                    <!-- معلومات المستخدم المحدد -->
                    <Border Grid.Row="1" Background="#FFF3E5F5" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="المستخدم المحدد:" FontSize="12" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBlock x:Name="SelectedUserName" Text="لم يتم تحديد مستخدم" FontSize="14"
                                      FontWeight="SemiBold" Foreground="#FF1976D2"/>
                            <TextBlock x:Name="SelectedUserRole" Text="" FontSize="12"
                                      Foreground="Gray" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Grid.Row="2" Margin="0,0,0,20">
                        <Button x:Name="AddUserButton" Content="➕ إضافة مستخدم جديد"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#FF4CAF50" Click="AddUser_Click"/>

                        <Button x:Name="EditUserButton" Content="✏️ تعديل المستخدم"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#FFFF9800" Click="EditUser_Click" IsEnabled="False"/>

                        <Button x:Name="DeleteUserButton" Content="🗑️ حذف المستخدم"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#FFF44336" Click="DeleteUser_Click" IsEnabled="False"/>

                        <Button x:Name="ResetPasswordButton" Content="🔑 تغيير كلمة المرور"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#FF9C27B0" Click="ResetPassword_Click" IsEnabled="False"/>
                    </StackPanel>

                    <!-- إحصائيات -->
                    <Border Grid.Row="3" Background="#FFE3F2FD" CornerRadius="8" Padding="15">
                        <StackPanel>
                            <TextBlock Text="📊 إحصائيات سريعة" FontSize="12" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBlock x:Name="AdminCountLabel" Text="المديرين: 2" FontSize="11" Margin="0,2"/>
                            <TextBlock x:Name="ActiveUsersLabel" Text="المستخدمين النشطين: 10" FontSize="11" Margin="0,2"/>
                            <TextBlock x:Name="InactiveUsersLabel" Text="المستخدمين غير النشطين: 0" FontSize="11" Margin="0,2"/>
                        </StackPanel>
                    </Border>

                    <!-- زر الإغلاق -->
                    <Button Grid.Row="4" x:Name="CloseButton" Content="إغلاق"
                           Style="{StaticResource ModernButtonStyle}"
                           Background="#FF607D8B" Click="Close_Click" Margin="0,20,0,0"/>
                </Grid>
            </Border>
        </Grid>

        <!-- شريط الحالة -->
        <Border Grid.Row="2" Background="#FF455A64">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="الحالة: جاهز" Foreground="White" FontSize="12"/>
                <TextBlock Text=" | " Foreground="White" FontSize="12"/>
                <TextBlock Text="المستخدم: المدير العام" Foreground="White" FontSize="12"/>
                <TextBlock Text=" | " Foreground="White" FontSize="12"/>
                <TextBlock x:Name="StatusLabel" Text="تم تحميل قائمة المستخدمين بنجاح" Foreground="LightGreen" FontSize="12"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
