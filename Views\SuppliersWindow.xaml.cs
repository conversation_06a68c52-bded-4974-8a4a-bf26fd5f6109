using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.Views.Dialogs;

namespace SalesManagementSystem.Views
{
    public partial class SuppliersWindow : Window
    {
        private readonly SupplierService _supplierService;
        private ObservableCollection<Supplier> _suppliers = new();
        private Supplier? _selectedSupplier;

        public SuppliersWindow()
        {
            InitializeComponent();

            var dbService = new DatabaseService();
            _supplierService = new SupplierService(dbService);

            LoadSuppliersAsync();
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierService.GetAllSuppliersAsync();
                _suppliers = new ObservableCollection<Supplier>(suppliers);
                SuppliersDataGrid.ItemsSource = _suppliers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(SupplierNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المورد", "تحذير",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var supplier = new Supplier
                {
                    Name = SupplierNameTextBox.Text.Trim(),
                    Phone = PhoneTextBox.Text.Trim(),
                    Email = EmailTextBox.Text.Trim(),
                    Address = AddressTextBox.Text.Trim(),
                    Balance = 0,
                    CreatedAt = DateTime.Now
                };

                if (_selectedSupplier != null)
                {
                    // Update existing supplier
                    supplier.Id = _selectedSupplier.Id;
                    supplier.CreatedAt = _selectedSupplier.CreatedAt;
                    supplier.UpdatedAt = DateTime.Now;

                    var success = await _supplierService.UpdateSupplierAsync(supplier);
                    if (success)
                    {
                        MessageBox.Show("تم تحديث المورد بنجاح!", "نجح التحديث",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        // Update in collection
                        var index = _suppliers.IndexOf(_selectedSupplier);
                        if (index >= 0)
                        {
                            _suppliers[index] = supplier;
                        }
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث المورد", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    // Add new supplier
                    var savedSupplier = await _supplierService.AddSupplierAsync(supplier);
                    if (savedSupplier != null)
                    {
                        MessageBox.Show("تم إضافة المورد بنجاح!", "نجح الحفظ",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        _suppliers.Add(savedSupplier);
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ المورد", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }

                // Clear form
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المورد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NewButton_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            SupplierNameTextBox.Text = string.Empty;
            PhoneTextBox.Text = string.Empty;
            EmailTextBox.Text = string.Empty;
            AddressTextBox.Text = string.Empty;
            _selectedSupplier = null;
            SuppliersDataGrid.SelectedItem = null;
        }

        private void SuppliersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (SuppliersDataGrid.SelectedItem is Supplier supplier)
            {
                _selectedSupplier = supplier;
                SupplierNameTextBox.Text = supplier.Name;
                PhoneTextBox.Text = supplier.Phone;
                EmailTextBox.Text = supplier.Email;
                AddressTextBox.Text = supplier.Address;
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Supplier supplier)
            {
                _selectedSupplier = supplier;
                SupplierNameTextBox.Text = supplier.Name;
                PhoneTextBox.Text = supplier.Phone;
                EmailTextBox.Text = supplier.Email;
                AddressTextBox.Text = supplier.Address;
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Supplier supplier)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المورد '{supplier.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var success = await _supplierService.DeleteSupplierAsync(supplier.Id);
                        if (success)
                        {
                            _suppliers.Remove(supplier);
                            MessageBox.Show("تم حذف المورد بنجاح!", "نجح الحذف",
                                MessageBoxButton.OK, MessageBoxImage.Information);

                            if (_selectedSupplier?.Id == supplier.Id)
                            {
                                ClearForm();
                            }
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف المورد", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var searchText = SearchTextBox.Text.Trim();
                if (string.IsNullOrWhiteSpace(searchText) || searchText == "البحث في الموردين...")
                {
                    await LoadSuppliersAsync();
                }
                else
                {
                    var suppliers = await _supplierService.SearchSuppliersAsync(searchText);
                    _suppliers = new ObservableCollection<Supplier>(suppliers);
                    SuppliersDataGrid.ItemsSource = _suppliers;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            base.OnClosed(e);
        }
    }
}
