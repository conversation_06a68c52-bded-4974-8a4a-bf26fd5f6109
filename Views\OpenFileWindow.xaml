<Window x:Class="SalesManagementSystem.Views.OpenFileWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="فتح ملف"
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Blue">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="📂" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="فتح ملف موجود" FontSize="18" 
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- File Types -->
            <GroupBox Grid.Column="0" Header="نوع الملف" Margin="0,0,10,0">
                <StackPanel Margin="10">
                    <CheckBox Content="📊 قواعد البيانات" IsChecked="True" Margin="0,5"/>
                    <CheckBox Content="📋 ملفات Excel" IsChecked="True" Margin="0,5"/>
                    <CheckBox Content="📄 ملفات PDF" Margin="0,5"/>
                    <CheckBox Content="💾 النسخ الاحتياطية" Margin="0,5"/>
                    <CheckBox Content="⚙️ ملفات الإعدادات" Margin="0,5"/>
                    <CheckBox Content="📝 ملفات نصية" Margin="0,5"/>
                </StackPanel>
            </GroupBox>

            <!-- File List -->
            <GroupBox Grid.Column="1" Header="الملفات المتاحة">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Search -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBox Width="200" Text="البحث في الملفات..." Margin="0,0,10,0"/>
                        <Button Content="🔍 بحث" Width="80" Background="Orange" Foreground="White"/>
                        <Button Content="🔄 تحديث" Width="80" Margin="10,0,0,0" Background="Green" Foreground="White"/>
                    </StackPanel>

                    <!-- Files DataGrid -->
                    <DataGrid Grid.Row="1" AutoGenerateColumns="False" CanUserAddRows="False">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم الملف" Width="*"/>
                            <DataGridTextColumn Header="النوع" Width="100"/>
                            <DataGridTextColumn Header="الحجم" Width="80"/>
                            <DataGridTextColumn Header="تاريخ التعديل" Width="120"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="👁️" Width="25" Height="25" Margin="2" 
                                                   Background="Blue" Foreground="White" ToolTip="معاينة"/>
                                            <Button Content="📋" Width="25" Height="25" Margin="2" 
                                                   Background="Green" Foreground="White" ToolTip="تفاصيل"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- File Info -->
                    <GroupBox Grid.Row="2" Header="معلومات الملف المحدد" Margin="0,10,0,0">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="المسار: C:\SalesSystem\Data\database.db" Margin="0,2"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="الحجم: 2.5 MB" Margin="0,2"/>
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ الإنشاء: 2024/01/15" Margin="0,2"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="آخر تعديل: 2024/01/20" Margin="0,2"/>
                        </Grid>
                    </GroupBox>
                </Grid>
            </GroupBox>
        </Grid>

        <!-- Footer -->
        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="📂 فتح الملف" Width="120" Height="35" Margin="10" 
                       Background="Blue" Foreground="White" FontWeight="Bold" Click="Open_Click"/>
                <Button Content="📋 فتح للقراءة فقط" Width="140" Height="35" Margin="10" 
                       Background="Orange" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إلغاء" Width="100" Height="35" Margin="10" 
                       Background="Red" Foreground="White" FontWeight="Bold" Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
