using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    public class Setting : INotifyPropertyChanged
    {
        private int _id;
        private string _key = string.Empty;
        private string _value = string.Empty;
        private string _description = string.Empty;
        private string _createdAt = string.Empty;
        private string _updatedAt = string.Empty;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Key
        {
            get => _key;
            set
            {
                if (_key != value)
                {
                    _key = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    _value = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        // Calculated properties
        public string FormattedCreatedAt
        {
            get
            {
                if (string.IsNullOrEmpty(CreatedAt))
                    return string.Empty;

                if (DateTime.TryParse(CreatedAt, out DateTime date))
                    return date.ToString("yyyy-MM-dd HH:mm:ss");

                return CreatedAt;
            }
        }

        public string FormattedUpdatedAt
        {
            get
            {
                if (string.IsNullOrEmpty(UpdatedAt))
                    return string.Empty;

                if (DateTime.TryParse(UpdatedAt, out DateTime date))
                    return date.ToString("yyyy-MM-dd HH:mm:ss");

                return UpdatedAt;
            }
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    public class SettingValidator : AbstractValidator<Setting>
    {
        public SettingValidator()
        {
            RuleFor(s => s.Key)
                .NotEmpty().WithMessage("Setting key is required")
                .MaximumLength(50).WithMessage("Setting key cannot exceed 50 characters");

            RuleFor(s => s.Value)
                .NotNull().WithMessage("Setting value is required");

            RuleFor(s => s.Description)
                .MaximumLength(255).WithMessage("Description cannot exceed 255 characters");
        }
    }
}