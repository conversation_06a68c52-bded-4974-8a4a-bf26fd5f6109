using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class DashboardView : UserControl
    {
        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private readonly SaleService _saleService;
        private readonly CustomerService _customerService;
        private readonly ExpenseService _expenseService;

        public DashboardView()
        {
            InitializeComponent();

            // Initialize services
            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);
            _customerService = new CustomerService(_dbService);
            _saleService = new SaleService(_dbService, _productService, _customerService);
            _expenseService = new ExpenseService(_dbService);

            Loaded += DashboardView_Loaded;
        }

        private async void DashboardView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDashboardDataAsync();
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                // Load statistics
                await LoadStatisticsAsync();

                // Load recent sales
                await LoadRecentSalesAsync();

                // Load low stock products
                await LoadLowStockProductsAsync();

                // Load top selling products
                await LoadTopSellingProductsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStatisticsAsync()
        {
            // Get date range for current month
            var startDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);

            // Load total sales
            var totalSales = await _saleService.GetTotalSalesAsync(startDate, endDate);
            TotalSalesText.Text = totalSales.ToString("C");

            // Load total expenses
            var totalExpenses = await _expenseService.GetTotalExpensesAsync(startDate, endDate);
            TotalExpensesText.Text = totalExpenses.ToString("C");

            // Calculate net profit
            var netProfit = totalSales - totalExpenses;
            NetProfitText.Text = netProfit.ToString("C");
            NetProfitText.Foreground = netProfit >= 0 ?
                System.Windows.Media.Brushes.Green :
                System.Windows.Media.Brushes.Red;

            // Load low stock count
            var lowStockProducts = await _productService.GetLowStockProductsAsync();
            LowStockText.Text = lowStockProducts.Count().ToString();
        }

        private async Task LoadRecentSalesAsync()
        {
            var recentSales = await _saleService.GetRecentSalesAsync(10);
            RecentSalesGrid.ItemsSource = recentSales.Select(s => new DashboardSale
            {
                InvoiceNumber = s.InvoiceNumber,
                CustomerName = s.CustomerName ?? "عميل نقدي",
                Total = s.Total,
                Date = s.Date
            }).ToList();
        }

        private DateTime TryParseDate(string dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return DateTime.Now;

            if (DateTime.TryParse(dateString, out DateTime result))
                return result;

            return DateTime.Now;
        }

        private async Task LoadLowStockProductsAsync()
        {
            var lowStockProducts = await _productService.GetLowStockProductsAsync();
            LowStockGrid.ItemsSource = lowStockProducts.Take(10).ToList();
        }

        private async Task LoadTopSellingProductsAsync()
        {
            var startDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            var endDate = DateTime.Now;

            var topProducts = await _productService.GetTopSellingProductsAsync(5, startDate, endDate);
            TopProductsList.ItemsSource = topProducts.Select(p => new
            {
                Name = p.Name,
                TotalSold = p.TotalSold ?? 0
            }).ToList();
        }

        private void ViewAllSales_Click(object sender, RoutedEventArgs e)
        {
            // Navigate to sales page
            var mainWindow = Window.GetWindow(this) as MainWindow;
            mainWindow?.NavigateToPage("Sales");
        }

        private void ViewAllProducts_Click(object sender, RoutedEventArgs e)
        {
            // Navigate to products page
            var mainWindow = Window.GetWindow(this) as MainWindow;
            mainWindow?.NavigateToPage("Products");
        }
    }

    // Helper classes for data binding
    public class DashboardSale
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public decimal Total { get; set; }
        public DateTime Date { get; set; }
    }

    public class TopProduct
    {
        public string Name { get; set; } = string.Empty;
        public int TotalSold { get; set; }
    }
}
