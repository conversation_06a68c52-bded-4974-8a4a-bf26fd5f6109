<UserControl x:Class="SalesManagementSystem.Views.BackupMonitorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- تحويل شدة التنبيه إلى لون -->
        <Style x:Key="AlertSeverityIndicator" TargetType="Border">
            <Setter Property="Background" Value="Blue"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Severity}" Value="Info">
                    <Setter Property="Background" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Severity}" Value="Warning">
                    <Setter Property="Background" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Severity}" Value="Error">
                    <Setter Property="Background" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Severity}" Value="Critical">
                    <Setter Property="Background" Value="#9C27B0"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والحالة -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="MonitorDashboard" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="مراقب النسخ الاحتياطي" 
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                        <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                            <Ellipse Width="12" Height="12" 
                                   Fill="{Binding Monitor.StatusColor, Converter={StaticResource ColorToBrushConverter}}"
                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding Monitor.StatusDisplay}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     VerticalAlignment="Center"/>
                            <TextBlock Text=" • " 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     VerticalAlignment="Center" Margin="8,0"/>
                            <TextBlock Text="{Binding Monitor.CurrentActivity}" 
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding GenerateReportCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تقرير الحالة"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding ClearAlertsCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="BellOff" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="مسح التنبيهات"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات المهام -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي المهام -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="DatabaseExport" Width="28" Height="28" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Monitor.TotalJobs}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي المهام" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- قيد التشغيل -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Play" Width="28" Height="28" 
                                           Foreground="Orange"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Monitor.RunningJobs}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Orange"/>
                    <TextBlock Text="قيد التشغيل" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- مجدولة -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Schedule" Width="28" Height="28" 
                                           Foreground="Blue"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Monitor.ScheduledJobs}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Blue"/>
                    <TextBlock Text="مجدولة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- مكتملة -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CheckCircle" Width="28" Height="28" 
                                           Foreground="Green"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Monitor.CompletedJobs}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Green"/>
                    <TextBlock Text="مكتملة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- فاشلة -->
                <StackPanel Grid.Column="4" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AlertCircle" Width="28" Height="28" 
                                           Foreground="Red"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Monitor.FailedJobs}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Red"/>
                    <TextBlock Text="فاشلة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- معدل النجاح -->
                <StackPanel Grid.Column="5" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ChartLine" Width="28" Height="28" 
                                           Foreground="{Binding Monitor.SuccessRateColor, Converter={StaticResource ColorToBrushConverter}}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Monitor.FormattedSuccessRate}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{Binding Monitor.SuccessRateColor, Converter={StaticResource ColorToBrushConverter}}"/>
                    <TextBlock Text="معدل النجاح" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- موارد النظام -->
        <materialDesign:Card Grid.Row="2" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- استخدام المعالج -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Cpu64Bit" Width="24" Height="24" 
                                           Foreground="{Binding Monitor.CpuUsageColor, Converter={StaticResource ColorToBrushConverter}}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="المعالج" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    <ProgressBar Value="{Binding Monitor.SystemCpuUsage}" 
                               Maximum="100" 
                               Width="80" Height="8"
                               Foreground="{Binding Monitor.CpuUsageColor, Converter={StaticResource ColorToBrushConverter}}"
                               Margin="0,4,0,0"/>
                    <TextBlock Text="{Binding Monitor.FormattedCpuUsage}" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{Binding Monitor.CpuUsageColor, Converter={StaticResource ColorToBrushConverter}}"/>
                </StackPanel>

                <!-- استخدام الذاكرة -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Memory" Width="24" Height="24" 
                                           Foreground="{Binding Monitor.MemoryUsageColor, Converter={StaticResource ColorToBrushConverter}}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="الذاكرة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    <ProgressBar Value="{Binding Monitor.SystemMemoryUsage}" 
                               Maximum="100" 
                               Width="80" Height="8"
                               Foreground="{Binding Monitor.MemoryUsageColor, Converter={StaticResource ColorToBrushConverter}}"
                               Margin="0,4,0,0"/>
                    <TextBlock Text="{Binding Monitor.FormattedMemoryUsage}" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{Binding Monitor.MemoryUsageColor, Converter={StaticResource ColorToBrushConverter}}"/>
                </StackPanel>

                <!-- استخدام القرص -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Harddisk" Width="24" Height="24" 
                                           Foreground="{Binding Monitor.DiskUsageColor, Converter={StaticResource ColorToBrushConverter}}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="القرص" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    <ProgressBar Value="{Binding Monitor.SystemDiskUsage}" 
                               Maximum="100" 
                               Width="80" Height="8"
                               Foreground="{Binding Monitor.DiskUsageColor, Converter={StaticResource ColorToBrushConverter}}"
                               Margin="0,4,0,0"/>
                    <TextBlock Text="{Binding Monitor.FormattedDiskUsage}" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{Binding Monitor.DiskUsageColor, Converter={StaticResource ColorToBrushConverter}}"/>
                </StackPanel>

                <!-- استخدام الشبكة -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Network" Width="24" Height="24" 
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="الشبكة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    <ProgressBar Value="{Binding Monitor.NetworkBandwidthUsage}" 
                               Maximum="100" 
                               Width="80" Height="8"
                               Foreground="{DynamicResource SecondaryHueMidBrush}"
                               Margin="0,4,0,0"/>
                    <TextBlock Text="{Binding Monitor.FormattedBandwidthUsage}" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- التنبيهات والمعلومات -->
        <Grid Grid.Row="3" Margin="16,8,16,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة التنبيهات -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان التنبيهات -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                        <materialDesign:PackIcon Kind="Bell" Width="20" Height="20" 
                                               VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="التنبيهات" 
                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                 VerticalAlignment="Center"/>
                        <materialDesign:Chip Content="{Binding Monitor.AlertsCount}" 
                                           Margin="8,0,0,0"
                                           FontSize="10"/>
                    </StackPanel>

                    <!-- قائمة التنبيهات -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding Monitor.Alerts}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Margin="0,0,0,8" 
                                          Padding="12"
                                          CornerRadius="4"
                                          Background="{DynamicResource MaterialDesignCardBackground}"
                                          BorderThickness="1"
                                          BorderBrush="{DynamicResource MaterialDesignDivider}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- مؤشر الشدة -->
                                            <Border Grid.Column="0" 
                                                  Width="4" Height="40"
                                                  CornerRadius="2"
                                                  Style="{StaticResource AlertSeverityIndicator}"
                                                  VerticalAlignment="Top"
                                                  Margin="0,0,12,0"/>

                                            <!-- محتوى التنبيه -->
                                            <StackPanel Grid.Column="1">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding SeverityDisplay}" 
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             Foreground="{Binding SeverityColor, Converter={StaticResource ColorToBrushConverter}}"
                                                             FontWeight="Bold"/>
                                                    <TextBlock Text=" • " 
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             Margin="4,0"/>
                                                    <TextBlock Text="{Binding FormattedCreatedAt}" 
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             Opacity="0.7"/>
                                                </StackPanel>
                                                <TextBlock Text="{Binding Message}" 
                                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                         TextWrapping="Wrap"
                                                         Margin="0,4,0,0"/>
                                                <TextBlock Text="{Binding Details}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         TextWrapping="Wrap"
                                                         Opacity="0.7"
                                                         Margin="0,4,0,0"
                                                         Visibility="{Binding Details, Converter={StaticResource StringToVisibilityConverter}}"/>
                                            </StackPanel>

                                            <!-- حالة القراءة -->
                                            <Ellipse Grid.Column="2" 
                                                   Width="8" Height="8"
                                                   Fill="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Top"
                                                   Visibility="{Binding IsRead, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>

            <!-- معلومات إضافية -->
            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                <!-- معلومات عامة -->
                <materialDesign:Card Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="Information" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="معلومات عامة" 
                                     Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="آخر فحص:" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,8,4"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Monitor.TimeSinceLastCheck}" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,4"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="المهمة التالية:" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,8,4"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Monitor.TimeToNextJob}" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,4"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="حجم النسخ:" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,8,4"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Monitor.FormattedTotalBackupSize}" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,4"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="متوسط المدة:" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,8,4"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding Monitor.FormattedAverageBackupDuration}" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,4"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="إجمالي الوقت:" Style="{StaticResource MaterialDesignCaptionTextBlock}" Margin="0,0,8,4"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding Monitor.FormattedTotalBackupTime}" Style="{StaticResource MaterialDesignBody2TextBlock}" Margin="0,0,0,4"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- إجراءات سريعة -->
                <materialDesign:Card Padding="16">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="Flash" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="إجراءات سريعة" 
                                     Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding RunManualBackupCommand}"
                                HorizontalAlignment="Stretch"
                                Margin="0,0,0,8">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Play" Width="16" Height="16" Margin="0,0,8,0"/>
                                <TextBlock Text="نسخ احتياطي يدوي"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding TestSystemCommand}"
                                HorizontalAlignment="Stretch"
                                Margin="0,0,0,8">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="TestTube" Width="16" Height="16" Margin="0,0,8,0"/>
                                <TextBlock Text="اختبار النظام"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding ViewLogsCommand}"
                                HorizontalAlignment="Stretch">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16" Margin="0,0,8,0"/>
                                <TextBlock Text="عرض السجلات"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </Grid>

        <!-- مؤشر التحميل -->
        <Grid Grid.RowSpan="4" 
              Background="White" 
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري التحديث..." 
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
