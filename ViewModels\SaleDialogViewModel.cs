using System;
using System.Collections.ObjectModel;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Input;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class SaleDialogViewModel : BaseViewModel
    {
        #region Services

        private readonly DatabaseService _dbService;
        private readonly SaleService _saleService;
        private readonly ProductService _productService;
        private readonly CustomerService _customerService;

        #endregion

        #region Properties

        private Sale _sale = new();
        public Sale Sale
        {
            get => _sale;
            set
            {
                if (SetProperty(ref _sale, value))
                {
                    OnSaleChanged();
                }
            }
        }

        private ObservableCollection<Customer> _customers = new();
        public ObservableCollection<Customer> Customers
        {
            get => _customers;
            set => SetProperty(ref _customers, value);
        }

        private Customer? _selectedCustomer;
        public Customer? SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                if (SetProperty(ref _selectedCustomer, value))
                {
                    if (value != null)
                        Sale.CustomerId = value.Id;
                }
            }
        }

        private ObservableCollection<Product> _products = new();
        public ObservableCollection<Product> Products
        {
            get => _products;
            set => SetProperty(ref _products, value);
        }

        private Product? _selectedProduct;
        public Product? SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                if (SetProperty(ref _selectedProduct, value))
                {
                    if (value != null)
                    {
                        ItemUnitPrice = value.SalePrice;
                    }
                }
            }
        }

        private ObservableCollection<SaleItem> _saleItems = new();
        public ObservableCollection<SaleItem> SaleItems
        {
            get => _saleItems;
            set => SetProperty(ref _saleItems, value);
        }

        private ObservableCollection<string> _paymentMethods = new();
        public ObservableCollection<string> PaymentMethods
        {
            get => _paymentMethods;
            set => SetProperty(ref _paymentMethods, value);
        }

        private ObservableCollection<string> _paymentStatuses = new();
        public ObservableCollection<string> PaymentStatuses
        {
            get => _paymentStatuses;
            set => SetProperty(ref _paymentStatuses, value);
        }

        // Item Entry Properties
        private int _itemQuantity = 1;
        public int ItemQuantity
        {
            get => _itemQuantity;
            set
            {
                if (SetProperty(ref _itemQuantity, value))
                {
                    OnPropertyChanged(nameof(ItemTotal));
                }
            }
        }

        private decimal _itemUnitPrice;
        public decimal ItemUnitPrice
        {
            get => _itemUnitPrice;
            set
            {
                if (SetProperty(ref _itemUnitPrice, value))
                {
                    OnPropertyChanged(nameof(ItemTotal));
                }
            }
        }

        public decimal ItemTotal => ItemQuantity * ItemUnitPrice;

        private string _windowTitle = "فاتورة مبيعات جديدة";
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }

        private string _headerIcon = "Receipt";
        public string HeaderIcon
        {
            get => _headerIcon;
            set => SetProperty(ref _headerIcon, value);
        }

        private bool _isEditMode;
        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                if (SetProperty(ref _isEditMode, value))
                {
                    UpdateWindowTitle();
                }
            }
        }

        #endregion

        #region Commands

        private DelegateCommand? _saveCommand;
        public DelegateCommand SaveCommand => _saveCommand ??= new DelegateCommand(SaveSale, CanSaveSale);

        private DelegateCommand? _cancelCommand;
        public DelegateCommand CancelCommand => _cancelCommand ??= new DelegateCommand(Cancel);

        private DelegateCommand? _addItemCommand;
        public DelegateCommand AddItemCommand => _addItemCommand ??= new DelegateCommand(AddItem, CanAddItem);

        private DelegateCommand<SaleItem>? _removeItemCommand;
        public DelegateCommand<SaleItem> RemoveItemCommand => _removeItemCommand ??= new DelegateCommand<SaleItem>(RemoveItem);

        private DelegateCommand? _addCustomerCommand;
        public DelegateCommand AddCustomerCommand => _addCustomerCommand ??= new DelegateCommand(AddCustomer);

        private DelegateCommand? _addProductCommand;
        public DelegateCommand AddProductCommand => _addProductCommand ??= new DelegateCommand(AddProduct);

        private DelegateCommand? _saveAndPrintCommand;
        public DelegateCommand SaveAndPrintCommand => _saveAndPrintCommand ??= new DelegateCommand(SaveAndPrint, CanSaveSale);

        private DelegateCommand? _clearAllCommand;
        public DelegateCommand ClearAllCommand => _clearAllCommand ??= new DelegateCommand(ClearAll);

        #endregion

        #region Constructor

        public SaleDialogViewModel()
        {
            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);
            _customerService = new CustomerService(_dbService);
            _saleService = new SaleService(_dbService, _productService, _customerService);

            InitializeData();
            _ = LoadDataAsync();
        }

        public SaleDialogViewModel(Sale sale) : this()
        {
            Sale = sale.Clone();
            IsEditMode = true;
        }

        #endregion

        #region Methods

        private void InitializeData()
        {
            InitializePaymentMethods();
            InitializePaymentStatuses();

            // Initialize sale with default values
            Sale.Date = DateTime.Now;
            Sale.PaymentMethod = "نقدي";
            Sale.PaymentStatus = "مدفوع";
        }

        private void InitializePaymentMethods()
        {
            PaymentMethods.Clear();
            PaymentMethods.Add("نقدي");
            PaymentMethods.Add("بطاقة ائتمان");
            PaymentMethods.Add("بطاقة خصم");
            PaymentMethods.Add("تحويل بنكي");
            PaymentMethods.Add("شيك");
            PaymentMethods.Add("آجل");
        }

        private void InitializePaymentStatuses()
        {
            PaymentStatuses.Clear();
            PaymentStatuses.Add("مدفوع");
            PaymentStatuses.Add("مدفوع جزئياً");
            PaymentStatuses.Add("غير مدفوع");
            PaymentStatuses.Add("ملغي");
        }

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تحميل البيانات...";
                ClearError();

                await LoadCustomersAsync();
                await LoadProductsAsync();

                if (!IsEditMode)
                {
                    Sale.InvoiceNumber = await GenerateInvoiceNumberAsync();
                }
                else
                {
                    await LoadSaleItemsAsync();
                    await LoadSelectedCustomerAsync();
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل البيانات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تحميل بيانات حوار المبيعات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadCustomersAsync()
        {
            var customers = await _customerService.GetAllCustomersAsync();
            Customers.Clear();

            foreach (var customer in customers.Where(c => c.IsActive))
            {
                Customers.Add(customer);
            }
        }

        private async Task LoadProductsAsync()
        {
            var products = await _productService.GetAllProductsAsync();
            Products.Clear();

            foreach (var product in products.Where(p => p.IsActive))
            {
                Products.Add(product);
            }
        }

        private Task LoadSaleItemsAsync()
        {
            if (Sale.Items != null)
            {
                SaleItems.Clear();
                foreach (var item in Sale.Items)
                {
                    SaleItems.Add(item);
                }
            }
            return Task.CompletedTask;
        }

        private Task LoadSelectedCustomerAsync()
        {
            if (Sale.CustomerId > 0)
            {
                SelectedCustomer = Customers.FirstOrDefault(c => c.Id == Sale.CustomerId);
            }
            return Task.CompletedTask;
        }

        private void OnSaleChanged()
        {
            CalculateTotals();
            SaveCommand.RaiseCanExecuteChanged();
        }

        private void UpdateWindowTitle()
        {
            WindowTitle = IsEditMode ? "تعديل فاتورة المبيعات" : "فاتورة مبيعات جديدة";
            HeaderIcon = IsEditMode ? "ReceiptTextEdit" : "Receipt";
        }

        private void CalculateTotals()
        {
            Sale.Subtotal = SaleItems.Sum(item => item.Total);
            Sale.Tax = Sale.Subtotal * 0.15m; // 15% tax rate
            Sale.Total = Sale.Subtotal + Sale.Tax - Sale.Discount;
        }

        private Task<string> GenerateInvoiceNumberAsync()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd");
                var random = new Random().Next(1000, 9999);
                return Task.FromResult($"INV-{timestamp}-{random}");
            }
            catch
            {
                return Task.FromResult($"INV-{DateTime.Now:yyyyMMddHHmmss}");
            }
        }

        #endregion

        #region Command Handlers

        private void AddItem()
        {
            if (SelectedProduct == null) return;

            // Check stock availability
            var totalRequestedQuantity = ItemQuantity;
            var existingItem = SaleItems.FirstOrDefault(item => item.ProductId == SelectedProduct.Id);
            if (existingItem != null)
            {
                totalRequestedQuantity += existingItem.Quantity;
            }

            // Validate stock
            if (SelectedProduct.TrackStock && totalRequestedQuantity > SelectedProduct.Quantity)
            {
                var result = System.Windows.MessageBox.Show(
                    $"الكمية المطلوبة ({totalRequestedQuantity}) أكبر من المخزون المتاح ({SelectedProduct.Quantity}).\nهل تريد المتابعة؟",
                    "تحذير المخزون",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning);

                if (result == System.Windows.MessageBoxResult.No)
                    return;
            }

            // Add or update item
            if (existingItem != null)
            {
                existingItem.Quantity += ItemQuantity;
                existingItem.Total = existingItem.Quantity * existingItem.UnitPrice;
            }
            else
            {
                var newItem = new SaleItem
                {
                    ProductId = SelectedProduct.Id,
                    ProductName = SelectedProduct.Name,
                    ProductCode = SelectedProduct.Code,
                    Quantity = ItemQuantity,
                    UnitPrice = ItemUnitPrice,
                    Total = ItemTotal
                };
                SaleItems.Add(newItem);
            }

            // Reset item entry
            SelectedProduct = null;
            ItemQuantity = 1;
            ItemUnitPrice = 0;

            CalculateTotals();
            AddItemCommand.RaiseCanExecuteChanged();

            LoggingService.LogSystemEvent("إضافة صنف للفاتورة", $"تم إضافة {ItemQuantity} من {SelectedProduct?.Name}");
        }

        private bool CanAddItem()
        {
            return SelectedProduct != null && ItemQuantity > 0 && ItemUnitPrice > 0;
        }

        private void RemoveItem(SaleItem item)
        {
            if (item != null)
            {
                SaleItems.Remove(item);
                CalculateTotals();
            }
        }

        private async void SaveSale()
        {
            try
            {
                await SaveSaleInternal();
                RequestClose?.Invoke(true);
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ الفاتورة: {ex.Message}");
                LoggingService.LogError(ex, $"خطأ في حفظ الفاتورة: {Sale.InvoiceNumber}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanSaveSale()
        {
            return SelectedCustomer != null &&
                   SaleItems.Count > 0 &&
                   !IsLoading;
        }

        private bool ValidateSale()
        {
            if (SelectedCustomer == null)
            {
                SetError("يجب اختيار عميل");
                return false;
            }

            if (SaleItems.Count == 0)
            {
                SetError("يجب إضافة صنف واحد على الأقل");
                return false;
            }

            if (Sale.Total <= 0)
            {
                SetError("مجموع الفاتورة يجب أن يكون أكبر من صفر");
                return false;
            }

            return true;
        }

        private void AddCustomer()
        {
            try
            {
                var customerDialog = new Views.Dialogs.CustomerDialog();
                if (customerDialog.ShowDialog() == true && customerDialog.Result != null)
                {
                    // Reload customers and select the new one
                    _ = LoadCustomersAsync();
                    SelectedCustomer = customerDialog.Result;
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في إضافة العميل: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في إضافة عميل من حوار المبيعات");
            }
        }

        private void AddProduct()
        {
            try
            {
                var productDialog = new Views.Dialogs.ProductDialog();
                if (productDialog.ShowDialog() == true && productDialog.Result != null)
                {
                    // Reload products and select the new one
                    _ = LoadProductsAsync();
                    SelectedProduct = productDialog.Result;
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في إضافة المنتج: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في إضافة منتج من حوار المبيعات");
            }
        }

        private async void SaveAndPrint()
        {
            try
            {
                // Save first
                await SaveSaleInternal();

                if (Sale.Id > 0) // If save was successful
                {
                    PrintSaleInvoice();
                    System.Windows.MessageBox.Show("تم حفظ الفاتورة بنجاح وإرسالها للطباعة.",
                        "نجح", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ وطباعة الفاتورة: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في حفظ وطباعة الفاتورة");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ClearAll()
        {
            try
            {
                var result = System.Windows.MessageBox.Show(
                    "هل أنت متأكد من مسح جميع البيانات؟",
                    "تأكيد المسح",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    // Clear all items
                    SaleItems.Clear();

                    // Reset sale data
                    Sale.Discount = 0;
                    Sale.Notes = string.Empty;

                    // Reset item entry
                    SelectedProduct = null;
                    ItemQuantity = 1;
                    ItemUnitPrice = 0;

                    // Recalculate totals
                    CalculateTotals();

                    LoggingService.LogSystemEvent("مسح فاتورة", "تم مسح جميع بيانات الفاتورة");
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في مسح البيانات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في مسح بيانات الفاتورة");
            }
        }

        private async Task SaveSaleInternal()
        {
            IsLoading = true;
            LoadingMessage = IsEditMode ? "جاري تحديث الفاتورة..." : "جاري حفظ الفاتورة...";
            ClearError();

            // Validate input
            if (!ValidateSale())
                return;

            // Prepare sale items
            Sale.Items.Clear();
            foreach (var item in SaleItems)
            {
                Sale.Items.Add(item);
            }

            // Save sale
            if (IsEditMode)
            {
                var success = await _saleService.UpdateSaleAsync(Sale);
                if (success)
                {
                    LoggingService.LogSystemEvent("تحديث فاتورة", $"تم تحديث الفاتورة: {Sale.InvoiceNumber}");
                    SaleSaved?.Invoke(Sale);
                }
                else
                {
                    throw new Exception("فشل في تحديث الفاتورة");
                }
            }
            else
            {
                var result = await _saleService.AddSaleAsync(Sale);
                if (result != null)
                {
                    Sale = result;
                    LoggingService.LogSystemEvent("إضافة فاتورة", $"تم إضافة الفاتورة: {Sale.InvoiceNumber}");
                    SaleSaved?.Invoke(Sale);
                }
                else
                {
                    throw new Exception("فشل في حفظ الفاتورة");
                }
            }
        }

        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }

        private void PrintSaleInvoice()
        {
            try
            {
                // إنشاء مستند الطباعة
                PrintDocument printDoc = new PrintDocument();
                printDoc.PrintPage += new PrintPageEventHandler(PrintInvoicePage);

                // عرض مربع حوار الطباعة
                System.Windows.Controls.PrintDialog printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    printDoc.Print();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في طباعة الفاتورة");
                System.Windows.MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void PrintInvoicePage(object sender, PrintPageEventArgs e)
        {
            // تحديد الخطوط والفرش للطباعة
            Font titleFont = new Font("Arial", 16, FontStyle.Bold);
            Font headerFont = new Font("Arial", 12, FontStyle.Bold);
            Font normalFont = new Font("Arial", 10);
            Brush brush = Brushes.Black;

            // تحديد المواقع والأبعاد
            float yPos = 50;
            int leftMargin = 50;
            int width = 700;

            // طباعة ترويسة الفاتورة
            e.Graphics?.DrawString("نظام إدارة المبيعات", titleFont, brush, leftMargin, yPos);
            yPos += 30;
            e.Graphics.DrawString($"فاتورة بيع رقم: {Sale.InvoiceNumber}", headerFont, brush, leftMargin, yPos);
            yPos += 30;
            e.Graphics.DrawString($"التاريخ: {Sale.Date:dd/MM/yyyy}", normalFont, brush, leftMargin, yPos);
            yPos += 20;
            e.Graphics.DrawString($"العميل: {Sale.CustomerName}", normalFont, brush, leftMargin, yPos);
            yPos += 30;

            // طباعة عناوين الأعمدة
            e.Graphics.DrawString("المنتج", headerFont, brush, leftMargin, yPos);
            e.Graphics.DrawString("الكمية", headerFont, brush, leftMargin + 250, yPos);
            e.Graphics.DrawString("السعر", headerFont, brush, leftMargin + 350, yPos);
            e.Graphics.DrawString("الإجمالي", headerFont, brush, leftMargin + 450, yPos);
            yPos += 20;

            // رسم خط أفقي
            e.Graphics.DrawLine(Pens.Black, leftMargin, yPos, leftMargin + width, yPos);
            yPos += 10;

            // طباعة عناصر الفاتورة
            foreach (var item in Sale.Items)
            {
                e.Graphics.DrawString(item.ProductName, normalFont, brush, leftMargin, yPos);
                e.Graphics.DrawString(item.Quantity.ToString(), normalFont, brush, leftMargin + 250, yPos);
                e.Graphics.DrawString(item.UnitPrice.ToString("C"), normalFont, brush, leftMargin + 350, yPos);
                e.Graphics.DrawString((item.Quantity * item.UnitPrice).ToString("C"), normalFont, brush, leftMargin + 450, yPos);
                yPos += 20;

                // التحقق من الحاجة لصفحة جديدة
                if (yPos > e.MarginBounds.Height - 150)
                {
                    e.HasMorePages = true;
                    return;
                }
            }

            // رسم خط أفقي
            e.Graphics.DrawLine(Pens.Black, leftMargin, yPos, leftMargin + width, yPos);
            yPos += 20;

            // طباعة ملخص الفاتورة
            var totalAmount = Sale.Items.Sum(i => i.Quantity * i.UnitPrice);
            e.Graphics.DrawString($"إجمالي الفاتورة: {totalAmount:C}", headerFont, brush, leftMargin + 350, yPos);
            yPos += 20;
            e.Graphics.DrawString($"الخصم: 0%", normalFont, brush, leftMargin + 350, yPos);
            yPos += 20;
            e.Graphics.DrawString($"الضريبة: 0%", normalFont, brush, leftMargin + 350, yPos);
            yPos += 20;
            e.Graphics.DrawString($"المبلغ النهائي: {totalAmount:C}", headerFont, brush, leftMargin + 350, yPos);
            yPos += 40;

            // طباعة ملاحظات
            if (!string.IsNullOrEmpty(Sale.Notes))
            {
                e.Graphics.DrawString("ملاحظات:", headerFont, brush, leftMargin, yPos);
                yPos += 20;
                e.Graphics.DrawString(Sale.Notes, normalFont, brush, leftMargin, yPos);
            }

            // لا توجد صفحات إضافية
            e.HasMorePages = false;
        }

        #endregion

        #region Events

        public event Action<Sale>? SaleSaved;
        public event Action<bool>? RequestClose;

        #endregion
    }
}
