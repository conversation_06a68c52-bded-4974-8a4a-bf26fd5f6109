using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج التفاعل مع العميل
    /// </summary>
    public class Interaction : INotifyPropertyChanged
    {
        private int _id;
        private int _customerId;
        private int? _contactId;
        private int? _opportunityId;
        private InteractionType _interactionType = InteractionType.Call;
        private InteractionDirection _direction = InteractionDirection.Outbound;
        private string _subject = string.Empty;
        private string _description = string.Empty;
        private DateTime _interactionDate = DateTime.Now;
        private TimeSpan _duration;
        private InteractionOutcome _outcome = InteractionOutcome.Successful;
        private string _nextAction = string.Empty;
        private DateTime? _nextActionDate;
        private string _attachments = string.Empty;
        private string _location = string.Empty;
        private string _participants = string.Empty;
        private string _notes = string.Empty;
        private string _recordedBy = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? ContactId
        {
            get => _contactId;
            set
            {
                if (_contactId != value)
                {
                    _contactId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? OpportunityId
        {
            get => _opportunityId;
            set
            {
                if (_opportunityId != value)
                {
                    _opportunityId = value;
                    OnPropertyChanged();
                }
            }
        }

        public InteractionType InteractionType
        {
            get => _interactionType;
            set
            {
                if (_interactionType != value)
                {
                    _interactionType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(InteractionTypeDisplay));
                    OnPropertyChanged(nameof(InteractionTypeIcon));
                }
            }
        }

        public InteractionDirection Direction
        {
            get => _direction;
            set
            {
                if (_direction != value)
                {
                    _direction = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(DirectionDisplay));
                    OnPropertyChanged(nameof(DirectionIcon));
                }
            }
        }

        public string Subject
        {
            get => _subject;
            set
            {
                if (_subject != value)
                {
                    _subject = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime InteractionDate
        {
            get => _interactionDate;
            set
            {
                if (_interactionDate != value)
                {
                    _interactionDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedInteractionDate));
                    OnPropertyChanged(nameof(DaysAgo));
                }
            }
        }

        public TimeSpan Duration
        {
            get => _duration;
            set
            {
                if (_duration != value)
                {
                    _duration = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDuration));
                }
            }
        }

        public InteractionOutcome Outcome
        {
            get => _outcome;
            set
            {
                if (_outcome != value)
                {
                    _outcome = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(OutcomeDisplay));
                    OnPropertyChanged(nameof(OutcomeColor));
                }
            }
        }

        public string NextAction
        {
            get => _nextAction;
            set
            {
                if (_nextAction != value)
                {
                    _nextAction = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? NextActionDate
        {
            get => _nextActionDate;
            set
            {
                if (_nextActionDate != value)
                {
                    _nextActionDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedNextActionDate));
                }
            }
        }

        public string Attachments
        {
            get => _attachments;
            set
            {
                if (_attachments != value)
                {
                    _attachments = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasAttachments));
                }
            }
        }

        public string Location
        {
            get => _location;
            set
            {
                if (_location != value)
                {
                    _location = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Participants
        {
            get => _participants;
            set
            {
                if (_participants != value)
                {
                    _participants = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string RecordedBy
        {
            get => _recordedBy;
            set
            {
                if (_recordedBy != value)
                {
                    _recordedBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool HasAttachments => !string.IsNullOrEmpty(Attachments);

        public int DaysAgo
        {
            get
            {
                return (int)(DateTime.Now - InteractionDate).TotalDays;
            }
        }

        // Display Properties
        public string InteractionTypeDisplay
        {
            get
            {
                return InteractionType switch
                {
                    InteractionType.Call => "مكالمة",
                    InteractionType.Email => "بريد إلكتروني",
                    InteractionType.Meeting => "اجتماع",
                    InteractionType.SMS => "رسالة نصية",
                    InteractionType.WhatsApp => "واتساب",
                    InteractionType.SocialMedia => "وسائل التواصل",
                    InteractionType.Visit => "زيارة",
                    InteractionType.Presentation => "عرض تقديمي",
                    InteractionType.Demo => "عرض توضيحي",
                    InteractionType.Proposal => "عرض سعر",
                    InteractionType.Contract => "عقد",
                    InteractionType.Support => "دعم فني",
                    InteractionType.Other => "أخرى",
                    _ => "غير محدد"
                };
            }
        }

        public string DirectionDisplay
        {
            get
            {
                return Direction switch
                {
                    InteractionDirection.Inbound => "وارد",
                    InteractionDirection.Outbound => "صادر",
                    _ => "غير محدد"
                };
            }
        }

        public string OutcomeDisplay
        {
            get
            {
                return Outcome switch
                {
                    InteractionOutcome.Successful => "ناجح",
                    InteractionOutcome.FollowUpRequired => "يحتاج متابعة",
                    InteractionOutcome.NoResponse => "لا يوجد رد",
                    InteractionOutcome.NotInterested => "غير مهتم",
                    InteractionOutcome.Postponed => "مؤجل",
                    InteractionOutcome.Cancelled => "ملغي",
                    _ => "غير محدد"
                };
            }
        }

        public string InteractionTypeIcon
        {
            get
            {
                return InteractionType switch
                {
                    InteractionType.Call => "Phone",
                    InteractionType.Email => "Email",
                    InteractionType.Meeting => "AccountGroup",
                    InteractionType.SMS => "Message",
                    InteractionType.WhatsApp => "WhatsApp",
                    InteractionType.SocialMedia => "Share",
                    InteractionType.Visit => "MapMarker",
                    InteractionType.Presentation => "Presentation",
                    InteractionType.Demo => "PlayCircle",
                    InteractionType.Proposal => "FileDocument",
                    InteractionType.Contract => "FileContract",
                    InteractionType.Support => "Headset",
                    InteractionType.Other => "DotsHorizontal",
                    _ => "Information"
                };
            }
        }

        public string DirectionIcon
        {
            get
            {
                return Direction switch
                {
                    InteractionDirection.Inbound => "ArrowDown",
                    InteractionDirection.Outbound => "ArrowUp",
                    _ => "ArrowLeftRight"
                };
            }
        }

        public string OutcomeColor
        {
            get
            {
                return Outcome switch
                {
                    InteractionOutcome.Successful => "Green",
                    InteractionOutcome.FollowUpRequired => "Orange",
                    InteractionOutcome.NoResponse => "Gray",
                    InteractionOutcome.NotInterested => "Red",
                    InteractionOutcome.Postponed => "Blue",
                    InteractionOutcome.Cancelled => "Red",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedInteractionDate => InteractionDate.ToString("dd/MM/yyyy HH:mm");
        public string FormattedDuration
        {
            get
            {
                if (Duration.TotalMinutes < 1) return "أقل من دقيقة";
                if (Duration.TotalHours < 1) return $"{Duration.Minutes} دقيقة";
                return $"{Duration.Hours} ساعة {Duration.Minutes} دقيقة";
            }
        }
        public string FormattedNextActionDate => NextActionDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");

        #endregion

        #region Methods

        public void SetDuration(int minutes)
        {
            Duration = TimeSpan.FromMinutes(minutes);
            UpdatedAt = DateTime.Now;
        }

        public void SetNextAction(string action, DateTime? actionDate = null)
        {
            NextAction = action;
            NextActionDate = actionDate;
            UpdatedAt = DateTime.Now;
        }

        public void AddAttachment(string filePath)
        {
            if (string.IsNullOrEmpty(Attachments))
                Attachments = filePath;
            else
                Attachments += ";" + filePath;
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Enums

    public enum InteractionType
    {
        Call,               // مكالمة
        Email,              // بريد إلكتروني
        Meeting,            // اجتماع
        SMS,                // رسالة نصية
        WhatsApp,           // واتساب
        SocialMedia,        // وسائل التواصل
        Visit,              // زيارة
        Presentation,       // عرض تقديمي
        Demo,               // عرض توضيحي
        Proposal,           // عرض سعر
        Contract,           // عقد
        Support,            // دعم فني
        Other               // أخرى
    }

    public enum InteractionDirection
    {
        Inbound,            // وارد
        Outbound            // صادر
    }

    public enum InteractionOutcome
    {
        Successful,         // ناجح
        FollowUpRequired,   // يحتاج متابعة
        NoResponse,         // لا يوجد رد
        NotInterested,      // غير مهتم
        Postponed,          // مؤجل
        Cancelled           // ملغي
    }

    #endregion

    #region Validation

    public class InteractionValidator : AbstractValidator<Interaction>
    {
        public InteractionValidator()
        {
            RuleFor(i => i.CustomerId)
                .GreaterThan(0).WithMessage("العميل مطلوب");

            RuleFor(i => i.Subject)
                .NotEmpty().WithMessage("موضوع التفاعل مطلوب")
                .MaximumLength(200).WithMessage("موضوع التفاعل لا يمكن أن يتجاوز 200 حرف");

            RuleFor(i => i.Description)
                .NotEmpty().WithMessage("وصف التفاعل مطلوب")
                .MaximumLength(1000).WithMessage("وصف التفاعل لا يمكن أن يتجاوز 1000 حرف");

            RuleFor(i => i.InteractionDate)
                .LessThanOrEqualTo(DateTime.Now).WithMessage("تاريخ التفاعل لا يمكن أن يكون في المستقبل");

            RuleFor(i => i.Duration)
                .GreaterThanOrEqualTo(TimeSpan.Zero).WithMessage("مدة التفاعل لا يمكن أن تكون سالبة");

            RuleFor(i => i.NextActionDate)
                .GreaterThanOrEqualTo(DateTime.Today).When(i => i.NextActionDate.HasValue)
                .WithMessage("تاريخ الإجراء التالي يجب أن يكون في المستقبل");
        }
    }

    #endregion
}
