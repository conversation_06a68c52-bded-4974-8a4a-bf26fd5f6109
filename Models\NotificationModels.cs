using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// أولوية الإشعار
    /// </summary>
    public enum NotificationPriority
    {
        Low = 0,
        Normal = 1,
        High = 2,
        Critical = 3
    }

    /// <summary>
    /// نوع الإشعار
    /// </summary>
    public enum NotificationType
    {
        Info = 0,
        Success = 1,
        Warning = 2,
        Error = 3
    }

    /// <summary>
    /// معاملات حدث إشعار Toast
    /// </summary>
    public class NotificationToastEventArgs : EventArgs
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public NotificationType Type { get; set; } = NotificationType.Info;
        public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
        public TimeSpan Duration { get; set; } = TimeSpan.FromSeconds(5);
        public string? ActionText { get; set; }
        public Action? ActionCallback { get; set; }
        public bool IsClosable { get; set; } = true;
        public string? IconKind { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نشاط العميل
    /// </summary>
    public class CustomerActivity
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public string ActivityType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime ActivityDate { get; set; } = DateTime.Now;
        public string? Details { get; set; }
        public decimal? Amount { get; set; }
        public string? Status { get; set; }
        public int? RelatedOrderId { get; set; }
        public int? RelatedProductId { get; set; }
        public string? Notes { get; set; }
        public string PerformedBy { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Customer? Customer { get; set; }
        public virtual Sale? RelatedOrder { get; set; }
        public virtual Product? RelatedProduct { get; set; }
    }

    /// <summary>
    /// فئة المنتج
    /// </summary>
    public class ProductCategory
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? ImagePath { get; set; }
        public bool IsActive { get; set; } = true;
        public int? ParentCategoryId { get; set; }
        public int SortOrder { get; set; } = 0;
        public string? MetaTitle { get; set; }
        public string? MetaDescription { get; set; }
        public string? MetaKeywords { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ProductCategory? ParentCategory { get; set; }
        public virtual ICollection<ProductCategory> SubCategories { get; set; } = new List<ProductCategory>();
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }

    /// <summary>
    /// خدمة البريد الإلكتروني
    /// </summary>
    public class EmailService
    {
        public string SmtpServer { get; set; } = string.Empty;
        public int SmtpPort { get; set; } = 587;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public bool EnableSsl { get; set; } = true;
        public string FromEmail { get; set; } = string.Empty;
        public string FromName { get; set; } = string.Empty;

        /// <summary>
        /// إرسال بريد إلكتروني
        /// </summary>
        public async Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            try
            {
                // تنفيذ إرسال البريد الإلكتروني
                await Task.Delay(100); // محاكاة الإرسال
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إرسال بريد إلكتروني متعدد المستقبلين
        /// </summary>
        public async Task<bool> SendBulkEmailAsync(List<string> recipients, string subject, string body, bool isHtml = true)
        {
            try
            {
                foreach (var recipient in recipients)
                {
                    await SendEmailAsync(recipient, subject, body, isHtml);
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إرسال بريد إلكتروني مع مرفقات
        /// </summary>
        public async Task<bool> SendEmailWithAttachmentsAsync(string to, string subject, string body,
            List<string> attachmentPaths, bool isHtml = true)
        {
            try
            {
                // تنفيذ إرسال البريد الإلكتروني مع المرفقات
                await Task.Delay(100); // محاكاة الإرسال
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة إعدادات البريد الإلكتروني
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                // اختبار الاتصال
                await Task.Delay(100);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
