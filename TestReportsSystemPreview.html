<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التقارير المتقدم - نظام إدارة المبيعات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }

        .header h1 {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            font-size: 28px;
            font-weight: 600;
        }

        .content {
            padding: 24px;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 16px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
        }

        .section-content {
            padding: 20px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .test-btn {
            padding: 16px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .test-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .test-btn:hover::before {
            left: 100%;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .test-btn.primary { background: linear-gradient(135deg, #2196F3, #1976D2); }
        .test-btn.success { background: linear-gradient(135deg, #4CAF50, #388E3C); }
        .test-btn.warning { background: linear-gradient(135deg, #FF9800, #F57C00); }
        .test-btn.info { background: linear-gradient(135deg, #00BCD4, #0097A7); }
        .test-btn.purple { background: linear-gradient(135deg, #9C27B0, #7B1FA2); }
        .test-btn.red { background: linear-gradient(135deg, #F44336, #D32F2F); }

        .results-section {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }

        .results-section::-webkit-scrollbar {
            width: 8px;
        }

        .results-section::-webkit-scrollbar-track {
            background: #2a2a2a;
        }

        .results-section::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 4px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success { background: #4CAF50; }
        .status-error { background: #F44336; }
        .status-warning { background: #FF9800; }
        .status-info { background: #2196F3; }

        .material-icons {
            font-size: 20px;
        }

        @media (max-width: 768px) {
            .button-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 24px;
            }
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 4px 0;
        }

        .log-timestamp {
            color: #888;
            margin-left: 8px;
        }

        .log-success { color: #4CAF50; }
        .log-error { color: #F44336; }
        .log-warning { color: #FF9800; }
        .log-info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <span class="material-icons">bug_report</span>
                اختبار نظام التقارير المتقدم
            </h1>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Report Generation Tests -->
            <div class="test-section">
                <div class="section-header">
                    <span class="material-icons" style="vertical-align: middle; margin-left: 8px;">assessment</span>
                    اختبارات إنشاء التقارير
                </div>
                <div class="section-content">
                    <div class="button-grid">
                        <button class="test-btn primary" onclick="testSalesReport()">
                            <span class="material-icons">attach_money</span>
                            تقرير المبيعات
                        </button>
                        <button class="test-btn success" onclick="testInventoryReport()">
                            <span class="material-icons">inventory</span>
                            تقرير المخزون
                        </button>
                        <button class="test-btn warning" onclick="testProfitLossReport()">
                            <span class="material-icons">trending_up</span>
                            الأرباح والخسائر
                        </button>
                        <button class="test-btn info" onclick="testCustomerReport()">
                            <span class="material-icons">people</span>
                            تقرير العملاء
                        </button>
                        <button class="test-btn purple" onclick="testProductReport()">
                            <span class="material-icons">shopping_cart</span>
                            تقرير المنتجات
                        </button>
                        <button class="test-btn red" onclick="testTrendAnalysis()">
                            <span class="material-icons">timeline</span>
                            تحليل الاتجاهات
                        </button>
                    </div>
                </div>
            </div>

            <!-- Export Tests -->
            <div class="test-section">
                <div class="section-header">
                    <span class="material-icons" style="vertical-align: middle; margin-left: 8px;">file_download</span>
                    اختبارات التصدير
                </div>
                <div class="section-content">
                    <div class="button-grid">
                        <button class="test-btn success" onclick="testExportExcel()">
                            <span class="material-icons">table_chart</span>
                            تصدير Excel
                        </button>
                        <button class="test-btn red" onclick="testExportPDF()">
                            <span class="material-icons">picture_as_pdf</span>
                            تصدير PDF
                        </button>
                        <button class="test-btn info" onclick="testExportCSV()">
                            <span class="material-icons">description</span>
                            تصدير CSV
                        </button>
                    </div>
                </div>
            </div>

            <!-- Chart Tests -->
            <div class="test-section">
                <div class="section-header">
                    <span class="material-icons" style="vertical-align: middle; margin-left: 8px;">bar_chart</span>
                    اختبارات الرسوم البيانية
                </div>
                <div class="section-content">
                    <div class="button-grid">
                        <button class="test-btn primary" onclick="testLineChart()">
                            <span class="material-icons">show_chart</span>
                            رسم خطي
                        </button>
                        <button class="test-btn warning" onclick="testPieChart()">
                            <span class="material-icons">pie_chart</span>
                            رسم دائري
                        </button>
                        <button class="test-btn purple" onclick="testBarChart()">
                            <span class="material-icons">bar_chart</span>
                            رسم أعمدة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Advanced Features -->
            <div class="test-section">
                <div class="section-header">
                    <span class="material-icons" style="vertical-align: middle; margin-left: 8px;">settings</span>
                    الميزات المتقدمة
                </div>
                <div class="section-content">
                    <div class="button-grid">
                        <button class="test-btn primary" onclick="openReportsWindow()">
                            <span class="material-icons">open_in_new</span>
                            فتح نافذة التقارير
                        </button>
                        <button class="test-btn success" onclick="generateTestData()">
                            <span class="material-icons">data_usage</span>
                            إنشاء بيانات تجريبية
                        </button>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="test-section">
                <div class="section-header">
                    <span class="material-icons" style="vertical-align: middle; margin-left: 8px;">terminal</span>
                    نتائج الاختبارات
                </div>
                <div class="section-content">
                    <div class="results-section" id="testResults">
                        <div class="log-entry log-info">
                            <span class="log-timestamp">[14:30:25]</span>
                            <span class="status-indicator status-info"></span>
                            نظام التقارير المتقدم جاهز للاختبار
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addLogEntry(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const statusClass = `status-${type}`;
            const logClass = `log-${type}`;
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${logClass}`;
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="status-indicator ${statusClass}"></span>
                ${message}
            `;
            
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function simulateAsyncOperation(message, duration = 2000) {
            addLogEntry(`🔍 ${message}...`, 'info');
            
            return new Promise((resolve) => {
                setTimeout(() => {
                    const success = Math.random() > 0.1; // 90% success rate
                    if (success) {
                        addLogEntry(`✅ ${message} - تم بنجاح`, 'success');
                        resolve(true);
                    } else {
                        addLogEntry(`❌ ${message} - فشل`, 'error');
                        resolve(false);
                    }
                }, duration);
            });
        }

        // Report Generation Tests
        async function testSalesReport() {
            const success = await simulateAsyncOperation('اختبار تقرير المبيعات');
            if (success) {
                addLogEntry('   الفترة: 01/01/2024 - 31/12/2024', 'info');
                addLogEntry('   إجمالي المبيعات: 2,450,000 ر.س', 'info');
                addLogEntry('   عدد المبيعات: 1,247', 'info');
            }
        }

        async function testInventoryReport() {
            const success = await simulateAsyncOperation('اختبار تقرير المخزون');
            if (success) {
                addLogEntry('   إجمالي المنتجات: 156', 'info');
                addLogEntry('   المنتجات منخفضة المخزون: 12', 'warning');
            }
        }

        async function testProfitLossReport() {
            const success = await simulateAsyncOperation('اختبار تقرير الأرباح والخسائر');
            if (success) {
                addLogEntry('   إجمالي الإيرادات: 2,450,000 ر.س', 'info');
                addLogEntry('   إجمالي التكاليف: 1,965,000 ر.س', 'info');
                addLogEntry('   صافي الربح: 485,000 ر.س', 'success');
                addLogEntry('   هامش الربح: 19.80%', 'success');
            }
        }

        async function testCustomerReport() {
            const success = await simulateAsyncOperation('اختبار تقرير العملاء');
            if (success) {
                addLogEntry('   عدد العملاء: 89', 'info');
                addLogEntry('   أفضل عميل: شركة الأمل التجارية (75,000 ر.س)', 'success');
            }
        }

        async function testProductReport() {
            const success = await simulateAsyncOperation('اختبار تقرير المنتجات');
            if (success) {
                addLogEntry('   عدد المنتجات: 156', 'info');
                addLogEntry('   أفضل منتج: لابتوب ديل XPS 13 (180,000 ر.س)', 'success');
            }
        }

        async function testTrendAnalysis() {
            const success = await simulateAsyncOperation('اختبار تحليل الاتجاهات');
            if (success) {
                addLogEntry('   معدل نمو المبيعات: 15.30%', 'success');
                addLogEntry('   معدل نمو الأرباح: 18.75%', 'success');
                addLogEntry('   عدد الأشهر المحللة: 12', 'info');
            }
        }

        // Export Tests
        async function testExportExcel() {
            const success = await simulateAsyncOperation('اختبار تصدير Excel', 1500);
            if (success) {
                addLogEntry('   تم حفظ الملف: SalesReport_2024.xlsx', 'success');
            }
        }

        async function testExportPDF() {
            const success = await simulateAsyncOperation('اختبار تصدير PDF', 1500);
            if (success) {
                addLogEntry('   تم حفظ الملف: SalesReport_2024.pdf', 'success');
            }
        }

        async function testExportCSV() {
            const success = await simulateAsyncOperation('اختبار تصدير CSV', 1000);
            if (success) {
                addLogEntry('   تم حفظ الملف: InventoryReport_2024.csv', 'success');
            }
        }

        // Chart Tests
        async function testLineChart() {
            const success = await simulateAsyncOperation('اختبار الرسم الخطي', 1000);
            if (success) {
                addLogEntry('   عدد السلاسل: 1', 'info');
                addLogEntry('   نوع البيانات: المبيعات الشهرية', 'info');
            }
        }

        async function testPieChart() {
            const success = await simulateAsyncOperation('اختبار الرسم الدائري', 1000);
            if (success) {
                addLogEntry('   عدد الشرائح: 6', 'info');
                addLogEntry('   نوع البيانات: أفضل المنتجات', 'info');
            }
        }

        async function testBarChart() {
            const success = await simulateAsyncOperation('اختبار رسم الأعمدة', 1000);
            if (success) {
                addLogEntry('   عدد السلاسل: 2', 'info');
                addLogEntry('   نوع البيانات: الأرباح والخسائر', 'info');
            }
        }

        // Advanced Features
        function openReportsWindow() {
            addLogEntry('🪟 فتح نافذة التقارير...', 'info');
            setTimeout(() => {
                window.open('ReportsSystemPreview.html', '_blank');
                addLogEntry('✅ تم فتح نافذة التقارير بنجاح', 'success');
            }, 500);
        }

        async function generateTestData() {
            addLogEntry('🎲 إنشاء بيانات تجريبية...', 'info');
            
            // Simulate multiple steps
            setTimeout(() => addLogEntry('   إنشاء عملاء وهميين...', 'info'), 500);
            setTimeout(() => addLogEntry('   إنشاء منتجات وهمية...', 'info'), 1000);
            setTimeout(() => addLogEntry('   إنشاء مبيعات وهمية...', 'info'), 1500);
            setTimeout(() => addLogEntry('   إنشاء مشتريات وهمية...', 'info'), 2000);
            
            setTimeout(() => {
                addLogEntry('✅ تم إنشاء البيانات التجريبية بنجاح', 'success');
                addLogEntry('   تم إنشاء 50 عميل وهمي', 'info');
                addLogEntry('   تم إنشاء 100 منتج وهمي', 'info');
                addLogEntry('   تم إنشاء 500 عملية بيع وهمية', 'info');
                addLogEntry('   تم إنشاء 200 عملية شراء وهمية', 'info');
            }, 2500);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addLogEntry('🚀 تم تحميل نظام اختبار التقارير بنجاح', 'success');
            addLogEntry('📊 جميع الوحدات جاهزة للاختبار', 'info');
        });
    </script>
</body>
</html>
