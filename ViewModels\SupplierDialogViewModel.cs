using System;
using System.Threading.Tasks;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class SupplierDialogViewModel : BaseViewModel
    {
        #region Services
        
        private readonly SupplierService _supplierService;
        
        #endregion

        #region Properties

        private Supplier _supplier = new();
        public Supplier Supplier
        {
            get => _supplier;
            set
            {
                if (SetProperty(ref _supplier, value))
                {
                    OnSupplierChanged();
                }
            }
        }

        private string _windowTitle = "إضافة مورد جديد";
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }

        private bool _isEditMode;
        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                if (SetProperty(ref _isEditMode, value))
                {
                    UpdateWindowTitle();
                }
            }
        }

        #endregion

        #region Commands

        private DelegateCommand? _saveCommand;
        public DelegateCommand SaveCommand => _saveCommand ??= new DelegateCommand(SaveSupplier, CanSaveSupplier);

        private DelegateCommand? _cancelCommand;
        public DelegateCommand CancelCommand => _cancelCommand ??= new DelegateCommand(Cancel);

        #endregion

        #region Events

        public event Action<Supplier>? SupplierSaved;
        public event Action<bool>? RequestClose;

        #endregion

        #region Constructor

        public SupplierDialogViewModel()
        {
            var dbService = new DatabaseService();
            _supplierService = new SupplierService(dbService);
            
            // مراقبة تغييرات خصائص المورد
            Supplier.PropertyChanged += Supplier_PropertyChanged;
        }

        public SupplierDialogViewModel(Supplier supplier) : this()
        {
            Supplier = supplier.Clone();
            IsEditMode = true;
        }

        #endregion

        #region Methods

        private void OnSupplierChanged()
        {
            SaveCommand.RaiseCanExecuteChanged();
        }

        private void Supplier_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // تحديث حالة زر الحفظ عند تغيير أي خاصية من خصائص المورد
            SaveCommand.RaiseCanExecuteChanged();
        }

        private void UpdateWindowTitle()
        {
            WindowTitle = IsEditMode ? "تعديل المورد" : "إضافة مورد جديد";
        }

        private async void SaveSupplier()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = IsEditMode ? "جاري تحديث المورد..." : "جاري حفظ المورد...";
                ClearError();

                // Validate input
                if (!ValidateSupplier())
                {
                    return;
                }

                // Save supplier
                if (IsEditMode)
                {
                    var success = await _supplierService.UpdateSupplierAsync(Supplier);
                    if (success)
                    {
                        LoggingService.LogSystemEvent("تحديث مورد", $"تم تحديث المورد '{Supplier.Name}' بنجاح");

                        // عرض رسالة نجاح
                        System.Windows.MessageBox.Show(
                            $"تم تحديث المورد '{Supplier.Name}' بنجاح!",
                            "نجح التحديث",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Information);

                        SupplierSaved?.Invoke(Supplier);
                        RequestClose?.Invoke(true);
                    }
                    else
                    {
                        SetError("فشل في تحديث المورد");
                    }
                }
                else
                {
                    var savedSupplier = await _supplierService.AddSupplierAsync(Supplier);
                    if (savedSupplier != null)
                    {
                        Supplier = savedSupplier;
                        LoggingService.LogSystemEvent("إضافة مورد", $"تم إضافة المورد '{Supplier.Name}' بنجاح");

                        // عرض رسالة نجاح
                        System.Windows.MessageBox.Show(
                            $"تم إضافة المورد '{Supplier.Name}' بنجاح!",
                            "نجح الحفظ",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Information);

                        SupplierSaved?.Invoke(Supplier);
                        RequestClose?.Invoke(true);
                    }
                    else
                    {
                        SetError("فشل في حفظ المورد");
                    }
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ المورد: {ex.Message}");
                LoggingService.LogError(ex, $"خطأ في حفظ المورد: {Supplier.Name}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanSaveSupplier()
        {
            return !string.IsNullOrWhiteSpace(Supplier.Name) && !IsLoading;
        }

        private bool ValidateSupplier()
        {
            ClearValidationErrors();

            if (string.IsNullOrWhiteSpace(Supplier.Name))
            {
                AddValidationError(nameof(Supplier.Name), "اسم المورد مطلوب");
            }

            if (!string.IsNullOrWhiteSpace(Supplier.Email) && !IsValidEmail(Supplier.Email))
            {
                AddValidationError(nameof(Supplier.Email), "البريد الإلكتروني غير صحيح");
            }

            if (HasValidationErrors)
            {
                var firstError = GetValidationErrors(nameof(Supplier.Name)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Supplier.Email)).FirstOrDefault();

                SetError(firstError ?? "يرجى تصحيح الأخطاء المدخلة");
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void ClearValidationErrors()
        {
            // Clear all validation errors
            var properties = new[] { nameof(Supplier.Name), nameof(Supplier.Email) };
            foreach (var property in properties)
            {
                var errors = GetValidationErrors(property).ToList();
                foreach (var error in errors)
                {
                    RemoveValidationError(property, error);
                }
            }
        }

        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }

        #endregion
    }
}
