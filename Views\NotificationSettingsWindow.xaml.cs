using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// نافذة إدارة الإشعارات
    /// </summary>
    public partial class NotificationSettingsWindow : Window
    {
        private readonly NotificationSettingsService _notificationSettingsService;
        private readonly EnhancedNotificationService _notificationService;

        public NotificationSettingsWindow()
        {
            InitializeComponent();
            _notificationSettingsService = NotificationSettingsService.Instance;
            _notificationService = EnhancedNotificationService.Instance;
            
            LoadCurrentSettings();
            UpdateActiveNotificationsCount();
        }

        // تحميل الإعدادات الحالية
        private void LoadCurrentSettings()
        {
            try
            {
                var settings = _notificationSettingsService.GetCurrentSettings();
                
                // إشعارات المبيعات
                SaleCompletedCheckBox.IsChecked = settings.SaleCompleted;
                SaleCancelledCheckBox.IsChecked = settings.SaleCancelled;
                DailySalesTargetCheckBox.IsChecked = settings.DailySalesTarget;
                LargeSalesCheckBox.IsChecked = settings.LargeSales;
                
                // إشعارات المخزون
                StockOutCheckBox.IsChecked = settings.StockOut;
                LowStockCheckBox.IsChecked = settings.LowStock;
                NewProductCheckBox.IsChecked = settings.NewProduct;
                PriceUpdateCheckBox.IsChecked = settings.PriceUpdate;
                
                // إشعارات النظام
                UserLoginCheckBox.IsChecked = settings.UserLogin;
                BackupCreatedCheckBox.IsChecked = settings.BackupCreated;
                SystemErrorCheckBox.IsChecked = settings.SystemError;
                
                // إشعارات الأمان
                FailedLoginCheckBox.IsChecked = settings.FailedLogin;
                PasswordChangeCheckBox.IsChecked = settings.PasswordChange;
                NewUserCheckBox.IsChecked = settings.NewUser;
                
                // إعدادات عامة
                NotificationSoundsCheckBox.IsChecked = settings.NotificationSounds;
                SaveNotificationLogCheckBox.IsChecked = settings.SaveNotificationLog;
                NotificationDurationTextBox.Text = settings.NotificationDuration.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // تحديث عدد الإشعارات النشطة
        private void UpdateActiveNotificationsCount()
        {
            try
            {
                var settings = _notificationSettingsService.GetCurrentSettings();
                var activeCount = CountActiveNotifications(settings);
                ActiveNotificationsCount.Text = activeCount.ToString();
            }
            catch
            {
                ActiveNotificationsCount.Text = "0";
            }
        }

        // حساب عدد الإشعارات النشطة
        private int CountActiveNotifications(NotificationSettingsService.NotificationSettings settings)
        {
            int count = 0;
            
            // إشعارات المبيعات
            if (settings.SaleCompleted) count++;
            if (settings.SaleCancelled) count++;
            if (settings.DailySalesTarget) count++;
            if (settings.LargeSales) count++;
            
            // إشعارات المخزون
            if (settings.StockOut) count++;
            if (settings.LowStock) count++;
            if (settings.NewProduct) count++;
            if (settings.PriceUpdate) count++;
            
            // إشعارات النظام
            if (settings.UserLogin) count++;
            if (settings.BackupCreated) count++;
            if (settings.SystemError) count++;
            
            // إشعارات الأمان
            if (settings.FailedLogin) count++;
            if (settings.PasswordChange) count++;
            if (settings.NewUser) count++;
            
            return count;
        }

        // حفظ الإعدادات
        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateSettings())
                    return;

                var settings = new NotificationSettingsService.NotificationSettings
                {
                    // إشعارات المبيعات
                    SaleCompleted = SaleCompletedCheckBox.IsChecked ?? false,
                    SaleCancelled = SaleCancelledCheckBox.IsChecked ?? false,
                    DailySalesTarget = DailySalesTargetCheckBox.IsChecked ?? false,
                    LargeSales = LargeSalesCheckBox.IsChecked ?? false,
                    
                    // إشعارات المخزون
                    StockOut = StockOutCheckBox.IsChecked ?? false,
                    LowStock = LowStockCheckBox.IsChecked ?? false,
                    NewProduct = NewProductCheckBox.IsChecked ?? false,
                    PriceUpdate = PriceUpdateCheckBox.IsChecked ?? false,
                    
                    // إشعارات النظام
                    UserLogin = UserLoginCheckBox.IsChecked ?? false,
                    BackupCreated = BackupCreatedCheckBox.IsChecked ?? false,
                    SystemError = SystemErrorCheckBox.IsChecked ?? false,
                    
                    // إشعارات الأمان
                    FailedLogin = FailedLoginCheckBox.IsChecked ?? false,
                    PasswordChange = PasswordChangeCheckBox.IsChecked ?? false,
                    NewUser = NewUserCheckBox.IsChecked ?? false,
                    
                    // إعدادات عامة
                    NotificationSounds = NotificationSoundsCheckBox.IsChecked ?? false,
                    SaveNotificationLog = SaveNotificationLogCheckBox.IsChecked ?? false,
                    NotificationDuration = int.Parse(NotificationDurationTextBox.Text)
                };

                if (_notificationSettingsService.SaveSettings(settings))
                {
                    UpdateActiveNotificationsCount();
                    
                    MessageBox.Show("تم حفظ إعدادات الإشعارات بنجاح", "نجح الحفظ", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                        
                    _notificationService.AddNotification(
                        "إعدادات الإشعارات",
                        "تم تحديث إعدادات الإشعارات بنجاح",
                        Models.EnhancedNotificationType.Success,
                        Models.EnhancedNotificationPriority.Normal,
                        "نظام");
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الإعدادات", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // التحقق من صحة الإعدادات
        private bool ValidateSettings()
        {
            // التحقق من مدة عرض الإشعار
            if (!int.TryParse(NotificationDurationTextBox.Text, out int duration) || duration < 1 || duration > 60)
            {
                MessageBox.Show("مدة عرض الإشعار يجب أن تكون بين 1 و 60 ثانية", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                NotificationDurationTextBox.Focus();
                return false;
            }

            return true;
        }

        // تحديد جميع الإشعارات
        private void SelectAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SetAllCheckBoxes(true);
                UpdateActiveNotificationsCount();
                
                MessageBox.Show("تم تفعيل جميع الإشعارات", "معلومات", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد الكل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // إلغاء تحديد جميع الإشعارات
        private void DeselectAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SetAllCheckBoxes(false);
                UpdateActiveNotificationsCount();
                
                MessageBox.Show("تم إلغاء تفعيل جميع الإشعارات", "معلومات", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء التحديد: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // تعيين حالة جميع مربعات الاختيار
        private void SetAllCheckBoxes(bool isChecked)
        {
            // إشعارات المبيعات
            SaleCompletedCheckBox.IsChecked = isChecked;
            SaleCancelledCheckBox.IsChecked = isChecked;
            DailySalesTargetCheckBox.IsChecked = isChecked;
            LargeSalesCheckBox.IsChecked = isChecked;
            
            // إشعارات المخزون
            StockOutCheckBox.IsChecked = isChecked;
            LowStockCheckBox.IsChecked = isChecked;
            NewProductCheckBox.IsChecked = isChecked;
            PriceUpdateCheckBox.IsChecked = isChecked;
            
            // إشعارات النظام
            UserLoginCheckBox.IsChecked = isChecked;
            BackupCreatedCheckBox.IsChecked = isChecked;
            SystemErrorCheckBox.IsChecked = isChecked;
            
            // إشعارات الأمان
            FailedLoginCheckBox.IsChecked = isChecked;
            PasswordChangeCheckBox.IsChecked = isChecked;
            NewUserCheckBox.IsChecked = isChecked;
            
            // إعدادات عامة (باستثناء مدة العرض)
            NotificationSoundsCheckBox.IsChecked = isChecked;
            SaveNotificationLogCheckBox.IsChecked = isChecked;
        }

        // إعادة تعيين الإعدادات
        private void Reset_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إعادة تعيين جميع إعدادات الإشعارات إلى القيم الافتراضية؟",
                "تأكيد إعادة التعيين", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _notificationSettingsService.ResetToDefaults();
                    LoadCurrentSettings();
                    UpdateActiveNotificationsCount();
                    
                    MessageBox.Show("تم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية", "تم بنجاح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                        
                    _notificationService.AddNotification(
                        "إعادة تعيين الإعدادات",
                        "تم إعادة تعيين إعدادات الإشعارات إلى القيم الافتراضية",
                        Models.EnhancedNotificationType.Warning,
                        Models.EnhancedNotificationPriority.Normal,
                        "نظام");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة التعيين: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // إغلاق النافذة
        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
