using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views.Dialogs
{
    public partial class CustomerDialog : Window
    {
        private readonly CustomerDialogViewModel _viewModel;

        public Customer? Result { get; private set; }

        public CustomerDialog(Customer? existingCustomer = null)
        {
            InitializeComponent();
            
            _viewModel = existingCustomer != null 
                ? new CustomerDialogViewModel(existingCustomer) 
                : new CustomerDialogViewModel();
            
            DataContext = _viewModel;
            
            // Subscribe to events
            _viewModel.CustomerSaved += OnCustomerSaved;
            _viewModel.RequestClose += OnRequestClose;
        }

        #region Event Handlers

        private void OnCustomerSaved(Customer customer)
        {
            Result = customer;
            DialogResult = true;
        }

        private void OnRequestClose(bool result)
        {
            DialogResult = result;
            Close();
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // Unsubscribe from events
            _viewModel.CustomerSaved -= OnCustomerSaved;
            _viewModel.RequestClose -= OnRequestClose;
            
            base.OnClosed(e);
        }

        #endregion
    }
}
