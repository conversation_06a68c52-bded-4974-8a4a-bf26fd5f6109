using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج جهة الاتصال
    /// </summary>
    public class Contact : INotifyPropertyChanged
    {
        private int _id;
        private int _customerId;
        private string _firstName = string.Empty;
        private string _lastName = string.Empty;
        private string _jobTitle = string.Empty;
        private string _department = string.Empty;
        private ContactType _contactType = ContactType.Primary;
        private string _email = string.Empty;
        private string _phone = string.Empty;
        private string _mobile = string.Empty;
        private string _extension = string.Empty;
        private string _directLine = string.Empty;
        private DateTime? _birthDate;
        private string _notes = string.Empty;
        private bool _isActive = true;
        private bool _isPrimary;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string FirstName
        {
            get => _firstName;
            set
            {
                if (_firstName != value)
                {
                    _firstName = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullName));
                }
            }
        }

        public string LastName
        {
            get => _lastName;
            set
            {
                if (_lastName != value)
                {
                    _lastName = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullName));
                }
            }
        }

        public string JobTitle
        {
            get => _jobTitle;
            set
            {
                if (_jobTitle != value)
                {
                    _jobTitle = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Department
        {
            get => _department;
            set
            {
                if (_department != value)
                {
                    _department = value;
                    OnPropertyChanged();
                }
            }
        }

        public ContactType ContactType
        {
            get => _contactType;
            set
            {
                if (_contactType != value)
                {
                    _contactType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ContactTypeDisplay));
                    OnPropertyChanged(nameof(ContactTypeIcon));
                }
            }
        }

        public string Email
        {
            get => _email;
            set
            {
                if (_email != value)
                {
                    _email = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Phone
        {
            get => _phone;
            set
            {
                if (_phone != value)
                {
                    _phone = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Mobile
        {
            get => _mobile;
            set
            {
                if (_mobile != value)
                {
                    _mobile = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Extension
        {
            get => _extension;
            set
            {
                if (_extension != value)
                {
                    _extension = value;
                    OnPropertyChanged();
                }
            }
        }

        public string DirectLine
        {
            get => _directLine;
            set
            {
                if (_directLine != value)
                {
                    _directLine = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? BirthDate
        {
            get => _birthDate;
            set
            {
                if (_birthDate != value)
                {
                    _birthDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedBirthDate));
                    OnPropertyChanged(nameof(Age));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsPrimary
        {
            get => _isPrimary;
            set
            {
                if (_isPrimary != value)
                {
                    _isPrimary = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public string FullName => $"{FirstName} {LastName}".Trim();

        public int Age
        {
            get
            {
                if (!BirthDate.HasValue) return 0;
                var today = DateTime.Today;
                var age = today.Year - BirthDate.Value.Year;
                if (BirthDate.Value.Date > today.AddYears(-age)) age--;
                return age;
            }
        }

        public string ContactTypeDisplay
        {
            get
            {
                return ContactType switch
                {
                    ContactType.Primary => "رئيسي",
                    ContactType.Secondary => "ثانوي",
                    ContactType.Technical => "تقني",
                    ContactType.Financial => "مالي",
                    ContactType.Administrative => "إداري",
                    ContactType.Sales => "مبيعات",
                    ContactType.Support => "دعم",
                    _ => "غير محدد"
                };
            }
        }

        public string ContactTypeIcon
        {
            get
            {
                return ContactType switch
                {
                    ContactType.Primary => "Star",
                    ContactType.Secondary => "Account",
                    ContactType.Technical => "Wrench",
                    ContactType.Financial => "CurrencyUsd",
                    ContactType.Administrative => "Briefcase",
                    ContactType.Sales => "Handshake",
                    ContactType.Support => "Headset",
                    _ => "Account"
                };
            }
        }

        public string FormattedBirthDate => BirthDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.Now;
        }

        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.Now;
        }

        public void SetAsPrimary()
        {
            IsPrimary = true;
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Enums

    public enum ContactType
    {
        Primary,        // رئيسي
        Secondary,      // ثانوي
        Technical,      // تقني
        Financial,      // مالي
        Administrative, // إداري
        Sales,          // مبيعات
        Support         // دعم
    }

    #endregion

    #region Validation

    public class ContactValidator : AbstractValidator<Contact>
    {
        public ContactValidator()
        {
            RuleFor(c => c.FirstName)
                .NotEmpty().WithMessage("الاسم الأول مطلوب")
                .MaximumLength(50).WithMessage("الاسم الأول لا يمكن أن يتجاوز 50 حرف");

            RuleFor(c => c.LastName)
                .NotEmpty().WithMessage("الاسم الأخير مطلوب")
                .MaximumLength(50).WithMessage("الاسم الأخير لا يمكن أن يتجاوز 50 حرف");

            RuleFor(c => c.Email)
                .EmailAddress().When(c => !string.IsNullOrEmpty(c.Email))
                .WithMessage("البريد الإلكتروني غير صحيح");

            RuleFor(c => c.CustomerId)
                .GreaterThan(0).WithMessage("معرف العميل مطلوب");

            RuleFor(c => c.BirthDate)
                .LessThan(DateTime.Today).When(c => c.BirthDate.HasValue)
                .WithMessage("تاريخ الميلاد يجب أن يكون في الماضي");
        }
    }

    #endregion
}
