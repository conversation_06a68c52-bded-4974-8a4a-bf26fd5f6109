using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.Views.Dialogs;

namespace SalesManagementSystem
{
    public partial class TestSaleDialog : Window
    {
        public TestSaleDialog()
        {
            InitializeComponent();
        }

        private void NewSaleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saleDialog = new SaleDialog();
                var result = saleDialog.ShowDialog();
                
                if (result == true && saleDialog.Result != null)
                {
                    var sale = saleDialog.Result;
                    ResultTextBlock.Text = $"تم إنشاء فاتورة جديدة:\n" +
                                         $"رقم الفاتورة: {sale.InvoiceNumber}\n" +
                                         $"العميل: {sale.CustomerName}\n" +
                                         $"المجموع: {sale.Total:F2}\n" +
                                         $"عدد الأصناف: {sale.Items.Count}";
                }
                else
                {
                    ResultTextBlock.Text = "تم إلغاء إنشاء الفاتورة";
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                ResultTextBlock.Text = $"خطأ: {ex.Message}";
            }
        }

        private void EditSaleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Create a sample sale for editing
                var sampleSale = new Sale
                {
                    Id = 1,
                    InvoiceNumber = "INV-20241201-001",
                    CustomerId = 1,
                    CustomerName = "عميل تجريبي",
                    Date = System.DateTime.Now,
                    PaymentMethod = "نقدي",
                    PaymentStatus = "مدفوع",
                    Notes = "فاتورة تجريبية للاختبار"
                };
                
                // Add sample items
                sampleSale.Items.Add(new SaleItem
                {
                    Id = 1,
                    ProductId = 1,
                    ProductName = "منتج تجريبي 1",
                    ProductCode = "PRD001",
                    Quantity = 2,
                    UnitPrice = 50.00m,
                    Total = 100.00m
                });
                
                sampleSale.Items.Add(new SaleItem
                {
                    Id = 2,
                    ProductId = 2,
                    ProductName = "منتج تجريبي 2",
                    ProductCode = "PRD002",
                    Quantity = 1,
                    UnitPrice = 75.00m,
                    Total = 75.00m
                });
                
                sampleSale.CalculateSubtotal();
                
                var saleDialog = new SaleDialog(sampleSale);
                var result = saleDialog.ShowDialog();
                
                if (result == true && saleDialog.Result != null)
                {
                    var sale = saleDialog.Result;
                    ResultTextBlock.Text = $"تم تحديث الفاتورة:\n" +
                                         $"رقم الفاتورة: {sale.InvoiceNumber}\n" +
                                         $"العميل: {sale.CustomerName}\n" +
                                         $"المجموع: {sale.Total:F2}\n" +
                                         $"عدد الأصناف: {sale.Items.Count}";
                }
                else
                {
                    ResultTextBlock.Text = "تم إلغاء تحديث الفاتورة";
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                ResultTextBlock.Text = $"خطأ: {ex.Message}";
            }
        }
    }
}
