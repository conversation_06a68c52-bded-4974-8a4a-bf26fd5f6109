<Window x:Class="SalesManagementSystem.Views.Dialogs.ChangePasswordDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تغيير كلمة المرور" 
        Height="500" 
        Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
        
        <Style x:Key="PasswordTextBox" TargetType="PasswordBox" BasedOn="{StaticResource MaterialDesignOutlinedPasswordBox}">
            <Setter Property="Margin" Value="0,12"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="LockReset" 
                                   Width="32" Height="32" 
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="تغيير كلمة المرور" 
                      Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                      VerticalAlignment="Center" 
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- User Info -->
                <GroupBox Header="معلومات المستخدم" 
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <Grid Margin="16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="اسم المستخدم" 
                                      Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                      Opacity="0.7"/>
                            <TextBlock Text="{Binding User.Username}" 
                                      Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                      FontWeight="Medium"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="الاسم الكامل" 
                                      Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                      Opacity="0.7"/>
                            <TextBlock Text="{Binding User.FullName}" 
                                      Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                      FontWeight="Medium"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- Password Change Form -->
                <GroupBox Header="تغيير كلمة المرور" 
                         Style="{DynamicResource MaterialDesignCardGroupBox}">
                    <StackPanel Margin="16">
                        
                        <!-- Current Password -->
                        <PasswordBox x:Name="CurrentPasswordBox"
                                    Style="{StaticResource PasswordTextBox}"
                                    materialDesign:HintAssist.Hint="كلمة المرور الحالية"
                                    PasswordChanged="CurrentPasswordBox_PasswordChanged"
                                    Visibility="{Binding RequireCurrentPassword, Converter={StaticResource BoolToVisConverter}}"/>

                        <!-- New Password -->
                        <PasswordBox x:Name="NewPasswordBox"
                                    Style="{StaticResource PasswordTextBox}"
                                    materialDesign:HintAssist.Hint="كلمة المرور الجديدة"
                                    PasswordChanged="NewPasswordBox_PasswordChanged"/>

                        <!-- Password Strength Indicator -->
                        <Border Background="{DynamicResource MaterialDesignSelection}"
                               CornerRadius="4"
                               Padding="12"
                               Margin="0,8,0,0"
                               Visibility="{Binding ShowPasswordStrength, Converter={StaticResource BoolToVisConverter}}">
                            <StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0"
                                              Text="قوة كلمة المرور:"
                                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                              VerticalAlignment="Center"/>
                                    
                                    <ProgressBar Grid.Column="1"
                                                Value="{Binding PasswordStrengthScore}"
                                                Maximum="5"
                                                Height="8"
                                                Margin="8,0"
                                                VerticalAlignment="Center">
                                        <ProgressBar.Foreground>
                                            <SolidColorBrush Color="{Binding PasswordStrengthColor}"/>
                                        </ProgressBar.Foreground>
                                    </ProgressBar>
                                    
                                    <TextBlock Grid.Column="2"
                                              Text="{Binding PasswordStrengthText}"
                                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                              FontWeight="Medium"
                                              VerticalAlignment="Center">
                                        <TextBlock.Foreground>
                                            <SolidColorBrush Color="{Binding PasswordStrengthColor}"/>
                                        </TextBlock.Foreground>
                                    </TextBlock>
                                </Grid>
                                
                                <!-- Password Requirements -->
                                <ItemsControl ItemsSource="{Binding PasswordRequirements}" 
                                             Margin="0,8,0,0">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" Margin="0,2">
                                                <materialDesign:PackIcon Kind="{Binding Icon}" 
                                                                       Width="16" Height="16"
                                                                       VerticalAlignment="Center"
                                                                       Foreground="{Binding Color}"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding Text}"
                                                          Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                                          VerticalAlignment="Center"
                                                          Foreground="{Binding Color}"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>

                        <!-- Confirm Password -->
                        <PasswordBox x:Name="ConfirmPasswordBox"
                                    Style="{StaticResource PasswordTextBox}"
                                    materialDesign:HintAssist.Hint="تأكيد كلمة المرور الجديدة"
                                    PasswordChanged="ConfirmPasswordBox_PasswordChanged"/>

                        <!-- Password Match Indicator -->
                        <StackPanel Orientation="Horizontal" 
                                   Margin="0,8,0,0"
                                   Visibility="{Binding ShowPasswordMatch, Converter={StaticResource BoolToVisConverter}}">
                            <materialDesign:PackIcon Kind="{Binding PasswordMatchIcon}" 
                                                   Width="16" Height="16"
                                                   VerticalAlignment="Center"
                                                   Foreground="{Binding PasswordMatchColor}"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding PasswordMatchText}"
                                      Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                      VerticalAlignment="Center"
                                      Foreground="{Binding PasswordMatchColor}"/>
                        </StackPanel>

                        <!-- Error Message -->
                        <Border Background="#FFEBEE"
                               BorderBrush="#F44336"
                               BorderThickness="1"
                               CornerRadius="4"
                               Padding="12"
                               Margin="0,16,0,0"
                               Visibility="{Binding HasError, Converter={StaticResource BoolToVisConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AlertCircle" 
                                                       Foreground="#F44336"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding ErrorMessage}"
                                          Foreground="#F44336"
                                          TextWrapping="Wrap"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Left" 
                   Margin="0,24,0,0">
            
            <Button Command="{Binding ChangePasswordCommand}"
                   Style="{DynamicResource MaterialDesignRaisedButton}"
                   Width="120"
                   Margin="0,0,12,0"
                   IsEnabled="{Binding CanChangePassword}">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ContentSave" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"
                                           Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}, ConverterParameter=Inverted}"/>
                    <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                               Width="16" Height="16"
                               IsIndeterminate="True"
                               Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}"
                               Margin="0,0,8,0"/>
                    <TextBlock Text="تغيير" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <Button Command="{Binding CancelCommand}"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Width="100">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Cancel" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="إلغاء" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </StackPanel>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}">
            <StackPanel HorizontalAlignment="Center" 
                       VerticalAlignment="Center">
                <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="48" Height="48"/>
                <TextBlock Text="{Binding LoadingMessage}" 
                          Foreground="White"
                          HorizontalAlignment="Center"
                          Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
