using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج بيانات الأرباح
    /// </summary>
    public class ProfitData : INotifyPropertyChanged
    {
        private DateTime _date;
        private decimal _totalSales;
        private decimal _totalExpenses;
        private decimal _netProfit;
        private int _salesCount;
        private int _expensesCount;

        public DateTime Date
        {
            get => _date;
            set
            {
                _date = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(FormattedDate));
                OnPropertyChanged(nameof(FormattedTime));
            }
        }

        public decimal TotalSales
        {
            get => _totalSales;
            set
            {
                _totalSales = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(FormattedSales));
                UpdateNetProfit();
            }
        }

        public decimal TotalExpenses
        {
            get => _totalExpenses;
            set
            {
                _totalExpenses = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(FormattedExpenses));
                UpdateNetProfit();
            }
        }

        public decimal NetProfit
        {
            get => _netProfit;
            private set
            {
                _netProfit = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(FormattedNetProfit));
                OnPropertyChanged(nameof(ProfitColor));
                OnPropertyChanged(nameof(ProfitIcon));
            }
        }

        public int SalesCount
        {
            get => _salesCount;
            set
            {
                _salesCount = value;
                OnPropertyChanged();
            }
        }

        public int ExpensesCount
        {
            get => _expensesCount;
            set
            {
                _expensesCount = value;
                OnPropertyChanged();
            }
        }

        // خصائص للعرض المنسق
        public string FormattedDate => Date.ToString("yyyy/MM/dd");
        public string FormattedTime => Date.ToString("HH:mm");
        public string FormattedSales => $"{TotalSales:F2} دج";
        public string FormattedExpenses => $"{TotalExpenses:F2} دج";
        public string FormattedNetProfit => $"{NetProfit:F2} دج";

        public string ProfitColor => NetProfit >= 0 ? "Green" : "Red";
        public string ProfitIcon => NetProfit >= 0 ? "📈" : "📉";

        private void UpdateNetProfit()
        {
            NetProfit = TotalSales - TotalExpenses;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج نقطة الرسم البياني للأرباح
    /// </summary>
    public class ProfitChartDataPoint
    {
        public string Label { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public DateTime DateTime { get; set; }
        public string FormattedValue => $"{Value:F2} دج";
        public string Color => Value >= 0 ? "#4CAF50" : "#F44336";
    }

    /// <summary>
    /// فترة زمنية للتقرير
    /// </summary>
    public enum ProfitPeriod
    {
        Today,          // اليوم (بالساعات)
        Yesterday,      // أمس
        ThisWeek,       // هذا الأسبوع
        ThisMonth,      // هذا الشهر
        LastMonth,      // الشهر الماضي
        Custom          // فترة مخصصة
    }

    /// <summary>
    /// ملخص الأرباح
    /// </summary>
    public class ProfitSummary
    {
        public decimal TotalSales { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit => TotalSales - TotalExpenses;
        public decimal ProfitMargin => TotalSales > 0 ? (NetProfit / TotalSales) * 100 : 0;
        public int TotalTransactions { get; set; }
        public int TotalExpenseEntries { get; set; }
        public decimal AverageSaleValue => TotalTransactions > 0 ? TotalSales / TotalTransactions : 0;
        public decimal AverageExpenseValue => TotalExpenseEntries > 0 ? TotalExpenses / TotalExpenseEntries : 0;

        // خصائص للعرض المنسق
        public string FormattedTotalSales => $"{TotalSales:F2} دج";
        public string FormattedTotalExpenses => $"{TotalExpenses:F2} دج";
        public string FormattedNetProfit => $"{NetProfit:F2} دج";
        public string FormattedProfitMargin => $"{ProfitMargin:F1}%";
        public string FormattedAverageSale => $"{AverageSaleValue:F2} دج";
        public string FormattedAverageExpense => $"{AverageExpenseValue:F2} دج";

        public string ProfitColor => NetProfit >= 0 ? "Green" : "Red";
        public string ProfitIcon => NetProfit >= 0 ? "📈" : "📉";
        public string MarginColor => ProfitMargin >= 20 ? "Green" : ProfitMargin >= 10 ? "Orange" : "Red";
    }

    /// <summary>
    /// خدمة حساب الأرباح
    /// </summary>
    public static class ProfitCalculator
    {
        /// <summary>
        /// حساب الأرباح لفترة محددة
        /// </summary>
        public static ProfitSummary CalculateProfitSummary(DateTime startDate, DateTime endDate,
            List<Sale> sales, List<Expense> expenses)
        {
            var filteredSales = sales.Where(s => s.Date >= startDate && s.Date <= endDate).ToList();
            var filteredExpenses = expenses.Where(e => e.Date >= startDate && e.Date <= endDate).ToList();

            return new ProfitSummary
            {
                TotalSales = filteredSales.Sum(s => s.Total),
                TotalExpenses = filteredExpenses.Sum(e => e.Amount),
                TotalTransactions = filteredSales.Count,
                TotalExpenseEntries = filteredExpenses.Count
            };
        }

        /// <summary>
        /// إنشاء بيانات الرسم البياني بالساعات
        /// </summary>
        public static List<ProfitChartDataPoint> CreateHourlyChartData(DateTime date,
            List<Sale> sales, List<Expense> expenses)
        {
            var chartData = new List<ProfitChartDataPoint>();

            for (int hour = 0; hour < 24; hour++)
            {
                var hourStart = date.Date.AddHours(hour);
                var hourEnd = hourStart.AddHours(1);

                var hourlySales = sales.Where(s => s.Date >= hourStart && s.Date < hourEnd).Sum(s => s.Total);
                var hourlyExpenses = expenses.Where(e => e.Date >= hourStart && e.Date < hourEnd).Sum(e => e.Amount);
                var hourlyProfit = hourlySales - hourlyExpenses;

                chartData.Add(new ProfitChartDataPoint
                {
                    Label = $"{hour:00}:00",
                    Value = hourlyProfit,
                    DateTime = hourStart
                });
            }

            return chartData;
        }

        /// <summary>
        /// إنشاء بيانات الرسم البياني بالأيام
        /// </summary>
        public static List<ProfitChartDataPoint> CreateDailyChartData(DateTime startDate, DateTime endDate,
            List<Sale> sales, List<Expense> expenses)
        {
            var chartData = new List<ProfitChartDataPoint>();
            var currentDate = startDate.Date;

            while (currentDate <= endDate.Date)
            {
                var dayEnd = currentDate.AddDays(1);

                var dailySales = sales.Where(s => s.Date >= currentDate && s.Date < dayEnd).Sum(s => s.Total);
                var dailyExpenses = expenses.Where(e => e.Date >= currentDate && e.Date < dayEnd).Sum(e => e.Amount);
                var dailyProfit = dailySales - dailyExpenses;

                chartData.Add(new ProfitChartDataPoint
                {
                    Label = currentDate.ToString("MM/dd"),
                    Value = dailyProfit,
                    DateTime = currentDate
                });

                currentDate = currentDate.AddDays(1);
            }

            return chartData;
        }
    }
}
