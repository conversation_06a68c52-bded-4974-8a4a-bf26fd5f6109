using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج مراقب النسخ الاحتياطي
    /// </summary>
    public class BackupMonitor : INotifyPropertyChanged
    {
        private int _id;
        private string _monitorName = string.Empty;
        private bool _isEnabled = true;
        private DateTime _lastCheckTime = DateTime.Now;
        private MonitorStatus _status = MonitorStatus.Active;
        private int _totalJobs;
        private int _runningJobs;
        private int _scheduledJobs;
        private int _completedJobs;
        private int _failedJobs;
        private int _cancelledJobs;
        private double _overallSuccessRate;
        private long _totalBackupSize;
        private long _totalRestoredSize;
        private TimeSpan _averageBackupDuration;
        private TimeSpan _totalBackupTime;
        private int _alertsCount;
        private int _warningsCount;
        private int _errorsCount;
        private double _systemCpuUsage;
        private double _systemMemoryUsage;
        private double _systemDiskUsage;
        private double _networkBandwidthUsage;
        private string _currentActivity = string.Empty;
        private DateTime _nextScheduledJob = DateTime.Now.AddHours(1);
        private string _lastError = string.Empty;
        private DateTime? _lastErrorTime;
        private string _performanceMetrics = string.Empty;
        private ObservableCollection<BackupAlert> _alerts = new();
        private ObservableCollection<SystemMetric> _systemMetrics = new();
        private ObservableCollection<JobMetric> _jobMetrics = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string MonitorName
        {
            get => _monitorName;
            set
            {
                if (_monitorName != value)
                {
                    _monitorName = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                if (_isEnabled != value)
                {
                    _isEnabled = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime LastCheckTime
        {
            get => _lastCheckTime;
            set
            {
                if (_lastCheckTime != value)
                {
                    _lastCheckTime = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLastCheckTime));
                    OnPropertyChanged(nameof(TimeSinceLastCheck));
                }
            }
        }

        public MonitorStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                }
            }
        }

        public int TotalJobs
        {
            get => _totalJobs;
            set
            {
                if (_totalJobs != value)
                {
                    _totalJobs = value;
                    OnPropertyChanged();
                    CalculateSuccessRate();
                }
            }
        }

        public int RunningJobs
        {
            get => _runningJobs;
            set
            {
                if (_runningJobs != value)
                {
                    _runningJobs = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ScheduledJobs
        {
            get => _scheduledJobs;
            set
            {
                if (_scheduledJobs != value)
                {
                    _scheduledJobs = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CompletedJobs
        {
            get => _completedJobs;
            set
            {
                if (_completedJobs != value)
                {
                    _completedJobs = value;
                    OnPropertyChanged();
                    CalculateSuccessRate();
                }
            }
        }

        public int FailedJobs
        {
            get => _failedJobs;
            set
            {
                if (_failedJobs != value)
                {
                    _failedJobs = value;
                    OnPropertyChanged();
                    CalculateSuccessRate();
                }
            }
        }

        public int CancelledJobs
        {
            get => _cancelledJobs;
            set
            {
                if (_cancelledJobs != value)
                {
                    _cancelledJobs = value;
                    OnPropertyChanged();
                }
            }
        }

        public double OverallSuccessRate
        {
            get => _overallSuccessRate;
            set
            {
                if (_overallSuccessRate != value)
                {
                    _overallSuccessRate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedSuccessRate));
                    OnPropertyChanged(nameof(SuccessRateColor));
                }
            }
        }

        public long TotalBackupSize
        {
            get => _totalBackupSize;
            set
            {
                if (_totalBackupSize != value)
                {
                    _totalBackupSize = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalBackupSize));
                }
            }
        }

        public long TotalRestoredSize
        {
            get => _totalRestoredSize;
            set
            {
                if (_totalRestoredSize != value)
                {
                    _totalRestoredSize = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalRestoredSize));
                }
            }
        }

        public TimeSpan AverageBackupDuration
        {
            get => _averageBackupDuration;
            set
            {
                if (_averageBackupDuration != value)
                {
                    _averageBackupDuration = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAverageBackupDuration));
                }
            }
        }

        public TimeSpan TotalBackupTime
        {
            get => _totalBackupTime;
            set
            {
                if (_totalBackupTime != value)
                {
                    _totalBackupTime = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalBackupTime));
                }
            }
        }

        public int AlertsCount
        {
            get => _alertsCount;
            set
            {
                if (_alertsCount != value)
                {
                    _alertsCount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasAlerts));
                }
            }
        }

        public int WarningsCount
        {
            get => _warningsCount;
            set
            {
                if (_warningsCount != value)
                {
                    _warningsCount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasWarnings));
                }
            }
        }

        public int ErrorsCount
        {
            get => _errorsCount;
            set
            {
                if (_errorsCount != value)
                {
                    _errorsCount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasErrors));
                }
            }
        }

        public double SystemCpuUsage
        {
            get => _systemCpuUsage;
            set
            {
                if (_systemCpuUsage != value)
                {
                    _systemCpuUsage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCpuUsage));
                    OnPropertyChanged(nameof(CpuUsageColor));
                }
            }
        }

        public double SystemMemoryUsage
        {
            get => _systemMemoryUsage;
            set
            {
                if (_systemMemoryUsage != value)
                {
                    _systemMemoryUsage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedMemoryUsage));
                    OnPropertyChanged(nameof(MemoryUsageColor));
                }
            }
        }

        public double SystemDiskUsage
        {
            get => _systemDiskUsage;
            set
            {
                if (_systemDiskUsage != value)
                {
                    _systemDiskUsage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDiskUsage));
                    OnPropertyChanged(nameof(DiskUsageColor));
                }
            }
        }

        public double NetworkBandwidthUsage
        {
            get => _networkBandwidthUsage;
            set
            {
                if (_networkBandwidthUsage != value)
                {
                    _networkBandwidthUsage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedBandwidthUsage));
                }
            }
        }

        public string CurrentActivity
        {
            get => _currentActivity;
            set
            {
                if (_currentActivity != value)
                {
                    _currentActivity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        public DateTime NextScheduledJob
        {
            get => _nextScheduledJob;
            set
            {
                if (_nextScheduledJob != value)
                {
                    _nextScheduledJob = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedNextScheduledJob));
                    OnPropertyChanged(nameof(TimeToNextJob));
                }
            }
        }

        public string LastError
        {
            get => _lastError;
            set
            {
                if (_lastError != value)
                {
                    _lastError = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasLastError));
                }
            }
        }

        public DateTime? LastErrorTime
        {
            get => _lastErrorTime;
            set
            {
                if (_lastErrorTime != value)
                {
                    _lastErrorTime = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLastErrorTime));
                }
            }
        }

        public string PerformanceMetrics
        {
            get => _performanceMetrics;
            set
            {
                if (_performanceMetrics != value)
                {
                    _performanceMetrics = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<BackupAlert> Alerts
        {
            get => _alerts;
            set
            {
                if (_alerts != value)
                {
                    _alerts = value;
                    OnPropertyChanged();
                    UpdateAlertCounts();
                }
            }
        }

        public ObservableCollection<SystemMetric> SystemMetrics
        {
            get => _systemMetrics;
            set
            {
                if (_systemMetrics != value)
                {
                    _systemMetrics = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<JobMetric> JobMetrics
        {
            get => _jobMetrics;
            set
            {
                if (_jobMetrics != value)
                {
                    _jobMetrics = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsActive => !string.IsNullOrEmpty(CurrentActivity);
        public bool HasAlerts => AlertsCount > 0;
        public bool HasWarnings => WarningsCount > 0;
        public bool HasErrors => ErrorsCount > 0;
        public bool HasLastError => !string.IsNullOrEmpty(LastError);

        public string TimeSinceLastCheck
        {
            get
            {
                var timeSpan = DateTime.Now - LastCheckTime;
                if (timeSpan.TotalMinutes < 1)
                    return "الآن";
                if (timeSpan.TotalHours < 1)
                    return $"{(int)timeSpan.TotalMinutes} دقيقة";
                if (timeSpan.TotalDays < 1)
                    return $"{(int)timeSpan.TotalHours} ساعة";
                return $"{(int)timeSpan.TotalDays} يوم";
            }
        }

        public string TimeToNextJob
        {
            get
            {
                var timeSpan = NextScheduledJob - DateTime.Now;
                if (timeSpan.TotalMinutes < 0)
                    return "متأخر";
                if (timeSpan.TotalMinutes < 1)
                    return "أقل من دقيقة";
                if (timeSpan.TotalHours < 1)
                    return $"{(int)timeSpan.TotalMinutes} دقيقة";
                if (timeSpan.TotalDays < 1)
                    return $"{(int)timeSpan.TotalHours} ساعة";
                return $"{(int)timeSpan.TotalDays} يوم";
            }
        }

        // Display Properties
        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    MonitorStatus.Active => "نشط",
                    MonitorStatus.Warning => "تحذير",
                    MonitorStatus.Error => "خطأ",
                    MonitorStatus.Disabled => "معطل",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    MonitorStatus.Active => "Green",
                    MonitorStatus.Warning => "Orange",
                    MonitorStatus.Error => "Red",
                    MonitorStatus.Disabled => "Gray",
                    _ => "Gray"
                };
            }
        }

        public string SuccessRateColor
        {
            get
            {
                return OverallSuccessRate switch
                {
                    >= 95 => "Green",
                    >= 80 => "Orange",
                    _ => "Red"
                };
            }
        }

        public string CpuUsageColor
        {
            get
            {
                return SystemCpuUsage switch
                {
                    >= 90 => "Red",
                    >= 70 => "Orange",
                    _ => "Green"
                };
            }
        }

        public string MemoryUsageColor
        {
            get
            {
                return SystemMemoryUsage switch
                {
                    >= 90 => "Red",
                    >= 70 => "Orange",
                    _ => "Green"
                };
            }
        }

        public string DiskUsageColor
        {
            get
            {
                return SystemDiskUsage switch
                {
                    >= 90 => "Red",
                    >= 80 => "Orange",
                    _ => "Green"
                };
            }
        }

        // Formatted Properties
        public string FormattedLastCheckTime => LastCheckTime.ToString("dd/MM/yyyy HH:mm:ss");
        public string FormattedNextScheduledJob => NextScheduledJob.ToString("dd/MM/yyyy HH:mm");
        public string FormattedLastErrorTime => LastErrorTime?.ToString("dd/MM/yyyy HH:mm") ?? "لا يوجد";
        public string FormattedSuccessRate => $"{OverallSuccessRate:F1}%";
        public string FormattedCpuUsage => $"{SystemCpuUsage:F1}%";
        public string FormattedMemoryUsage => $"{SystemMemoryUsage:F1}%";
        public string FormattedDiskUsage => $"{SystemDiskUsage:F1}%";
        public string FormattedBandwidthUsage => $"{NetworkBandwidthUsage:F1} Mbps";
        public string FormattedTotalBackupSize => FormatFileSize(TotalBackupSize);
        public string FormattedTotalRestoredSize => FormatFileSize(TotalRestoredSize);
        public string FormattedAverageBackupDuration => AverageBackupDuration.ToString(@"hh\:mm\:ss");
        public string FormattedTotalBackupTime => TotalBackupTime.ToString(@"dd\.hh\:mm\:ss");

        #endregion

        #region Methods

        private void CalculateSuccessRate()
        {
            var totalCompleted = CompletedJobs + FailedJobs;
            if (totalCompleted > 0)
                OverallSuccessRate = (double)CompletedJobs / totalCompleted * 100;
            else
                OverallSuccessRate = 0;
        }

        private void UpdateAlertCounts()
        {
            if (Alerts == null) return;

            AlertsCount = Alerts.Count;
            WarningsCount = Alerts.Count(a => a.Severity == AlertSeverity.Warning);
            ErrorsCount = Alerts.Count(a => a.Severity == AlertSeverity.Error || a.Severity == AlertSeverity.Critical);
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";

            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }

            return $"{size:F2} {sizes[order]}";
        }

        public void UpdateSystemMetrics(double cpu, double memory, double disk, double bandwidth)
        {
            SystemCpuUsage = cpu;
            SystemMemoryUsage = memory;
            SystemDiskUsage = disk;
            NetworkBandwidthUsage = bandwidth;
            LastCheckTime = DateTime.Now;

            // Add to metrics history
            SystemMetrics.Add(new SystemMetric
            {
                Timestamp = DateTime.Now,
                CpuUsage = cpu,
                MemoryUsage = memory,
                DiskUsage = disk,
                BandwidthUsage = bandwidth
            });

            // Keep only last 100 metrics
            while (SystemMetrics.Count > 100)
            {
                SystemMetrics.RemoveAt(0);
            }

            // Update status based on metrics
            UpdateStatus();
        }

        public void AddAlert(string message, AlertSeverity severity, string details = "")
        {
            var alert = new BackupAlert
            {
                Message = message,
                Severity = severity,
                Details = details,
                CreatedAt = DateTime.Now,
                IsRead = false
            };

            Alerts.Add(alert);
            UpdateAlertCounts();

            if (severity == AlertSeverity.Error || severity == AlertSeverity.Critical)
            {
                LastError = message;
                LastErrorTime = DateTime.Now;
            }

            UpdateStatus();
        }

        public void ClearAlerts()
        {
            Alerts.Clear();
            UpdateAlertCounts();
            UpdateStatus();
        }

        private void UpdateStatus()
        {
            if (!IsEnabled)
            {
                Status = MonitorStatus.Disabled;
                return;
            }

            if (HasErrors || SystemCpuUsage >= 90 || SystemMemoryUsage >= 90 || SystemDiskUsage >= 90)
            {
                Status = MonitorStatus.Error;
            }
            else if (HasWarnings || SystemCpuUsage >= 70 || SystemMemoryUsage >= 70 || SystemDiskUsage >= 80)
            {
                Status = MonitorStatus.Warning;
            }
            else
            {
                Status = MonitorStatus.Active;
            }
        }

        public void UpdateJobMetrics(int total, int running, int scheduled, int completed, int failed, int cancelled)
        {
            TotalJobs = total;
            RunningJobs = running;
            ScheduledJobs = scheduled;
            CompletedJobs = completed;
            FailedJobs = failed;
            CancelledJobs = cancelled;

            // Add to job metrics history
            JobMetrics.Add(new JobMetric
            {
                Timestamp = DateTime.Now,
                TotalJobs = total,
                RunningJobs = running,
                CompletedJobs = completed,
                FailedJobs = failed
            });

            // Keep only last 100 metrics
            while (JobMetrics.Count > 100)
            {
                JobMetrics.RemoveAt(0);
            }

            LastCheckTime = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// تنبيه النسخ الاحتياطي
    /// </summary>
    public class BackupAlert
    {
        public int Id { get; set; }
        public string Message { get; set; } = string.Empty;
        public AlertSeverity Severity { get; set; } = AlertSeverity.Info;
        public string Details { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public bool IsRead { get; set; }
        public bool IsResolved { get; set; }

        public string SeverityDisplay
        {
            get
            {
                return Severity switch
                {
                    AlertSeverity.Info => "معلومات",
                    AlertSeverity.Warning => "تحذير",
                    AlertSeverity.Error => "خطأ",
                    AlertSeverity.Critical => "حرج",
                    _ => "غير محدد"
                };
            }
        }

        public string SeverityColor
        {
            get
            {
                return Severity switch
                {
                    AlertSeverity.Info => "Blue",
                    AlertSeverity.Warning => "Orange",
                    AlertSeverity.Error => "Red",
                    AlertSeverity.Critical => "Purple",
                    _ => "Gray"
                };
            }
        }

        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");
    }

    /// <summary>
    /// مقياس النظام
    /// </summary>
    public class SystemMetric
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public double DiskUsage { get; set; }
        public double BandwidthUsage { get; set; }
    }

    /// <summary>
    /// مقياس المهام
    /// </summary>
    public class JobMetric
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public int TotalJobs { get; set; }
        public int RunningJobs { get; set; }
        public int CompletedJobs { get; set; }
        public int FailedJobs { get; set; }
    }

    #endregion

    #region Enums

    public enum MonitorStatus
    {
        Active,         // نشط
        Warning,        // تحذير
        Error,          // خطأ
        Disabled        // معطل
    }



    #endregion
}
