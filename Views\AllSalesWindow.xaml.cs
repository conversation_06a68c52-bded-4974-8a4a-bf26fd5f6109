using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class AllSalesWindow : Window
    {
        private readonly InvoiceService _invoiceService;
        private readonly CustomerService _customerService;
        private List<Invoice> _allInvoices;
        private ObservableCollection<Invoice> _filteredInvoices;

        public AllSalesWindow()
        {
            InitializeComponent();

            var databaseService = new DatabaseService();
            var settingsService = new SettingsService(databaseService);
            var notificationService = new NotificationService(databaseService, settingsService);
            var paymentService = new PaymentService(databaseService, notificationService);
            _invoiceService = new InvoiceService(databaseService, notificationService, paymentService);
            _customerService = new CustomerService(databaseService);
            _allInvoices = new List<Invoice>();
            _filteredInvoices = new ObservableCollection<Invoice>();

            SalesDataGrid.ItemsSource = _filteredInvoices;

            LoadInvoicesAsync();
        }

        private async void LoadInvoicesAsync()
        {
            try
            {
                // تحميل جميع الفواتير
                _allInvoices = (await _invoiceService.GetAllInvoicesAsync()).ToList();

                // تحميل بيانات العملاء لكل فاتورة
                foreach (var invoice in _allInvoices)
                {
                    if (invoice.CustomerId.HasValue)
                    {
                        invoice.Customer = await _customerService.GetCustomerByIdAsync(invoice.CustomerId.Value);
                    }
                }

                ApplyFilters();
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفواتير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyFilters()
        {
            var filtered = _allInvoices.AsEnumerable();

            // تصفية بالنص
            string searchText = SearchTextBox.Text?.Trim();
            if (!string.IsNullOrEmpty(searchText))
            {
                filtered = filtered.Where(i =>
                    i.InvoiceNumber.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                    (i.Customer?.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false));
            }

            // تصفية بالتاريخ
            if (StartDatePicker.SelectedDate.HasValue)
            {
                filtered = filtered.Where(i => i.InvoiceDate >= StartDatePicker.SelectedDate.Value);
            }

            if (EndDatePicker.SelectedDate.HasValue)
            {
                filtered = filtered.Where(i => i.InvoiceDate <= EndDatePicker.SelectedDate.Value);
            }

            // تصفية بنوع الفاتورة
            if (InvoiceTypeComboBox.SelectedIndex > 0)
            {
                string selectedType = ((ComboBoxItem)InvoiceTypeComboBox.SelectedItem).Content.ToString();
                if (selectedType == "بيع عادي")
                {
                    filtered = filtered.Where(i => i.InvoiceType == InvoiceType.Sales &&
                        !i.Notes.Contains("بيع سريع"));
                }
                else if (selectedType == "بيع سريع")
                {
                    filtered = filtered.Where(i => i.Notes.Contains("بيع سريع"));
                }
            }

            // تصفية بالحالة
            if (StatusComboBox.SelectedIndex > 0)
            {
                string selectedStatus = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString();
                InvoiceStatus status = selectedStatus switch
                {
                    "مدفوعة" => InvoiceStatus.Paid,
                    "معلقة" => InvoiceStatus.Sent,
                    "ملغية" => InvoiceStatus.Cancelled,
                    _ => InvoiceStatus.Draft
                };
                filtered = filtered.Where(i => i.Status == status);
            }

            // ترتيب بالتاريخ (الأحدث أولاً)
            filtered = filtered.OrderByDescending(i => i.InvoiceDate);

            _filteredInvoices.Clear();
            foreach (var invoice in filtered)
            {
                _filteredInvoices.Add(invoice);
            }

            UpdateSummary();
        }

        private void UpdateSummary()
        {
            var invoicesCount = _filteredInvoices.Count;
            var totalItems = _filteredInvoices.Sum(i => i.ItemsCount);
            var totalQuantity = _filteredInvoices.Sum(i => i.TotalQuantity);
            var totalAmount = _filteredInvoices.Sum(i => i.TotalAmount);

            InvoicesCountLabel.Text = invoicesCount.ToString();
            TotalItemsLabel.Text = totalItems.ToString();
            TotalQuantityLabel.Text = totalQuantity.ToString();
            TotalAmountLabel.Text = $"{totalAmount:F2} دج";
            TotalSalesLabel.Text = $"إجمالي المبيعات: {totalAmount:F2} دج";
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void InvoiceTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            LoadInvoicesAsync();
        }

        private void SalesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            ViewDetails_Click(sender, e);
        }

        private void ViewDetails_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                var detailsMessage = $"تفاصيل الفاتورة: {selectedInvoice.InvoiceNumber}\n\n" +
                    $"العميل: {selectedInvoice.Customer?.Name ?? "غير محدد"}\n" +
                    $"التاريخ: {selectedInvoice.FormattedInvoiceDate}\n" +
                    $"نوع الفاتورة: {selectedInvoice.InvoiceTypeDisplay}\n" +
                    $"الحالة: {selectedInvoice.StatusDisplay}\n" +
                    $"عدد الأصناف: {selectedInvoice.ItemsCount}\n" +
                    $"إجمالي الكمية: {selectedInvoice.TotalQuantity}\n" +
                    $"المبلغ الفرعي: {selectedInvoice.Subtotal:F2} دج\n" +
                    $"الخصم: {selectedInvoice.DiscountAmount:F2} دج\n" +
                    $"الضريبة: {selectedInvoice.TaxAmount:F2} دج\n" +
                    $"الإجمالي: {selectedInvoice.TotalAmount:F2} دج\n" +
                    $"المدفوع: {selectedInvoice.PaidAmount:F2} دج\n" +
                    $"المتبقي: {selectedInvoice.RemainingAmount:F2} دج\n\n" +
                    $"ملاحظات: {selectedInvoice.Notes}";

                MessageBox.Show(detailsMessage, "تفاصيل الفاتورة",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void Print_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                MessageBox.Show($"سيتم طباعة الفاتورة: {selectedInvoice.InvoiceNumber}", "طباعة",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void Edit_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                if (selectedInvoice.Status == InvoiceStatus.Paid)
                {
                    MessageBox.Show("لا يمكن تعديل فاتورة مدفوعة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                MessageBox.Show($"سيتم فتح نافذة تعديل الفاتورة: {selectedInvoice.InvoiceNumber}", "تعديل",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void Delete_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                if (selectedInvoice.Status == InvoiceStatus.Paid)
                {
                    MessageBox.Show("لا يمكن حذف فاتورة مدفوعة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل تريد حذف الفاتورة: {selectedInvoice.InvoiceNumber}؟",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _invoiceService.DeleteInvoiceAsync(selectedInvoice.Id);
                        MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح الحذف",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadInvoicesAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void NewInvoice_Click(object sender, RoutedEventArgs e)
        {
            var newInvoiceWindow = new NewInvoiceWindow();
            if (newInvoiceWindow.ShowDialog() == true)
            {
                LoadInvoicesAsync(); // إعادة تحميل القائمة
            }
        }

        private void QuickSale_Click(object sender, RoutedEventArgs e)
        {
            var quickSaleWindow = new QuickSaleWindow();
            quickSaleWindow.ShowDialog();
            LoadInvoicesAsync(); // إعادة تحميل القائمة
        }

        private void Report_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير نافذة التقارير قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
