using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class AllSalesWindow : Window
    {
        private readonly InvoiceService _invoiceService;
        private readonly CustomerService _customerService;
        private List<Invoice> _allInvoices;
        private ObservableCollection<Invoice> _filteredInvoices;

        public AllSalesWindow()
        {
            try
            {
                InitializeComponent();

                var databaseService = new DatabaseService();
                var settingsService = new SettingsService(databaseService);
                var notificationService = new NotificationService(databaseService, settingsService);
                var paymentService = new PaymentService(databaseService, notificationService);
                _invoiceService = new InvoiceService(databaseService, notificationService, paymentService);
                _customerService = new CustomerService(databaseService);
                _allInvoices = new List<Invoice>();
                _filteredInvoices = new ObservableCollection<Invoice>();

                if (SalesDataGrid != null)
                {
                    SalesDataGrid.ItemsSource = _filteredInvoices;
                }

                LoadInvoicesAsync();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تهيئة نافذة المبيعات");
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadInvoicesAsync()
        {
            try
            {
                // تحميل جميع الفواتير مع تحميل كامل للبيانات
                var invoices = await LoadInvoicesWithFullDataAsync();
                _allInvoices = invoices?.ToList() ?? new List<Invoice>();

                // إذا لم توجد فواتير، إنشاء بيانات تجريبية للاختبار (فقط إذا كانت قاعدة البيانات فارغة تماماً)
                if (!_allInvoices.Any())
                {
                    // التحقق من وجود بيانات في جدول Sales أيضاً
                    var hasSalesData = await CheckForExistingSalesDataAsync();
                    if (!hasSalesData)
                    {
                        await CreateSampleInvoicesAsync();
                        var sampleInvoices = await LoadInvoicesWithFullDataAsync();
                        _allInvoices = sampleInvoices?.ToList() ?? new List<Invoice>();
                    }
                }

                ApplyFilters();
                UpdateSummary();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل الفواتير");
                MessageBox.Show($"خطأ في تحميل الفواتير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // تهيئة قوائم فارغة في حالة الخطأ
                _allInvoices = new List<Invoice>();
                _filteredInvoices = new ObservableCollection<Invoice>();
                if (SalesDataGrid != null)
                {
                    SalesDataGrid.ItemsSource = _filteredInvoices;
                }
            }
        }

        private async Task<List<Invoice>> LoadInvoicesWithFullDataAsync()
        {
            try
            {
                var allInvoices = new List<Invoice>();

                // 1. تحميل الفواتير من جدول Invoices
                var invoicesFromInvoicesTable = await LoadFromInvoicesTableAsync();
                allInvoices.AddRange(invoicesFromInvoicesTable);

                // 2. تحميل البيانات من جدول Sales (البيع السريع القديم)
                var invoicesFromSalesTable = await LoadFromSalesTableAsync();
                allInvoices.AddRange(invoicesFromSalesTable);

                // ترتيب حسب التاريخ (الأحدث أولاً)
                return allInvoices.OrderByDescending(i => i.InvoiceDate).ToList();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل البيانات الكاملة للفواتير");
                throw;
            }
        }

        private async Task<List<Invoice>> LoadFromInvoicesTableAsync()
        {
            try
            {
                // تحميل الفواتير الأساسية من جدول Invoices
                var basicInvoices = await _invoiceService.GetAllInvoicesAsync(1000);
                var fullInvoices = new List<Invoice>();

                foreach (var invoice in basicInvoices.Where(i => i != null))
                {
                    try
                    {
                        // تحميل الفاتورة الكاملة مع العناصر
                        var fullInvoice = await _invoiceService.GetInvoiceByIdAsync(invoice.Id);
                        if (fullInvoice != null)
                        {
                            // تحميل بيانات العميل
                            await LoadCustomerDataAsync(fullInvoice);

                            // التأكد من وجود رقم فاتورة
                            EnsureInvoiceNumber(fullInvoice);

                            // إعادة حساب المجاميع
                            RecalculateInvoiceTotals(fullInvoice);

                            // تعيين طريقة الدفع من Terms إذا كانت فارغة
                            if (string.IsNullOrEmpty(fullInvoice.PaymentMethod))
                            {
                                fullInvoice.PaymentMethod = !string.IsNullOrEmpty(fullInvoice.Terms) ? fullInvoice.Terms : "نقدي";
                            }

                            fullInvoices.Add(fullInvoice);
                        }
                        else
                        {
                            // إذا فشل تحميل الفاتورة الكاملة، استخدم البيانات الأساسية
                            await LoadCustomerDataAsync(invoice);
                            EnsureInvoiceNumber(invoice);
                            invoice.Items = new ObservableCollection<InvoiceItem>();
                            invoice.PaymentMethod = !string.IsNullOrEmpty(invoice.Terms) ? invoice.Terms : "نقدي";
                            fullInvoices.Add(invoice);
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في تحميل الفاتورة {invoice.Id}");

                        // إضافة الفاتورة مع بيانات افتراضية
                        invoice.Customer = "عميل غير محدد";
                        invoice.Items = new ObservableCollection<InvoiceItem>();
                        invoice.PaymentMethod = "نقدي";
                        EnsureInvoiceNumber(invoice);
                        fullInvoices.Add(invoice);
                    }
                }

                return fullInvoices;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل البيانات من جدول Invoices");
                return new List<Invoice>();
            }
        }

        private async Task<List<Invoice>> LoadFromSalesTableAsync()
        {
            try
            {
                var databaseService = new DatabaseService();

                // تحميل البيانات من جدول Sales
                const string salesSql = @"
                    SELECT s.*, c.Name as CustomerName
                    FROM Sales s
                    LEFT JOIN Customers c ON s.CustomerId = c.Id
                    ORDER BY s.Date DESC
                    LIMIT 1000";

                var salesData = await databaseService.QueryAsync<dynamic>(salesSql);
                var invoices = new List<Invoice>();

                foreach (var sale in salesData)
                {
                    try
                    {
                        // تحويل بيانات Sales إلى Invoice
                        var invoice = new Invoice
                        {
                            Id = sale.Id + 10000, // إضافة offset لتجنب التضارب مع IDs من جدول Invoices
                            InvoiceNumber = sale.InvoiceNumber ?? $"QS-{sale.Id:D6}",
                            InvoiceType = InvoiceType.Sales,
                            Status = sale.PaymentStatus == "مدفوع" ? InvoiceStatus.Paid : InvoiceStatus.Sent,
                            CustomerId = sale.CustomerId,
                            Customer = sale.CustomerName ?? "عميل افتراضي",
                            InvoiceDate = DateTime.TryParse(sale.Date, out DateTime date) ? date : DateTime.Now,
                            Subtotal = sale.Subtotal ?? 0,
                            TaxAmount = sale.Tax ?? 0,
                            DiscountAmount = sale.Discount ?? 0,
                            TotalAmount = sale.Total ?? 0,
                            PaidAmount = sale.PaymentStatus == "مدفوع" ? (sale.Total ?? 0) : 0,
                            RemainingAmount = sale.PaymentStatus == "مدفوع" ? 0 : (sale.Total ?? 0),
                            PaymentMethod = sale.PaymentMethod ?? "نقدي",
                            Notes = sale.Notes ?? "بيع سريع",
                            CreatedBy = "admin",
                            CreatedAt = DateTime.TryParse(sale.CreatedAt, out DateTime created) ? created : DateTime.Now,
                            Items = new ObservableCollection<InvoiceItem>()
                        };

                        // تحميل عناصر البيع
                        const string itemsSql = @"
                            SELECT si.*, p.Name as ProductName, p.Barcode
                            FROM SaleItems si
                            LEFT JOIN Products p ON si.ProductId = p.Id
                            WHERE si.SaleId = @SaleId";

                        var itemsData = await databaseService.QueryAsync<dynamic>(itemsSql, new { SaleId = sale.Id });

                        foreach (var item in itemsData)
                        {
                            var invoiceItem = new InvoiceItem
                            {
                                Id = item.Id,
                                InvoiceId = invoice.Id,
                                ProductId = item.ProductId ?? 0,
                                ProductName = item.ProductName ?? "منتج غير محدد",
                                Description = item.Barcode ?? "",
                                Quantity = item.Quantity ?? 0,
                                UnitPrice = item.UnitPrice ?? 0,
                                Discount = item.Discount ?? 0,
                                Total = item.Total ?? 0
                            };

                            invoice.Items.Add(invoiceItem);
                        }

                        // إعادة حساب المجاميع إذا لزم الأمر
                        if (invoice.Items.Any())
                        {
                            var calculatedSubtotal = invoice.Items.Sum(i => i.Total);
                            if (invoice.Subtotal == 0)
                            {
                                invoice.Subtotal = calculatedSubtotal;
                                invoice.TotalAmount = calculatedSubtotal + invoice.TaxAmount - invoice.DiscountAmount;
                            }
                        }

                        invoices.Add(invoice);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في تحويل بيانات البيع {sale.Id}");
                    }
                }

                return invoices;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل البيانات من جدول Sales");
                return new List<Invoice>();
            }
        }

        private async Task LoadCustomerDataAsync(Invoice invoice)
        {
            try
            {
                if (invoice.CustomerId.HasValue && invoice.CustomerId > 0)
                {
                    var customer = await _customerService.GetCustomerByIdAsync(invoice.CustomerId.Value);
                    invoice.Customer = customer?.Name ?? "عميل غير محدد";
                }
                else
                {
                    invoice.Customer = "عميل افتراضي";
                }
            }
            catch
            {
                invoice.Customer = "عميل غير محدد";
            }
        }

        private void EnsureInvoiceNumber(Invoice invoice)
        {
            if (string.IsNullOrEmpty(invoice.InvoiceNumber))
            {
                invoice.InvoiceNumber = $"INV-{invoice.Id:D6}";
            }
        }

        private void RecalculateInvoiceTotals(Invoice invoice)
        {
            try
            {
                if (invoice.Items != null && invoice.Items.Any())
                {
                    // حساب المجموع الفرعي من العناصر
                    var calculatedSubtotal = invoice.Items.Sum(i => i.Quantity * i.UnitPrice);

                    // إذا كان المجموع الفرعي المحفوظ صفر أو مختلف، استخدم المحسوب
                    if (invoice.Subtotal == 0 || Math.Abs(invoice.Subtotal - calculatedSubtotal) > 0.01m)
                    {
                        invoice.Subtotal = calculatedSubtotal;
                    }

                    // حساب المجموع الكلي
                    var calculatedTotal = invoice.Subtotal - invoice.DiscountAmount + invoice.TaxAmount;
                    if (invoice.TotalAmount == 0 || Math.Abs(invoice.TotalAmount - calculatedTotal) > 0.01m)
                    {
                        invoice.TotalAmount = calculatedTotal;
                    }

                    // حساب المبلغ المتبقي
                    invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إعادة حساب مجاميع الفاتورة {invoice.Id}");
            }
        }

        private async Task<bool> CheckForExistingSalesDataAsync()
        {
            try
            {
                var databaseService = new DatabaseService();

                // التحقق من جدول Sales
                const string salesCountSql = "SELECT COUNT(*) FROM Sales";
                var salesCount = await databaseService.QuerySingleAsync<int>(salesCountSql);

                return salesCount > 0;
            }
            catch
            {
                return false;
            }
        }

        private async Task CreateSampleInvoicesAsync()
        {
            try
            {
                // إنشاء فواتير تجريبية للاختبار
                var sampleInvoices = new List<Invoice>
                {
                    new Invoice
                    {
                        InvoiceNumber = "INV-000001",
                        InvoiceType = InvoiceType.Sales,
                        Status = InvoiceStatus.Paid,
                        InvoiceDate = DateTime.Now.AddDays(-5),
                        Customer = "أحمد محمد",
                        Subtotal = 2500.00m,
                        TaxAmount = 250.00m,
                        DiscountAmount = 100.00m,
                        TotalAmount = 2650.00m,
                        PaidAmount = 2650.00m,
                        RemainingAmount = 0.00m,
                        Notes = "فاتورة تجريبية",
                        PaymentMethod = "نقدي",
                        CreatedBy = "النظام",
                        CreatedAt = DateTime.Now.AddDays(-5),
                        Items = new ObservableCollection<InvoiceItem>
                        {
                            new InvoiceItem
                            {
                                ProductName = "لابتوب HP",
                                Quantity = 1,
                                UnitPrice = 2000.00m,
                                Discount = 50.00m,
                                Description = "لابتوب HP Core i5"
                            },
                            new InvoiceItem
                            {
                                ProductName = "ماوس لاسلكي",
                                Quantity = 2,
                                UnitPrice = 250.00m,
                                Discount = 50.00m,
                                Description = "ماوس لاسلكي عالي الجودة"
                            }
                        }
                    },
                    new Invoice
                    {
                        InvoiceNumber = "INV-000002",
                        InvoiceType = InvoiceType.Sales,
                        Status = InvoiceStatus.Sent,
                        InvoiceDate = DateTime.Now.AddDays(-3),
                        Customer = "فاطمة علي",
                        Subtotal = 1800.00m,
                        TaxAmount = 180.00m,
                        DiscountAmount = 80.00m,
                        TotalAmount = 1900.00m,
                        PaidAmount = 1000.00m,
                        RemainingAmount = 900.00m,
                        Notes = "فاتورة تجريبية - مدفوعة جزئياً",
                        PaymentMethod = "بطاقة ائتمان",
                        CreatedBy = "النظام",
                        CreatedAt = DateTime.Now.AddDays(-3),
                        Items = new ObservableCollection<InvoiceItem>
                        {
                            new InvoiceItem
                            {
                                ProductName = "طابعة Canon",
                                Quantity = 1,
                                UnitPrice = 1500.00m,
                                Discount = 50.00m,
                                Description = "طابعة Canon متعددة الوظائف"
                            },
                            new InvoiceItem
                            {
                                ProductName = "كابل USB",
                                Quantity = 3,
                                UnitPrice = 100.00m,
                                Discount = 30.00m,
                                Description = "كابل USB عالي السرعة"
                            }
                        }
                    },
                    new Invoice
                    {
                        InvoiceNumber = "INV-000003",
                        InvoiceType = InvoiceType.Sales,
                        Status = InvoiceStatus.Draft,
                        InvoiceDate = DateTime.Now.AddDays(-1),
                        Customer = "محمد حسن",
                        Subtotal = 3200.00m,
                        TaxAmount = 320.00m,
                        DiscountAmount = 200.00m,
                        TotalAmount = 3320.00m,
                        PaidAmount = 0.00m,
                        RemainingAmount = 3320.00m,
                        Notes = "فاتورة تجريبية - مسودة",
                        PaymentMethod = "تحويل بنكي",
                        CreatedBy = "النظام",
                        CreatedAt = DateTime.Now.AddDays(-1),
                        Items = new ObservableCollection<InvoiceItem>
                        {
                            new InvoiceItem
                            {
                                ProductName = "شاشة Samsung 24 بوصة",
                                Quantity = 2,
                                UnitPrice = 1500.00m,
                                Discount = 100.00m,
                                Description = "شاشة Samsung LED 24 بوصة"
                            },
                            new InvoiceItem
                            {
                                ProductName = "لوحة مفاتيح ميكانيكية",
                                Quantity = 1,
                                UnitPrice = 200.00m,
                                Discount = 100.00m,
                                Description = "لوحة مفاتيح ميكانيكية RGB"
                            }
                        }
                    }
                };

                // حفظ الفواتير التجريبية
                foreach (var invoice in sampleInvoices)
                {
                    try
                    {
                        await _invoiceService.AddInvoiceAsync(invoice);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في حفظ الفاتورة التجريبية {invoice.InvoiceNumber}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء الفواتير التجريبية");
            }
        }

        private void ApplyFilters()
        {
            try
            {
                if (_allInvoices == null)
                {
                    _allInvoices = new List<Invoice>();
                }

                var filtered = _allInvoices.AsEnumerable();

                // تصفية بالنص
                string? searchText = SearchTextBox?.Text?.Trim();
                if (!string.IsNullOrEmpty(searchText))
                {
                    filtered = filtered.Where(i =>
                        (i.InvoiceNumber ?? "").Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                        (i.Customer ?? "").Contains(searchText, StringComparison.OrdinalIgnoreCase));
                }

                // تصفية بالتاريخ
                if (StartDatePicker?.SelectedDate.HasValue == true)
                {
                    filtered = filtered.Where(i => i.InvoiceDate >= StartDatePicker.SelectedDate.Value);
                }

                if (EndDatePicker?.SelectedDate.HasValue == true)
                {
                    filtered = filtered.Where(i => i.InvoiceDate <= EndDatePicker.SelectedDate.Value);
                }

                // تصفية بنوع الفاتورة
                if (InvoiceTypeComboBox?.SelectedIndex > 0 && InvoiceTypeComboBox.SelectedItem is ComboBoxItem typeItem)
                {
                    string? selectedType = typeItem.Content?.ToString();
                    if (selectedType == "بيع عادي")
                    {
                        filtered = filtered.Where(i => i.InvoiceType == InvoiceType.Sales &&
                            !(i.Notes ?? "").Contains("بيع سريع"));
                    }
                    else if (selectedType == "بيع سريع")
                    {
                        filtered = filtered.Where(i => (i.Notes ?? "").Contains("بيع سريع"));
                    }
                }

                // تصفية بالحالة
                if (StatusComboBox?.SelectedIndex > 0 && StatusComboBox.SelectedItem is ComboBoxItem statusItem)
                {
                    string? selectedStatus = statusItem.Content?.ToString();
                    InvoiceStatus status = selectedStatus switch
                    {
                        "مدفوعة" => InvoiceStatus.Paid,
                        "معلقة" => InvoiceStatus.Sent,
                        "ملغية" => InvoiceStatus.Cancelled,
                        _ => InvoiceStatus.Draft
                    };
                    filtered = filtered.Where(i => i.Status == status);
                }

                // ترتيب بالتاريخ (الأحدث أولاً)
                filtered = filtered.OrderByDescending(i => i.InvoiceDate);

                if (_filteredInvoices == null)
                {
                    _filteredInvoices = new ObservableCollection<Invoice>();
                }

                _filteredInvoices.Clear();
                foreach (var invoice in filtered)
                {
                    _filteredInvoices.Add(invoice);
                }

                UpdateSummary();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تطبيق المرشحات");
                MessageBox.Show($"خطأ في تطبيق المرشحات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateSummary()
        {
            try
            {
                if (_filteredInvoices == null)
                {
                    _filteredInvoices = new ObservableCollection<Invoice>();
                }

                var invoicesCount = _filteredInvoices.Count;
                var totalItems = _filteredInvoices.Sum(i => i?.Items?.Count ?? 0);
                var totalQuantity = _filteredInvoices.Sum(i => GetSafeTotalQuantity(i));
                var totalAmount = _filteredInvoices.Sum(i => i?.TotalAmount ?? 0);

                // تحديث العدادات
                if (InvoicesCountLabel != null)
                    InvoicesCountLabel.Text = invoicesCount.ToString("N0");

                if (TotalItemsLabel != null)
                    TotalItemsLabel.Text = totalItems.ToString("N0");

                if (TotalQuantityLabel != null)
                    TotalQuantityLabel.Text = totalQuantity.ToString("N0");

                if (TotalAmountLabel != null)
                    TotalAmountLabel.Text = $"{totalAmount:N2} دج";

                if (TotalSalesLabel != null)
                    TotalSalesLabel.Text = $"إجمالي المبيعات: {totalAmount:N2} دج";

                // تحديث عنوان النافذة
                this.Title = $"جميع عمليات المبيعات - {invoicesCount} فاتورة - {totalAmount:N2} دج";
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحديث الملخص");

                // قيم افتراضية في حالة الخطأ
                if (InvoicesCountLabel != null) InvoicesCountLabel.Text = "0";
                if (TotalItemsLabel != null) TotalItemsLabel.Text = "0";
                if (TotalQuantityLabel != null) TotalQuantityLabel.Text = "0";
                if (TotalAmountLabel != null) TotalAmountLabel.Text = "0.00 دج";
                if (TotalSalesLabel != null) TotalSalesLabel.Text = "إجمالي المبيعات: 0.00 دج";
            }
        }

        private decimal GetSafeTotalQuantity(Invoice? invoice)
        {
            try
            {
                if (invoice?.Items == null)
                    return 0;

                return invoice.Items.Sum(i => i?.Quantity ?? 0);
            }
            catch
            {
                return 0;
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في البحث النصي");
            }
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصفية التاريخ");
            }
        }

        private void InvoiceTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصفية نوع الفاتورة");
            }
        }

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصفية الحالة");
            }
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            LoadInvoicesAsync();
        }

        private void SalesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            ViewDetails_Click(sender, e);
        }

        private async void ViewDetails_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                try
                {
                    // تحميل تفاصيل الفاتورة الكاملة
                    var fullInvoice = await _invoiceService.GetInvoiceByIdAsync(selectedInvoice.Id);
                    if (fullInvoice != null)
                    {
                        selectedInvoice = fullInvoice;
                    }

                    var detailsMessage = $"تفاصيل الفاتورة: {selectedInvoice.InvoiceNumber}\n\n" +
                        $"العميل: {selectedInvoice.Customer ?? "غير محدد"}\n" +
                        $"التاريخ: {selectedInvoice.FormattedInvoiceDate}\n" +
                        $"نوع الفاتورة: {selectedInvoice.InvoiceTypeDisplay}\n" +
                        $"الحالة: {selectedInvoice.StatusDisplay}\n" +
                        $"عدد الأصناف: {selectedInvoice.ItemsCount}\n" +
                        $"إجمالي الكمية: {GetSafeTotalQuantity(selectedInvoice)}\n" +
                        $"المبلغ الفرعي: {selectedInvoice.Subtotal:F2} دج\n" +
                        $"الخصم: {selectedInvoice.DiscountAmount:F2} دج\n" +
                        $"الضريبة: {selectedInvoice.TaxAmount:F2} دج\n" +
                        $"الإجمالي: {selectedInvoice.TotalAmount:F2} دج\n" +
                        $"المدفوع: {selectedInvoice.PaidAmount:F2} دج\n" +
                        $"المتبقي: {selectedInvoice.RemainingAmount:F2} دج\n\n" +
                        $"ملاحظات: {selectedInvoice.Notes ?? "لا توجد ملاحظات"}";

                    MessageBox.Show(detailsMessage, "تفاصيل الفاتورة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في عرض تفاصيل الفاتورة: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void Print_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                MessageBox.Show($"سيتم طباعة الفاتورة: {selectedInvoice.InvoiceNumber}", "طباعة",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void Edit_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                if (selectedInvoice.Status == InvoiceStatus.Paid)
                {
                    MessageBox.Show("لا يمكن تعديل فاتورة مدفوعة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                MessageBox.Show($"سيتم فتح نافذة تعديل الفاتورة: {selectedInvoice.InvoiceNumber}", "تعديل",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void Delete_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                if (selectedInvoice.Status == InvoiceStatus.Paid)
                {
                    MessageBox.Show("لا يمكن حذف فاتورة مدفوعة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل تريد حذف الفاتورة: {selectedInvoice.InvoiceNumber}؟",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _invoiceService.DeleteInvoiceAsync(selectedInvoice.Id);
                        MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح الحذف",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadInvoicesAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void NewInvoice_Click(object sender, RoutedEventArgs e)
        {
            var newInvoiceWindow = new NewInvoiceWindow();
            if (newInvoiceWindow.ShowDialog() == true)
            {
                LoadInvoicesAsync(); // إعادة تحميل القائمة
            }
        }

        private void QuickSale_Click(object sender, RoutedEventArgs e)
        {
            var quickSaleWindow = new QuickSaleWindow();
            quickSaleWindow.ShowDialog();
            LoadInvoicesAsync(); // إعادة تحميل القائمة
        }

        private void Report_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير نافذة التقارير قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
