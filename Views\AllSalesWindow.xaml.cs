using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class AllSalesWindow : Window
    {
        private readonly InvoiceService _invoiceService;
        private readonly CustomerService _customerService;
        private List<Invoice> _allInvoices;
        private ObservableCollection<Invoice> _filteredInvoices;

        public AllSalesWindow()
        {
            try
            {
                InitializeComponent();

                var databaseService = new DatabaseService();
                var settingsService = new SettingsService(databaseService);
                var notificationService = new NotificationService(databaseService, settingsService);
                var paymentService = new PaymentService(databaseService, notificationService);
                _invoiceService = new InvoiceService(databaseService, notificationService, paymentService);
                _customerService = new CustomerService(databaseService);
                _allInvoices = new List<Invoice>();
                _filteredInvoices = new ObservableCollection<Invoice>();

                if (SalesDataGrid != null)
                {
                    SalesDataGrid.ItemsSource = _filteredInvoices;
                }

                LoadInvoicesAsync();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تهيئة نافذة المبيعات");
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadInvoicesAsync()
        {
            try
            {
                // تحميل جميع الفواتير
                var invoices = await _invoiceService.GetAllInvoicesAsync();
                _allInvoices = invoices?.ToList() ?? new List<Invoice>();

                // تحميل بيانات العملاء وعناصر الفاتورة لكل فاتورة
                foreach (var invoice in _allInvoices.Where(i => i != null))
                {
                    try
                    {
                        // تحميل بيانات العميل
                        if (invoice.CustomerId.HasValue && invoice.CustomerId > 0)
                        {
                            var customer = await _customerService.GetCustomerByIdAsync(invoice.CustomerId.Value);
                            if (customer != null)
                            {
                                invoice.Customer = customer.Name ?? "عميل غير محدد";
                            }
                            else
                            {
                                invoice.Customer = "عميل غير محدد";
                            }
                        }
                        else
                        {
                            invoice.Customer = "عميل افتراضي";
                        }

                        // تحميل عناصر الفاتورة
                        var fullInvoice = await _invoiceService.GetInvoiceByIdAsync(invoice.Id);
                        if (fullInvoice != null && fullInvoice.Items != null)
                        {
                            invoice.Items = new ObservableCollection<InvoiceItem>(fullInvoice.Items);

                            // تحديث الحسابات
                            invoice.Subtotal = invoice.Items.Sum(i => i.Quantity * i.UnitPrice);
                            invoice.TotalAmount = invoice.Subtotal - invoice.DiscountAmount + invoice.TaxAmount;

                            // إنشاء رقم فاتورة إذا لم يكن موجود
                            if (string.IsNullOrEmpty(invoice.InvoiceNumber))
                            {
                                invoice.InvoiceNumber = $"INV-{invoice.Id:D6}";
                            }
                        }
                        else
                        {
                            // إذا لم توجد عناصر، تعيين قيم افتراضية
                            invoice.Items = new ObservableCollection<InvoiceItem>();
                            if (string.IsNullOrEmpty(invoice.InvoiceNumber))
                            {
                                invoice.InvoiceNumber = $"INV-{invoice.Id:D6}";
                            }
                        }
                    }
                    catch (Exception customerEx)
                    {
                        LoggingService.LogError(customerEx, $"خطأ في تحميل بيانات الفاتورة {invoice.Id}");
                        invoice.Customer = "عميل غير محدد";
                        invoice.Items = new ObservableCollection<InvoiceItem>();
                        if (string.IsNullOrEmpty(invoice.InvoiceNumber))
                        {
                            invoice.InvoiceNumber = $"INV-{invoice.Id:D6}";
                        }
                    }
                }

                ApplyFilters();
                UpdateSummary();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل الفواتير");
                MessageBox.Show($"خطأ في تحميل الفواتير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // تهيئة قوائم فارغة في حالة الخطأ
                _allInvoices = new List<Invoice>();
                _filteredInvoices = new ObservableCollection<Invoice>();
                if (SalesDataGrid != null)
                {
                    SalesDataGrid.ItemsSource = _filteredInvoices;
                }
            }
        }

        private void ApplyFilters()
        {
            try
            {
                if (_allInvoices == null)
                {
                    _allInvoices = new List<Invoice>();
                }

                var filtered = _allInvoices.AsEnumerable();

                // تصفية بالنص
                string? searchText = SearchTextBox?.Text?.Trim();
                if (!string.IsNullOrEmpty(searchText))
                {
                    filtered = filtered.Where(i =>
                        (i.InvoiceNumber ?? "").Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                        (i.Customer ?? "").Contains(searchText, StringComparison.OrdinalIgnoreCase));
                }

                // تصفية بالتاريخ
                if (StartDatePicker?.SelectedDate.HasValue == true)
                {
                    filtered = filtered.Where(i => i.InvoiceDate >= StartDatePicker.SelectedDate.Value);
                }

                if (EndDatePicker?.SelectedDate.HasValue == true)
                {
                    filtered = filtered.Where(i => i.InvoiceDate <= EndDatePicker.SelectedDate.Value);
                }

                // تصفية بنوع الفاتورة
                if (InvoiceTypeComboBox?.SelectedIndex > 0 && InvoiceTypeComboBox.SelectedItem is ComboBoxItem typeItem)
                {
                    string? selectedType = typeItem.Content?.ToString();
                    if (selectedType == "بيع عادي")
                    {
                        filtered = filtered.Where(i => i.InvoiceType == InvoiceType.Sales &&
                            !(i.Notes ?? "").Contains("بيع سريع"));
                    }
                    else if (selectedType == "بيع سريع")
                    {
                        filtered = filtered.Where(i => (i.Notes ?? "").Contains("بيع سريع"));
                    }
                }

                // تصفية بالحالة
                if (StatusComboBox?.SelectedIndex > 0 && StatusComboBox.SelectedItem is ComboBoxItem statusItem)
                {
                    string? selectedStatus = statusItem.Content?.ToString();
                    InvoiceStatus status = selectedStatus switch
                    {
                        "مدفوعة" => InvoiceStatus.Paid,
                        "معلقة" => InvoiceStatus.Sent,
                        "ملغية" => InvoiceStatus.Cancelled,
                        _ => InvoiceStatus.Draft
                    };
                    filtered = filtered.Where(i => i.Status == status);
                }

                // ترتيب بالتاريخ (الأحدث أولاً)
                filtered = filtered.OrderByDescending(i => i.InvoiceDate);

                if (_filteredInvoices == null)
                {
                    _filteredInvoices = new ObservableCollection<Invoice>();
                }

                _filteredInvoices.Clear();
                foreach (var invoice in filtered)
                {
                    _filteredInvoices.Add(invoice);
                }

                UpdateSummary();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تطبيق المرشحات");
                MessageBox.Show($"خطأ في تطبيق المرشحات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateSummary()
        {
            try
            {
                if (_filteredInvoices == null)
                {
                    _filteredInvoices = new ObservableCollection<Invoice>();
                }

                var invoicesCount = _filteredInvoices.Count;
                var totalItems = _filteredInvoices.Sum(i => i?.ItemsCount ?? 0);
                var totalQuantity = _filteredInvoices.Sum(i => GetSafeTotalQuantity(i));
                var totalAmount = _filteredInvoices.Sum(i => i?.TotalAmount ?? 0);

                if (InvoicesCountLabel != null)
                    InvoicesCountLabel.Text = invoicesCount.ToString();

                if (TotalItemsLabel != null)
                    TotalItemsLabel.Text = totalItems.ToString();

                if (TotalQuantityLabel != null)
                    TotalQuantityLabel.Text = totalQuantity.ToString();

                if (TotalAmountLabel != null)
                    TotalAmountLabel.Text = $"{totalAmount:F2} دج";

                if (TotalSalesLabel != null)
                    TotalSalesLabel.Text = $"إجمالي المبيعات: {totalAmount:F2} دج";
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحديث الملخص");
            }
        }

        private decimal GetSafeTotalQuantity(Invoice? invoice)
        {
            try
            {
                if (invoice?.Items == null)
                    return 0;

                return invoice.Items.Sum(i => i?.Quantity ?? 0);
            }
            catch
            {
                return 0;
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في البحث النصي");
            }
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصفية التاريخ");
            }
        }

        private void InvoiceTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصفية نوع الفاتورة");
            }
        }

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصفية الحالة");
            }
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            LoadInvoicesAsync();
        }

        private void SalesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            ViewDetails_Click(sender, e);
        }

        private async void ViewDetails_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                try
                {
                    // تحميل تفاصيل الفاتورة الكاملة
                    var fullInvoice = await _invoiceService.GetInvoiceByIdAsync(selectedInvoice.Id);
                    if (fullInvoice != null)
                    {
                        selectedInvoice = fullInvoice;
                    }

                    var detailsMessage = $"تفاصيل الفاتورة: {selectedInvoice.InvoiceNumber}\n\n" +
                        $"العميل: {selectedInvoice.Customer ?? "غير محدد"}\n" +
                        $"التاريخ: {selectedInvoice.FormattedInvoiceDate}\n" +
                        $"نوع الفاتورة: {selectedInvoice.InvoiceTypeDisplay}\n" +
                        $"الحالة: {selectedInvoice.StatusDisplay}\n" +
                        $"عدد الأصناف: {selectedInvoice.ItemsCount}\n" +
                        $"إجمالي الكمية: {GetSafeTotalQuantity(selectedInvoice)}\n" +
                        $"المبلغ الفرعي: {selectedInvoice.Subtotal:F2} دج\n" +
                        $"الخصم: {selectedInvoice.DiscountAmount:F2} دج\n" +
                        $"الضريبة: {selectedInvoice.TaxAmount:F2} دج\n" +
                        $"الإجمالي: {selectedInvoice.TotalAmount:F2} دج\n" +
                        $"المدفوع: {selectedInvoice.PaidAmount:F2} دج\n" +
                        $"المتبقي: {selectedInvoice.RemainingAmount:F2} دج\n\n" +
                        $"ملاحظات: {selectedInvoice.Notes ?? "لا توجد ملاحظات"}";

                    MessageBox.Show(detailsMessage, "تفاصيل الفاتورة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في عرض تفاصيل الفاتورة: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void Print_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                MessageBox.Show($"سيتم طباعة الفاتورة: {selectedInvoice.InvoiceNumber}", "طباعة",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void Edit_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                if (selectedInvoice.Status == InvoiceStatus.Paid)
                {
                    MessageBox.Show("لا يمكن تعديل فاتورة مدفوعة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                MessageBox.Show($"سيتم فتح نافذة تعديل الفاتورة: {selectedInvoice.InvoiceNumber}", "تعديل",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void Delete_Click(object sender, RoutedEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                if (selectedInvoice.Status == InvoiceStatus.Paid)
                {
                    MessageBox.Show("لا يمكن حذف فاتورة مدفوعة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل تريد حذف الفاتورة: {selectedInvoice.InvoiceNumber}؟",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _invoiceService.DeleteInvoiceAsync(selectedInvoice.Id);
                        MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح الحذف",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadInvoicesAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void NewInvoice_Click(object sender, RoutedEventArgs e)
        {
            var newInvoiceWindow = new NewInvoiceWindow();
            if (newInvoiceWindow.ShowDialog() == true)
            {
                LoadInvoicesAsync(); // إعادة تحميل القائمة
            }
        }

        private void QuickSale_Click(object sender, RoutedEventArgs e)
        {
            var quickSaleWindow = new QuickSaleWindow();
            quickSaleWindow.ShowDialog();
            LoadInvoicesAsync(); // إعادة تحميل القائمة
        }

        private void Report_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير نافذة التقارير قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
