using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using Prism.Commands;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class SettingsDialogViewModel : BaseViewModel
    {
        #region Services

        private readonly SettingsService _settingsService;

        #endregion

        #region Properties

        private ObservableCollection<SettingsCategory> _categories = new();
        public ObservableCollection<SettingsCategory> Categories
        {
            get => _categories;
            set => SetProperty(ref _categories, value);
        }

        private SettingsCategory? _selectedCategory;
        public SettingsCategory? SelectedCategory
        {
            get => _selectedCategory;
            set => SetProperty(ref _selectedCategory, value);
        }

        #endregion

        #region Commands

        private DelegateCommand? _saveCommand;
        public DelegateCommand SaveCommand => _saveCommand ??= new DelegateCommand(SaveSettings, CanSaveSettings);

        private DelegateCommand? _resetCommand;
        public DelegateCommand ResetCommand => _resetCommand ??= new DelegateCommand(ResetSettings);

        private DelegateCommand? _cancelCommand;
        public DelegateCommand CancelCommand => _cancelCommand ??= new DelegateCommand(Cancel);

        #endregion

        #region Constructor

        public SettingsDialogViewModel()
        {
            var dbService = new DatabaseService();
            _settingsService = new SettingsService(dbService);
            InitializeCategories();
            _ = LoadSettingsAsync();
        }

        #endregion

        #region Methods

        private void InitializeCategories()
        {
            Categories.Clear();

            Categories.Add(new GeneralSettingsCategory { Name = "عام", Icon = "Settings" });
            Categories.Add(new FinancialSettingsCategory { Name = "مالي", Icon = "CurrencyUsd" });
            Categories.Add(new InventorySettingsCategory { Name = "مخزون", Icon = "Package" });
            Categories.Add(new BackupSettingsCategory { Name = "نسخ احتياطي", Icon = "Backup" });
            Categories.Add(new PrintingSettingsCategory { Name = "طباعة", Icon = "Printer" });

            SelectedCategory = Categories[0];
        }

        private async Task LoadSettingsAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تحميل الإعدادات...";
                ClearError();

                foreach (var category in Categories)
                {
                    await category.LoadSettingsAsync(_settingsService);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل الإعدادات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تحميل إعدادات النظام");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Command Handlers

        private async void SaveSettings()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري حفظ الإعدادات...";
                ClearError();

                foreach (var category in Categories)
                {
                    await category.SaveSettingsAsync(_settingsService);
                }

                LoggingService.LogSystemEvent("حفظ الإعدادات", "تم حفظ إعدادات النظام بنجاح");
                SettingsSaved?.Invoke();
                RequestClose?.Invoke(true);
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ الإعدادات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في حفظ إعدادات النظام");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanSaveSettings()
        {
            return !IsLoading;
        }

        private async void ResetSettings()
        {
            try
            {
                // تأكيد من المستخدم
                var result = System.Windows.MessageBox.Show(
                    "هل أنت متأكد من أنك تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
                    "تأكيد إعادة التعيين",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    IsLoading = true;
                    LoadingMessage = "جاري إعادة تعيين الإعدادات...";
                    ClearError();

                    foreach (var category in Categories)
                    {
                        category.ResetToDefaults();
                    }

                    LoggingService.LogSystemEvent("إعادة تعيين الإعدادات", "تم إعادة تعيين إعدادات النظام للقيم الافتراضية");
                    System.Windows.MessageBox.Show("تم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية", "تم", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }

                await Task.CompletedTask; // To satisfy async requirement
            }
            catch (Exception ex)
            {
                SetError($"خطأ في إعادة تعيين الإعدادات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في إعادة تعيين إعدادات النظام");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }

        #endregion

        #region Events

        public event Action? SettingsSaved;
        public event Action<bool>? RequestClose;

        #endregion
    }

    #region Settings Categories

    public abstract class SettingsCategory : BaseViewModel
    {
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;

        public abstract Task LoadSettingsAsync(SettingsService settingsService);
        public abstract Task SaveSettingsAsync(SettingsService settingsService);
        public abstract void ResetToDefaults();
    }

    public class GeneralSettingsCategory : SettingsCategory
    {
        private string _companyName = string.Empty;
        public string CompanyName
        {
            get => _companyName;
            set => SetProperty(ref _companyName, value);
        }

        private string _companyAddress = string.Empty;
        public string CompanyAddress
        {
            get => _companyAddress;
            set => SetProperty(ref _companyAddress, value);
        }

        private string _companyPhone = string.Empty;
        public string CompanyPhone
        {
            get => _companyPhone;
            set => SetProperty(ref _companyPhone, value);
        }

        private string _taxNumber = string.Empty;
        public string TaxNumber
        {
            get => _taxNumber;
            set => SetProperty(ref _taxNumber, value);
        }

        public ObservableCollection<string> Languages { get; } = new() { "العربية", "English" };
        public ObservableCollection<string> Themes { get; } = new() { "فاتح", "داكن" };

        private string _selectedLanguage = "العربية";
        public string SelectedLanguage
        {
            get => _selectedLanguage;
            set => SetProperty(ref _selectedLanguage, value);
        }

        private string _selectedTheme = "فاتح";
        public string SelectedTheme
        {
            get => _selectedTheme;
            set => SetProperty(ref _selectedTheme, value);
        }

        private bool _startWithWindows;
        public bool StartWithWindows
        {
            get => _startWithWindows;
            set => SetProperty(ref _startWithWindows, value);
        }

        public override async Task LoadSettingsAsync(SettingsService settingsService)
        {
            CompanyName = await settingsService.GetSettingAsync("CompanyName", "شركة إدارة المبيعات");
            CompanyAddress = await settingsService.GetSettingAsync("CompanyAddress", "");
            CompanyPhone = await settingsService.GetSettingAsync("CompanyPhone", "");
            TaxNumber = await settingsService.GetSettingAsync("TaxNumber", "");
            SelectedLanguage = await settingsService.GetSettingAsync("Language", "العربية");
            SelectedTheme = await settingsService.GetSettingAsync("Theme", "فاتح");
            StartWithWindows = bool.Parse(await settingsService.GetSettingAsync("StartWithWindows", "false"));
        }

        public override async Task SaveSettingsAsync(SettingsService settingsService)
        {
            await settingsService.SetSettingAsync("CompanyName", CompanyName);
            await settingsService.SetSettingAsync("CompanyAddress", CompanyAddress);
            await settingsService.SetSettingAsync("CompanyPhone", CompanyPhone);
            await settingsService.SetSettingAsync("TaxNumber", TaxNumber);
            await settingsService.SetSettingAsync("Language", SelectedLanguage);
            await settingsService.SetSettingAsync("Theme", SelectedTheme);
            await settingsService.SetSettingAsync("StartWithWindows", StartWithWindows.ToString());
        }

        public override void ResetToDefaults()
        {
            CompanyName = "شركة إدارة المبيعات";
            CompanyAddress = "";
            CompanyPhone = "";
            TaxNumber = "";
            SelectedLanguage = "العربية";
            SelectedTheme = "فاتح";
            StartWithWindows = false;
        }
    }

    public class FinancialSettingsCategory : SettingsCategory
    {
        public ObservableCollection<string> Currencies { get; } = new() { "ريال سعودي", "دولار أمريكي", "يورو" };

        private string _defaultCurrency = "ريال سعودي";
        public string DefaultCurrency
        {
            get => _defaultCurrency;
            set => SetProperty(ref _defaultCurrency, value);
        }

        private decimal _taxRate = 15.0m;
        public decimal TaxRate
        {
            get => _taxRate;
            set => SetProperty(ref _taxRate, value);
        }

        private bool _autoCalculateTax = true;
        public bool AutoCalculateTax
        {
            get => _autoCalculateTax;
            set => SetProperty(ref _autoCalculateTax, value);
        }

        private bool _allowNegativeStock;
        public bool AllowNegativeStock
        {
            get => _allowNegativeStock;
            set => SetProperty(ref _allowNegativeStock, value);
        }

        public override async Task LoadSettingsAsync(SettingsService settingsService)
        {
            DefaultCurrency = await settingsService.GetSettingAsync("DefaultCurrency", "ريال سعودي");
            TaxRate = decimal.Parse(await settingsService.GetSettingAsync("TaxRate", "15.0"));
            AutoCalculateTax = bool.Parse(await settingsService.GetSettingAsync("AutoCalculateTax", "true"));
            AllowNegativeStock = bool.Parse(await settingsService.GetSettingAsync("AllowNegativeStock", "false"));
        }

        public override async Task SaveSettingsAsync(SettingsService settingsService)
        {
            await settingsService.SetSettingAsync("DefaultCurrency", DefaultCurrency);
            await settingsService.SetSettingAsync("TaxRate", TaxRate.ToString());
            await settingsService.SetSettingAsync("AutoCalculateTax", AutoCalculateTax.ToString());
            await settingsService.SetSettingAsync("AllowNegativeStock", AllowNegativeStock.ToString());
        }

        public override void ResetToDefaults()
        {
            DefaultCurrency = "ريال سعودي";
            TaxRate = 15.0m;
            AutoCalculateTax = true;
            AllowNegativeStock = false;
        }
    }

    public class InventorySettingsCategory : SettingsCategory
    {
        private int _lowStockThreshold = 10;
        public int LowStockThreshold
        {
            get => _lowStockThreshold;
            set => SetProperty(ref _lowStockThreshold, value);
        }

        private bool _showLowStockAlerts = true;
        public bool ShowLowStockAlerts
        {
            get => _showLowStockAlerts;
            set => SetProperty(ref _showLowStockAlerts, value);
        }

        private bool _autoTrackStock = true;
        public bool AutoTrackStock
        {
            get => _autoTrackStock;
            set => SetProperty(ref _autoTrackStock, value);
        }

        public override async Task LoadSettingsAsync(SettingsService settingsService)
        {
            LowStockThreshold = int.Parse(await settingsService.GetSettingAsync("LowStockThreshold", "10"));
            ShowLowStockAlerts = bool.Parse(await settingsService.GetSettingAsync("ShowLowStockAlerts", "true"));
            AutoTrackStock = bool.Parse(await settingsService.GetSettingAsync("AutoTrackStock", "true"));
        }

        public override async Task SaveSettingsAsync(SettingsService settingsService)
        {
            await settingsService.SetSettingAsync("LowStockThreshold", LowStockThreshold.ToString());
            await settingsService.SetSettingAsync("ShowLowStockAlerts", ShowLowStockAlerts.ToString());
            await settingsService.SetSettingAsync("AutoTrackStock", AutoTrackStock.ToString());
        }

        public override void ResetToDefaults()
        {
            LowStockThreshold = 10;
            ShowLowStockAlerts = true;
            AutoTrackStock = true;
        }
    }

    public class BackupSettingsCategory : SettingsCategory
    {
        public ObservableCollection<string> BackupIntervals { get; } = new() { "يومي", "أسبوعي", "شهري" };

        private bool _autoBackupEnabled;
        public bool AutoBackupEnabled
        {
            get => _autoBackupEnabled;
            set => SetProperty(ref _autoBackupEnabled, value);
        }

        private string _backupInterval = "أسبوعي";
        public string BackupInterval
        {
            get => _backupInterval;
            set => SetProperty(ref _backupInterval, value);
        }

        private string _backupPath = "Backups";
        public string BackupPath
        {
            get => _backupPath;
            set => SetProperty(ref _backupPath, value);
        }

        private RelayCommand? _createBackupCommand;
        public RelayCommand CreateBackupCommand => _createBackupCommand ??= new RelayCommand(CreateBackup);

        private RelayCommand? _restoreBackupCommand;
        public RelayCommand RestoreBackupCommand => _restoreBackupCommand ??= new RelayCommand(RestoreBackup);

        public override async Task LoadSettingsAsync(SettingsService settingsService)
        {
            AutoBackupEnabled = bool.Parse(await settingsService.GetSettingAsync("AutoBackupEnabled", "false"));
            BackupInterval = await settingsService.GetSettingAsync("BackupInterval", "أسبوعي");
            BackupPath = await settingsService.GetSettingAsync("BackupPath", "Backups");
        }

        public override async Task SaveSettingsAsync(SettingsService settingsService)
        {
            await settingsService.SetSettingAsync("AutoBackupEnabled", AutoBackupEnabled.ToString());
            await settingsService.SetSettingAsync("BackupInterval", BackupInterval);
            await settingsService.SetSettingAsync("BackupPath", BackupPath);
        }

        public override void ResetToDefaults()
        {
            AutoBackupEnabled = false;
            BackupInterval = "أسبوعي";
            BackupPath = "Backups";
        }

        private async void CreateBackup()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري إنشاء نسخة احتياطية...";

                // التحقق من وجود مسار النسخ الاحتياطي
                if (string.IsNullOrWhiteSpace(BackupPath))
                {
                    System.Windows.MessageBox.Show("الرجاء تحديد مسار النسخ الاحتياطي أولاً", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    return;
                }

                // التأكد من وجود المجلد
                if (!System.IO.Directory.Exists(BackupPath))
                {
                    try
                    {
                        System.IO.Directory.CreateDirectory(BackupPath);
                    }
                    catch (Exception ex)
                    {
                        System.Windows.MessageBox.Show($"فشل في إنشاء مجلد النسخ الاحتياطي: {ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }
                }

                // إنشاء اسم ملف النسخة الاحتياطية
                string backupFileName = $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}.bak";
                string backupFilePath = System.IO.Path.Combine(BackupPath, backupFileName);

                // استخدام خدمة النسخ الاحتياطي لإنشاء نسخة
                var dbService = new DatabaseService();
                var backupService = new BackupService(dbService, "SalesManagement.db");
                var result = await backupService.CreateBackupAsync("نسخة احتياطية يدوية");
                bool success = !string.IsNullOrEmpty(result);

                if (success)
                {
                    System.Windows.MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح في:\n{backupFilePath}", "تم", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    LoggingService.LogSystemEvent("نسخ احتياطي", $"تم إنشاء نسخة احتياطية في: {backupFilePath}");
                }
                else
                {
                    System.Windows.MessageBox.Show("فشل في إنشاء النسخة الاحتياطية", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    LoggingService.LogError(new Exception("فشل في إنشاء النسخة الاحتياطية"), "فشل إنشاء نسخة احتياطية");
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);

                LoggingService.LogError(ex, "خطأ في إنشاء نسخة احتياطية");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void RestoreBackup()
        {
            try
            {
                // عرض مربع حوار لاختيار ملف النسخة الاحتياطية
                var dialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختر ملف النسخة الاحتياطية",
                    Filter = "ملفات النسخ الاحتياطي (*.bak)|*.bak|جميع الملفات (*.*)|*.*",
                    InitialDirectory = BackupPath
                };

                if (dialog.ShowDialog() == true)
                {
                    // تأكيد من المستخدم
                    var result = System.Windows.MessageBox.Show(
                        "سيتم استبدال البيانات الحالية بالنسخة الاحتياطية. هل تريد المتابعة؟",
                        "تأكيد استعادة النسخة الاحتياطية",
                        System.Windows.MessageBoxButton.YesNo,
                        System.Windows.MessageBoxImage.Warning);

                    if (result == System.Windows.MessageBoxResult.Yes)
                    {
                        IsLoading = true;
                        LoadingMessage = "جاري استعادة النسخة الاحتياطية...";

                        // استخدام خدمة النسخ الاحتياطي لاستعادة النسخة
                        var dbService = new DatabaseService();
                var backupService = new BackupService(dbService, "SalesManagement.db");
                        bool success = await backupService.RestoreBackupAsync(dialog.FileName);

                        if (success)
                        {
                            System.Windows.MessageBox.Show(
                                "تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تشغيل التطبيق.",
                                "نجاح",
                                System.Windows.MessageBoxButton.OK,
                                System.Windows.MessageBoxImage.Information);

                            LoggingService.LogSystemEvent("استعادة نسخة احتياطية", $"تم استعادة نسخة احتياطية من: {dialog.FileName}");

                            // إعادة تشغيل التطبيق
                            System.Windows.Application.Current.Shutdown();
                            System.Diagnostics.Process.Start(System.Windows.Application.ResourceAssembly.Location);
                        }
                        else
                        {
                            System.Windows.MessageBox.Show(
                                "فشل استعادة النسخة الاحتياطية",
                                "خطأ",
                                System.Windows.MessageBoxButton.OK,
                                System.Windows.MessageBoxImage.Error);

                            LoggingService.LogError(new Exception("فشل في استعادة النسخة الاحتياطية"), "فشل استعادة نسخة احتياطية");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);

                LoggingService.LogError(ex, "خطأ في استعادة نسخة احتياطية");
            }
            finally
            {
                IsLoading = false;
            }
        }
    }

    public class PrintingSettingsCategory : SettingsCategory
    {
        public ObservableCollection<string> Printers { get; } = new() { "الطابعة الافتراضية" };
        public ObservableCollection<string> PaperSizes { get; } = new() { "A4", "A5", "Letter" };

        private string _defaultPrinter = "الطابعة الافتراضية";
        public string DefaultPrinter
        {
            get => _defaultPrinter;
            set => SetProperty(ref _defaultPrinter, value);
        }

        private string _paperSize = "A4";
        public string PaperSize
        {
            get => _paperSize;
            set => SetProperty(ref _paperSize, value);
        }

        private bool _autoPrintInvoices;
        public bool AutoPrintInvoices
        {
            get => _autoPrintInvoices;
            set => SetProperty(ref _autoPrintInvoices, value);
        }

        private bool _showPrintPreview = true;
        public bool ShowPrintPreview
        {
            get => _showPrintPreview;
            set => SetProperty(ref _showPrintPreview, value);
        }

        public override async Task LoadSettingsAsync(SettingsService settingsService)
        {
            DefaultPrinter = await settingsService.GetSettingAsync("DefaultPrinter", "الطابعة الافتراضية");
            PaperSize = await settingsService.GetSettingAsync("PaperSize", "A4");
            AutoPrintInvoices = bool.Parse(await settingsService.GetSettingAsync("AutoPrintInvoices", "false"));
            ShowPrintPreview = bool.Parse(await settingsService.GetSettingAsync("ShowPrintPreview", "true"));
        }

        public override async Task SaveSettingsAsync(SettingsService settingsService)
        {
            await settingsService.SetSettingAsync("DefaultPrinter", DefaultPrinter);
            await settingsService.SetSettingAsync("PaperSize", PaperSize);
            await settingsService.SetSettingAsync("AutoPrintInvoices", AutoPrintInvoices.ToString());
            await settingsService.SetSettingAsync("ShowPrintPreview", ShowPrintPreview.ToString());
        }

        public override void ResetToDefaults()
        {
            DefaultPrinter = "الطابعة الافتراضية";
            PaperSize = "A4";
            AutoPrintInvoices = false;
            ShowPrintPreview = true;
        }
    }

    #endregion
}
