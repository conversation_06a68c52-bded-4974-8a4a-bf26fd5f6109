using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class UnifiedSalesWindow : Window, INotifyPropertyChanged
    {
        private readonly UnifiedSalesService _unifiedSalesService;
        private readonly CustomerService _customerService;
        private ObservableCollection<UnifiedSale> _allSales;
        private ObservableCollection<UnifiedSale> _filteredSales;
        private List<Customer> _customers;

        public UnifiedSalesWindow()
        {
            InitializeComponent();
            DataContext = this;

            var dbService = new DatabaseService();
            _unifiedSalesService = new UnifiedSalesService(dbService);
            _customerService = new CustomerService(dbService);
            
            _allSales = new ObservableCollection<UnifiedSale>();
            _filteredSales = new ObservableCollection<UnifiedSale>();
            _customers = new List<Customer>();

            SalesDataGrid.ItemsSource = _filteredSales;

            Loaded += UnifiedSalesWindow_Loaded;
        }

        private async void UnifiedSalesWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري تحميل البيانات...";

                // تحميل العملاء
                _customers = (await _customerService.GetAllCustomersAsync()).ToList();
                await LoadCustomersComboBoxAsync();

                // تحميل المبيعات
                var sales = await _unifiedSalesService.GetAllUnifiedSalesAsync();
                
                _allSales.Clear();
                _filteredSales.Clear();

                foreach (var sale in sales)
                {
                    _allSales.Add(sale);
                    _filteredSales.Add(sale);
                }

                UpdateStatistics();
                UpdateStatusBar();

                StatusTextBlock.Text = $"تم تحميل {_allSales.Count} عملية بيع بنجاح";
                LastUpdateTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");

                LoggingService.LogInfo($"تم تحميل {_allSales.Count} عملية بيع في النافذة الموحدة");
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في تحميل البيانات";
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                LoggingService.LogError(ex, "خطأ في تحميل البيانات في النافذة الموحدة");
            }
        }

        private async Task LoadCustomersComboBoxAsync()
        {
            CustomerComboBox.Items.Clear();
            CustomerComboBox.Items.Add(new ComboBoxItem { Content = "جميع العملاء", IsSelected = true });

            foreach (var customer in _customers)
            {
                CustomerComboBox.Items.Add(new ComboBoxItem { Content = customer.Name, Tag = customer.Id });
            }
        }

        private void UpdateStatistics()
        {
            var totalCount = _filteredSales.Count;
            var totalAmount = _filteredSales.Sum(s => s.TotalAmount);
            var pendingAmount = _filteredSales.Sum(s => s.RemainingAmount);
            var debtAmount = _filteredSales.Sum(s => s.PreviousDebt);

            TotalSalesCountText.Text = $"إجمالي العمليات: {totalCount}";
            TotalAmountText.Text = $"إجمالي المبيعات: {totalAmount:N2} دج";
            PendingAmountText.Text = $"المبالغ المعلقة: {pendingAmount:N2} دج";
            DebtAmountText.Text = $"إجمالي الديون: {debtAmount:N2} دج";
        }

        private void UpdateStatusBar()
        {
            var invoiceCount = _filteredSales.Count(s => s.SaleType == "فاتورة");
            var quickSaleCount = _filteredSales.Count(s => s.SaleType == "بيع سريع");
            
            FilterStatusTextBlock.Text = $"الفواتير: {invoiceCount} | البيع السريع: {quickSaleCount}";
        }

        private void ApplyFilters()
        {
            try
            {
                var filtered = _allSales.AsEnumerable();

                // تصفية النص
                var searchText = SearchTextBox.Text?.Trim();
                if (!string.IsNullOrEmpty(searchText))
                {
                    filtered = filtered.Where(s =>
                        s.SaleNumber.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                        s.CustomerName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                        s.Notes.Contains(searchText, StringComparison.OrdinalIgnoreCase));
                }

                // تصفية التواريخ
                if (FromDatePicker.SelectedDate.HasValue)
                {
                    filtered = filtered.Where(s => s.SaleDate.Date >= FromDatePicker.SelectedDate.Value.Date);
                }

                if (ToDatePicker.SelectedDate.HasValue)
                {
                    filtered = filtered.Where(s => s.SaleDate.Date <= ToDatePicker.SelectedDate.Value.Date);
                }

                // تصفية نوع العملية
                var selectedSaleType = (SaleTypeComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();
                if (!string.IsNullOrEmpty(selectedSaleType) && selectedSaleType != "الكل")
                {
                    filtered = filtered.Where(s => s.SaleType == selectedSaleType);
                }

                // تصفية حالة الدفع
                var selectedPaymentStatus = (PaymentStatusComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();
                if (!string.IsNullOrEmpty(selectedPaymentStatus) && selectedPaymentStatus != "الكل")
                {
                    filtered = filtered.Where(s => s.PaymentStatus == selectedPaymentStatus);
                }

                // تصفية العميل
                var selectedCustomerItem = CustomerComboBox.SelectedItem as ComboBoxItem;
                if (selectedCustomerItem?.Tag != null)
                {
                    var customerId = (int)selectedCustomerItem.Tag;
                    filtered = filtered.Where(s => s.CustomerId == customerId);
                }

                _filteredSales.Clear();
                foreach (var sale in filtered.OrderByDescending(s => s.SaleDate))
                {
                    _filteredSales.Add(sale);
                }

                UpdateStatistics();
                UpdateStatusBar();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تطبيق التصفية");
            }
        }

        // معالجات الأحداث
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void SaleTypeFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void PaymentStatusFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void CustomerFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            SaleTypeComboBox.SelectedIndex = 0;
            PaymentStatusComboBox.SelectedIndex = 0;
            CustomerComboBox.SelectedIndex = 0;
            ApplyFilters();
        }

        private void SalesDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (SalesDataGrid.SelectedItem is UnifiedSale selectedSale)
            {
                ShowSaleDetails(selectedSale);
            }
        }

        private void ShowSaleDetails(UnifiedSale sale)
        {
            var details = $"تفاصيل العملية: {sale.SaleNumber}\n\n" +
                         $"النوع: {sale.SaleType}\n" +
                         $"العميل: {sale.CustomerName}\n" +
                         $"التاريخ: {sale.SaleDateFormatted}\n" +
                         $"المبلغ الفرعي: {sale.SubtotalFormatted} دج\n" +
                         $"الخصم: {sale.DiscountFormatted} دج\n" +
                         $"الضريبة: {sale.TaxFormatted} دج\n" +
                         $"الديون السابقة: {sale.PreviousDebtFormatted} دج\n" +
                         $"المبلغ الإجمالي: {sale.TotalAmountFormatted} دج\n" +
                         $"المبلغ المدفوع: {sale.PaidAmountFormatted} دج\n" +
                         $"المبلغ المتبقي: {sale.RemainingAmountFormatted} دج\n" +
                         $"طريقة الدفع: {sale.PaymentMethod}\n" +
                         $"حالة الدفع: {sale.PaymentStatus}\n" +
                         $"ملاحظات: {sale.Notes}";

            MessageBox.Show(details, "تفاصيل العملية", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void GenerateReportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة التقارير قيد التطوير", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة التصدير قيد التطوير", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
