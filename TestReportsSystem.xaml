<Window x:Class="SalesManagementSystem.TestReportsSystem"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="اختبار نظام التقارير المتقدم" 
        Height="700" 
        Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="ChartLine" 
                                   Width="32" Height="32" 
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="اختبار نظام التقارير المتقدم" 
                      Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                      VerticalAlignment="Center" 
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Test Actions -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" MaxHeight="200">
            <StackPanel>
                
                <!-- Report Generation Tests -->
                <GroupBox Header="اختبارات إنشاء التقارير" 
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0"
                                   x:Name="SalesReportButton"
                                   Click="SalesReportButton_Click"
                                   Style="{DynamicResource MaterialDesignRaisedButton}"
                                   Margin="0,0,8,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="تقرير المبيعات" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="1"
                                   x:Name="InventoryReportButton"
                                   Click="InventoryReportButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="4,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Package" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="تقرير المخزون" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="2"
                                   x:Name="ProfitLossButton"
                                   Click="ProfitLossButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="8,0,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="TrendingUp" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="الأرباح والخسائر" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                        <Grid Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0"
                                   x:Name="CustomerReportButton"
                                   Click="CustomerReportButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="0,0,8,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Account" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="تقرير العملاء" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="1"
                                   x:Name="ProductReportButton"
                                   Click="ProductReportButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="4,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ShoppingCart" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="تقرير المنتجات" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="2"
                                   x:Name="TrendAnalysisButton"
                                   Click="TrendAnalysisButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="8,0,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartTimeline" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="تحليل الاتجاهات" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- Export Tests -->
                <GroupBox Header="اختبارات التصدير" 
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0"
                                   x:Name="ExportExcelButton"
                                   Click="ExportExcelButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="0,0,8,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileExcel" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="تصدير Excel" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="1"
                                   x:Name="ExportPdfButton"
                                   Click="ExportPdfButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="4,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FilePdf" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="تصدير PDF" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="2"
                                   x:Name="ExportCsvButton"
                                   Click="ExportCsvButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="8,0,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDelimited" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="تصدير CSV" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- Chart Tests -->
                <GroupBox Header="اختبارات الرسوم البيانية" 
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0"
                                   x:Name="LineChartButton"
                                   Click="LineChartButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="0,0,8,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartLine" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="رسم خطي" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="1"
                                   x:Name="PieChartButton"
                                   Click="PieChartButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="4,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartPie" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="رسم دائري" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="2"
                                   x:Name="BarChartButton"
                                   Click="BarChartButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="8,0,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartBar" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="رسم أعمدة" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- Advanced Features -->
                <GroupBox Header="الميزات المتقدمة" 
                         Style="{DynamicResource MaterialDesignCardGroupBox}">
                    <StackPanel Margin="16">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0"
                                   x:Name="OpenReportsWindowButton"
                                   Click="OpenReportsWindowButton_Click"
                                   Style="{DynamicResource MaterialDesignRaisedButton}"
                                   Margin="0,0,8,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="WindowMaximize" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="فتح نافذة التقارير" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="1"
                                   x:Name="GenerateTestDataButton"
                                   Click="GenerateTestDataButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="8,0,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="DatabasePlus" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="إنشاء بيانات تجريبية" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Test Results -->
        <GroupBox Grid.Row="2" Header="نتائج الاختبارات" 
                 Style="{DynamicResource MaterialDesignCardGroupBox}">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <TextBox x:Name="ResultsTextBox"
                        Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                        AcceptsReturn="True"
                        IsReadOnly="True"
                        TextWrapping="Wrap"
                        VerticalScrollBarVisibility="Auto"
                        Text="جاهز لبدء اختبار نظام التقارير..."
                        Margin="16"/>
            </ScrollViewer>
        </GroupBox>
    </Grid>
</Window>
