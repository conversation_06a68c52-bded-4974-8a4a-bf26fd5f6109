<Window x:Class="SalesManagementSystem.Views.ExpensesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="💰 إدارة المصاريف"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                               Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF673AB7">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="💰" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة المصاريف" FontSize="18" 
                              FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="إجمالي المصاريف:" FontSize="12" Foreground="LightGray" Margin="0,0,5,0"/>
                    <TextBlock x:Name="TotalExpensesLabel" Text="0.00 دج" FontSize="16" 
                              FontWeight="Bold" Foreground="Yellow"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Controls Section -->
        <Border Grid.Row="1" Background="LightGray" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="➕ إضافة مصروف جديد" Width="150" Height="40" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF4CAF50" Click="AddExpense_Click"/>
                <Button Content="✏️ تعديل المصروف" Width="130" Height="40" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF2196F3" Click="EditExpense_Click"/>
                <Button Content="🗑️ حذف المصروف" Width="120" Height="40" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FFF44336" Click="DeleteExpense_Click"/>
                <Button Content="📊 تقرير المصاريف" Width="130" Height="40" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FFFF9800" Click="ExpenseReport_Click"/>
                <Button Content="🔍 بحث" Width="80" Height="40" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF9C27B0" Click="Search_Click"/>
            </StackPanel>
        </Border>

        <!-- Expenses DataGrid -->
        <DataGrid Grid.Row="2" x:Name="ExpensesDataGrid" Margin="10" AutoGenerateColumns="False" 
                 CanUserAddRows="False" GridLinesVisibility="All" AlternatingRowBackground="LightGray"
                 SelectionMode="Single">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60" IsReadOnly="True"/>
                <DataGridTemplateColumn Header="التصنيف" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding CategoryIcon}" FontSize="16" Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding CategoryName}" VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="العنوان" Binding="{Binding Title}" Width="150"/>
                <DataGridTextColumn Header="المبلغ" Binding="{Binding FormattedAmount}" Width="100" IsReadOnly="True"/>
                <DataGridTextColumn Header="التاريخ" Binding="{Binding FormattedDate}" Width="100" IsReadOnly="True"/>
                <DataGridTextColumn Header="اسم الموظف" Binding="{Binding EmployeeName}" Width="120"/>
                <DataGridTextColumn Header="اسم المنتج" Binding="{Binding ProductName}" Width="120"/>
                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="150"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Summary Section -->
        <Border Grid.Row="3" Background="LightBlue" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="عدد المصاريف" FontWeight="Bold" FontSize="14"/>
                    <TextBlock x:Name="ExpensesCountLabel" Text="0" FontSize="18" Foreground="Blue" FontWeight="Bold"/>
                </StackPanel>

                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="مصاريف هذا الشهر" FontWeight="Bold" FontSize="14"/>
                    <TextBlock x:Name="MonthlyExpensesLabel" Text="0.00 دج" FontSize="18" Foreground="Orange" FontWeight="Bold"/>
                </StackPanel>

                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="أكبر مصروف" FontWeight="Bold" FontSize="14"/>
                    <TextBlock x:Name="MaxExpenseLabel" Text="0.00 دج" FontSize="18" Foreground="Red" FontWeight="Bold"/>
                </StackPanel>

                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="متوسط المصاريف" FontWeight="Bold" FontSize="14"/>
                    <TextBlock x:Name="AverageExpenseLabel" Text="0.00 دج" FontSize="18" Foreground="Green" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
