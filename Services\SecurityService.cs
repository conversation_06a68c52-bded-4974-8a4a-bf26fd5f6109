using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace SalesManagementSystem.Services
{
    public class SecurityService
    {
        private const int SaltSize = 32; // 256 bits
        private const int HashSize = 32; // 256 bits
        private const int Iterations = 10000; // PBKDF2 iterations

        /// <summary>
        /// Creates a hash from a password
        /// </summary>
        /// <param name="password">The password to hash</param>
        /// <returns>The hash and salt as base64 strings</returns>
        public (string hash, string salt) HashPassword(string password)
        {
            // Generate a random salt
            byte[] salt = new byte[SaltSize];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(salt);
            }

            // Hash the password with the salt
            byte[] hash = HashPassword(password, salt);

            // Return hash and salt as base64 strings
            return (Convert.ToBase64String(hash), Convert.ToBase64String(salt));
        }

        /// <summary>
        /// Verifies a password against a hash
        /// </summary>
        /// <param name="password">The password to verify</param>
        /// <param name="hashBase64">The hash as base64 string</param>
        /// <param name="saltBase64">The salt as base64 string</param>
        /// <returns>True if the password is correct</returns>
        public bool VerifyPassword(string password, string hashBase64, string saltBase64)
        {
            try
            {
                // Convert base64 strings back to byte arrays
                byte[] hash = Convert.FromBase64String(hashBase64);
                byte[] salt = Convert.FromBase64String(saltBase64);

                // Hash the provided password with the stored salt
                byte[] testHash = HashPassword(password, salt);

                // Compare the hashes
                return SlowEquals(hash, testHash);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Hashes a password with a given salt
        /// </summary>
        /// <param name="password">The password to hash</param>
        /// <param name="salt">The salt</param>
        /// <returns>The hash</returns>
        private byte[] HashPassword(string password, byte[] salt)
        {
            using (var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256))
            {
                return pbkdf2.GetBytes(HashSize);
            }
        }

        /// <summary>
        /// Compares two byte arrays in length-constant time to prevent timing attacks
        /// </summary>
        /// <param name="a">First byte array</param>
        /// <param name="b">Second byte array</param>
        /// <returns>True if arrays are equal</returns>
        private bool SlowEquals(byte[] a, byte[] b)
        {
            uint diff = (uint)a.Length ^ (uint)b.Length;
            for (int i = 0; i < a.Length && i < b.Length; i++)
                diff |= (uint)(a[i] ^ b[i]);
            return diff == 0;
        }

        /// <summary>
        /// Generates a secure random password
        /// </summary>
        /// <param name="length">Password length</param>
        /// <param name="includeSpecialChars">Include special characters</param>
        /// <returns>Generated password</returns>
        public string GeneratePassword(int length = 12, bool includeSpecialChars = true)
        {
            const string lowercase = "abcdefghijklmnopqrstuvwxyz";
            const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string digits = "0123456789";
            const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

            string chars = lowercase + uppercase + digits;
            if (includeSpecialChars)
                chars += specialChars;

            var password = new StringBuilder();
            using (var rng = RandomNumberGenerator.Create())
            {
                byte[] randomBytes = new byte[length];
                rng.GetBytes(randomBytes);

                for (int i = 0; i < length; i++)
                {
                    password.Append(chars[randomBytes[i] % chars.Length]);
                }
            }

            // Ensure password contains at least one character from each required category
            var result = password.ToString();
            if (!ContainsRequiredCharacters(result, includeSpecialChars))
            {
                // Regenerate if requirements not met
                return GeneratePassword(length, includeSpecialChars);
            }

            return result;
        }

        /// <summary>
        /// Validates password strength
        /// </summary>
        /// <param name="password">Password to validate</param>
        /// <returns>Password strength score (0-5)</returns>
        public PasswordStrength ValidatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return new PasswordStrength { Score = 0 };

            int score = 0;
            var feedback = new List<string>();

            // Length check
            if (password.Length >= 8)
                score++;
            else
                feedback.Add("يجب أن تكون كلمة المرور 8 أحرف على الأقل");

            if (password.Length >= 12)
                score++;

            // Character variety checks
            if (password.Any(char.IsLower))
                score++;
            else
                feedback.Add("يجب أن تحتوي على أحرف صغيرة");

            if (password.Any(char.IsUpper))
                score++;
            else
                feedback.Add("يجب أن تحتوي على أحرف كبيرة");

            if (password.Any(char.IsDigit))
                score++;
            else
                feedback.Add("يجب أن تحتوي على أرقام");

            if (password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c)))
                score++;
            else
                feedback.Add("يجب أن تحتوي على رموز خاصة");

            // Common password check
            if (IsCommonPassword(password))
            {
                score = Math.Max(0, score - 2);
                feedback.Add("كلمة المرور شائعة جداً");
            }

            return new PasswordStrength
            {
                Score = Math.Min(5, score),
                Feedback = feedback
            };
        }

        /// <summary>
        /// Encrypts sensitive data
        /// </summary>
        /// <param name="plainText">Text to encrypt</param>
        /// <param name="key">Encryption key</param>
        /// <returns>Encrypted text as base64</returns>
        public string EncryptData(string plainText, string key)
        {
            try
            {
                using (var aes = Aes.Create())
                {
                    // Derive key from password
                    var keyBytes = Encoding.UTF8.GetBytes(key);
                    var salt = new byte[16];
                    using (var rng = RandomNumberGenerator.Create())
                    {
                        rng.GetBytes(salt);
                    }

                    using (var pbkdf2 = new Rfc2898DeriveBytes(keyBytes, salt, 1000, HashAlgorithmName.SHA256))
                    {
                        aes.Key = pbkdf2.GetBytes(32); // 256-bit key
                        aes.IV = pbkdf2.GetBytes(16);  // 128-bit IV
                    }

                    using (var encryptor = aes.CreateEncryptor())
                    {
                        var plainBytes = Encoding.UTF8.GetBytes(plainText);
                        var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);

                        // Combine salt + IV + encrypted data
                        var result = new byte[salt.Length + aes.IV.Length + encryptedBytes.Length];
                        Buffer.BlockCopy(salt, 0, result, 0, salt.Length);
                        Buffer.BlockCopy(aes.IV, 0, result, salt.Length, aes.IV.Length);
                        Buffer.BlockCopy(encryptedBytes, 0, result, salt.Length + aes.IV.Length, encryptedBytes.Length);

                        return Convert.ToBase64String(result);
                    }
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Decrypts sensitive data
        /// </summary>
        /// <param name="encryptedText">Encrypted text as base64</param>
        /// <param name="key">Decryption key</param>
        /// <returns>Decrypted plain text</returns>
        public string DecryptData(string encryptedText, string key)
        {
            try
            {
                var encryptedBytes = Convert.FromBase64String(encryptedText);

                // Extract salt, IV, and encrypted data
                var salt = new byte[16];
                var iv = new byte[16];
                var encrypted = new byte[encryptedBytes.Length - 32];

                Buffer.BlockCopy(encryptedBytes, 0, salt, 0, 16);
                Buffer.BlockCopy(encryptedBytes, 16, iv, 0, 16);
                Buffer.BlockCopy(encryptedBytes, 32, encrypted, 0, encrypted.Length);

                using (var aes = Aes.Create())
                {
                    // Derive key from password
                    var keyBytes = Encoding.UTF8.GetBytes(key);
                    using (var pbkdf2 = new Rfc2898DeriveBytes(keyBytes, salt, 1000, HashAlgorithmName.SHA256))
                    {
                        aes.Key = pbkdf2.GetBytes(32);
                        aes.IV = iv;
                    }

                    using (var decryptor = aes.CreateDecryptor())
                    {
                        var decryptedBytes = decryptor.TransformFinalBlock(encrypted, 0, encrypted.Length);
                        return Encoding.UTF8.GetString(decryptedBytes);
                    }
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        private bool ContainsRequiredCharacters(string password, bool includeSpecialChars)
        {
            bool hasLower = password.Any(char.IsLower);
            bool hasUpper = password.Any(char.IsUpper);
            bool hasDigit = password.Any(char.IsDigit);
            bool hasSpecial = !includeSpecialChars || password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c));

            return hasLower && hasUpper && hasDigit && hasSpecial;
        }

        private bool IsCommonPassword(string password)
        {
            // List of common passwords (simplified)
            var commonPasswords = new[]
            {
                "password", "123456", "123456789", "12345678", "12345",
                "1234567", "admin", "qwerty", "abc123", "password123",
                "admin123", "root", "user", "test", "guest"
            };

            return commonPasswords.Contains(password.ToLower());
        }

        /// <summary>
        /// Generates a secure random password
        /// </summary>
        /// <param name="length">Password length</param>
        /// <returns>Generated password</returns>
        public string GenerateSecurePassword(int length = 12)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
            var random = new Random();
            var password = new char[length];

            for (int i = 0; i < length; i++)
            {
                password[i] = chars[random.Next(chars.Length)];
            }

            return new string(password);
        }

    }

    public class PasswordStrength
    {
        public int Score { get; set; }
        public List<string> Feedback { get; set; } = new();

        public string Level => Score switch
        {
            0 => "ضعيف جداً",
            1 => "ضعيف",
            2 => "متوسط",
            3 => "جيد",
            4 => "قوي",
            5 => "قوي جداً",
            _ => "غير محدد"
        };

        public string Color => Score switch
        {
            0 or 1 => "#F44336", // Red
            2 => "#FF9800",      // Orange
            3 => "#FFC107",      // Amber
            4 => "#4CAF50",      // Green
            5 => "#2E7D32",      // Dark Green
            _ => "#9E9E9E"       // Grey
        };
    }
}
