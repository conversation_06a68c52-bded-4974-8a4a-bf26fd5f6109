<UserControl x:Class="SalesManagementSystem.Views.CustomersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- Boolean to Color Converter -->
        <Style x:Key="BoolToColorConverter" TargetType="Border">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsActive}" Value="True">
                    <Setter Property="Background" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding IsActive}" Value="False">
                    <Setter Property="Background" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Boolean to Status Text Converter -->
        <Style x:Key="BoolToStatusConverter" TargetType="TextBlock">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsActive}" Value="True">
                    <Setter Property="Text" Value="نشط"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding IsActive}" Value="False">
                    <Setter Property="Text" Value="غير نشط"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="0" Padding="15" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox x:Name="SearchTextBox" Grid.Column="0"
                        materialDesign:HintAssist.Hint="البحث في العملاء (الاسم، الهاتف، البريد الإلكتروني)"
                        materialDesign:TextFieldAssist.HasClearButton="True"
                        materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        Margin="0,0,15,0"
                        TextChanged="SearchTextBox_TextChanged"/>

                <!-- Add Customer Button -->
                <Button x:Name="AddCustomerButton" Grid.Column="1"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Click="AddCustomerButton_Click"
                       Margin="0,0,10,0"
                       Background="{DynamicResource PrimaryHueMidBrush}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AccountPlus" Margin="0,0,8,0"/>
                        <TextBlock Text="إضافة عميل جديد"/>
                    </StackPanel>
                </Button>

                <!-- Refresh Button -->
                <Button x:Name="RefreshButton" Grid.Column="2"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Click="RefreshButton_Click"
                       ToolTip="تحديث قائمة العملاء">
                    <materialDesign:PackIcon Kind="Refresh"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Customers DataGrid -->
        <materialDesign:Card Grid.Row="1" Padding="0">
            <DataGrid x:Name="CustomersDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     materialDesign:DataGridAssist.CellPadding="8"
                     materialDesign:DataGridAssist.ColumnHeaderPadding="8">

                <DataGrid.Columns>
                    <!-- Customer Code -->
                    <DataGridTextColumn Header="كود العميل"
                                      Binding="{Binding Code}"
                                      Width="120"
                                      FontWeight="Bold"/>

                    <!-- Customer Name -->
                    <DataGridTextColumn Header="اسم العميل"
                                      Binding="{Binding Name}"
                                      Width="*"
                                      FontWeight="SemiBold"/>

                    <!-- Phone -->
                    <DataGridTextColumn Header="رقم الهاتف"
                                      Binding="{Binding Phone}"
                                      Width="140"/>

                    <!-- Email -->
                    <DataGridTextColumn Header="البريد الإلكتروني"
                                      Binding="{Binding Email}"
                                      Width="180"/>

                    <!-- Customer Type -->
                    <DataGridTextColumn Header="نوع العميل"
                                      Binding="{Binding CustomerType}"
                                      Width="100"/>

                    <!-- Balance -->
                    <DataGridTextColumn Header="الرصيد"
                                      Binding="{Binding Balance, StringFormat='{}{0:N2} ريال'}"
                                      Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding HasOutstandingBalance}" Value="True">
                                        <Setter Property="Foreground" Value="#F44336"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- Status -->
                    <DataGridTemplateColumn Header="الحالة" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="12" Padding="8,4">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsActive}" Value="True">
                                                    <Setter Property="Background" Value="#4CAF50"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding IsActive}" Value="False">
                                                    <Setter Property="Background" Value="#F44336"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock Foreground="White"
                                              FontSize="10"
                                              FontWeight="Bold"
                                              HorizontalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsActive}" Value="True">
                                                        <Setter Property="Text" Value="نشط"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsActive}" Value="False">
                                                        <Setter Property="Text" Value="غير نشط"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- Actions -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="180">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="تعديل العميل"
                                           Click="EditCustomer_Click"
                                           Tag="{Binding}"
                                           Margin="2">
                                        <materialDesign:PackIcon Kind="Pencil"
                                                               Foreground="{StaticResource PrimaryHueMidBrush}"
                                                               Width="18" Height="18"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="حذف العميل"
                                           Click="DeleteCustomer_Click"
                                           Tag="{Binding}"
                                           Margin="2">
                                        <materialDesign:PackIcon Kind="Delete"
                                                               Foreground="#F44336"
                                                               Width="18" Height="18"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="عرض المشتريات"
                                           Click="ViewPurchases_Click"
                                           Tag="{Binding}"
                                           Margin="2">
                                        <materialDesign:PackIcon Kind="ShoppingCart"
                                                               Foreground="#4CAF50"
                                                               Width="18" Height="18"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="إدارة الديون"
                                           Click="ManageDebt_Click"
                                           Tag="{Binding}"
                                           Margin="2">
                                        <materialDesign:PackIcon Kind="CurrencyUsd"
                                                               Foreground="#FF9800"
                                                               Width="18" Height="18"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
