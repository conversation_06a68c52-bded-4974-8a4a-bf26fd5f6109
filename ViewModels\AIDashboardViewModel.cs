using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using LiveCharts;
using LiveCharts.Wpf;

namespace SalesManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel للوحة تحكم الذكاء الاصطناعي
    /// </summary>
    public class AIDashboardViewModel : INotifyPropertyChanged
    {
        private readonly AIAnalyticsService _aiService;
        private readonly NotificationService _notificationService;

        private bool _isLoading;
        private int _totalModels;
        private int _deployedModels;
        private int _dailyPredictions;
        private decimal _averageAccuracy;
        private int _completedAnalyses;
        private string _averageProcessingTime = "0.5s";

        private ObservableCollection<AIModel> _activeModels = new();
        private ObservableCollection<SmartInsight> _smartInsights = new();
        private ObservableCollection<SmartAlert> _smartAlerts = new();

        // Chart properties
        private SeriesCollection _analyticsSeries = new();
        private Axis _analyticsAxisX = new();
        private Axis _analyticsAxisY = new();

        public AIDashboardViewModel(
            AIAnalyticsService aiService,
            NotificationService notificationService)
        {
            _aiService = aiService ?? throw new ArgumentNullException(nameof(aiService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));

            InitializeCommands();
            _ = Task.Run(async () => await LoadDashboardDataAsync());
        }

        #region Properties

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }

        public int TotalModels
        {
            get => _totalModels;
            set
            {
                if (_totalModels != value)
                {
                    _totalModels = value;
                    OnPropertyChanged();
                }
            }
        }

        public int DeployedModels
        {
            get => _deployedModels;
            set
            {
                if (_deployedModels != value)
                {
                    _deployedModels = value;
                    OnPropertyChanged();
                }
            }
        }

        public int DailyPredictions
        {
            get => _dailyPredictions;
            set
            {
                if (_dailyPredictions != value)
                {
                    _dailyPredictions = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal AverageAccuracy
        {
            get => _averageAccuracy;
            set
            {
                if (_averageAccuracy != value)
                {
                    _averageAccuracy = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CompletedAnalyses
        {
            get => _completedAnalyses;
            set
            {
                if (_completedAnalyses != value)
                {
                    _completedAnalyses = value;
                    OnPropertyChanged();
                }
            }
        }

        public string AverageProcessingTime
        {
            get => _averageProcessingTime;
            set
            {
                if (_averageProcessingTime != value)
                {
                    _averageProcessingTime = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<AIModel> ActiveModels
        {
            get => _activeModels;
            set
            {
                if (_activeModels != value)
                {
                    _activeModels = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<SmartInsight> SmartInsights
        {
            get => _smartInsights;
            set
            {
                if (_smartInsights != value)
                {
                    _smartInsights = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<SmartAlert> SmartAlerts
        {
            get => _smartAlerts;
            set
            {
                if (_smartAlerts != value)
                {
                    _smartAlerts = value;
                    OnPropertyChanged();
                }
            }
        }

        // Chart Properties
        public SeriesCollection AnalyticsSeries
        {
            get => _analyticsSeries;
            set
            {
                if (_analyticsSeries != value)
                {
                    _analyticsSeries = value;
                    OnPropertyChanged();
                }
            }
        }

        public Axis AnalyticsAxisX
        {
            get => _analyticsAxisX;
            set
            {
                if (_analyticsAxisX != value)
                {
                    _analyticsAxisX = value;
                    OnPropertyChanged();
                }
            }
        }

        public Axis AnalyticsAxisY
        {
            get => _analyticsAxisY;
            set
            {
                if (_analyticsAxisY != value)
                {
                    _analyticsAxisY = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Commands

        public ICommand CreateModelCommand { get; private set; } = null!;
        public ICommand RunPredictionCommand { get; private set; } = null!;
        public ICommand AnalyzeDataCommand { get; private set; } = null!;
        public ICommand ViewModelsCommand { get; private set; } = null!;
        public ICommand ViewPredictionsCommand { get; private set; } = null!;
        public ICommand ViewAnalyticsCommand { get; private set; } = null!;
        public ICommand ViewInsightsCommand { get; private set; } = null!;
        public ICommand ViewAlertsCommand { get; private set; } = null!;
        public ICommand ViewModelCommand { get; private set; } = null!;
        public ICommand RunModelCommand { get; private set; } = null!;
        public ICommand ViewAllModelsCommand { get; private set; } = null!;
        public ICommand ViewAllAnalyticsCommand { get; private set; } = null!;
        public ICommand RefreshCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            CreateModelCommand = new RelayCommand(async () => await CreateModelAsync());
            RunPredictionCommand = new RelayCommand(async () => await RunPredictionAsync());
            AnalyzeDataCommand = new RelayCommand(async () => await AnalyzeDataAsync());
            ViewModelsCommand = new RelayCommand(() => NavigateToModels());
            ViewPredictionsCommand = new RelayCommand(() => NavigateToPredictions());
            ViewAnalyticsCommand = new RelayCommand(() => NavigateToAnalytics());
            ViewInsightsCommand = new RelayCommand(() => NavigateToInsights());
            ViewAlertsCommand = new RelayCommand(() => NavigateToAlerts());
            ViewModelCommand = new RelayCommand<AIModel>(model => ViewModelDetails(model!));
            RunModelCommand = new RelayCommand<AIModel>(model => _ = RunModel(model!));
            ViewAllModelsCommand = new RelayCommand(() => NavigateToModels());
            ViewAllAnalyticsCommand = new RelayCommand(() => NavigateToAnalytics());
            RefreshCommand = new RelayCommand(async () => await RefreshDashboardAsync());
        }

        #endregion

        #region Methods

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                IsLoading = true;

                // تحميل إحصائيات النماذج
                await LoadModelStatisticsAsync();

                // تحميل النماذج النشطة
                await LoadActiveModelsAsync();

                // تحميل الرؤى الذكية
                await LoadSmartInsightsAsync();

                // تحميل التنبيهات الذكية
                await LoadSmartAlertsAsync();

                // تحميل بيانات الرسوم البيانية
                await LoadAnalyticsChartsAsync();
            }
            catch (Exception ex)
            {
                await _notificationService.ShowErrorAsync("خطأ في تحميل لوحة التحكم", ex.Message);
                LoggingService.LogError(ex, "خطأ في تحميل لوحة تحكم الذكاء الاصطناعي");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadModelStatisticsAsync()
        {
            try
            {
                // هذه بيانات وهمية - في التطبيق الحقيقي ستأتي من قاعدة البيانات
                await Task.Delay(100); // محاكاة عملية تحميل البيانات
                TotalModels = 12;
                DeployedModels = 8;
                DailyPredictions = 1247;
                AverageAccuracy = 94.2m;
                CompletedAnalyses = 156;
                AverageProcessingTime = "0.3s";
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل إحصائيات النماذج");
            }
        }

        private async Task LoadActiveModelsAsync()
        {
            try
            {
                // بيانات وهمية للنماذج النشطة
                await Task.Delay(100); // محاكاة عملية تحميل البيانات
                var models = new List<AIModel>
                {
                    new AIModel
                    {
                        Id = 1,
                        Name = "نموذج تنبؤ المبيعات",
                        Type = AIModelType.Prediction,
                        Status = AIModelStatus.Deployed,
                        Accuracy = 95.8m,
                        Algorithm = "Random Forest",
                        Framework = "Scikit-learn"
                    },
                    new AIModel
                    {
                        Id = 2,
                        Name = "نموذج تصنيف العملاء",
                        Type = AIModelType.Classification,
                        Status = AIModelStatus.Deployed,
                        Accuracy = 92.3m,
                        Algorithm = "XGBoost",
                        Framework = "XGBoost"
                    },
                    new AIModel
                    {
                        Id = 3,
                        Name = "نموذج توصيات المنتجات",
                        Type = AIModelType.Recommendation,
                        Status = AIModelStatus.Training,
                        Accuracy = 0,
                        Algorithm = "Collaborative Filtering",
                        Framework = "TensorFlow"
                    },
                    new AIModel
                    {
                        Id = 4,
                        Name = "نموذج كشف الاحتيال",
                        Type = AIModelType.Anomaly,
                        Status = AIModelStatus.Deployed,
                        Accuracy = 97.1m,
                        Algorithm = "Isolation Forest",
                        Framework = "Scikit-learn"
                    }
                };

                ActiveModels = new ObservableCollection<AIModel>(models);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل النماذج النشطة");
            }
        }

        private async Task LoadSmartInsightsAsync()
        {
            try
            {
                await Task.Delay(100); // محاكاة عملية تحميل البيانات
                var insights = new List<SmartInsight>
                {
                    new SmartInsight
                    {
                        Title = "زيادة في المبيعات",
                        Description = "تشير التحليلات إلى زيادة متوقعة في المبيعات بنسبة 15% خلال الشهر القادم",
                        CreatedAt = DateTime.Now.AddHours(-2)
                    },
                    new SmartInsight
                    {
                        Title = "تحسن في رضا العملاء",
                        Description = "معدل رضا العملاء ارتفع إلى 94% بناءً على تحليل المشاعر للتعليقات",
                        CreatedAt = DateTime.Now.AddHours(-4)
                    },
                    new SmartInsight
                    {
                        Title = "فرصة تحسين المخزون",
                        Description = "يمكن تقليل تكاليف المخزون بنسبة 12% من خلال تحسين استراتيجية الطلب",
                        CreatedAt = DateTime.Now.AddHours(-6)
                    }
                };

                SmartInsights = new ObservableCollection<SmartInsight>(insights);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل الرؤى الذكية");
            }
        }

        private async Task LoadSmartAlertsAsync()
        {
            try
            {
                await Task.Delay(100); // محاكاة عملية تحميل البيانات
                var alerts = new List<SmartAlert>
                {
                    new SmartAlert
                    {
                        Title = "تحذير مخزون منخفض",
                        Message = "مستوى المخزون لمنتج XYZ أقل من الحد الأدنى المطلوب",
                        Severity = AlertSeverity.Warning,
                        CreatedAt = DateTime.Now.AddMinutes(-30)
                    },
                    new SmartAlert
                    {
                        Title = "نشاط مشبوه",
                        Message = "تم اكتشاف نشاط مشبوه في المعاملة #12345",
                        Severity = AlertSeverity.Critical,
                        CreatedAt = DateTime.Now.AddMinutes(-45)
                    },
                    new SmartAlert
                    {
                        Title = "فرصة بيع جديدة",
                        Message = "العميل ABC مرشح قوي لشراء منتج جديد بناءً على سلوكه",
                        Severity = AlertSeverity.Info,
                        CreatedAt = DateTime.Now.AddHours(-1)
                    }
                };

                SmartAlerts = new ObservableCollection<SmartAlert>(alerts);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل التنبيهات الذكية");
            }
        }

        private async Task LoadAnalyticsChartsAsync()
        {
            try
            {
                // إنشاء بيانات وهمية للرسم البياني
                await Task.Delay(100); // محاكاة عملية تحميل البيانات
                var values = new ChartValues<double> { 10, 15, 8, 20, 25, 18, 30, 22, 35, 28, 40, 33 };

                AnalyticsSeries = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "دقة النماذج",
                        Values = values,
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8
                    }
                };

                AnalyticsAxisX = new Axis
                {
                    Title = "الشهر",
                    Labels = new[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                                   "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر" }
                };

                AnalyticsAxisY = new Axis
                {
                    Title = "الدقة (%)",
                    LabelFormatter = value => value.ToString("N0")
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل بيانات الرسوم البيانية");
            }
        }

        private async Task CreateModelAsync()
        {
            try
            {
                // فتح نافذة إنشاء نموذج جديد
                var modelDialog = new ModelCreationDialog();
                var result = modelDialog.ShowDialog();

                if (result == true && modelDialog.ModelConfiguration != null)
                {
                    await _aiService.TrainModelAsync(modelDialog.ModelConfiguration);
                    await RefreshDashboardAsync();

                    await _notificationService.ShowSuccessAsync(
                        "تم بدء تدريب النموذج",
                        $"تم بدء تدريب النموذج {modelDialog.ModelConfiguration.Name} بنجاح"
                    );
                }
            }
            catch (Exception ex)
            {
                await _notificationService.ShowErrorAsync("خطأ في إنشاء النموذج", ex.Message);
                LoggingService.LogError(ex, "خطأ في إنشاء نموذج جديد");
            }
        }

        private async Task RunPredictionAsync()
        {
            try
            {
                // فتح نافذة تشغيل التنبؤ
                var predictionDialog = new PredictionDialog();
                var result = predictionDialog.ShowDialog();

                if (result == true)
                {
                    // تشغيل التنبؤ المحدد
                    await _notificationService.ShowInfoAsync(
                        "جاري تشغيل التنبؤ",
                        "سيتم إشعارك عند اكتمال التنبؤ"
                    );
                }
            }
            catch (Exception ex)
            {
                await _notificationService.ShowErrorAsync("خطأ في تشغيل التنبؤ", ex.Message);
                LoggingService.LogError(ex, "خطأ في تشغيل التنبؤ");
            }
        }

        private async Task AnalyzeDataAsync()
        {
            try
            {
                // فتح نافذة تحليل البيانات
                var analysisDialog = new DataAnalysisDialog();
                var result = analysisDialog.ShowDialog();

                if (result == true)
                {
                    await _notificationService.ShowInfoAsync(
                        "جاري تحليل البيانات",
                        "سيتم إشعارك عند اكتمال التحليل"
                    );
                }
            }
            catch (Exception ex)
            {
                await _notificationService.ShowErrorAsync("خطأ في تحليل البيانات", ex.Message);
                LoggingService.LogError(ex, "خطأ في تحليل البيانات");
            }
        }

        private void NavigateToModels()
        {
            NavigationService.NavigateTo("AIModelManagement");
        }

        private void NavigateToPredictions()
        {
            NavigationService.NavigateTo("Predictions");
        }

        private void NavigateToAnalytics()
        {
            NavigationService.NavigateTo("AdvancedAnalytics");
        }

        private void NavigateToInsights()
        {
            NavigationService.NavigateTo("SmartInsights");
        }

        private void NavigateToAlerts()
        {
            NavigationService.NavigateTo("SmartAlerts");
        }

        private void ViewModelDetails(AIModel model)
        {
            if (model != null)
            {
                var modelDetailsDialog = new ModelDetailsDialog(model);
                modelDetailsDialog.ShowDialog();
            }
        }

        private async Task RunModel(AIModel model)
        {
            if (model != null && model.IsDeployed)
            {
                try
                {
                    await _notificationService.ShowInfoAsync(
                        "تشغيل النموذج",
                        $"جاري تشغيل النموذج {model.Name}"
                    );

                    // تشغيل النموذج
                    // Implementation would go here
                }
                catch (Exception ex)
                {
                    await _notificationService.ShowErrorAsync("خطأ في تشغيل النموذج", ex.Message);
                    LoggingService.LogError(ex, $"خطأ في تشغيل النموذج {model.Id}");
                }
            }
        }

        private async Task RefreshDashboardAsync()
        {
            await LoadDashboardDataAsync();
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Helper Classes

    // هذه الفئات ستحتاج إلى تطوير منفصل
    public class SmartInsight
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");
    }

    public class SmartAlert
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public AlertSeverity Severity { get; set; }
        public DateTime CreatedAt { get; set; }

        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");

        public string SeverityIcon
        {
            get
            {
                return Severity switch
                {
                    AlertSeverity.Info => "Information",
                    AlertSeverity.Warning => "Warning",
                    AlertSeverity.Error => "Error",
                    AlertSeverity.Critical => "AlertCircle",
                    _ => "Information"
                };
            }
        }

        public string SeverityColor
        {
            get
            {
                return Severity switch
                {
                    AlertSeverity.Info => "Blue",
                    AlertSeverity.Warning => "Orange",
                    AlertSeverity.Error => "Red",
                    AlertSeverity.Critical => "Purple",
                    _ => "Gray"
                };
            }
        }

        public string SeverityBackground
        {
            get
            {
                return Severity switch
                {
                    AlertSeverity.Info => "#E3F2FD",
                    AlertSeverity.Warning => "#FFF3E0",
                    AlertSeverity.Error => "#FFEBEE",
                    AlertSeverity.Critical => "#F3E5F5",
                    _ => "#FAFAFA"
                };
            }
        }
    }

    // Placeholder dialog classes
    public class ModelCreationDialog
    {
        public AIModelConfiguration? ModelConfiguration { get; set; }
        public bool? ShowDialog() => true;
    }

    public class PredictionDialog
    {
        public bool? ShowDialog() => true;
    }

    public class DataAnalysisDialog
    {
        public bool? ShowDialog() => true;
    }

    public class ModelDetailsDialog
    {
        public ModelDetailsDialog(AIModel model) { }
        public bool? ShowDialog() => true;
    }

    #endregion
}
