<Window x:Class="SalesManagementSystem.Views.InventoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المخزون"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <Border Grid.Row="0" Background="Orange">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="📦" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="إدارة المخزون والجرد" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <TabControl Grid.Row="1" Margin="10">
            <TabItem Header="حركة المخزون">
                <Grid Margin="20">
                    <StackPanel>
                        <GroupBox Header="إضافة حركة مخزون" Margin="0,0,0,20">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <ComboBox Grid.Row="0" Grid.Column="0" Margin="0,0,10,10">
                                    <ComboBoxItem Content="لابتوب ديل"/>
                                    <ComboBoxItem Content="ماوس لوجيتك"/>
                                </ComboBox>
                                <ComboBox Grid.Row="0" Grid.Column="1" Margin="5,0,5,10">
                                    <ComboBoxItem Content="إضافة للمخزون"/>
                                    <ComboBoxItem Content="خصم من المخزون"/>
                                    <ComboBoxItem Content="تعديل الكمية"/>
                                </ComboBox>
                                <TextBox Grid.Row="0" Grid.Column="2" Margin="10,0,0,10" Text="الكمية"/>

                                <TextBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,10,10" Text="ملاحظات الحركة"/>
                                <Button Grid.Row="1" Grid.Column="2" Content="إضافة الحركة" Margin="10,0,0,10" Background="Orange" Foreground="White"/>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="تقرير المخزون">
                            <StackPanel Margin="10">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="LightBlue" Padding="10" Margin="5">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="إجمالي المنتجات" FontWeight="Bold"/>
                                            <TextBlock Text="1250" FontSize="20" Foreground="Blue"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="1" Background="LightGreen" Padding="10" Margin="5">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="قيمة المخزون" FontWeight="Bold"/>
                                            <TextBlock Text="325750.50 دج" FontSize="20" Foreground="Green"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="2" Background="LightYellow" Padding="10" Margin="5">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="تحت الحد الأدنى" FontWeight="Bold"/>
                                            <TextBlock Text="25" FontSize="20" Foreground="Orange"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="3" Background="LightPink" Padding="10" Margin="5">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="نفد المخزون" FontWeight="Bold"/>
                                            <TextBlock Text="8" FontSize="20" Foreground="Red"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </Grid>
            </TabItem>

            <TabItem Header="الجرد">
                <Grid Margin="20">
                    <StackPanel>
                        <GroupBox Header="بدء جرد جديد">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="نوع الجرد:" Margin="0,0,0,10"/>
                                <ComboBox Grid.Row="0" Grid.Column="1" Margin="10,0,0,10">
                                    <ComboBoxItem Content="جرد شامل"/>
                                    <ComboBoxItem Content="جرد جزئي"/>
                                    <ComboBoxItem Content="جرد دوري"/>
                                </ComboBox>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ الجرد:" Margin="0,0,0,10"/>
                                <DatePicker Grid.Row="1" Grid.Column="1" Margin="10,0,0,10"/>

                                <Button Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Content="🔍 بدء الجرد"
                                       Background="Orange" Foreground="White" Height="35" Margin="0,10,0,0"/>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>

        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="📊 تقرير المخزون" Width="130" Height="35" Margin="10"
                       Background="Orange" Foreground="White" FontWeight="Bold"/>
                <Button Content="📤 تصدير" Width="100" Height="35" Margin="10"
                       Background="Blue" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إغلاق" Width="100" Height="35" Margin="10"
                       Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
