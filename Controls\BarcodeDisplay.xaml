<UserControl x:Class="SalesManagementSystem.Controls.BarcodeDisplay"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
    </UserControl.Resources>

    <Border Background="{DynamicResource MaterialDesignPaper}"
            CornerRadius="8"
            Padding="16">
        <StackPanel>
            <!-- Header -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                <materialDesign:PackIcon Kind="Barcode"
                                       Width="24" Height="24"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                <TextBlock Text="عرض الباركود"
                          Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                          VerticalAlignment="Center"
                          Margin="8,0,0,0"/>
            </StackPanel>

            <!-- Barcode Input -->
            <GroupBox Header="إدخال النص"
                     Style="{DynamicResource MaterialDesignCardGroupBox}"
                     Margin="0,0,0,16">
                <StackPanel Margin="16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="8"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0"
                                x:Name="BarcodeTextBox"
                                Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                                materialDesign:HintAssist.Hint="أدخل النص لتوليد الباركود"
                                Text="{Binding BarcodeText, UpdateSourceTrigger=PropertyChanged}"/>

                        <Button Grid.Column="2"
                               Command="{Binding GenerateBarcodeCommand}"
                               Style="{DynamicResource MaterialDesignRaisedButton}"
                               Width="100">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Creation"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="توليد" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!-- Barcode Settings -->
            <GroupBox Header="إعدادات الباركود"
                     Style="{DynamicResource MaterialDesignCardGroupBox}"
                     Margin="0,0,0,16">
                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="16"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="العرض"
                                  Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                  Margin="0,0,0,4"/>
                        <Slider x:Name="WidthSlider"
                               Minimum="200" Maximum="600"
                               Value="{Binding BarcodeWidth}"
                               TickFrequency="50"
                               IsSnapToTickEnabled="True"/>
                        <TextBlock Text="{Binding ElementName=WidthSlider, Path=Value, StringFormat='{}{0:F0} px'}"
                                  HorizontalAlignment="Center"
                                  Style="{DynamicResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الارتفاع"
                                  Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                  Margin="0,0,0,4"/>
                        <Slider x:Name="HeightSlider"
                               Minimum="50" Maximum="200"
                               Value="{Binding BarcodeHeight}"
                               TickFrequency="25"
                               IsSnapToTickEnabled="True"/>
                        <TextBlock Text="{Binding ElementName=HeightSlider, Path=Value, StringFormat='{}{0:F0} px'}"
                                  HorizontalAlignment="Center"
                                  Style="{DynamicResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- Barcode Display -->
            <GroupBox Header="الباركود المولد"
                     Style="{DynamicResource MaterialDesignCardGroupBox}"
                     Visibility="{Binding HasBarcode, Converter={StaticResource BoolToVisConverter}}">
                <StackPanel Margin="16">
                    <!-- Barcode Image -->
                    <Border Background="White"
                           BorderBrush="{DynamicResource MaterialDesignDivider}"
                           BorderThickness="1"
                           CornerRadius="4"
                           Padding="16"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,16">
                        <Image x:Name="BarcodeImageControl"
                              Source="{Binding BarcodeImage}"
                              Stretch="None"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"/>
                    </Border>

                    <!-- Barcode Text -->
                    <TextBlock Text="{Binding BarcodeText}"
                              HorizontalAlignment="Center"
                              Style="{DynamicResource MaterialDesignBody2TextBlock}"
                              Margin="0,0,0,16"/>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal"
                               HorizontalAlignment="Center">

                        <Button Command="{Binding SaveBarcodeCommand}"
                               Style="{DynamicResource MaterialDesignRaisedButton}"
                               Margin="0,0,8,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="حفظ كصورة" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Command="{Binding PrintBarcodeCommand}"
                               Style="{DynamicResource MaterialDesignOutlinedButton}"
                               Margin="0,0,8,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Printer"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="طباعة" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Command="{Binding CopyBarcodeCommand}"
                               Style="{DynamicResource MaterialDesignOutlinedButton}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentCopy"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="نسخ" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </GroupBox>

            <!-- Loading Indicator -->
            <StackPanel Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       Margin="0,16,0,0"
                       Visibility="{Binding IsGenerating, Converter={StaticResource BoolToVisConverter}}">
                <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="24" Height="24"
                           Margin="0,0,8,0"/>
                <TextBlock Text="جاري توليد الباركود..."
                          VerticalAlignment="Center"/>
            </StackPanel>

            <!-- Error Message -->
            <Border Background="{DynamicResource ValidationErrorBrush}"
                   CornerRadius="4"
                   Padding="12"
                   Opacity="0.1"
                   Margin="0,16,0,0"
                   Visibility="{Binding HasError, Converter={StaticResource BoolToVisConverter}}">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="AlertCircle"
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource ValidationErrorBrush}"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding ErrorMessage}"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource ValidationErrorBrush}"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </Border>
</UserControl>
