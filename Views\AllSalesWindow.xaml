<Window x:Class="SalesManagementSystem.Views.AllSalesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="جميع عمليات المبيعات" Height="700" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <!-- Style for center-aligned text in DataGrid -->
        <Style x:Key="CenterAlignedTextBlock" TargetType="TextBlock">
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>

        <!-- Style for right-aligned text in DataGrid -->
        <Style x:Key="RightAlignedTextBlock" TargetType="TextBlock">
            <Setter Property="TextAlignment" Value="Right"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>

        <!-- Style for left-aligned text in DataGrid -->
        <Style x:Key="LeftAlignedTextBlock" TargetType="TextBlock">
            <Setter Property="TextAlignment" Value="Left"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Receipt" Width="32" Height="32"
                                           Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="جميع عمليات المبيعات" FontSize="24" FontWeight="Bold"
                              Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock x:Name="TotalSalesLabel" Text="إجمالي المبيعات: 0.00 دج" FontSize="18" FontWeight="Bold"
                              Foreground="Yellow" VerticalAlignment="Center" Margin="0,0,20,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Background="LightBlue" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="🔍 البحث والتصفية:" FontWeight="Bold" FontSize="14"
                          VerticalAlignment="Center" Margin="0,0,10,0"/>

                <TextBox Grid.Column="1" x:Name="SearchTextBox" Height="35" Margin="0,0,10,0"
                        ToolTip="ابحث برقم الفاتورة أو اسم العميل..."
                        TextChanged="SearchTextBox_TextChanged"/>

                <StackPanel Grid.Column="2" Margin="0,0,10,0">
                    <TextBlock Text="من تاريخ:" FontSize="12" Margin="0,0,0,2"/>
                    <DatePicker x:Name="StartDatePicker" Height="35" Width="120"
                               SelectedDateChanged="DateFilter_Changed"/>
                </StackPanel>

                <StackPanel Grid.Column="3" Margin="0,0,10,0">
                    <TextBlock Text="إلى تاريخ:" FontSize="12" Margin="0,0,0,2"/>
                    <DatePicker x:Name="EndDatePicker" Height="35" Width="120"
                               SelectedDateChanged="DateFilter_Changed"/>
                </StackPanel>

                <StackPanel Grid.Column="4" Margin="0,0,10,0">
                    <TextBlock Text="نوع الفاتورة:" FontSize="12" Margin="0,0,0,2"/>
                    <ComboBox x:Name="InvoiceTypeComboBox" Height="35" Width="120"
                             SelectionChanged="InvoiceTypeComboBox_SelectionChanged">
                        <ComboBoxItem Content="الكل" IsSelected="True"/>
                        <ComboBoxItem Content="بيع عادي"/>
                        <ComboBoxItem Content="بيع سريع"/>
                    </ComboBox>
                </StackPanel>

                <StackPanel Grid.Column="5" Margin="0,0,10,0">
                    <TextBlock Text="الحالة:" FontSize="12" Margin="0,0,0,2"/>
                    <ComboBox x:Name="StatusComboBox" Height="35" Width="100"
                             SelectionChanged="StatusComboBox_SelectionChanged">
                        <ComboBoxItem Content="الكل" IsSelected="True"/>
                        <ComboBoxItem Content="مدفوعة"/>
                        <ComboBoxItem Content="معلقة"/>
                        <ComboBoxItem Content="ملغية"/>
                    </ComboBox>
                </StackPanel>

                <Button Grid.Column="6" Width="100" Height="35" Click="Refresh_Click"
                       Background="#607D8B" Foreground="White" FontWeight="Bold"
                       Style="{StaticResource MaterialDesignRaisedButton}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- Sales DataGrid -->
        <DataGrid Grid.Row="2" x:Name="SalesDataGrid" Margin="10" AutoGenerateColumns="False"
                 CanUserAddRows="False" GridLinesVisibility="All" AlternatingRowBackground="LightGray"
                 SelectionMode="Single" MouseDoubleClick="SalesDataGrid_MouseDoubleClick">
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120" IsReadOnly="True"/>
                <DataGridTextColumn Header="التاريخ" Binding="{Binding FormattedInvoiceDate}" Width="100" IsReadOnly="True"/>
                <DataGridTextColumn Header="العميل" Binding="{Binding Customer}" Width="150" IsReadOnly="True"/>
                <DataGridTextColumn Header="نوع الفاتورة" Binding="{Binding InvoiceTypeDisplay}" Width="100" IsReadOnly="True"/>
                <DataGridTextColumn Header="عدد الأصناف" Binding="{Binding ItemsCount}" Width="80"
                                   ElementStyle="{StaticResource CenterAlignedTextBlock}" IsReadOnly="True"/>
                <DataGridTextColumn Header="إجمالي الكمية" Binding="{Binding TotalQuantity}" Width="80"
                                   ElementStyle="{StaticResource CenterAlignedTextBlock}" IsReadOnly="True"/>
                <DataGridTextColumn Header="المبلغ الفرعي" Binding="{Binding Subtotal, StringFormat='{}{0:F2}'}" Width="120"
                                   ElementStyle="{StaticResource CenterAlignedTextBlock}" IsReadOnly="True"/>
                <DataGridTextColumn Header="الخصم" Binding="{Binding DiscountAmount, StringFormat='{}{0:F2}'}" Width="80"
                                   ElementStyle="{StaticResource CenterAlignedTextBlock}" IsReadOnly="True"/>
                <DataGridTextColumn Header="الضريبة" Binding="{Binding TaxAmount, StringFormat='{}{0:F2}'}" Width="80"
                                   ElementStyle="{StaticResource CenterAlignedTextBlock}" IsReadOnly="True"/>
                <DataGridTextColumn Header="الإجمالي" Binding="{Binding TotalAmount, StringFormat='{}{0:F2}'}" Width="120"
                                   ElementStyle="{StaticResource CenterAlignedTextBlock}" IsReadOnly="True"/>
                <DataGridTemplateColumn Header="الحالة" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Border Background="{Binding StatusColor}" CornerRadius="10" Padding="5,2">
                                <TextBlock Text="{Binding StatusDisplay}" Foreground="White"
                                          FontWeight="Bold" HorizontalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="100" IsReadOnly="True"/>
                <DataGridTextColumn Header="المنشئ" Binding="{Binding CreatedBy}" Width="80" IsReadOnly="True"/>
                <DataGridTemplateColumn Header="العمليات" Width="220">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <!-- زر عرض التفاصيل -->
                                <Button Width="35" Height="30" Margin="2" ToolTip="عرض التفاصيل"
                                       Click="ViewDetails_Click" Background="#2196F3" Foreground="White"
                                       Style="{StaticResource MaterialDesignRaisedButton}">
                                    <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                </Button>

                                <!-- زر الطباعة -->
                                <Button Width="35" Height="30" Margin="2" ToolTip="طباعة"
                                       Click="Print_Click" Background="#4CAF50" Foreground="White"
                                       Style="{StaticResource MaterialDesignRaisedButton}">
                                    <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"/>
                                </Button>

                                <!-- زر التعديل -->
                                <Button Width="35" Height="30" Margin="2" ToolTip="تعديل"
                                       Click="Edit_Click" Background="#FF9800" Foreground="White"
                                       Style="{StaticResource MaterialDesignRaisedButton}">
                                    <materialDesign:PackIcon Kind="Pencil" Width="16" Height="16"/>
                                </Button>

                                <!-- زر الحذف -->
                                <Button Width="35" Height="30" Margin="2" ToolTip="حذف"
                                       Click="Delete_Click" Background="#F44336" Foreground="White"
                                       Style="{StaticResource MaterialDesignRaisedButton}">
                                    <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Summary -->
        <Border Grid.Row="3" Background="LightCyan" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <!-- زر فاتورة جديدة -->
                    <Button Width="140" Height="40" Margin="0,0,10,0" Click="NewInvoice_Click"
                           Background="#2196F3" Foreground="White" FontWeight="Bold"
                           Style="{StaticResource MaterialDesignRaisedButton}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,5,0"/>
                            <TextBlock Text="فاتورة جديدة" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- زر بيع سريع -->
                    <Button Width="140" Height="40" Margin="0,0,10,0" Click="QuickSale_Click"
                           Background="#4CAF50" Foreground="White" FontWeight="Bold"
                           Style="{StaticResource MaterialDesignRaisedButton}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Flash" Width="18" Height="18" Margin="0,0,5,0"/>
                            <TextBlock Text="بيع سريع" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- زر التقرير -->
                    <Button Width="140" Height="40" Margin="0,0,10,0" Click="Report_Click"
                           Background="#9C27B0" Foreground="White" FontWeight="Bold"
                           Style="{StaticResource MaterialDesignRaisedButton}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartLine" Width="18" Height="18" Margin="0,0,5,0"/>
                            <TextBlock Text="تقرير" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- زر التحديث -->
                    <Button Width="140" Height="40" Margin="0,0,10,0" Click="Refresh_Click"
                           Background="#607D8B" Foreground="White" FontWeight="Bold"
                           Style="{StaticResource MaterialDesignRaisedButton}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,5,0"/>
                            <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <StackPanel Grid.Column="1" Margin="20,0">
                    <TextBlock Text="عدد الفواتير:" FontWeight="Bold" FontSize="12"/>
                    <TextBlock x:Name="InvoicesCountLabel" Text="0" FontWeight="Bold" FontSize="16"
                              Foreground="DarkBlue" HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Margin="20,0">
                    <TextBlock Text="إجمالي الأصناف:" FontWeight="Bold" FontSize="12"/>
                    <TextBlock x:Name="TotalItemsLabel" Text="0" FontWeight="Bold" FontSize="16"
                              Foreground="DarkGreen" HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="3" Margin="20,0">
                    <TextBlock Text="إجمالي الكمية:" FontWeight="Bold" FontSize="12"/>
                    <TextBlock x:Name="TotalQuantityLabel" Text="0" FontWeight="Bold" FontSize="16"
                              Foreground="DarkOrange" HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="4" Margin="20,0">
                    <TextBlock Text="إجمالي المبيعات:" FontWeight="Bold" FontSize="12"/>
                    <TextBlock x:Name="TotalAmountLabel" Text="0.00 دج" FontWeight="Bold" FontSize="18"
                              Foreground="DarkRed" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
