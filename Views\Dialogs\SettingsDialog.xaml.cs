using System.Windows;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views.Dialogs
{
    public partial class SettingsDialog : Window
    {
        private readonly SettingsDialogViewModel _viewModel;

        public bool SettingsChanged { get; private set; }

        public SettingsDialog()
        {
            InitializeComponent();
            
            _viewModel = new SettingsDialogViewModel();
            DataContext = _viewModel;
            
            // Subscribe to events
            _viewModel.SettingsSaved += OnSettingsSaved;
            _viewModel.RequestClose += OnRequestClose;
        }

        #region Event Handlers

        private void OnSettingsSaved()
        {
            SettingsChanged = true;
            DialogResult = true;
        }

        private void OnRequestClose(bool result)
        {
            DialogResult = result;
            Close();
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // Unsubscribe from events
            _viewModel.SettingsSaved -= OnSettingsSaved;
            _viewModel.RequestClose -= OnRequestClose;
            
            base.OnClosed(e);
        }

        #endregion
    }
}
