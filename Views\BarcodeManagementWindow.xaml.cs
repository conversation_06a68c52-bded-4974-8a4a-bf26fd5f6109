using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using SalesManagementSystem.Controls;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// نافذة إدارة الباركود
    /// </summary>
    public partial class BarcodeManagementWindow : Window, INotifyPropertyChanged
    {
        private readonly ProductService _productService;
        private readonly BarcodeService _barcodeService;
        private readonly PrintService _printService;
        
        private ObservableCollection<Product> _products = new();
        private Product? _selectedProduct;
        private string _searchText = string.Empty;
        private bool _hasSelectedProduct;

        public BarcodeManagementWindow()
        {
            InitializeComponent();
            DataContext = this;
            
            var dbService = new DatabaseService();
            _productService = new ProductService(dbService);
            _barcodeService = new BarcodeService();
            _printService = new PrintService(new SettingsService(dbService), _barcodeService);
            
            InitializeCommands();
            _ = LoadProductsAsync();
        }

        #region Properties

        public ObservableCollection<Product> Products
        {
            get => _products;
            set
            {
                if (_products != value)
                {
                    _products = value;
                    OnPropertyChanged();
                }
            }
        }

        public Product? SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                if (_selectedProduct != value)
                {
                    _selectedProduct = value;
                    HasSelectedProduct = value != null;
                    OnPropertyChanged();
                }
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged();
                    _ = FilterProductsAsync();
                }
            }
        }

        public bool HasSelectedProduct
        {
            get => _hasSelectedProduct;
            set
            {
                if (_hasSelectedProduct != value)
                {
                    _hasSelectedProduct = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Commands

        public ICommand GenerateProductBarcodeCommand { get; private set; } = null!;
        public ICommand PrintProductBarcodeCommand { get; private set; } = null!;
        public ICommand GenerateAllBarcodesCommand { get; private set; } = null!;
        public ICommand PrintAllBarcodesCommand { get; private set; } = null!;
        public ICommand EditProductCommand { get; private set; } = null!;
        public ICommand RefreshCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            GenerateProductBarcodeCommand = new RelayCommand<Product>(async (product) => await GenerateProductBarcodeAsync(product));
            PrintProductBarcodeCommand = new RelayCommand<Product>(async (product) => await PrintProductBarcodeAsync(product));
            GenerateAllBarcodesCommand = new RelayCommand(async () => await GenerateAllBarcodesAsync());
            PrintAllBarcodesCommand = new RelayCommand(async () => await PrintAllBarcodesAsync());
            EditProductCommand = new RelayCommand(() => EditProduct(), () => SelectedProduct != null);
            RefreshCommand = new RelayCommand(async () => await LoadProductsAsync());
        }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل المنتجات
        /// </summary>
        private async Task LoadProductsAsync()
        {
            try
            {
                var products = await _productService.GetAllProductsAsync();
                Products = new ObservableCollection<Product>(products);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل المنتجات");
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصفية المنتجات
        /// </summary>
        private async Task FilterProductsAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    await LoadProductsAsync();
                }
                else
                {
                    var products = await _productService.SearchProductsAsync(SearchText);
                    Products = new ObservableCollection<Product>(products);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصفية المنتجات");
            }
        }

        /// <summary>
        /// توليد باركود للمنتج
        /// </summary>
        private async Task GenerateProductBarcodeAsync(Product? product)
        {
            if (product == null) return;

            try
            {
                // توليد باركود جديد إذا لم يكن موجوداً
                if (string.IsNullOrWhiteSpace(product.Barcode))
                {
                    product.Barcode = GenerateUniqueBarcode(product);
                    await _productService.UpdateProductAsync(product);
                    
                    MessageBox.Show("تم توليد الباركود وحفظه بنجاح", "نجح التوليد", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("المنتج يحتوي على باركود بالفعل", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في توليد باركود المنتج: {product.Name}");
                MessageBox.Show($"خطأ في توليد الباركود: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة باركود المنتج
        /// </summary>
        private async Task PrintProductBarcodeAsync(Product? product)
        {
            if (product == null || string.IsNullOrWhiteSpace(product.Barcode)) return;

            try
            {
                var success = await _printService.PrintProductBarcodeAsync(product);
                if (success)
                {
                    MessageBox.Show("تم طباعة الباركود بنجاح", "نجحت الطباعة", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في طباعة باركود المنتج: {product.Name}");
                MessageBox.Show($"خطأ في طباعة الباركود: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// توليد باركود لجميع المنتجات
        /// </summary>
        private async Task GenerateAllBarcodesAsync()
        {
            try
            {
                var result = MessageBox.Show("هل تريد توليد باركود لجميع المنتجات التي لا تحتوي على باركود؟", 
                    "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    int generatedCount = 0;
                    
                    foreach (var product in Products.Where(p => string.IsNullOrWhiteSpace(p.Barcode)))
                    {
                        product.Barcode = GenerateUniqueBarcode(product);
                        await _productService.UpdateProductAsync(product);
                        generatedCount++;
                    }
                    
                    MessageBox.Show($"تم توليد {generatedCount} باركود بنجاح", "اكتمل التوليد", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في توليد باركود جميع المنتجات");
                MessageBox.Show($"خطأ في توليد الباركودات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة جميع الباركودات
        /// </summary>
        private async Task PrintAllBarcodesAsync()
        {
            try
            {
                var productsWithBarcodes = Products.Where(p => !string.IsNullOrWhiteSpace(p.Barcode)).ToList();
                
                if (!productsWithBarcodes.Any())
                {
                    MessageBox.Show("لا توجد منتجات تحتوي على باركود للطباعة", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل تريد طباعة {productsWithBarcodes.Count} باركود؟", 
                    "تأكيد الطباعة", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    foreach (var product in productsWithBarcodes)
                    {
                        await _printService.PrintProductBarcodeAsync(product);
                    }
                    
                    MessageBox.Show("تم طباعة جميع الباركودات بنجاح", "اكتملت الطباعة", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في طباعة جميع الباركودات");
                MessageBox.Show($"خطأ في طباعة الباركودات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعديل المنتج
        /// </summary>
        private void EditProduct()
        {
            if (SelectedProduct == null) return;

            // يمكن فتح نافذة تعديل المنتج هنا
            MessageBox.Show("ميزة تعديل المنتج ستكون متاحة قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// توليد باركود فريد للمنتج
        /// </summary>
        private string GenerateUniqueBarcode(Product product)
        {
            // استخدام كود المنتج مع timestamp لضمان الفرادة
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            return $"{product.Code}{timestamp}".PadLeft(12, '0');
        }

        #endregion

        #region Event Handlers

        private void BarcodeScanner_ProductFound(object sender, ProductFoundEventArgs e)
        {
            SelectedProduct = e.Product;
        }

        private void BarcodeScanner_ProductSelected(object sender, ProductSelectedEventArgs e)
        {
            SelectedProduct = e.Product;
            
            if (e.Action == ProductSelectionAction.ViewDetails)
            {
                // عرض تفاصيل إضافية أو فتح نافذة التفاصيل
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
