<Window x:Class="SalesManagementSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - نظام إدارة المبيعات"
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        Background="Transparent"
        AllowsTransparency="True">

    <Border Background="#FF1976D2" CornerRadius="15">
        <Border.Effect>
            <DropShadowEffect Color="Black" BlurRadius="20" ShadowDepth="5" Opacity="0.3"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Border Grid.Row="0" Background="#FF0D47A1" CornerRadius="15,15,0,0" Height="50">
                <Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="🔐" FontSize="20" Foreground="White" Margin="0,0,10,0"/>
                        <TextBlock Text="تسجيل الدخول" FontSize="16" FontWeight="Bold" Foreground="White"/>
                    </StackPanel>

                    <!-- زر الإغلاق -->
                    <Button x:Name="CloseButton" Content="✕"
                           HorizontalAlignment="Right" VerticalAlignment="Center"
                           Width="30" Height="30" Margin="10,0"
                           Background="Transparent" BorderThickness="0"
                           Foreground="White" FontSize="16" FontWeight="Bold"
                           Cursor="Hand" Click="CloseButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#33FFFFFF"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </Grid>
            </Border>

            <!-- محتوى النافذة -->
            <Grid Grid.Row="1" Margin="40,30">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- شعار النظام -->
                <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,30">
                    <TextBlock Text="🏪" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Yellow" BlurRadius="10" ShadowDepth="0" Opacity="0.8"/>
                        </TextBlock.Effect>
                    </TextBlock>
                    <TextBlock Text="نظام إدارة المبيعات المتكامل"
                              FontSize="14" FontWeight="Bold" Foreground="White"
                              HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- اسم المستخدم -->
                <TextBlock Grid.Row="1" Text="اسم المستخدم:" FontSize="12" Foreground="White" Margin="0,0,0,5"/>
                <Border Grid.Row="2" Background="White" CornerRadius="5" Margin="0,0,0,15">
                    <TextBox x:Name="UsernameTextBox" Height="35" FontSize="14"
                            Padding="10,8" Background="Transparent" BorderThickness="0"
                            Text="admin">
                        <TextBox.Style>
                            <Style TargetType="TextBox">
                                <Style.Triggers>
                                    <Trigger Property="IsFocused" Value="True">
                                        <Setter Property="BorderThickness" Value="2"/>
                                        <Setter Property="BorderBrush" Value="#FF4CAF50"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBox.Style>
                    </TextBox>
                </Border>

                <!-- كلمة المرور -->
                <TextBlock Grid.Row="3" Text="كلمة المرور:" FontSize="12" Foreground="White" Margin="0,0,0,5"/>
                <Border Grid.Row="4" Background="White" CornerRadius="5" Margin="0,0,0,20">
                    <PasswordBox x:Name="PasswordBox" Height="35" FontSize="14"
                                Padding="10,8" Background="Transparent" BorderThickness="0"
                                Password="0000">
                        <PasswordBox.Style>
                            <Style TargetType="PasswordBox">
                                <Style.Triggers>
                                    <Trigger Property="IsFocused" Value="True">
                                        <Setter Property="BorderThickness" Value="2"/>
                                        <Setter Property="BorderBrush" Value="#FF4CAF50"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </PasswordBox.Style>
                    </PasswordBox>
                </Border>

                <!-- زر تسجيل الدخول -->
                <Border Grid.Row="5" Background="#FF4CAF50" CornerRadius="5">
                    <Button x:Name="LoginButton" Content="تسجيل الدخول"
                           Height="40" FontSize="14" FontWeight="Bold"
                           Background="Transparent" Foreground="White"
                           BorderThickness="0" Cursor="Hand" Click="LoginButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FF66BB6A"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#FF388E3C"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </Border>

                <!-- رسالة الخطأ -->
                <TextBlock x:Name="ErrorMessage" Grid.Row="6"
                          Text="" FontSize="12" Foreground="#FFFF5252"
                          HorizontalAlignment="Center" VerticalAlignment="Top"
                          Margin="0,15,0,0" Visibility="Collapsed"/>
            </Grid>

            <!-- شريط المعلومات السفلي -->
            <Border Grid.Row="2" Background="#FF0D47A1" CornerRadius="0,0,15,15" Height="40">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="المستخدم الافتراضي: admin | كلمة المرور: 0000"
                              FontSize="10" Foreground="#FFE3F2FD"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
