<UserControl x:Class="SalesManagementSystem.Views.AdvancedAnalyticsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ChartBox" Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="التحليلات المتقدمة والذكاء الاصطناعي"
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding RunAnalysisCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Play" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تشغيل تحليل"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding PredictTrendsCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Crystal" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تنبؤ الاتجاهات"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding DetectAnomaliesCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AlertCircle" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="كشف الشذوذ"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding ExportAnalysisCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExport" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط التحكم والفلاتر -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- نوع التحليل -->
                <ComboBox Grid.Column="0"
                          SelectedItem="{Binding SelectedAnalysisType}"
                          ItemsSource="{Binding AnalysisTypes}"
                          materialDesign:HintAssist.Hint="نوع التحليل"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="0,0,8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="{Binding Icon}" Width="16" Height="16" Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding Display}"/>
                            </StackPanel>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- مصدر البيانات -->
                <ComboBox Grid.Column="1"
                          SelectedItem="{Binding SelectedDataSource}"
                          ItemsSource="{Binding DataSources}"
                          materialDesign:HintAssist.Hint="مصدر البيانات"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- تاريخ البداية -->
                <DatePicker Grid.Column="2"
                           SelectedDate="{Binding FromDate}"
                           materialDesign:HintAssist.Hint="من تاريخ"
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Margin="8,0"/>

                <!-- تاريخ النهاية -->
                <DatePicker Grid.Column="3"
                           SelectedDate="{Binding ToDate}"
                           materialDesign:HintAssist.Hint="إلى تاريخ"
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Margin="8,0"/>

                <!-- زر التطبيق -->
                <Button Grid.Column="4"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding ApplyFiltersCommand}"
                        Margin="8,0,0,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FilterVariant" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="تطبيق"/>
                    </StackPanel>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- محتوى التحليلات -->
        <ScrollViewer Grid.Row="2" Margin="16,8,16,16" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- مؤشرات الأداء الرئيسية -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Speedometer" Width="24" Height="24"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="مؤشرات الأداء الذكية"
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- المؤشرات -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- تنبؤ المبيعات -->
                            <Border Grid.Column="0"
                                  Background="#E3F2FD"
                                  CornerRadius="8"
                                  Padding="16"
                                  Margin="0,0,8,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Crystal" Width="32" Height="32"
                                                           Foreground="#1976D2" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding PredictedSales, StringFormat='{}{0:C}'}"
                                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                             HorizontalAlignment="Center"
                                             Foreground="#1976D2"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="تنبؤ المبيعات"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"/>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,4,0,0">
                                        <materialDesign:PackIcon Kind="{Binding SalesTrendIcon}" Width="12" Height="12"
                                                               Foreground="{Binding SalesTrendColor}"/>
                                        <TextBlock Text="{Binding SalesTrendPercentage}"
                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                 Foreground="{Binding SalesTrendColor}"
                                                 Margin="4,0,0,0"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- احتمالية الاحتفاظ -->
                            <Border Grid.Column="1"
                                  Background="#E8F5E8"
                                  CornerRadius="8"
                                  Padding="16"
                                  Margin="8,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="AccountHeart" Width="32" Height="32"
                                                           Foreground="#388E3C" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding CustomerRetentionRate, StringFormat='{}{0:F1}%'}"
                                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                             HorizontalAlignment="Center"
                                             Foreground="#388E3C"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="معدل الاحتفاظ"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"/>
                                    <ProgressBar Value="{Binding CustomerRetentionRate}"
                                               Maximum="100"
                                               Height="4"
                                               Margin="0,4,0,0"
                                               Foreground="#388E3C"/>
                                </StackPanel>
                            </Border>

                            <!-- مخاطر الاحتيال -->
                            <Border Grid.Column="2"
                                  Background="#FFEBEE"
                                  CornerRadius="8"
                                  Padding="16"
                                  Margin="8,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Shield" Width="32" Height="32"
                                                           Foreground="#D32F2F" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding FraudRiskScore, StringFormat='{}{0:F1}%'}"
                                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                             HorizontalAlignment="Center"
                                             Foreground="#D32F2F"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="مخاطر الاحتيال"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"/>
                                    <materialDesign:Chip Content="{Binding FraudRiskLevel}"
                                                       FontSize="10"
                                                       Background="{Binding FraudRiskColor}"
                                                       Margin="0,4,0,0"/>
                                </StackPanel>
                            </Border>

                            <!-- تحسين المخزون -->
                            <Border Grid.Column="3"
                                  Background="#FFF3E0"
                                  CornerRadius="8"
                                  Padding="16"
                                  Margin="8,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Warehouse" Width="32" Height="32"
                                                           Foreground="#F57C00" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding InventoryOptimization, StringFormat='{}{0:F1}%'}"
                                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                             HorizontalAlignment="Center"
                                             Foreground="#F57C00"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="تحسين المخزون"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding ReorderRecommendations}"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"
                                             Margin="0,4,0,0"/>
                                </StackPanel>
                            </Border>

                            <!-- دقة التنبؤات -->
                            <Border Grid.Column="4"
                                  Background="#F3E5F5"
                                  CornerRadius="8"
                                  Padding="16"
                                  Margin="8,0,0,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Target" Width="32" Height="32"
                                                           Foreground="#7B1FA2" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding PredictionAccuracy, StringFormat='{}{0:F1}%'}"
                                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                             HorizontalAlignment="Center"
                                             Foreground="#7B1FA2"
                                             FontWeight="Bold"/>
                                    <TextBlock Text="دقة التنبؤات"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             HorizontalAlignment="Center"/>
                                    <materialDesign:Chip Content="{Binding AccuracyLevel}"
                                                       FontSize="10"
                                                       Background="{Binding AccuracyColor}"
                                                       Margin="0,4,0,0"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Grid>
                </materialDesign:Card>

                <!-- الرسوم البيانية التفاعلية -->
                <Grid Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- تحليل الاتجاهات -->
                    <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" Padding="16">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="300"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="TrendingUp" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="تحليل الاتجاهات والتنبؤات"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding TrendAnalysisSeries}"
                                              AxisX="{Binding TrendAxisX}"
                                              AxisY="{Binding TrendAxisY}"
                                              LegendLocation="Bottom"
                                              Hoverable="True">
                                <lvc:CartesianChart.DataTooltip>
                                    <lvc:DefaultTooltip Background="Black" Foreground="White"/>
                                </lvc:CartesianChart.DataTooltip>
                            </lvc:CartesianChart>
                        </Grid>
                    </materialDesign:Card>

                    <!-- كشف الشذوذ -->
                    <materialDesign:Card Grid.Column="1" Margin="8,0,0,0" Padding="16">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="300"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="AlertCircle" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="كشف الشذوذ والانحرافات"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <lvc:CartesianChart Grid.Row="1"
                                              Series="{Binding AnomalyDetectionSeries}"
                                              AxisX="{Binding AnomalyAxisX}"
                                              AxisY="{Binding AnomalyAxisY}"
                                              LegendLocation="Bottom"/>
                        </Grid>
                    </materialDesign:Card>
                </Grid>

                <!-- التوصيات الذكية والرؤى -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- التوصيات الذكية -->
                    <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" Padding="16">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="Lightbulb" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="التوصيات الذكية والرؤى"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" MaxHeight="400">
                                <ItemsControl ItemsSource="{Binding SmartRecommendations}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <materialDesign:Card Margin="0,0,0,8" Padding="12">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- أيقونة التوصية -->
                                                    <materialDesign:PackIcon Grid.Column="0"
                                                                           Kind="{Binding Icon}"
                                                                           Width="24" Height="24"
                                                                           Foreground="{Binding PriorityColor}"
                                                                           VerticalAlignment="Top"
                                                                           Margin="0,0,12,0"/>

                                                    <!-- محتوى التوصية -->
                                                    <StackPanel Grid.Column="1">
                                                        <TextBlock Text="{Binding Title}"
                                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                                 FontWeight="Bold"/>
                                                        <TextBlock Text="{Binding Description}"
                                                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                                 TextWrapping="Wrap"
                                                                 Margin="0,4,0,0"/>
                                                        <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                                                            <materialDesign:Chip Content="{Binding Category}"
                                                                               FontSize="10"
                                                                               Margin="0,0,8,0"/>
                                                            <materialDesign:Chip Content="{Binding ImpactLevel}"
                                                                               FontSize="10"
                                                                               Background="{Binding ImpactColor}"/>
                                                        </StackPanel>
                                                    </StackPanel>

                                                    <!-- درجة الثقة -->
                                                    <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                                                        <TextBlock Text="درجة الثقة"
                                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                                 HorizontalAlignment="Center"/>
                                                        <TextBlock Text="{Binding ConfidenceScore, StringFormat='{}{0:F1}%'}"
                                                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                                 HorizontalAlignment="Center"
                                                                 Foreground="{Binding ConfidenceColor}"
                                                                 FontWeight="Bold"/>
                                                        <ProgressBar Value="{Binding ConfidenceScore}"
                                                                   Maximum="100"
                                                                   Width="60"
                                                                   Height="4"
                                                                   Margin="0,4,0,0"/>
                                                    </StackPanel>
                                                </Grid>
                                            </materialDesign:Card>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </Grid>
                    </materialDesign:Card>

                    <!-- التنبيهات والإشعارات -->
                    <materialDesign:Card Grid.Column="1" Margin="8,0,0,0" Padding="16">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="Bell" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="التنبيهات الذكية"
                                         Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" MaxHeight="400">
                                <ItemsControl ItemsSource="{Binding SmartAlerts}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Margin="0,0,0,8"
                                                  Padding="8"
                                                  CornerRadius="4"
                                                  Background="{Binding SeverityBackground}"
                                                  BorderThickness="1"
                                                  BorderBrush="{Binding SeverityColor}">
                                                <StackPanel>
                                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                        <materialDesign:PackIcon Kind="{Binding SeverityIcon}"
                                                                               Width="16" Height="16"
                                                                               Foreground="{Binding SeverityColor}"
                                                                               VerticalAlignment="Center"/>
                                                        <TextBlock Text="{Binding Title}"
                                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                                 FontWeight="Bold"
                                                                 Margin="8,0,0,0"/>
                                                    </StackPanel>
                                                    <TextBlock Text="{Binding Message}"
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             TextWrapping="Wrap"/>
                                                    <TextBlock Text="{Binding FormattedCreatedAt}"
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             Opacity="0.5"
                                                             Margin="0,4,0,0"/>
                                                </StackPanel>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </Grid>
                    </materialDesign:Card>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Grid Grid.RowSpan="3"
              Background="White"
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري تحليل البيانات..."
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
