<Window x:Class="SalesManagementSystem.Views.ArabicNumbersDemo"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="عرض توضيحي للأرقام الإنجليزية والدينار الجزائري"
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="DarkGreen">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="🇩🇿" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="عرض توضيحي للأرقام الإنجليزية والدينار الجزائري" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel>
                <GroupBox Header="مقارنة الأرقام" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="النظام السابق (ريال سعودي):" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="النظام الجديد (دينار جزائري):" FontWeight="Bold" Margin="0,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="الأرقام: ٠١٢٣٤٥٦٧٨٩" FontSize="20" Margin="0,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="الأرقام: 0123456789" FontSize="20" Margin="0,5"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="السعر: ١٢٥٠.٧٥ ريال" FontSize="16" Margin="0,5"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="1250.75 دج" FontSize="16" Margin="0,5"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="الخصم: ١٥%" FontSize="16" Margin="0,5"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="15%" FontSize="16" Margin="0,5"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="التاريخ: ٢٠٢٤/٠١/١٥" FontSize="16" Margin="0,5"/>
                        <TextBlock Grid.Row="4" Grid.Column="1" Text="2024/01/20" FontSize="16" Margin="0,5"/>
                    </Grid>
                </GroupBox>

                <GroupBox Header="أمثلة من النظام" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock Text="أمثلة على استخدام الدينار الجزائري والأرقام الإنجليزية في النظام:" FontWeight="Bold" Margin="0,0,0,10"/>

                        <Border Background="LightBlue" Padding="10" Margin="0,5">
                            <StackPanel>
                                <TextBlock Text="فاتورة مبيعات:" FontWeight="Bold"/>
                                <TextBlock Text="رقم الفاتورة: 12345"/>
                                <TextBlock Text="التاريخ: 2024/01/15"/>
                                <TextBlock Text="المبلغ: 2500.50 دج"/>
                                <TextBlock Text="الخصم: 10%"/>
                            </StackPanel>
                        </Border>

                        <Border Background="LightGreen" Padding="10" Margin="0,5">
                            <StackPanel>
                                <TextBlock Text="بيانات المنتج:" FontWeight="Bold"/>
                                <TextBlock Text="الباركود: 987654321"/>
                                <TextBlock Text="الكمية المتاحة: 150"/>
                                <TextBlock Text="سعر البيع: 75.25 دج"/>
                                <TextBlock Text="الحد الأدنى: 20"/>
                            </StackPanel>
                        </Border>

                        <Border Background="LightYellow" Padding="10" Margin="0,5">
                            <StackPanel>
                                <TextBlock Text="بيانات العميل:" FontWeight="Bold"/>
                                <TextBlock Text="رقم الهاتف: 0501234567"/>
                                <TextBlock Text="الرصيد المستحق: 3250.00 دج"/>
                                <TextBlock Text="عدد الفواتير: 25"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="إحصائيات النظام">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي المبيعات:" FontWeight="Bold"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="125750.50 دج" Foreground="Green" FontWeight="Bold"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="عدد الفواتير:" FontWeight="Bold"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="342" Foreground="Blue" FontWeight="Bold"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="عدد المنتجات:" FontWeight="Bold"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="1250" Foreground="Orange" FontWeight="Bold"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="معدل النمو:" FontWeight="Bold"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="12.5%" Foreground="Purple" FontWeight="Bold"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="🇩🇿 النظام الآن يستخدم الدينار الجزائري والأرقام الإنجليزية!" FontSize="14" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
                <Button Content="❌ إغلاق" Width="100" Height="35"
                       Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
