<Window x:Class="SalesManagementSystem.Views.SalesListWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="قائمة المبيعات"
        Height="700" Width="1100"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Green">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="🧾" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="قائمة فواتير المبيعات" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Background="LightGray" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBox Width="150" Margin="0,0,10,0" Text="البحث في الفواتير..."/>
                <Button Content="🔍 بحث" Width="80" Margin="0,0,10,0" Background="Blue" Foreground="White"/>

                <TextBlock Text="من تاريخ:" VerticalAlignment="Center" Margin="20,0,5,0"/>
                <DatePicker Width="120" Margin="0,0,10,0"/>

                <TextBlock Text="إلى تاريخ:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <DatePicker Width="120" Margin="0,0,10,0"/>

                <TextBlock Text="العميل:" VerticalAlignment="Center" Margin="20,0,5,0"/>
                <ComboBox Width="120" Margin="0,0,10,0">
                    <ComboBoxItem Content="جميع العملاء"/>
                    <ComboBoxItem Content="أحمد محمد"/>
                    <ComboBoxItem Content="فاطمة علي"/>
                </ComboBox>

                <Button Content="🔄 تحديث" Width="80" Margin="10,0,0,0" Background="Green" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Sales DataGrid -->
        <DataGrid Grid.Row="2" Margin="10" AutoGenerateColumns="False" CanUserAddRows="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الفاتورة" Width="100"/>
                <DataGridTextColumn Header="التاريخ" Width="100"/>
                <DataGridTextColumn Header="العميل" Width="150"/>
                <DataGridTextColumn Header="عدد الأصناف" Width="100"/>
                <DataGridTextColumn Header="المبلغ الإجمالي" Width="120"/>
                <DataGridTextColumn Header="الخصم" Width="80"/>
                <DataGridTextColumn Header="الضريبة" Width="80"/>
                <DataGridTextColumn Header="المبلغ النهائي" Width="120"/>
                <DataGridTextColumn Header="طريقة الدفع" Width="100"/>
                <DataGridTextColumn Header="الحالة" Width="80"/>
                <DataGridTemplateColumn Header="الإجراءات" Width="200">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Content="👁️" Width="30" Height="25" Margin="2"
                                       Background="Blue" Foreground="White" ToolTip="عرض"/>
                                <Button Content="✏️" Width="30" Height="25" Margin="2"
                                       Background="Orange" Foreground="White" ToolTip="تعديل"/>
                                <Button Content="🖨️" Width="30" Height="25" Margin="2"
                                       Background="Green" Foreground="White" ToolTip="طباعة"/>
                                <Button Content="📧" Width="30" Height="25" Margin="2"
                                       Background="Purple" Foreground="White" ToolTip="إرسال"/>
                                <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                       Background="Red" Foreground="White" ToolTip="حذف"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Footer with Statistics -->
        <Border Grid.Row="3" Background="LightGray">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Statistics -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="إجمالي الفواتير: " FontWeight="Bold"/>
                    <TextBlock Text="25" Foreground="Blue" FontWeight="Bold" Margin="0,0,20,0"/>

                    <TextBlock Text="إجمالي المبيعات: " FontWeight="Bold"/>
                    <TextBlock Text="125750.50 دج" Foreground="Green" FontWeight="Bold" Margin="0,0,20,0"/>

                    <TextBlock Text="متوسط الفاتورة: " FontWeight="Bold"/>
                    <TextBlock Text="5030.02 دج" Foreground="Orange" FontWeight="Bold"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="➕ فاتورة جديدة" Width="120" Height="35" Margin="5"
                           Background="Green" Foreground="White" FontWeight="Bold"/>
                    <Button Content="📊 تقرير المبيعات" Width="120" Height="35" Margin="5"
                           Background="Blue" Foreground="White" FontWeight="Bold"/>
                    <Button Content="📤 تصدير" Width="80" Height="35" Margin="5"
                           Background="Orange" Foreground="White" FontWeight="Bold"/>
                    <Button Content="❌ إغلاق" Width="80" Height="35" Margin="5"
                           Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
