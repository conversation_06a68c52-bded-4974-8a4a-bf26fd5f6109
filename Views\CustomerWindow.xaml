<Window x:Class="SalesManagementSystem.Views.CustomerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="إضافة عميل جديد"
        Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <Border Grid.Row="0" Background="Teal">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="👤" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="إضافة عميل جديد" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel>
                <GroupBox Header="بيانات العميل الأساسية" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم العميل:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="0" Margin="0,20,10,10"/>

                        <TextBlock Grid.Row="0" Grid.Column="1" Text="رقم الهاتف:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Margin="10,20,0,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="البريد الإلكتروني:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="0" Margin="0,20,10,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="1" Text="الرقم الضريبي:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Margin="10,20,0,10"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Text="العنوان:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,20,0,10"
                                Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>

                        <CheckBox Grid.Row="3" Grid.Column="0" Content="عميل نشط" IsChecked="True" Margin="0,10,0,0"/>
                        <CheckBox Grid.Row="3" Grid.Column="1" Content="عميل مميز" Margin="10,10,0,0"/>
                    </Grid>
                </GroupBox>

                <GroupBox Header="معلومات إضافية">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="نوع العميل:" Margin="0,0,0,5"/>
                        <ComboBox Grid.Row="0" Grid.Column="0" Margin="0,20,10,10">
                            <ComboBoxItem Content="عميل فردي"/>
                            <ComboBoxItem Content="شركة"/>
                            <ComboBoxItem Content="مؤسسة"/>
                        </ComboBox>

                        <TextBlock Grid.Row="0" Grid.Column="1" Text="حد الائتمان:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Margin="10,20,0,10" Text="0"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="نسبة الخصم (%):" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="0" Margin="0,20,10,10" Text="0"/>

                        <TextBlock Grid.Row="1" Grid.Column="1" Text="تاريخ التسجيل:" Margin="0,0,0,5"/>
                        <DatePicker Grid.Row="1" Grid.Column="1" Margin="10,20,0,10" SelectedDate="{x:Static sys:DateTime.Now}"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Text="ملاحظات:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,20,0,10"
                                Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ العميل" Width="120" Height="35" Margin="10"
                       Background="Teal" Foreground="White" FontWeight="Bold"/>
                <Button Content="🔄 جديد" Width="100" Height="35" Margin="10"
                       Background="Blue" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إلغاء" Width="100" Height="35" Margin="10"
                       Background="Red" Foreground="White" FontWeight="Bold" Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
