using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الرسوم المتحركة والتأثيرات البصرية
    /// </summary>
    public class AnimationService
    {
        #region Singleton

        private static AnimationService? _instance;
        public static AnimationService Instance => _instance ??= new AnimationService();

        #endregion

        #region Animation Methods

        /// <summary>
        /// تأثير الظهور التدريجي
        /// </summary>
        public async Task FadeInAsync(UIElement element, double duration = 0.3, double fromOpacity = 0, double toOpacity = 1)
        {
            var animation = new DoubleAnimation
            {
                From = fromOpacity,
                To = toOpacity,
                Duration = TimeSpan.FromSeconds(duration),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };

            var tcs = new TaskCompletionSource<bool>();
            animation.Completed += (s, e) => tcs.SetResult(true);

            element.BeginAnimation(UIElement.OpacityProperty, animation);
            await tcs.Task;
        }

        /// <summary>
        /// تأثير الاختفاء التدريجي
        /// </summary>
        public async Task FadeOutAsync(UIElement element, double duration = 0.3, double fromOpacity = 1, double toOpacity = 0)
        {
            var animation = new DoubleAnimation
            {
                From = fromOpacity,
                To = toOpacity,
                Duration = TimeSpan.FromSeconds(duration),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };

            var tcs = new TaskCompletionSource<bool>();
            animation.Completed += (s, e) => tcs.SetResult(true);

            element.BeginAnimation(UIElement.OpacityProperty, animation);
            await tcs.Task;
        }

        /// <summary>
        /// تأثير الانزلاق من اليمين
        /// </summary>
        public async Task SlideInFromRightAsync(UIElement element, double duration = 0.4, double distance = 300)
        {
            if (element.RenderTransform is not TransformGroup transformGroup)
            {
                transformGroup = new TransformGroup();
                transformGroup.Children.Add(new TranslateTransform());
                element.RenderTransform = transformGroup;
            }

            var translateTransform = transformGroup.Children.OfType<TranslateTransform>().FirstOrDefault();
            if (translateTransform == null)
            {
                translateTransform = new TranslateTransform();
                transformGroup.Children.Add(translateTransform);
            }

            var slideAnimation = new DoubleAnimation
            {
                From = distance,
                To = 0,
                Duration = TimeSpan.FromSeconds(duration),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };

            var fadeAnimation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromSeconds(duration)
            };

            var tcs = new TaskCompletionSource<bool>();
            slideAnimation.Completed += (s, e) => tcs.SetResult(true);

            translateTransform.BeginAnimation(TranslateTransform.XProperty, slideAnimation);
            element.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);

            await tcs.Task;
        }

        /// <summary>
        /// تأثير الانزلاق من اليسار
        /// </summary>
        public async Task SlideInFromLeftAsync(UIElement element, double duration = 0.4, double distance = 300)
        {
            await SlideInFromRightAsync(element, duration, -distance);
        }

        /// <summary>
        /// تأثير الانزلاق من الأعلى
        /// </summary>
        public async Task SlideInFromTopAsync(UIElement element, double duration = 0.4, double distance = 100)
        {
            if (element.RenderTransform is not TransformGroup transformGroup)
            {
                transformGroup = new TransformGroup();
                transformGroup.Children.Add(new TranslateTransform());
                element.RenderTransform = transformGroup;
            }

            var translateTransform = transformGroup.Children.OfType<TranslateTransform>().FirstOrDefault();
            if (translateTransform == null)
            {
                translateTransform = new TranslateTransform();
                transformGroup.Children.Add(translateTransform);
            }

            var slideAnimation = new DoubleAnimation
            {
                From = -distance,
                To = 0,
                Duration = TimeSpan.FromSeconds(duration),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };

            var fadeAnimation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromSeconds(duration)
            };

            var tcs = new TaskCompletionSource<bool>();
            slideAnimation.Completed += (s, e) => tcs.SetResult(true);

            translateTransform.BeginAnimation(TranslateTransform.YProperty, slideAnimation);
            element.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);

            await tcs.Task;
        }

        /// <summary>
        /// تأثير التكبير والظهور
        /// </summary>
        public async Task ScaleInAsync(UIElement element, double duration = 0.3, double fromScale = 0.8, double toScale = 1.0)
        {
            if (element.RenderTransform is not TransformGroup transformGroup)
            {
                transformGroup = new TransformGroup();
                transformGroup.Children.Add(new ScaleTransform());
                element.RenderTransform = transformGroup;
                element.RenderTransformOrigin = new Point(0.5, 0.5);
            }

            var scaleTransform = transformGroup.Children.OfType<ScaleTransform>().FirstOrDefault();
            if (scaleTransform == null)
            {
                scaleTransform = new ScaleTransform();
                transformGroup.Children.Add(scaleTransform);
            }

            var scaleXAnimation = new DoubleAnimation
            {
                From = fromScale,
                To = toScale,
                Duration = TimeSpan.FromSeconds(duration),
                EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
            };

            var scaleYAnimation = new DoubleAnimation
            {
                From = fromScale,
                To = toScale,
                Duration = TimeSpan.FromSeconds(duration),
                EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
            };

            var fadeAnimation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromSeconds(duration)
            };

            var tcs = new TaskCompletionSource<bool>();
            scaleXAnimation.Completed += (s, e) => tcs.SetResult(true);

            scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleXAnimation);
            scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleYAnimation);
            element.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);

            await tcs.Task;
        }

        /// <summary>
        /// تأثير الاهتزاز
        /// </summary>
        public async Task ShakeAsync(UIElement element, double duration = 0.5, double intensity = 10)
        {
            if (element.RenderTransform is not TransformGroup transformGroup)
            {
                transformGroup = new TransformGroup();
                transformGroup.Children.Add(new TranslateTransform());
                element.RenderTransform = transformGroup;
            }

            var translateTransform = transformGroup.Children.OfType<TranslateTransform>().FirstOrDefault();
            if (translateTransform == null)
            {
                translateTransform = new TranslateTransform();
                transformGroup.Children.Add(translateTransform);
            }

            var animation = new DoubleAnimationUsingKeyFrames
            {
                Duration = TimeSpan.FromSeconds(duration)
            };

            var keyTimes = new[] { 0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0 };
            var values = new[] { 0, -intensity, intensity, -intensity, intensity, -intensity, intensity, -intensity, intensity, -intensity, 0 };

            for (int i = 0; i < keyTimes.Length; i++)
            {
                animation.KeyFrames.Add(new LinearDoubleKeyFrame
                {
                    KeyTime = TimeSpan.FromSeconds(duration * keyTimes[i]),
                    Value = values[i]
                });
            }

            var tcs = new TaskCompletionSource<bool>();
            animation.Completed += (s, e) => tcs.SetResult(true);

            translateTransform.BeginAnimation(TranslateTransform.XProperty, animation);
            await tcs.Task;
        }

        /// <summary>
        /// تأثير النبض
        /// </summary>
        public void StartPulse(UIElement element, double duration = 1.0, double minOpacity = 0.5, double maxOpacity = 1.0)
        {
            var animation = new DoubleAnimation
            {
                From = minOpacity,
                To = maxOpacity,
                Duration = TimeSpan.FromSeconds(duration),
                AutoReverse = true,
                RepeatBehavior = RepeatBehavior.Forever,
                EasingFunction = new SineEase { EasingMode = EasingMode.EaseInOut }
            };

            element.BeginAnimation(UIElement.OpacityProperty, animation);
        }

        /// <summary>
        /// إيقاف تأثير النبض
        /// </summary>
        public void StopPulse(UIElement element)
        {
            element.BeginAnimation(UIElement.OpacityProperty, null);
            element.Opacity = 1.0;
        }

        /// <summary>
        /// تأثير الدوران
        /// </summary>
        public void StartRotation(UIElement element, double duration = 2.0, bool clockwise = true)
        {
            if (element.RenderTransform is not TransformGroup transformGroup)
            {
                transformGroup = new TransformGroup();
                transformGroup.Children.Add(new RotateTransform());
                element.RenderTransform = transformGroup;
                element.RenderTransformOrigin = new Point(0.5, 0.5);
            }

            var rotateTransform = transformGroup.Children.OfType<RotateTransform>().FirstOrDefault();
            if (rotateTransform == null)
            {
                rotateTransform = new RotateTransform();
                transformGroup.Children.Add(rotateTransform);
            }

            var animation = new DoubleAnimation
            {
                From = 0,
                To = clockwise ? 360 : -360,
                Duration = TimeSpan.FromSeconds(duration),
                RepeatBehavior = RepeatBehavior.Forever,
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseInOut }
            };

            rotateTransform.BeginAnimation(RotateTransform.AngleProperty, animation);
        }

        /// <summary>
        /// إيقاف تأثير الدوران
        /// </summary>
        public void StopRotation(UIElement element)
        {
            if (element.RenderTransform is TransformGroup transformGroup)
            {
                var rotateTransform = transformGroup.Children.OfType<RotateTransform>().FirstOrDefault();
                rotateTransform?.BeginAnimation(RotateTransform.AngleProperty, null);
            }
        }

        /// <summary>
        /// تأثير انتقال الصفحات
        /// </summary>
        public async Task PageTransitionAsync(UIElement oldPage, UIElement newPage, double duration = 0.5)
        {
            if (oldPage != null)
            {
                await SlideOutToLeftAsync(oldPage, duration);
                oldPage.Visibility = Visibility.Collapsed;
            }

            if (newPage != null)
            {
                newPage.Visibility = Visibility.Visible;
                await SlideInFromRightAsync(newPage, duration);
            }
        }

        /// <summary>
        /// تأثير الانزلاق للخارج إلى اليسار
        /// </summary>
        private async Task SlideOutToLeftAsync(UIElement element, double duration = 0.4)
        {
            if (element.RenderTransform is not TransformGroup transformGroup)
            {
                transformGroup = new TransformGroup();
                transformGroup.Children.Add(new TranslateTransform());
                element.RenderTransform = transformGroup;
            }

            var translateTransform = transformGroup.Children.OfType<TranslateTransform>().FirstOrDefault();
            if (translateTransform == null)
            {
                translateTransform = new TranslateTransform();
                transformGroup.Children.Add(translateTransform);
            }

            var slideAnimation = new DoubleAnimation
            {
                From = 0,
                To = -300,
                Duration = TimeSpan.FromSeconds(duration),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
            };

            var fadeAnimation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromSeconds(duration)
            };

            var tcs = new TaskCompletionSource<bool>();
            slideAnimation.Completed += (s, e) => tcs.SetResult(true);

            translateTransform.BeginAnimation(TranslateTransform.XProperty, slideAnimation);
            element.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);

            await tcs.Task;
        }

        #endregion
    }
}
