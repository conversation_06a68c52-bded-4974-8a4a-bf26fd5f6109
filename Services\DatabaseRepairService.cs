using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace SalesManagementSystem.Services
{
    public class DatabaseRepairService
    {
        private readonly DatabaseService _dbService;
        private readonly string _connectionString;

        public DatabaseRepairService(DatabaseService dbService)
        {
            _dbService = dbService;

            string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SalesManagementSystem");
            string dbPath = Path.Combine(appDataPath, "SalesManagement.db");
            _connectionString = $"Data Source={dbPath};Version=3;";
        }

        public async Task<bool> CheckAndRepairDatabaseAsync()
        {
            try
            {
                LoggingService.LogInfo("بدء فحص وإصلاح قاعدة البيانات...");

                using var connection = new SQLiteConnection(_connectionString);
                connection.Open();

                // Check for missing tables
                var missingTables = await CheckMissingTablesAsync(connection);

                if (missingTables.Count > 0)
                {
                    LoggingService.LogInfo($"تم العثور على {missingTables.Count} جدول مفقود");
                    await CreateMissingTablesAsync(connection, missingTables);
                }

                // Check for missing columns
                await CheckAndAddMissingColumnsAsync(connection);

                LoggingService.LogInfo("تم إكمال فحص وإصلاح قاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في فحص وإصلاح قاعدة البيانات");
                return false;
            }
        }

        private async Task<System.Collections.Generic.List<string>> CheckMissingTablesAsync(SQLiteConnection connection)
        {
            var missingTables = new System.Collections.Generic.List<string>();
            var requiredTables = new[]
            {
                "Products", "Categories", "Customers", "Suppliers", "Employees",
                "Expenses", "ExpenseCategories", "Sales", "SaleItems", "Purchases",
                "PurchaseItems", "Users", "Settings", "Notifications", "Payments",
                "Invoices", "InvoiceItems", "InstallmentPlans", "InstallmentPayments",
                "Warehouses", "WarehouseLocations", "InventoryItems", "InventoryTransfers",
                "InventoryTransferItems"
            };

            foreach (var tableName in requiredTables)
            {
                var checkSql = "SELECT name FROM sqlite_master WHERE type='table' AND name=@tableName";
                using var command = new SQLiteCommand(checkSql, connection);
                command.Parameters.AddWithValue("@tableName", tableName);

                var result = await command.ExecuteScalarAsync();
                if (result == null)
                {
                    missingTables.Add(tableName);
                    LoggingService.LogInfo($"جدول مفقود: {tableName}");
                }
            }

            return missingTables;
        }

        private async Task CreateMissingTablesAsync(SQLiteConnection connection, System.Collections.Generic.List<string> missingTables)
        {
            var tableCreationSql = new System.Collections.Generic.Dictionary<string, string>
            {
                ["Products"] = CreateProductsTableSql,
                ["Categories"] = CreateCategoriesTableSql,
                ["Customers"] = CreateCustomersTableSql,
                ["Suppliers"] = CreateSuppliersTableSql,
                ["Employees"] = CreateEmployeesTableSql,
                ["Expenses"] = CreateExpensesTableSql,
                ["ExpenseCategories"] = CreateExpenseCategoriesTableSql,
                ["Sales"] = CreateSalesTableSql,
                ["SaleItems"] = CreateSaleItemsTableSql,
                ["Purchases"] = CreatePurchasesTableSql,
                ["PurchaseItems"] = CreatePurchaseItemsTableSql,
                ["Users"] = CreateUsersTableSql,
                ["Settings"] = CreateSettingsTableSql,
                ["Notifications"] = CreateNotificationsTableSql,
                ["Payments"] = CreatePaymentsTableSql,
                ["Invoices"] = CreateInvoicesTableSql,
                ["InvoiceItems"] = CreateInvoiceItemsTableSql,
                ["InstallmentPlans"] = CreateInstallmentPlansTableSql,
                ["InstallmentPayments"] = CreateInstallmentPaymentsTableSql,
                ["Warehouses"] = CreateWarehousesTableSql,
                ["WarehouseLocations"] = CreateWarehouseLocationsTableSql,
                ["InventoryItems"] = CreateInventoryItemsTableSql,
                ["InventoryTransfers"] = CreateInventoryTransfersTableSql,
                ["InventoryTransferItems"] = CreateInventoryTransferItemsTableSql
            };

            foreach (var tableName in missingTables)
            {
                if (tableCreationSql.ContainsKey(tableName))
                {
                    try
                    {
                        using var command = new SQLiteCommand(tableCreationSql[tableName], connection);
                        await command.ExecuteNonQueryAsync();
                        LoggingService.LogInfo($"تم إنشاء الجدول: {tableName}");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في إنشاء الجدول: {tableName}");
                    }
                }
            }
        }

        private async Task CheckAndAddMissingColumnsAsync(SQLiteConnection connection)
        {
            try
            {
                // Check Products table for missing columns
                var checkColumnSql = "PRAGMA table_info(Products)";
                using var command = new SQLiteCommand(checkColumnSql, connection);
                using var reader = await command.ExecuteReaderAsync();

                var existingColumns = new HashSet<string>();
                while (await reader.ReadAsync())
                {
                    var columnName = reader["name"].ToString();
                    if (!string.IsNullOrEmpty(columnName))
                    {
                        existingColumns.Add(columnName);
                    }
                }
                reader.Close();

                // Define required columns for Products table
                var requiredColumns = new Dictionary<string, string>
                {
                    ["SalePrice"] = "REAL NOT NULL DEFAULT 0",
                    ["SalePrice2"] = "REAL DEFAULT 0",
                    ["WarehouseId"] = "INTEGER",
                    ["WarehouseName"] = "TEXT",
                    ["Unit"] = "TEXT DEFAULT 'قطعة'",
                    ["Barcode"] = "TEXT",
                    ["IsActive"] = "INTEGER DEFAULT 1",
                    ["TrackStock"] = "INTEGER DEFAULT 1",
                    ["MinQuantity"] = "INTEGER NOT NULL DEFAULT 0",
                    ["CreatedAt"] = "TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP",
                    ["UpdatedAt"] = "TEXT"
                };

                // Add missing columns
                foreach (var column in requiredColumns)
                {
                    if (!existingColumns.Contains(column.Key))
                    {
                        try
                        {
                            var alterSql = $"ALTER TABLE Products ADD COLUMN {column.Key} {column.Value}";
                            using var addColumn = new SQLiteCommand(alterSql, connection);
                            await addColumn.ExecuteNonQueryAsync();
                            LoggingService.LogInfo($"تم إضافة عمود {column.Key} إلى جدول Products");
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError(ex, $"خطأ في إضافة عمود {column.Key}");
                        }
                    }
                }

                // Check Categories table
                await CheckAndAddCategoryColumnsAsync(connection);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في فحص وإضافة الأعمدة المفقودة");
            }
        }

        private async Task CheckAndAddCategoryColumnsAsync(SQLiteConnection connection)
        {
            try
            {
                var checkColumnSql = "PRAGMA table_info(Categories)";
                using var command = new SQLiteCommand(checkColumnSql, connection);
                using var reader = await command.ExecuteReaderAsync();

                var existingColumns = new HashSet<string>();
                while (await reader.ReadAsync())
                {
                    var columnName = reader["name"].ToString();
                    if (!string.IsNullOrEmpty(columnName))
                    {
                        existingColumns.Add(columnName);
                    }
                }
                reader.Close();

                var requiredColumns = new Dictionary<string, string>
                {
                    ["Name"] = "TEXT NOT NULL",
                    ["Description"] = "TEXT",
                    ["CreatedAt"] = "TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP",
                    ["UpdatedAt"] = "TEXT"
                };

                foreach (var column in requiredColumns)
                {
                    if (!existingColumns.Contains(column.Key))
                    {
                        try
                        {
                            var alterSql = $"ALTER TABLE Categories ADD COLUMN {column.Key} {column.Value}";
                            using var addColumn = new SQLiteCommand(alterSql, connection);
                            await addColumn.ExecuteNonQueryAsync();
                            LoggingService.LogInfo($"تم إضافة عمود {column.Key} إلى جدول Categories");
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError(ex, $"خطأ في إضافة عمود {column.Key} للفئات");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في فحص أعمدة جدول Categories");
            }
        }

        #region Table Creation SQL

        private const string CreateWarehousesTableSql = @"
            CREATE TABLE IF NOT EXISTS Warehouses (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Code TEXT NOT NULL UNIQUE,
                Name TEXT NOT NULL,
                Description TEXT,
                Type TEXT NOT NULL DEFAULT 'Main',
                Status TEXT NOT NULL DEFAULT 'Active',
                Address TEXT,
                City TEXT,
                Phone TEXT,
                Email TEXT,
                ManagerName TEXT,
                TotalCapacity DECIMAL(10,2) DEFAULT 0,
                UsedCapacity DECIMAL(10,2) DEFAULT 0,
                AvailableCapacity DECIMAL(10,2) DEFAULT 0,
                IsDefault INTEGER DEFAULT 0,
                Notes TEXT,
                CreatedBy TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT
            );
            CREATE INDEX IF NOT EXISTS idx_warehouses_code ON Warehouses(Code);
            CREATE INDEX IF NOT EXISTS idx_warehouses_type ON Warehouses(Type);
            CREATE INDEX IF NOT EXISTS idx_warehouses_status ON Warehouses(Status);
        ";

        private const string CreateCategoriesTableSql = @"
            CREATE TABLE IF NOT EXISTS Categories (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Description TEXT,
                CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UpdatedAt TEXT
            );
        ";

        private const string CreateProductsTableSql = @"
            CREATE TABLE IF NOT EXISTS Products (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Code TEXT NOT NULL,
                Name TEXT NOT NULL,
                Description TEXT,
                CategoryId INTEGER,
                PurchasePrice REAL NOT NULL,
                SalePrice REAL NOT NULL,
                SalePrice2 REAL DEFAULT 0,
                Unit TEXT DEFAULT 'قطعة',
                Barcode TEXT,
                IsActive INTEGER DEFAULT 1,
                TrackStock INTEGER DEFAULT 1,
                Quantity INTEGER NOT NULL,
                MinQuantity INTEGER NOT NULL,
                WarehouseId INTEGER,
                WarehouseName TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
            );
            CREATE INDEX IF NOT EXISTS idx_products_code ON Products(Code);
            CREATE INDEX IF NOT EXISTS idx_products_category ON Products(CategoryId);
            CREATE INDEX IF NOT EXISTS idx_products_barcode ON Products(Barcode);
        ";

        // Add other table creation SQL as needed...
        private const string CreateCustomersTableSql = @"CREATE TABLE IF NOT EXISTS Customers (Id INTEGER PRIMARY KEY AUTOINCREMENT, Name TEXT NOT NULL, Phone TEXT, Email TEXT, Address TEXT, Notes TEXT, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreateSuppliersTableSql = @"CREATE TABLE IF NOT EXISTS Suppliers (Id INTEGER PRIMARY KEY AUTOINCREMENT, Name TEXT NOT NULL, Phone TEXT, Email TEXT, Address TEXT, Notes TEXT, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreateEmployeesTableSql = @"CREATE TABLE IF NOT EXISTS Employees (Id INTEGER PRIMARY KEY AUTOINCREMENT, Name TEXT NOT NULL, Phone TEXT, Email TEXT, Position TEXT, Salary REAL, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreateExpensesTableSql = @"CREATE TABLE IF NOT EXISTS Expenses (Id INTEGER PRIMARY KEY AUTOINCREMENT, Amount REAL NOT NULL, Description TEXT, CategoryId INTEGER, Date TEXT NOT NULL, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreateExpenseCategoriesTableSql = @"CREATE TABLE IF NOT EXISTS ExpenseCategories (Id INTEGER PRIMARY KEY AUTOINCREMENT, Name TEXT NOT NULL, Description TEXT);";
        private const string CreateSalesTableSql = @"CREATE TABLE IF NOT EXISTS Sales (Id INTEGER PRIMARY KEY AUTOINCREMENT, InvoiceNumber TEXT, CustomerId INTEGER, TotalAmount REAL, DiscountAmount REAL, NetAmount REAL, Date TEXT NOT NULL, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreateSaleItemsTableSql = @"CREATE TABLE IF NOT EXISTS SaleItems (Id INTEGER PRIMARY KEY AUTOINCREMENT, SaleId INTEGER, ProductId INTEGER, Quantity INTEGER, UnitPrice REAL, TotalPrice REAL, FOREIGN KEY (SaleId) REFERENCES Sales(Id), FOREIGN KEY (ProductId) REFERENCES Products(Id));";
        private const string CreatePurchasesTableSql = @"CREATE TABLE IF NOT EXISTS Purchases (Id INTEGER PRIMARY KEY AUTOINCREMENT, InvoiceNumber TEXT, SupplierId INTEGER, TotalAmount REAL, Date TEXT NOT NULL, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreatePurchaseItemsTableSql = @"CREATE TABLE IF NOT EXISTS PurchaseItems (Id INTEGER PRIMARY KEY AUTOINCREMENT, PurchaseId INTEGER, ProductId INTEGER, Quantity INTEGER, UnitPrice REAL, TotalPrice REAL, FOREIGN KEY (PurchaseId) REFERENCES Purchases(Id), FOREIGN KEY (ProductId) REFERENCES Products(Id));";
        private const string CreateUsersTableSql = @"CREATE TABLE IF NOT EXISTS Users (Id INTEGER PRIMARY KEY AUTOINCREMENT, Username TEXT NOT NULL UNIQUE, PasswordHash TEXT NOT NULL, FullName TEXT, Role TEXT DEFAULT 'User', IsActive INTEGER DEFAULT 1, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreateSettingsTableSql = @"CREATE TABLE IF NOT EXISTS Settings (Id INTEGER PRIMARY KEY AUTOINCREMENT, Key TEXT NOT NULL UNIQUE, Value TEXT, Description TEXT);";
        private const string CreateNotificationsTableSql = @"CREATE TABLE IF NOT EXISTS Notifications (Id INTEGER PRIMARY KEY AUTOINCREMENT, Title TEXT NOT NULL, Message TEXT NOT NULL, Type TEXT NOT NULL, IsRead INTEGER DEFAULT 0, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreatePaymentsTableSql = @"CREATE TABLE IF NOT EXISTS Payments (Id INTEGER PRIMARY KEY AUTOINCREMENT, Amount REAL NOT NULL, Type TEXT, Description TEXT, Date TEXT NOT NULL, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreateInvoicesTableSql = @"CREATE TABLE IF NOT EXISTS Invoices (Id INTEGER PRIMARY KEY AUTOINCREMENT, Number TEXT NOT NULL, Type TEXT, Amount REAL, Date TEXT NOT NULL, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreateInvoiceItemsTableSql = @"CREATE TABLE IF NOT EXISTS InvoiceItems (Id INTEGER PRIMARY KEY AUTOINCREMENT, InvoiceId INTEGER, Description TEXT, Quantity INTEGER, UnitPrice REAL, TotalPrice REAL, FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id));";
        private const string CreateInstallmentPlansTableSql = @"CREATE TABLE IF NOT EXISTS InstallmentPlans (Id INTEGER PRIMARY KEY AUTOINCREMENT, Name TEXT NOT NULL, TotalAmount REAL, InstallmentCount INTEGER, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreateInstallmentPaymentsTableSql = @"CREATE TABLE IF NOT EXISTS InstallmentPayments (Id INTEGER PRIMARY KEY AUTOINCREMENT, PlanId INTEGER, Amount REAL, DueDate TEXT, PaidDate TEXT, Status TEXT, FOREIGN KEY (PlanId) REFERENCES InstallmentPlans(Id));";
        private const string CreateWarehouseLocationsTableSql = @"CREATE TABLE IF NOT EXISTS WarehouseLocations (Id INTEGER PRIMARY KEY AUTOINCREMENT, WarehouseId INTEGER, Code TEXT, Name TEXT, Description TEXT, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, FOREIGN KEY (WarehouseId) REFERENCES Warehouses(Id));";
        private const string CreateInventoryItemsTableSql = @"CREATE TABLE IF NOT EXISTS InventoryItems (Id INTEGER PRIMARY KEY AUTOINCREMENT, ProductId INTEGER, WarehouseId INTEGER, Quantity INTEGER, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, FOREIGN KEY (ProductId) REFERENCES Products(Id), FOREIGN KEY (WarehouseId) REFERENCES Warehouses(Id));";
        private const string CreateInventoryTransfersTableSql = @"CREATE TABLE IF NOT EXISTS InventoryTransfers (Id INTEGER PRIMARY KEY AUTOINCREMENT, FromWarehouseId INTEGER, ToWarehouseId INTEGER, Date TEXT NOT NULL, Status TEXT, CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP);";
        private const string CreateInventoryTransferItemsTableSql = @"CREATE TABLE IF NOT EXISTS InventoryTransferItems (Id INTEGER PRIMARY KEY AUTOINCREMENT, TransferId INTEGER, ProductId INTEGER, Quantity INTEGER, FOREIGN KEY (TransferId) REFERENCES InventoryTransfers(Id), FOREIGN KEY (ProductId) REFERENCES Products(Id));";

        #endregion
    }
}
