using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Views
{
    public partial class ProfitsWindow : Window
    {
        private List<Sale> _sales = new List<Sale>();
        private List<Expense> _expenses = new List<Expense>();
        private ProfitPeriod _currentPeriod;
        private DateTime _startDate;
        private DateTime _endDate;

        public ProfitsWindow()
        {
            InitializeComponent();
            InitializeData();
            InitializePeriods();
            LoadTodayData();

            // ربط حدث تغيير حجم النافذة
            SizeChanged += Window_SizeChanged;
            ChartCanvas.SizeChanged += (s, e) => UpdateChart();
        }

        private void InitializeData()
        {
            // بيانات تجريبية للمبيعات
            _sales = new List<Sale>
            {
                new Sale { Id = 1, Date = DateTime.Now.AddHours(-2), Total = 15000, CustomerName = "أحمد محمد" },
                new Sale { Id = 2, Date = DateTime.Now.AddHours(-4), Total = 8500, CustomerName = "فاطمة علي" },
                new Sale { Id = 3, Date = DateTime.Now.AddHours(-6), Total = 22000, CustomerName = "محمد حسن" },
                new Sale { Id = 4, Date = DateTime.Now.AddHours(-8), Total = 12500, CustomerName = "سارة أحمد" },
                new Sale { Id = 5, Date = DateTime.Now.AddDays(-1).AddHours(-3), Total = 18000, CustomerName = "علي محمود" },
                new Sale { Id = 6, Date = DateTime.Now.AddDays(-1).AddHours(-7), Total = 9500, CustomerName = "نور الدين" },
                new Sale { Id = 7, Date = DateTime.Now.AddDays(-2), Total = 25000, CustomerName = "خديجة حسين" },
                new Sale { Id = 8, Date = DateTime.Now.AddDays(-3), Total = 14000, CustomerName = "يوسف عبدالله" }
            };

            // بيانات تجريبية للمصاريف
            _expenses = new List<Expense>
            {
                new Expense { Id = 1, Date = DateTime.Now.AddHours(-1), Amount = 5000, Title = "فاتورة كهرباء", CategoryName = "فواتير ومرافق" },
                new Expense { Id = 2, Date = DateTime.Now.AddHours(-5), Amount = 3000, Title = "مصاريف نقل", CategoryName = "نقل ومواصلات" },
                new Expense { Id = 3, Date = DateTime.Now.AddDays(-1), Amount = 45000, Title = "راتب موظف", CategoryName = "راتب موظف" },
                new Expense { Id = 4, Date = DateTime.Now.AddDays(-2), Amount = 8500, Title = "منتجات تالفة", CategoryName = "منتج تالف" },
                new Expense { Id = 5, Date = DateTime.Now.AddDays(-3), Amount = 12000, Title = "إيجار المحل", CategoryName = "إيجار" }
            };
        }

        private void InitializePeriods()
        {
            var periods = new List<ComboBoxItem>
            {
                new ComboBoxItem { Content = "📅 اليوم", Tag = ProfitPeriod.Today },
                new ComboBoxItem { Content = "📅 أمس", Tag = ProfitPeriod.Yesterday },
                new ComboBoxItem { Content = "📅 هذا الأسبوع", Tag = ProfitPeriod.ThisWeek },
                new ComboBoxItem { Content = "📅 هذا الشهر", Tag = ProfitPeriod.ThisMonth },
                new ComboBoxItem { Content = "📅 الشهر الماضي", Tag = ProfitPeriod.LastMonth },
                new ComboBoxItem { Content = "📅 فترة مخصصة", Tag = ProfitPeriod.Custom }
            };

            PeriodComboBox.ItemsSource = periods;
            PeriodComboBox.SelectedIndex = 0;
        }

        private void LoadTodayData()
        {
            _currentPeriod = ProfitPeriod.Today;
            _startDate = DateTime.Today;
            _endDate = DateTime.Today.AddDays(1).AddSeconds(-1);

            StartDatePicker.SelectedDate = _startDate;
            EndDatePicker.SelectedDate = _endDate.Date;
            StartDatePicker.Visibility = Visibility.Collapsed;
            EndDatePicker.Visibility = Visibility.Collapsed;

            UpdateData();
        }

        private void PeriodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PeriodComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                _currentPeriod = (ProfitPeriod)selectedItem.Tag;

                switch (_currentPeriod)
                {
                    case ProfitPeriod.Today:
                        _startDate = DateTime.Today;
                        _endDate = DateTime.Today.AddDays(1).AddSeconds(-1);
                        PeriodLabel.Text = "اليوم";
                        StartDatePicker.Visibility = Visibility.Collapsed;
                        EndDatePicker.Visibility = Visibility.Collapsed;
                        break;

                    case ProfitPeriod.Yesterday:
                        _startDate = DateTime.Today.AddDays(-1);
                        _endDate = DateTime.Today.AddSeconds(-1);
                        PeriodLabel.Text = "أمس";
                        StartDatePicker.Visibility = Visibility.Collapsed;
                        EndDatePicker.Visibility = Visibility.Collapsed;
                        break;

                    case ProfitPeriod.ThisWeek:
                        var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                        _startDate = startOfWeek;
                        _endDate = startOfWeek.AddDays(7).AddSeconds(-1);
                        PeriodLabel.Text = "هذا الأسبوع";
                        StartDatePicker.Visibility = Visibility.Collapsed;
                        EndDatePicker.Visibility = Visibility.Collapsed;
                        break;

                    case ProfitPeriod.ThisMonth:
                        _startDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                        _endDate = _startDate.AddMonths(1).AddSeconds(-1);
                        PeriodLabel.Text = "هذا الشهر";
                        StartDatePicker.Visibility = Visibility.Collapsed;
                        EndDatePicker.Visibility = Visibility.Collapsed;
                        break;

                    case ProfitPeriod.LastMonth:
                        var lastMonth = DateTime.Today.AddMonths(-1);
                        _startDate = new DateTime(lastMonth.Year, lastMonth.Month, 1);
                        _endDate = _startDate.AddMonths(1).AddSeconds(-1);
                        PeriodLabel.Text = "الشهر الماضي";
                        StartDatePicker.Visibility = Visibility.Collapsed;
                        EndDatePicker.Visibility = Visibility.Collapsed;
                        break;

                    case ProfitPeriod.Custom:
                        StartDatePicker.Visibility = Visibility.Visible;
                        EndDatePicker.Visibility = Visibility.Visible;
                        PeriodLabel.Text = "فترة مخصصة";
                        if (StartDatePicker.SelectedDate.HasValue && EndDatePicker.SelectedDate.HasValue)
                        {
                            _startDate = StartDatePicker.SelectedDate.Value;
                            _endDate = EndDatePicker.SelectedDate.Value.AddDays(1).AddSeconds(-1);
                        }
                        break;
                }

                UpdateData();
            }
        }

        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_currentPeriod == ProfitPeriod.Custom &&
                StartDatePicker.SelectedDate.HasValue &&
                EndDatePicker.SelectedDate.HasValue)
            {
                _startDate = StartDatePicker.SelectedDate.Value;
                _endDate = EndDatePicker.SelectedDate.Value.AddDays(1).AddSeconds(-1);
                PeriodLabel.Text = $"{_startDate:MM/dd} - {_endDate.Date:MM/dd}";
                UpdateData();
            }
        }

        private void UpdateData()
        {
            var summary = ProfitCalculator.CalculateProfitSummary(_startDate, _endDate, _sales, _expenses);

            // تحديث البطاقات
            TotalSalesLabel.Text = summary.FormattedTotalSales;
            TotalExpensesLabel.Text = summary.FormattedTotalExpenses;
            NetProfitLabel.Text = summary.FormattedNetProfit;
            ProfitMarginLabel.Text = summary.FormattedProfitMargin;

            SalesCountLabel.Text = $"{summary.TotalTransactions} عملية بيع";
            ExpensesCountLabel.Text = $"{summary.TotalExpenseEntries} مصروف";
            AverageSaleLabel.Text = summary.FormattedAverageSale;
            AverageExpenseLabel.Text = $"متوسط المصروف: {summary.FormattedAverageExpense}";

            // تحديث الألوان والأيقونات
            NetProfitLabel.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(summary.ProfitColor));
            ProfitIcon.Text = summary.ProfitIcon;
            ProfitMarginLabel.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(summary.MarginColor));

            // تحديث حالة الهامش
            MarginStatusLabel.Text = summary.ProfitMargin >= 20 ? "ممتاز" :
                                   summary.ProfitMargin >= 10 ? "جيد" : "يحتاج تحسين";

            // تحديث الرسم البياني
            UpdateChart();

            // تحديث شريط الحالة
            StatusLabel.Text = $"تم تحليل {summary.TotalTransactions} عملية بيع و {summary.TotalExpenseEntries} مصروف";
            LastUpdateLabel.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
        }

        private void UpdateChart()
        {
            ChartCanvas.Children.Clear();

            List<ProfitChartDataPoint> chartData;

            if (_currentPeriod == ProfitPeriod.Today || _currentPeriod == ProfitPeriod.Yesterday)
            {
                // رسم بياني بالساعات
                chartData = ProfitCalculator.CreateHourlyChartData(_startDate, _sales, _expenses);
                ChartTitle.Text = $"الأرباح حسب الساعات - {PeriodLabel.Text}";
            }
            else
            {
                // رسم بياني بالأيام
                chartData = ProfitCalculator.CreateDailyChartData(_startDate, _endDate.Date, _sales, _expenses);
                ChartTitle.Text = $"الأرباح حسب الأيام - {PeriodLabel.Text}";
            }

            if (chartData.Count == 0) return;

            DrawChart(chartData);
        }

        private void DrawChart(List<ProfitChartDataPoint> data)
        {
            if (ChartCanvas.ActualWidth == 0 || ChartCanvas.ActualHeight == 0) return;

            var maxValue = data.Max(d => Math.Abs(d.Value));
            if (maxValue == 0) maxValue = 1;

            var barWidth = (ChartCanvas.ActualWidth - 40) / data.Count;
            var chartHeight = ChartCanvas.ActualHeight - 40;
            var zeroLine = chartHeight / 2 + 20;

            // رسم خط الصفر
            var zeroLineShape = new Line
            {
                X1 = 20,
                Y1 = zeroLine,
                X2 = ChartCanvas.ActualWidth - 20,
                Y2 = zeroLine,
                Stroke = Brushes.Gray,
                StrokeThickness = 1,
                StrokeDashArray = new DoubleCollection { 5, 5 }
            };
            ChartCanvas.Children.Add(zeroLineShape);

            for (int i = 0; i < data.Count; i++)
            {
                var point = data[i];
                var barHeight = (double)(Math.Abs(point.Value) / maxValue) * (chartHeight / 2);
                var x = 20 + i * barWidth + barWidth * 0.1;
                var barActualWidth = barWidth * 0.8;

                var y = point.Value >= 0 ? zeroLine - barHeight : zeroLine;

                var bar = new Rectangle
                {
                    Width = barActualWidth,
                    Height = barHeight,
                    Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString(point.Color))
                };

                Canvas.SetLeft(bar, x);
                Canvas.SetTop(bar, y);
                ChartCanvas.Children.Add(bar);

                // تسمية المحور السيني
                var label = new TextBlock
                {
                    Text = point.Label,
                    FontSize = 10,
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                Canvas.SetLeft(label, x + barActualWidth / 2 - 15);
                Canvas.SetTop(label, ChartCanvas.ActualHeight - 15);
                ChartCanvas.Children.Add(label);

                // قيمة البار
                if (barHeight > 20)
                {
                    var valueLabel = new TextBlock
                    {
                        Text = $"{point.Value:F0}",
                        FontSize = 8,
                        Foreground = Brushes.White,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };

                    Canvas.SetLeft(valueLabel, x + barActualWidth / 2 - 10);
                    Canvas.SetTop(valueLabel, y + barHeight / 2 - 6);
                    ChartCanvas.Children.Add(valueLabel);
                }
            }
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            UpdateData();
        }

        private void DetailedReport_Click(object sender, RoutedEventArgs e)
        {
            var reportWindow = new ProfitDetailedReportWindow(_startDate, _endDate, _sales, _expenses);
            reportWindow.ShowDialog();
        }

        private void Export_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة التصدير قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Window_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (IsLoaded)
            {
                UpdateChart();
            }
        }
    }

    // نافذة التقرير المفصل
    public class ProfitDetailedReportWindow : Window
    {
        public ProfitDetailedReportWindow(DateTime startDate, DateTime endDate, List<Sale> sales, List<Expense> expenses)
        {
            Title = "📊 تقرير الأرباح المفصل";
            Width = 800;
            Height = 600;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            var summary = ProfitCalculator.CalculateProfitSummary(startDate, endDate, sales, expenses);

            var content = new StackPanel { Margin = new Thickness(20, 20, 20, 20) };

            content.Children.Add(new TextBlock
            {
                Text = $"تقرير الأرباح من {startDate:yyyy/MM/dd} إلى {endDate:yyyy/MM/dd}",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            });

            content.Children.Add(new TextBlock { Text = $"إجمالي المبيعات: {summary.FormattedTotalSales}", FontSize = 14, Margin = new Thickness(0, 5, 0, 0) });
            content.Children.Add(new TextBlock { Text = $"إجمالي المصاريف: {summary.FormattedTotalExpenses}", FontSize = 14, Margin = new Thickness(0, 5, 0, 0) });
            content.Children.Add(new TextBlock { Text = $"صافي الربح: {summary.FormattedNetProfit}", FontSize = 16, FontWeight = FontWeights.Bold, Margin = new Thickness(0, 5, 0, 0) });
            content.Children.Add(new TextBlock { Text = $"هامش الربح: {summary.FormattedProfitMargin}", FontSize = 14, Margin = new Thickness(0, 5, 0, 0) });

            Content = new ScrollViewer { Content = content };
        }
    }
}
