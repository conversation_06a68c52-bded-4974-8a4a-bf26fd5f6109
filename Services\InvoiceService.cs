using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الفواتير المتقدمة
    /// </summary>
    public class InvoiceService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;
        private readonly PaymentService _paymentService;

        public InvoiceService(DatabaseService dbService, NotificationService notificationService, PaymentService paymentService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _paymentService = paymentService ?? throw new ArgumentNullException(nameof(paymentService));
        }

        #region Invoice Management

        /// <summary>
        /// إضافة فاتورة جديدة
        /// </summary>
        public async Task<int> AddInvoiceAsync(Invoice invoice)
        {
            try
            {
                // توليد رقم الفاتورة
                if (string.IsNullOrEmpty(invoice.InvoiceNumber))
                {
                    invoice.InvoiceNumber = await GenerateInvoiceNumberAsync(invoice.InvoiceType);
                }

                // حساب الإجماليات
                invoice.CalculateSubtotal();
                invoice.CalculateTotal();

                const string sql = @"
                    INSERT INTO Invoices (
                        InvoiceNumber, InvoiceType, Status, CustomerId, SupplierId,
                        InvoiceDate, DueDate, Subtotal, TaxAmount, DiscountAmount,
                        TotalAmount, PaidAmount, RemainingAmount, Notes, Terms,
                        CreatedBy, CreatedAt
                    ) VALUES (
                        @InvoiceNumber, @InvoiceType, @Status, @CustomerId, @SupplierId,
                        @InvoiceDate, @DueDate, @Subtotal, @TaxAmount, @DiscountAmount,
                        @TotalAmount, @PaidAmount, @RemainingAmount, @Notes, @Terms,
                        @CreatedBy, @CreatedAt
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    invoice.InvoiceNumber,
                    InvoiceType = invoice.InvoiceType.ToString(),
                    Status = invoice.Status.ToString(),
                    invoice.CustomerId,
                    invoice.SupplierId,
                    InvoiceDate = invoice.InvoiceDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    DueDate = invoice.DueDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    invoice.Subtotal,
                    invoice.TaxAmount,
                    invoice.DiscountAmount,
                    invoice.TotalAmount,
                    invoice.PaidAmount,
                    invoice.RemainingAmount,
                    invoice.Notes,
                    invoice.Terms,
                    invoice.CreatedBy,
                    CreatedAt = invoice.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var invoiceId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                invoice.Id = invoiceId;

                // إضافة عناصر الفاتورة
                foreach (var item in invoice.Items)
                {
                    item.InvoiceId = invoiceId;
                    await AddInvoiceItemAsync(item);
                }

                // إرسال إشعار
                await SendInvoiceNotificationAsync(invoice, "تم إنشاء فاتورة جديدة");

                LoggingService.LogInfo($"تم إضافة فاتورة جديدة: {invoice.InvoiceNumber}");
                return invoiceId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إضافة الفاتورة: {invoice?.InvoiceNumber}");
                throw;
            }
        }

        /// <summary>
        /// تحديث فاتورة
        /// </summary>
        public async Task<bool> UpdateInvoiceAsync(Invoice invoice)
        {
            try
            {
                invoice.UpdatedAt = DateTime.Now;
                invoice.CalculateSubtotal();
                invoice.CalculateTotal();

                const string sql = @"
                    UPDATE Invoices SET
                        InvoiceType = @InvoiceType, Status = @Status, CustomerId = @CustomerId,
                        SupplierId = @SupplierId, InvoiceDate = @InvoiceDate, DueDate = @DueDate,
                        Subtotal = @Subtotal, TaxAmount = @TaxAmount, DiscountAmount = @DiscountAmount,
                        TotalAmount = @TotalAmount, PaidAmount = @PaidAmount, RemainingAmount = @RemainingAmount,
                        Notes = @Notes, Terms = @Terms, UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                var parameters = new
                {
                    invoice.Id,
                    InvoiceType = invoice.InvoiceType.ToString(),
                    Status = invoice.Status.ToString(),
                    invoice.CustomerId,
                    invoice.SupplierId,
                    InvoiceDate = invoice.InvoiceDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    DueDate = invoice.DueDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    invoice.Subtotal,
                    invoice.TaxAmount,
                    invoice.DiscountAmount,
                    invoice.TotalAmount,
                    invoice.PaidAmount,
                    invoice.RemainingAmount,
                    invoice.Notes,
                    invoice.Terms,
                    UpdatedAt = invoice.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var rowsAffected = await _dbService.ExecuteAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم تحديث الفاتورة: {invoice.InvoiceNumber}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث الفاتورة: {invoice?.Id}");
                throw;
            }
        }

        /// <summary>
        /// حذف فاتورة
        /// </summary>
        public async Task<bool> DeleteInvoiceAsync(int invoiceId)
        {
            try
            {
                // حذف عناصر الفاتورة أولاً
                await _dbService.ExecuteAsync("DELETE FROM InvoiceItems WHERE InvoiceId = @InvoiceId", new { InvoiceId = invoiceId });

                // حذف الفاتورة
                const string sql = "DELETE FROM Invoices WHERE Id = @Id";
                var rowsAffected = await _dbService.ExecuteAsync(sql, new { Id = invoiceId });

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم حذف الفاتورة: {invoiceId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في حذف الفاتورة: {invoiceId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على فاتورة بالمعرف
        /// </summary>
        public async Task<Invoice?> GetInvoiceByIdAsync(int invoiceId)
        {
            try
            {
                const string sql = "SELECT * FROM Invoices WHERE Id = @Id";
                var invoiceData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Id = invoiceId });

                if (invoiceData != null)
                {
                    var invoice = MapToInvoice(invoiceData);

                    // تحميل عناصر الفاتورة
                    invoice.Items = new System.Collections.ObjectModel.ObservableCollection<InvoiceItem>(
                        await GetInvoiceItemsAsync(invoiceId));

                    // تحميل المدفوعات
                    invoice.Payments = new System.Collections.ObjectModel.ObservableCollection<Payment>(
                        await GetInvoicePaymentsAsync(invoiceId));

                    return invoice;
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على الفاتورة: {invoiceId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع الفواتير
        /// </summary>
        public async Task<IEnumerable<Invoice>> GetAllInvoicesAsync(int limit = 100, int offset = 0)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM Invoices
                    ORDER BY CreatedAt DESC
                    LIMIT @Limit OFFSET @Offset";

                var invoicesData = await _dbService.QueryAsync<dynamic>(sql, new { Limit = limit, Offset = offset });
                return invoicesData.Select(MapToInvoice);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على الفواتير");
                throw;
            }
        }

        /// <summary>
        /// البحث في الفواتير
        /// </summary>
        public async Task<IEnumerable<Invoice>> SearchInvoicesAsync(string searchTerm, InvoiceStatus? status = null, InvoiceType? type = null)
        {
            try
            {
                var sql = @"
                    SELECT * FROM Invoices
                    WHERE (InvoiceNumber LIKE @SearchTerm OR Notes LIKE @SearchTerm)";

                var parameters = new Dictionary<string, object>
                {
                    ["SearchTerm"] = $"%{searchTerm}%"
                };

                if (status.HasValue)
                {
                    sql += " AND Status = @Status";
                    parameters["Status"] = status?.ToString() ?? string.Empty;
                }

                if (type.HasValue)
                {
                    sql += " AND InvoiceType = @InvoiceType";
                    parameters["InvoiceType"] = type?.ToString() ?? string.Empty;
                }

                sql += " ORDER BY CreatedAt DESC";

                var invoicesData = await _dbService.QueryAsync<dynamic>(sql, parameters);
                return invoicesData.Select(MapToInvoice);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في البحث في الفواتير: {searchTerm}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على الفواتير المستحقة
        /// </summary>
        public async Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync()
        {
            try
            {
                const string sql = @"
                    SELECT * FROM Invoices
                    WHERE DueDate < @CurrentDate
                    AND Status IN ('Sent', 'PartiallyPaid')
                    ORDER BY DueDate ASC";

                var currentDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                var invoicesData = await _dbService.QueryAsync<dynamic>(sql, new { CurrentDate = currentDate });
                return invoicesData.Select(MapToInvoice);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على الفواتير المستحقة");
                throw;
            }
        }

        /// <summary>
        /// تحديد الفاتورة كمرسلة
        /// </summary>
        public async Task<bool> MarkInvoiceAsSentAsync(int invoiceId)
        {
            try
            {
                var invoice = await GetInvoiceByIdAsync(invoiceId);
                if (invoice == null) return false;

                invoice.MarkAsSent();
                var success = await UpdateInvoiceAsync(invoice);

                if (success)
                {
                    await SendInvoiceNotificationAsync(invoice, "تم إرسال الفاتورة");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديد الفاتورة كمرسلة: {invoiceId}");
                throw;
            }
        }

        /// <summary>
        /// تحديد الفاتورة كمدفوعة
        /// </summary>
        public async Task<bool> MarkInvoiceAsPaidAsync(int invoiceId)
        {
            try
            {
                var invoice = await GetInvoiceByIdAsync(invoiceId);
                if (invoice == null) return false;

                invoice.MarkAsPaid();
                var success = await UpdateInvoiceAsync(invoice);

                if (success)
                {
                    await SendInvoiceNotificationAsync(invoice, "تم دفع الفاتورة بالكامل");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديد الفاتورة كمدفوعة: {invoiceId}");
                throw;
            }
        }

        /// <summary>
        /// إضافة دفعة للفاتورة
        /// </summary>
        public async Task<bool> AddPaymentToInvoiceAsync(int invoiceId, Payment payment)
        {
            try
            {
                var invoice = await GetInvoiceByIdAsync(invoiceId);
                if (invoice == null) return false;

                payment.InvoiceId = invoiceId;
                payment.Direction = invoice.InvoiceType == InvoiceType.Sales ? PaymentDirection.Incoming : PaymentDirection.Outgoing;

                var paymentId = await _paymentService.AddPaymentAsync(payment);
                if (paymentId > 0)
                {
                    invoice.AddPayment(payment);
                    await UpdateInvoiceAsync(invoice);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إضافة دفعة للفاتورة: {invoiceId}");
                throw;
            }
        }

        #endregion

        #region Invoice Items

        /// <summary>
        /// إضافة عنصر فاتورة
        /// </summary>
        public async Task<int> AddInvoiceItemAsync(InvoiceItem item)
        {
            try
            {
                const string sql = @"
                    INSERT INTO InvoiceItems (
                        InvoiceId, ProductId, ProductName, Description, Quantity,
                        UnitPrice, Discount, Total
                    ) VALUES (
                        @InvoiceId, @ProductId, @ProductName, @Description, @Quantity,
                        @UnitPrice, @Discount, @Total
                    );
                    SELECT last_insert_rowid();";

                var itemId = await _dbService.QuerySingleAsync<int>(sql, item);
                item.Id = itemId;

                return itemId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إضافة عنصر الفاتورة");
                throw;
            }
        }

        /// <summary>
        /// الحصول على عناصر الفاتورة
        /// </summary>
        public async Task<IEnumerable<InvoiceItem>> GetInvoiceItemsAsync(int invoiceId)
        {
            try
            {
                const string sql = "SELECT * FROM InvoiceItems WHERE InvoiceId = @InvoiceId";
                return await _dbService.QueryAsync<InvoiceItem>(sql, new { InvoiceId = invoiceId });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على عناصر الفاتورة: {invoiceId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مدفوعات الفاتورة
        /// </summary>
        public async Task<IEnumerable<Payment>> GetInvoicePaymentsAsync(int invoiceId)
        {
            try
            {
                const string sql = "SELECT * FROM Payments WHERE InvoiceId = @InvoiceId ORDER BY PaymentDate DESC";
                var paymentsData = await _dbService.QueryAsync<dynamic>(sql, new { InvoiceId = invoiceId });

                var payments = new List<Payment>();
                foreach (var data in paymentsData)
                {
                    payments.Add(MapToPayment(data));
                }

                return payments;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على مدفوعات الفاتورة: {invoiceId}");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// توليد رقم فاتورة جديد
        /// </summary>
        private async Task<string> GenerateInvoiceNumberAsync(InvoiceType invoiceType)
        {
            try
            {
                var prefix = invoiceType switch
                {
                    InvoiceType.Sales => "INV",
                    InvoiceType.Purchase => "PUR",
                    InvoiceType.Return => "RET",
                    InvoiceType.Credit => "CRD",
                    InvoiceType.Debit => "DBT",
                    _ => "INV"
                };

                const string sql = "SELECT COUNT(*) FROM Invoices WHERE DATE(CreatedAt) = DATE('now') AND InvoiceType = @InvoiceType";
                var todayCount = await _dbService.QuerySingleAsync<int>(sql, new { InvoiceType = invoiceType.ToString() });

                var today = DateTime.Now;
                return $"{prefix}-{today:yyyyMMdd}-{(todayCount + 1):D4}";
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في توليد رقم الفاتورة");
                return $"INV-{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// تحويل البيانات إلى نموذج الفاتورة
        /// </summary>
        private Invoice MapToInvoice(dynamic data)
        {
            return new Invoice
            {
                Id = data.Id,
                InvoiceNumber = data.InvoiceNumber ?? "",
                InvoiceType = Enum.TryParse<InvoiceType>(data.InvoiceType, out InvoiceType type) ? type : InvoiceType.Sales,
                Status = Enum.TryParse<InvoiceStatus>(data.Status, out InvoiceStatus status) ? status : InvoiceStatus.Draft,
                CustomerId = data.CustomerId,
                SupplierId = data.SupplierId,
                InvoiceDate = DateTime.TryParse(data.InvoiceDate, out DateTime invoiceDate) ? invoiceDate : DateTime.Now,
                DueDate = DateTime.TryParse(data.DueDate, out DateTime dueDate) ? dueDate : null,
                Subtotal = data.Subtotal ?? 0,
                TaxAmount = data.TaxAmount ?? 0,
                DiscountAmount = data.DiscountAmount ?? 0,
                TotalAmount = data.TotalAmount ?? 0,
                PaidAmount = data.PaidAmount ?? 0,
                RemainingAmount = data.RemainingAmount ?? 0,
                Notes = data.Notes ?? "",
                Terms = data.Terms ?? "",
                CreatedBy = data.CreatedBy ?? "",
                CreatedAt = DateTime.TryParse(data.CreatedAt, out DateTime createdAt) ? createdAt : DateTime.Now,
                UpdatedAt = DateTime.TryParse(data.UpdatedAt, out DateTime updatedAt) ? updatedAt : null
            };
        }

        /// <summary>
        /// تحويل البيانات إلى نموذج المدفوعة
        /// </summary>
        private Payment MapToPayment(dynamic data)
        {
            return new Payment
            {
                Id = data.Id,
                PaymentNumber = data.PaymentNumber ?? "",
                Amount = data.Amount ?? 0,
                PaymentType = Enum.TryParse<PaymentType>(data.PaymentType, out PaymentType type) ? type : PaymentType.Cash,
                Status = Enum.TryParse<PaymentStatus>(data.Status, out PaymentStatus status) ? status : PaymentStatus.Pending,
                Direction = Enum.TryParse<PaymentDirection>(data.Direction, out PaymentDirection direction) ? direction : PaymentDirection.Incoming,
                InvoiceId = data.InvoiceId,
                Description = data.Description ?? "",
                PaymentDate = DateTime.TryParse(data.PaymentDate, out DateTime paymentDate) ? paymentDate : DateTime.Now
            };
        }

        /// <summary>
        /// إرسال إشعار الفاتورة
        /// </summary>
        private async Task SendInvoiceNotificationAsync(Invoice invoice, string message)
        {
            try
            {
                var notification = new Notification
                {
                    Title = "إشعار فاتورة",
                    Message = $"{message}: {invoice.InvoiceNumber} - {invoice.FormattedTotalAmount}",
                    Type = invoice.IsOverdue ? "Overdue" : "Invoice",
                    Priority = invoice.IsOverdue ? "High" : "Normal",
                    ActionUrl = $"/invoices/{invoice.Id}",
                    CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await _notificationService.AddNotificationAsync(notification);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إرسال إشعار الفاتورة");
            }
        }

        /// <summary>
        /// إنشاء فاتورة بيع سريع
        /// </summary>
        public async Task<Invoice> CreateQuickSaleInvoiceAsync(List<QuickSaleItem> items, int customerId, string paymentMethod = "نقدي")
        {
            try
            {
                var invoice = new Invoice
                {
                    InvoiceType = InvoiceType.Sales,
                    Status = InvoiceStatus.Paid,
                    CustomerId = customerId,
                    InvoiceDate = DateTime.Now,
                    DueDate = DateTime.Now,
                    CreatedBy = "admin",
                    Notes = "فاتورة بيع سريع",
                    Terms = paymentMethod
                };

                // تحويل عناصر البيع السريع إلى عناصر فاتورة
                foreach (var item in items)
                {
                    var invoiceItem = new InvoiceItem
                    {
                        ProductId = item.ProductId,
                        ProductName = item.ProductName,
                        Description = item.Barcode,
                        Quantity = item.Quantity,
                        UnitPrice = item.UnitPrice,
                        Discount = 0,
                        Total = item.Total
                    };

                    invoice.Items.Add(invoiceItem);
                }

                // حساب المجاميع
                invoice.CalculateSubtotal();
                invoice.CalculateTotal();

                // تعيين كمدفوعة بالكامل
                invoice.PaidAmount = invoice.TotalAmount;
                invoice.RemainingAmount = 0;

                var invoiceId = await AddInvoiceAsync(invoice);
                invoice.Id = invoiceId;

                return invoice;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء فاتورة البيع السريع");
                throw;
            }
        }

        #endregion
    }
}
