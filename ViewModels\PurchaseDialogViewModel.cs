using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class PurchaseDialogViewModel : BaseViewModel
    {
        #region Services

        private readonly DatabaseService _dbService;
        private readonly PurchaseService _purchaseService;
        private readonly ProductService _productService;
        private readonly SupplierService _supplierService;

        #endregion

        #region Properties

        private Purchase _purchase = new();
        public Purchase Purchase
        {
            get => _purchase;
            set
            {
                if (SetProperty(ref _purchase, value))
                {
                    OnPurchaseChanged();
                }
            }
        }

        private ObservableCollection<Supplier> _suppliers = new();
        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set => SetProperty(ref _suppliers, value);
        }

        private Supplier? _selectedSupplier;
        public Supplier? SelectedSupplier
        {
            get => _selectedSupplier;
            set
            {
                if (SetProperty(ref _selectedSupplier, value))
                {
                    if (value != null)
                        Purchase.SupplierId = value.Id;
                }
            }
        }

        private ObservableCollection<Product> _products = new();
        public ObservableCollection<Product> Products
        {
            get => _products;
            set => SetProperty(ref _products, value);
        }

        private Product? _selectedProduct;
        public Product? SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                if (SetProperty(ref _selectedProduct, value))
                {
                    if (value != null)
                    {
                        ItemUnitPrice = value.PurchasePrice;
                    }
                }
            }
        }

        private ObservableCollection<PurchaseItem> _purchaseItems = new();
        public ObservableCollection<PurchaseItem> PurchaseItems
        {
            get => _purchaseItems;
            set => SetProperty(ref _purchaseItems, value);
        }

        private ObservableCollection<string> _paymentMethods = new();
        public ObservableCollection<string> PaymentMethods
        {
            get => _paymentMethods;
            set => SetProperty(ref _paymentMethods, value);
        }

        private ObservableCollection<string> _paymentStatuses = new();
        public ObservableCollection<string> PaymentStatuses
        {
            get => _paymentStatuses;
            set => SetProperty(ref _paymentStatuses, value);
        }

        // Item Entry Properties
        private int _itemQuantity = 1;
        public int ItemQuantity
        {
            get => _itemQuantity;
            set
            {
                if (SetProperty(ref _itemQuantity, value))
                {
                    OnPropertyChanged(nameof(ItemTotal));
                }
            }
        }

        private decimal _itemUnitPrice;
        public decimal ItemUnitPrice
        {
            get => _itemUnitPrice;
            set
            {
                if (SetProperty(ref _itemUnitPrice, value))
                {
                    OnPropertyChanged(nameof(ItemTotal));
                }
            }
        }

        public decimal ItemTotal => ItemQuantity * ItemUnitPrice;

        private string _windowTitle = "فاتورة شراء جديدة";
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }

        private string _headerIcon = "ShoppingCart";
        public string HeaderIcon
        {
            get => _headerIcon;
            set => SetProperty(ref _headerIcon, value);
        }

        private bool _isEditMode;
        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                if (SetProperty(ref _isEditMode, value))
                {
                    UpdateWindowTitle();
                }
            }
        }

        #endregion

        #region Commands

        private DelegateCommand? _saveCommand;
        public DelegateCommand SaveCommand => _saveCommand ??= new DelegateCommand(SavePurchase, CanSavePurchase);

        private DelegateCommand? _cancelCommand;
        public DelegateCommand CancelCommand => _cancelCommand ??= new DelegateCommand(Cancel);

        private DelegateCommand? _addItemCommand;
        public DelegateCommand AddItemCommand => _addItemCommand ??= new DelegateCommand(AddItem, CanAddItem);

        private DelegateCommand<PurchaseItem>? _removeItemCommand;
        public DelegateCommand<PurchaseItem> RemoveItemCommand => _removeItemCommand ??= new DelegateCommand<PurchaseItem>(RemoveItem);

        #endregion

        #region Constructor

        public PurchaseDialogViewModel()
        {
            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);
            _supplierService = new SupplierService(_dbService);
            _purchaseService = new PurchaseService(_dbService, _productService, _supplierService);

            InitializeData();
            _ = LoadDataAsync();
        }

        public PurchaseDialogViewModel(Purchase purchase) : this()
        {
            Purchase = purchase.Clone();
            IsEditMode = true;
        }

        #endregion

        #region Methods

        private void InitializeData()
        {
            InitializePaymentMethods();
            InitializePaymentStatuses();

            // Initialize purchase with default values
            Purchase.Date = DateTime.Now;
            Purchase.PaymentMethod = "نقدي";
            Purchase.PaymentStatus = "مدفوع";
        }

        private void InitializePaymentMethods()
        {
            PaymentMethods.Clear();
            PaymentMethods.Add("نقدي");
            PaymentMethods.Add("بطاقة ائتمان");
            PaymentMethods.Add("بطاقة خصم");
            PaymentMethods.Add("تحويل بنكي");
            PaymentMethods.Add("شيك");
            PaymentMethods.Add("آجل");
        }

        private void InitializePaymentStatuses()
        {
            PaymentStatuses.Clear();
            PaymentStatuses.Add("مدفوع");
            PaymentStatuses.Add("مدفوع جزئياً");
            PaymentStatuses.Add("غير مدفوع");
            PaymentStatuses.Add("ملغي");
        }

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تحميل البيانات...";
                ClearError();

                await LoadSuppliersAsync();
                await LoadProductsAsync();

                if (!IsEditMode)
                {
                    Purchase.InvoiceNumber = await GenerateInvoiceNumberAsync();
                }
                else
                {
                    await LoadPurchaseItemsAsync();
                    await LoadSelectedSupplierAsync();
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل البيانات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تحميل بيانات حوار المشتريات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadSuppliersAsync()
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync();
            Suppliers.Clear();

            foreach (var supplier in suppliers)
            {
                Suppliers.Add(supplier);
            }
        }

        private async Task LoadProductsAsync()
        {
            var products = await _productService.GetAllProductsAsync();
            Products.Clear();

            foreach (var product in products.Where(p => p.IsActive))
            {
                Products.Add(product);
            }
        }

        private Task LoadPurchaseItemsAsync()
        {
            if (Purchase.Items != null)
            {
                PurchaseItems.Clear();
                foreach (var item in Purchase.Items)
                {
                    PurchaseItems.Add(item);
                }
            }
            return Task.CompletedTask;
        }

        private Task LoadSelectedSupplierAsync()
        {
            if (Purchase.SupplierId > 0)
            {
                SelectedSupplier = Suppliers.FirstOrDefault(s => s.Id == Purchase.SupplierId);
            }
            return Task.CompletedTask;
        }

        private void OnPurchaseChanged()
        {
            CalculateTotals();
            SaveCommand.RaiseCanExecuteChanged();
        }

        private void UpdateWindowTitle()
        {
            WindowTitle = IsEditMode ? "تعديل فاتورة الشراء" : "فاتورة شراء جديدة";
            HeaderIcon = IsEditMode ? "ShoppingCartEdit" : "ShoppingCart";
        }

        private void CalculateTotals()
        {
            Purchase.Subtotal = PurchaseItems.Sum(item => item.Total);
            Purchase.Tax = Purchase.Subtotal * 0.15m; // 15% tax rate
            Purchase.Total = Purchase.Subtotal + Purchase.Tax - Purchase.Discount;
        }

        private Task<string> GenerateInvoiceNumberAsync()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd");
                var random = new Random().Next(1000, 9999);
                return Task.FromResult($"PUR-{timestamp}-{random}");
            }
            catch
            {
                return Task.FromResult($"PUR-{DateTime.Now:yyyyMMddHHmmss}");
            }
        }

        #endregion

        #region Command Handlers

        private void AddItem()
        {
            if (SelectedProduct == null) return;

            var existingItem = PurchaseItems.FirstOrDefault(item => item.ProductId == SelectedProduct.Id);
            if (existingItem != null)
            {
                existingItem.Quantity += ItemQuantity;
                existingItem.Total = existingItem.Quantity * existingItem.UnitPrice;
            }
            else
            {
                var newItem = new PurchaseItem
                {
                    ProductId = SelectedProduct.Id,
                    ProductName = SelectedProduct.Name,
                    Quantity = ItemQuantity,
                    UnitPrice = ItemUnitPrice,
                    Total = ItemTotal
                };
                PurchaseItems.Add(newItem);
            }

            // Reset item entry
            SelectedProduct = null;
            ItemQuantity = 1;
            ItemUnitPrice = 0;

            CalculateTotals();
            AddItemCommand.RaiseCanExecuteChanged();
        }

        private bool CanAddItem()
        {
            return SelectedProduct != null && ItemQuantity > 0 && ItemUnitPrice > 0;
        }

        private void RemoveItem(PurchaseItem item)
        {
            if (item != null)
            {
                PurchaseItems.Remove(item);
                CalculateTotals();
            }
        }

        private async void SavePurchase()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = IsEditMode ? "جاري تحديث الفاتورة..." : "جاري حفظ الفاتورة...";
                ClearError();

                // Validate input
                if (!ValidatePurchase())
                    return;

                // Prepare purchase items
                Purchase.Items.Clear();
                foreach (var item in PurchaseItems)
                {
                    Purchase.Items.Add(item);
                }

                // Save purchase
                if (IsEditMode)
                {
                    var success = await _purchaseService.UpdatePurchaseAsync(Purchase);
                    if (success)
                    {
                        LoggingService.LogSystemEvent("تحديث فاتورة شراء", $"تم تحديث الفاتورة: {Purchase.InvoiceNumber}");
                        PurchaseSaved?.Invoke(Purchase);
                        RequestClose?.Invoke(true);
                    }
                    else
                    {
                        SetError("فشل في تحديث الفاتورة");
                    }
                }
                else
                {
                    var result = await _purchaseService.AddPurchaseAsync(Purchase);
                    if (result != null)
                    {
                        Purchase = result;
                        LoggingService.LogSystemEvent("إضافة فاتورة شراء", $"تم إضافة الفاتورة: {Purchase.InvoiceNumber}");
                        PurchaseSaved?.Invoke(Purchase);
                        RequestClose?.Invoke(true);
                    }
                    else
                    {
                        SetError("فشل في حفظ الفاتورة");
                    }
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ الفاتورة: {ex.Message}");
                LoggingService.LogError(ex, $"خطأ في حفظ الفاتورة: {Purchase.InvoiceNumber}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanSavePurchase()
        {
            return SelectedSupplier != null &&
                   PurchaseItems.Count > 0 &&
                   !IsLoading;
        }

        private bool ValidatePurchase()
        {
            if (SelectedSupplier == null)
            {
                SetError("يجب اختيار مورد");
                return false;
            }

            if (PurchaseItems.Count == 0)
            {
                SetError("يجب إضافة صنف واحد على الأقل");
                return false;
            }

            if (Purchase.Total <= 0)
            {
                SetError("مجموع الفاتورة يجب أن يكون أكبر من صفر");
                return false;
            }

            return true;
        }

        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }

        #endregion

        #region Events

        public event Action<Purchase>? PurchaseSaved;
        public event Action<bool>? RequestClose;

        #endregion
    }
}
