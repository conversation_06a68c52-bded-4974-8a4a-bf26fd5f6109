<Window x:Class="SalesManagementSystem.Views.NewFileWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إنشاء ملف جديد"
        Height="400" Width="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Green">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="📄" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="إنشاء ملف جديد" FontSize="18" 
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="20">
            <StackPanel>
                <GroupBox Header="اختر نوع الملف الجديد" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <RadioButton Content="📊 قاعدة بيانات جديدة" FontSize="14" Margin="0,10" IsChecked="True"/>
                        <RadioButton Content="📋 ملف تصدير Excel" FontSize="14" Margin="0,10"/>
                        <RadioButton Content="📄 تقرير PDF" FontSize="14" Margin="0,10"/>
                        <RadioButton Content="💾 نسخة احتياطية" FontSize="14" Margin="0,10"/>
                        <RadioButton Content="⚙️ ملف إعدادات" FontSize="14" Margin="0,10"/>
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="تفاصيل الملف">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الملف:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Margin="0,0,0,10" Text="ملف_جديد"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="المجلد:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBox Width="300" Text="C:\SalesSystem\Data"/>
                            <Button Content="..." Width="30" Margin="5,0,0,0"/>
                        </StackPanel>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="الوصف:" VerticalAlignment="Top" Margin="0,0,10,10"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Height="60" TextWrapping="Wrap" AcceptsReturn="True" 
                                Margin="0,0,0,10" Text="وصف الملف الجديد"/>

                        <CheckBox Grid.Row="3" Grid.Column="1" Content="إنشاء نسخة احتياطية تلقائياً" IsChecked="True" Margin="0,10,0,0"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </Grid>

        <!-- Footer -->
        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="✅ إنشاء الملف" Width="120" Height="35" Margin="10" 
                       Background="Green" Foreground="White" FontWeight="Bold" Click="Create_Click"/>
                <Button Content="👁️ معاينة" Width="100" Height="35" Margin="10" 
                       Background="Blue" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إلغاء" Width="100" Height="35" Margin="10" 
                       Background="Red" Foreground="White" FontWeight="Bold" Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
