<Window x:Class="SalesManagementSystem.Views.UnifiedSalesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="🛒 جميع المبيعات الموحدة" Height="750" Width="1500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Window.Resources>
        <!-- أنماط النصوص -->
        <Style x:Key="CenterAlignedTextBlock" TargetType="TextBlock">
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
        
        <!-- أنماط الأزرار -->
        <Style x:Key="FilterButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان الرئيسي -->
        <Border Grid.Row="0" Background="DarkBlue" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🛒" FontSize="32" Foreground="White" Margin="0,0,15,0" VerticalAlignment="Center"/>
                    <StackPanel>
                        <TextBlock Text="جميع المبيعات الموحدة" FontSize="24" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="عرض شامل لجميع عمليات البيع (فواتير + بيع سريع)" FontSize="12" Foreground="LightBlue"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="🔄 تحديث" Style="{StaticResource FilterButtonStyle}" 
                            Background="Green" Foreground="White" Click="RefreshButton_Click"/>
                    <Button Content="📊 تقرير" Style="{StaticResource FilterButtonStyle}" 
                            Background="Orange" Foreground="White" Click="GenerateReportButton_Click"/>
                    <Button Content="📤 تصدير" Style="{StaticResource FilterButtonStyle}" 
                            Background="Purple" Foreground="White" Click="ExportButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- إحصائيات سريعة -->
        <Border Grid.Row="1" Background="LightGray" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Border Background="Green" CornerRadius="5" Padding="15,8" Margin="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📊" FontSize="18" Foreground="White" Margin="0,0,8,0"/>
                        <TextBlock x:Name="TotalSalesCountText" Text="إجمالي العمليات: 0" 
                                   Foreground="White" FontWeight="Bold" FontSize="14"/>
                    </StackPanel>
                </Border>
                <Border Background="Blue" CornerRadius="5" Padding="15,8" Margin="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💰" FontSize="18" Foreground="White" Margin="0,0,8,0"/>
                        <TextBlock x:Name="TotalAmountText" Text="إجمالي المبيعات: 0.00 دج" 
                                   Foreground="White" FontWeight="Bold" FontSize="14"/>
                    </StackPanel>
                </Border>
                <Border Background="Orange" CornerRadius="5" Padding="15,8" Margin="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⚠️" FontSize="18" Foreground="White" Margin="0,0,8,0"/>
                        <TextBlock x:Name="PendingAmountText" Text="المبالغ المعلقة: 0.00 دج" 
                                   Foreground="White" FontWeight="Bold" FontSize="14"/>
                    </StackPanel>
                </Border>
                <Border Background="Red" CornerRadius="5" Padding="15,8" Margin="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💳" FontSize="18" Foreground="White" Margin="0,0,8,0"/>
                        <TextBlock x:Name="DebtAmountText" Text="إجمالي الديون: 0.00 دج" 
                                   Foreground="White" FontWeight="Bold" FontSize="14"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>

        <!-- شريط البحث والتصفية -->
        <Border Grid.Row="2" Background="LightBlue" Padding="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- الصف الأول: البحث والتواريخ -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="🔍 البحث:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                    <TextBox x:Name="SearchTextBox" Width="300" Height="35" 
                             TextChanged="SearchTextBox_TextChanged"
                             ToolTip="البحث برقم الفاتورة أو اسم العميل أو الملاحظات"
                             FontSize="14"/>
                    
                    <TextBlock Text="📅 من تاريخ:" VerticalAlignment="Center" Margin="30,0,10,0" FontWeight="Bold"/>
                    <DatePicker x:Name="FromDatePicker" Width="140" Height="35"
                               SelectedDateChanged="DateFilter_Changed"/>
                    
                    <TextBlock Text="📅 إلى تاريخ:" VerticalAlignment="Center" Margin="20,0,10,0" FontWeight="Bold"/>
                    <DatePicker x:Name="ToDatePicker" Width="140" Height="35"
                               SelectedDateChanged="DateFilter_Changed"/>
                </StackPanel>
                
                <!-- الصف الثاني: التصفية -->
                <StackPanel Grid.Row="1" Orientation="Horizontal">
                    <TextBlock Text="📋 نوع العملية:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                    <ComboBox x:Name="SaleTypeComboBox" Width="130" Height="35"
                              SelectionChanged="SaleTypeFilter_Changed" FontSize="14">
                        <ComboBoxItem Content="الكل" IsSelected="True"/>
                        <ComboBoxItem Content="فاتورة"/>
                        <ComboBoxItem Content="بيع سريع"/>
                    </ComboBox>
                    
                    <TextBlock Text="💳 حالة الدفع:" VerticalAlignment="Center" Margin="30,0,10,0" FontWeight="Bold"/>
                    <ComboBox x:Name="PaymentStatusComboBox" Width="130" Height="35"
                              SelectionChanged="PaymentStatusFilter_Changed" FontSize="14">
                        <ComboBoxItem Content="الكل" IsSelected="True"/>
                        <ComboBoxItem Content="مدفوع"/>
                        <ComboBoxItem Content="جزئي"/>
                        <ComboBoxItem Content="غير مدفوع"/>
                    </ComboBox>
                    
                    <TextBlock Text="👥 العميل:" VerticalAlignment="Center" Margin="30,0,10,0" FontWeight="Bold"/>
                    <ComboBox x:Name="CustomerComboBox" Width="200" Height="35"
                              SelectionChanged="CustomerFilter_Changed" FontSize="14">
                        <ComboBoxItem Content="جميع العملاء" IsSelected="True"/>
                    </ComboBox>
                    
                    <Button Content="🗑️ مسح التصفية" Style="{StaticResource FilterButtonStyle}" 
                            Background="Gray" Foreground="White" Click="ClearFiltersButton_Click" Margin="30,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- جدول المبيعات -->
        <DataGrid Grid.Row="3" x:Name="SalesDataGrid" 
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="All"
                  HeadersVisibility="Column"
                  AlternatingRowBackground="LightGray"
                  SelectionMode="Single"
                  FontSize="12"
                  RowHeight="35"
                  MouseDoubleClick="SalesDataGrid_MouseDoubleClick">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="🔢 رقم العملية" Binding="{Binding SaleNumber}" Width="120"/>
                <DataGridTextColumn Header="📋 النوع" Binding="{Binding SaleType}" Width="90"/>
                <DataGridTextColumn Header="👤 العميل" Binding="{Binding CustomerName}" Width="150"/>
                <DataGridTextColumn Header="📅 التاريخ" Binding="{Binding SaleDateFormatted}" Width="130"/>
                <DataGridTextColumn Header="💰 المبلغ الفرعي" Binding="{Binding SubtotalFormatted}" Width="110"/>
                <DataGridTextColumn Header="🏷️ الخصم" Binding="{Binding DiscountFormatted}" Width="80"/>
                <DataGridTextColumn Header="📊 الضريبة" Binding="{Binding TaxFormatted}" Width="80"/>
                <DataGridTextColumn Header="💳 الديون السابقة" Binding="{Binding PreviousDebtFormatted}" Width="120"/>
                <DataGridTextColumn Header="💵 المبلغ الإجمالي" Binding="{Binding TotalAmountFormatted}" Width="130"/>
                <DataGridTextColumn Header="✅ المبلغ المدفوع" Binding="{Binding PaidAmountFormatted}" Width="130"/>
                <DataGridTextColumn Header="⚠️ المبلغ المتبقي" Binding="{Binding RemainingAmountFormatted}" Width="130"/>
                <DataGridTextColumn Header="💳 طريقة الدفع" Binding="{Binding PaymentMethod}" Width="110"/>
                <DataGridTextColumn Header="📊 حالة الدفع" Binding="{Binding PaymentStatus}" Width="100"/>
                <DataGridTextColumn Header="📝 ملاحظات" Binding="{Binding Notes}" Width="150"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- شريط الحالة -->
        <Border Grid.Row="4" Background="DarkBlue" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="StatusTextBlock" Text="جاري التحميل..." 
                               Foreground="White" FontWeight="Bold" FontSize="14"/>
                    <TextBlock x:Name="FilterStatusTextBlock" Text="" 
                               Foreground="LightGreen" FontWeight="Bold" FontSize="14"
                               Margin="30,0,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="آخر تحديث: " Foreground="LightBlue" FontSize="12"/>
                    <TextBlock x:Name="LastUpdateTextBlock" Text="" Foreground="Yellow" FontSize="12"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
