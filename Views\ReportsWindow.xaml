<Window x:Class="SalesManagementSystem.Views.ReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        Title="التقارير المتقدمة" 
        Height="800" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
        
        <Style x:Key="ReportCard" TargetType="GroupBox" BasedOn="{StaticResource MaterialDesignCardGroupBox}">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="16"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ChartLine" 
                                           Width="32" Height="32" 
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="التقارير المتقدمة" 
                              Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                              VerticalAlignment="Center" 
                              Margin="12,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Command="{Binding ExportToExcelCommand}"
                           Style="{DynamicResource MaterialDesignOutlinedButton}"
                           Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExcel" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="Excel" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Command="{Binding ExportToPdfCommand}"
                           Style="{DynamicResource MaterialDesignOutlinedButton}"
                           Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FilePdf" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="PDF" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Command="{Binding PrintReportCommand}"
                           Style="{DynamicResource MaterialDesignOutlinedButton}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Printer" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="طباعة" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Filters -->
        <materialDesign:Card Grid.Row="1" Margin="16,0,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <DatePicker Grid.Column="0"
                           materialDesign:HintAssist.Hint="من تاريخ"
                           SelectedDate="{Binding StartDate}"
                           Style="{DynamicResource MaterialDesignOutlinedDatePicker}"
                           Margin="0,0,8,0"/>

                <DatePicker Grid.Column="1"
                           materialDesign:HintAssist.Hint="إلى تاريخ"
                           SelectedDate="{Binding EndDate}"
                           Style="{DynamicResource MaterialDesignOutlinedDatePicker}"
                           Margin="4,0"/>

                <ComboBox Grid.Column="2"
                         materialDesign:HintAssist.Hint="نوع التقرير"
                         ItemsSource="{Binding ReportTypes}"
                         SelectedItem="{Binding SelectedReportType}"
                         Style="{DynamicResource MaterialDesignOutlinedComboBox}"
                         Margin="8,0,0,0"/>

                <Button Grid.Column="3"
                       Command="{Binding RefreshReportsCommand}"
                       Style="{DynamicResource MaterialDesignRaisedButton}"
                       Margin="16,0,0,0"
                       Width="120">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" 
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"
                                               Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}, ConverterParameter=Inverted}"/>
                        <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                                   Width="16" Height="16"
                                   IsIndeterminate="True"
                                   Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Content -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Margin="16,0,16,16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Summary Cards -->
                <UniformGrid Grid.Row="0" Rows="1" Columns="4" Margin="0,0,0,16">
                    
                    <materialDesign:Card Margin="4" Padding="16">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                       Width="24" Height="24"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <TextBlock Text="إجمالي المبيعات" 
                                          Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                          Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock Text="{Binding TotalSales, StringFormat=C}" 
                                      Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <materialDesign:Card Margin="4" Padding="16">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="TrendingUp" 
                                                       Width="24" Height="24"
                                                       Foreground="#4CAF50"/>
                                <TextBlock Text="إجمالي الأرباح" 
                                          Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                          Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock Text="{Binding TotalProfit, StringFormat=C}" 
                                      Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                                      Foreground="#4CAF50"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <materialDesign:Card Margin="4" Padding="16">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="Receipt" 
                                                       Width="24" Height="24"
                                                       Foreground="#FF9800"/>
                                <TextBlock Text="عدد الطلبات" 
                                          Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                          Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock Text="{Binding TotalOrders}" 
                                      Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                                      Foreground="#FF9800"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <materialDesign:Card Margin="4" Padding="16">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="Calculator" 
                                                       Width="24" Height="24"
                                                       Foreground="#9C27B0"/>
                                <TextBlock Text="متوسط قيمة الطلب" 
                                          Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                          Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock Text="{Binding AverageOrderValue, StringFormat=C}" 
                                      Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                                      Foreground="#9C27B0"/>
                        </StackPanel>
                    </materialDesign:Card>
                </UniformGrid>

                <!-- Chart -->
                <GroupBox Grid.Row="1" 
                         Header="الرسم البياني" 
                         Style="{StaticResource ReportCard}"
                         Visibility="{Binding ChartData, Converter={StaticResource BoolToVisConverter}}">
                    <lvc:CartesianChart Series="{Binding ChartData}" 
                                       LegendLocation="Bottom"
                                       Height="300">
                        <lvc:CartesianChart.AxisX>
                            <lvc:Axis Title="الفترة" Labels="{Binding ChartLabels}"/>
                        </lvc:CartesianChart.AxisX>
                        <lvc:CartesianChart.AxisY>
                            <lvc:Axis Title="القيمة" LabelFormatter="{Binding ChartFormatter}"/>
                        </lvc:CartesianChart.AxisY>
                    </lvc:CartesianChart>
                </GroupBox>

                <!-- Data Grid -->
                <GroupBox Grid.Row="2" 
                         Header="{Binding SelectedReportType}" 
                         Style="{StaticResource ReportCard}">
                    
                    <TabControl Style="{DynamicResource MaterialDesignTabControl}">
                        
                        <!-- Sales Data -->
                        <TabItem Header="بيانات المبيعات" 
                                Visibility="{Binding SalesData.Count, Converter={StaticResource BoolToVisConverter}}">
                            <DataGrid ItemsSource="{Binding SalesData}"
                                     Style="{DynamicResource MaterialDesignDataGrid}"
                                     AutoGenerateColumns="False"
                                     IsReadOnly="True">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="التاريخ" Binding="{Binding SaleDate, StringFormat=dd/MM/yyyy}"/>
                                    <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}"/>
                                    <DataGridTextColumn Header="المبلغ الإجمالي" Binding="{Binding TotalAmount, StringFormat=C}"/>
                                    <DataGridTextColumn Header="الخصم" Binding="{Binding DiscountAmount, StringFormat=C}"/>
                                    <DataGridTextColumn Header="الضريبة" Binding="{Binding TaxAmount, StringFormat=C}"/>
                                    <DataGridTextColumn Header="صافي المبلغ" Binding="{Binding NetAmount, StringFormat=C}"/>
                                    <DataGridTextColumn Header="البائع" Binding="{Binding SalespersonName}"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>

                        <!-- Product Sales Data -->
                        <TabItem Header="مبيعات المنتجات" 
                                Visibility="{Binding ProductSalesData.Count, Converter={StaticResource BoolToVisConverter}}">
                            <DataGrid ItemsSource="{Binding ProductSalesData}"
                                     Style="{DynamicResource MaterialDesignDataGrid}"
                                     AutoGenerateColumns="False"
                                     IsReadOnly="True">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="اسم المنتج" Binding="{Binding ProductName}"/>
                                    <DataGridTextColumn Header="الكود" Binding="{Binding ProductCode}"/>
                                    <DataGridTextColumn Header="الفئة" Binding="{Binding Category}"/>
                                    <DataGridTextColumn Header="الكمية المباعة" Binding="{Binding TotalQuantitySold}"/>
                                    <DataGridTextColumn Header="إجمالي الإيرادات" Binding="{Binding TotalRevenue, StringFormat=C}"/>
                                    <DataGridTextColumn Header="متوسط السعر" Binding="{Binding AveragePrice, StringFormat=C}"/>
                                    <DataGridTextColumn Header="عدد الطلبات" Binding="{Binding NumberOfOrders}"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>

                        <!-- Customer Sales Data -->
                        <TabItem Header="مبيعات العملاء" 
                                Visibility="{Binding CustomerSalesData.Count, Converter={StaticResource BoolToVisConverter}}">
                            <DataGrid ItemsSource="{Binding CustomerSalesData}"
                                     Style="{DynamicResource MaterialDesignDataGrid}"
                                     AutoGenerateColumns="False"
                                     IsReadOnly="True">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="اسم العميل" Binding="{Binding CustomerName}"/>
                                    <DataGridTextColumn Header="الهاتف" Binding="{Binding CustomerPhone}"/>
                                    <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding CustomerEmail}"/>
                                    <DataGridTextColumn Header="عدد الطلبات" Binding="{Binding TotalOrders}"/>
                                    <DataGridTextColumn Header="إجمالي المبلغ" Binding="{Binding TotalSpent, StringFormat=C}"/>
                                    <DataGridTextColumn Header="متوسط قيمة الطلب" Binding="{Binding AverageOrderValue, StringFormat=C}"/>
                                    <DataGridTextColumn Header="آخر طلب" Binding="{Binding LastOrderDate, StringFormat=dd/MM/yyyy}"/>
                                    <DataGridTextColumn Header="نوع العميل" Binding="{Binding CustomerType}"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>

                        <!-- Inventory Data -->
                        <TabItem Header="بيانات المخزون" 
                                Visibility="{Binding InventoryData.Count, Converter={StaticResource BoolToVisConverter}}">
                            <DataGrid ItemsSource="{Binding InventoryData}"
                                     Style="{DynamicResource MaterialDesignDataGrid}"
                                     AutoGenerateColumns="False"
                                     IsReadOnly="True">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="اسم المنتج" Binding="{Binding ProductName}"/>
                                    <DataGridTextColumn Header="الكود" Binding="{Binding ProductCode}"/>
                                    <DataGridTextColumn Header="الفئة" Binding="{Binding Category}"/>
                                    <DataGridTextColumn Header="المخزون الحالي" Binding="{Binding CurrentStock}"/>
                                    <DataGridTextColumn Header="الحد الأدنى" Binding="{Binding MinimumStock}"/>
                                    <DataGridTextColumn Header="سعر التكلفة" Binding="{Binding CostPrice, StringFormat=C}"/>
                                    <DataGridTextColumn Header="سعر البيع" Binding="{Binding SalePrice, StringFormat=C}"/>
                                    <DataGridTextColumn Header="هامش الربح %" Binding="{Binding ProfitMargin, StringFormat=F2}"/>
                                    <DataGridTextColumn Header="حالة المخزون" Binding="{Binding StockStatus}"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>
                    </TabControl>
                </GroupBox>
            </Grid>
        </ScrollViewer>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}">
            <StackPanel HorizontalAlignment="Center" 
                       VerticalAlignment="Center">
                <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="48" Height="48"/>
                <TextBlock Text="{Binding LoadingMessage}" 
                          Foreground="White"
                          HorizontalAlignment="Center"
                          Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>

        <!-- Error Message -->
        <materialDesign:Snackbar Grid.RowSpan="3"
                               MessageQueue="{materialDesign:MessageQueue}"
                               x:Name="ErrorSnackbar"/>
    </Grid>
</Window>
