using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class CategoryService
    {
        private readonly DatabaseService _dbService;

        public CategoryService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<IEnumerable<Category>> GetAllCategoriesAsync()
        {
            const string sql = "SELECT * FROM Categories ORDER BY Name";
            try
            {
                var categories = await _dbService.QueryAsync<Category>(sql);
                LoggingService.LogDatabaseOperation("استعلام", "Categories", true);
                return categories;
            }
            catch (Exception ex)
            {
                LoggingService.LogDatabaseOperation("استعلام", "Categories", false, ex.Message);
                throw;
            }
        }

        public async Task<Category?> GetCategoryByIdAsync(int id)
        {
            const string sql = "SELECT * FROM Categories WHERE Id = @Id";
            try
            {
                var categories = await _dbService.QueryAsync<Category>(sql, new { Id = id });
                var category = categories.FirstOrDefault();
                LoggingService.LogDatabaseOperation("استعلام بالمعرف", "Categories", true);
                return category;
            }
            catch (Exception ex)
            {
                LoggingService.LogDatabaseOperation("استعلام بالمعرف", "Categories", false, ex.Message);
                throw;
            }
        }

        public async Task<Category?> GetCategoryByNameAsync(string name)
        {
            const string sql = "SELECT * FROM Categories WHERE Name = @Name";
            try
            {
                var categories = await _dbService.QueryAsync<Category>(sql, new { Name = name });
                var category = categories.FirstOrDefault();
                LoggingService.LogDatabaseOperation("استعلام بالاسم", "Categories", true);
                return category;
            }
            catch (Exception ex)
            {
                LoggingService.LogDatabaseOperation("استعلام بالاسم", "Categories", false, ex.Message);
                throw;
            }
        }

        public async Task<int> AddCategoryAsync(Category category)
        {
            const string sql = @"
                INSERT INTO Categories (Name, Description, CreatedAt)
                VALUES (@Name, @Description, @CreatedAt);
                SELECT last_insert_rowid();";

            try
            {
                category.CreatedAt = DateTime.Now;
                var id = await _dbService.QuerySingleAsync<int>(sql, category);

                LoggingService.LogDatabaseOperation("إضافة", "Categories", true);
                LoggingService.LogSystemEvent("إضافة فئة", $"تم إضافة الفئة: {category.Name}");

                return id;
            }
            catch (Exception ex)
            {
                LoggingService.LogDatabaseOperation("إضافة", "Categories", false, ex.Message);
                LoggingService.LogError(ex, $"خطأ في إضافة الفئة: {category.Name}");
                throw;
            }
        }

        public async Task<bool> UpdateCategoryAsync(Category category)
        {
            const string sql = @"
                UPDATE Categories
                SET Name = @Name, Description = @Description, UpdatedAt = @UpdatedAt
                WHERE Id = @Id";

            try
            {
                category.UpdatedAt = DateTime.Now;
                var rowsAffected = await _dbService.ExecuteAsync(sql, category);
                var success = rowsAffected > 0;

                LoggingService.LogDatabaseOperation("تحديث", "Categories", success);
                if (success)
                {
                    LoggingService.LogSystemEvent("تحديث فئة", $"تم تحديث الفئة: {category.Name}");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogDatabaseOperation("تحديث", "Categories", false, ex.Message);
                LoggingService.LogError(ex, $"خطأ في تحديث الفئة: {category.Name}");
                throw;
            }
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            try
            {
                // First check if category has products
                var hasProducts = await HasProductsAsync(id);
                if (hasProducts)
                {
                    throw new InvalidOperationException("لا يمكن حذف الفئة لأنها تحتوي على منتجات");
                }

                // Get category name for logging
                var category = await GetCategoryByIdAsync(id);
                var categoryName = category?.Name ?? "غير معروف";

                const string sql = "DELETE FROM Categories WHERE Id = @Id";
                var rowsAffected = await _dbService.ExecuteAsync(sql, new { Id = id });
                var success = rowsAffected > 0;

                LoggingService.LogDatabaseOperation("حذف", "Categories", success);
                if (success)
                {
                    LoggingService.LogSystemEvent("حذف فئة", $"تم حذف الفئة: {categoryName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogDatabaseOperation("حذف", "Categories", false, ex.Message);
                LoggingService.LogError(ex, $"خطأ في حذف الفئة بالمعرف: {id}");
                throw;
            }
        }

        public async Task<bool> HasProductsAsync(int categoryId)
        {
            const string sql = "SELECT COUNT(*) FROM Products WHERE CategoryId = @CategoryId";
            try
            {
                var count = await _dbService.QuerySingleAsync<int>(sql, new { CategoryId = categoryId });
                return count > 0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في فحص المنتجات للفئة: {categoryId}");
                throw;
            }
        }

        public async Task<int> GetProductCountAsync(int categoryId)
        {
            const string sql = "SELECT COUNT(*) FROM Products WHERE CategoryId = @CategoryId";
            try
            {
                var count = await _dbService.QuerySingleAsync<int>(sql, new { CategoryId = categoryId });
                return count;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في عد المنتجات للفئة: {categoryId}");
                throw;
            }
        }

        public async Task<IEnumerable<Category>> GetCategoriesWithProductCountAsync()
        {
            const string sql = @"
                SELECT c.*,
                       COALESCE(p.ProductCount, 0) as ProductCount
                FROM Categories c
                LEFT JOIN (
                    SELECT CategoryId, COUNT(*) as ProductCount
                    FROM Products
                    GROUP BY CategoryId
                ) p ON c.Id = p.CategoryId
                ORDER BY c.Name";

            try
            {
                var categories = await _dbService.QueryAsync<Category>(sql);
                LoggingService.LogDatabaseOperation("استعلام مع عدد المنتجات", "Categories", true);
                return categories;
            }
            catch (Exception ex)
            {
                LoggingService.LogDatabaseOperation("استعلام مع عدد المنتجات", "Categories", false, ex.Message);
                throw;
            }
        }

        public async Task<IEnumerable<Category>> SearchCategoriesAsync(string searchTerm)
        {
            const string sql = @"
                SELECT * FROM Categories
                WHERE Name LIKE @SearchTerm OR Description LIKE @SearchTerm
                ORDER BY Name";

            try
            {
                var searchPattern = $"%{searchTerm}%";
                var categories = await _dbService.QueryAsync<Category>(sql, new { SearchTerm = searchPattern });
                LoggingService.LogDatabaseOperation("بحث", "Categories", true);
                return categories;
            }
            catch (Exception ex)
            {
                LoggingService.LogDatabaseOperation("بحث", "Categories", false, ex.Message);
                throw;
            }
        }

        public async Task<bool> CategoryExistsAsync(string name, int? excludeId = null)
        {
            string sql = "SELECT COUNT(*) FROM Categories WHERE Name = @Name";
            object parameters = new { Name = name };

            if (excludeId.HasValue)
            {
                sql += " AND Id != @ExcludeId";
                parameters = new { Name = name, ExcludeId = excludeId.Value };
            }

            try
            {
                var count = await _dbService.QuerySingleAsync<int>(sql, parameters);
                return count > 0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في فحص وجود الفئة: {name}");
                throw;
            }
        }

        public async Task<IEnumerable<Category>> GetTopCategoriesByProductCountAsync(int limit = 10)
        {
            const string sql = @"
                SELECT c.*,
                       COALESCE(p.ProductCount, 0) as ProductCount
                FROM Categories c
                LEFT JOIN (
                    SELECT CategoryId, COUNT(*) as ProductCount
                    FROM Products
                    GROUP BY CategoryId
                ) p ON c.Id = p.CategoryId
                ORDER BY ProductCount DESC, c.Name
                LIMIT @Limit";

            try
            {
                var categories = await _dbService.QueryAsync<Category>(sql, new { Limit = limit });
                LoggingService.LogDatabaseOperation("أعلى الفئات", "Categories", true);
                return categories;
            }
            catch (Exception ex)
            {
                LoggingService.LogDatabaseOperation("أعلى الفئات", "Categories", false, ex.Message);
                throw;
            }
        }

        public async Task<int> GetTotalCategoriesAsync()
        {
            const string sql = "SELECT COUNT(*) FROM Categories";
            try
            {
                var count = await _dbService.QuerySingleAsync<int>(sql);
                return count;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في عد إجمالي الفئات");
                throw;
            }
        }

        public async Task<bool> InitializeDefaultCategoriesAsync()
        {
            try
            {
                var existingCount = await GetTotalCategoriesAsync();
                if (existingCount > 0)
                {
                    return true; // Categories already exist
                }

                var defaultCategories = new[]
                {
                    new Category { Name = "إلكترونيات", Description = "الأجهزة الإلكترونية والتقنية" },
                    new Category { Name = "ملابس", Description = "الملابس والأزياء" },
                    new Category { Name = "طعام ومشروبات", Description = "المواد الغذائية والمشروبات" },
                    new Category { Name = "كتب ومكتبة", Description = "الكتب والمواد التعليمية" },
                    new Category { Name = "رياضة", Description = "المعدات والملابس الرياضية" },
                    new Category { Name = "منزل وحديقة", Description = "أدوات المنزل والحديقة" },
                    new Category { Name = "صحة وجمال", Description = "منتجات الصحة والجمال" },
                    new Category { Name = "ألعاب", Description = "الألعاب والترفيه" },
                    new Category { Name = "سيارات", Description = "قطع غيار ومستلزمات السيارات" },
                    new Category { Name = "متنوعة", Description = "منتجات متنوعة أخرى" }
                };

                foreach (var category in defaultCategories)
                {
                    await AddCategoryAsync(category);
                }

                LoggingService.LogSystemEvent("تهيئة الفئات الافتراضية", "تم إنشاء الفئات الافتراضية بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تهيئة الفئات الافتراضية");
                return false;
            }
        }
    }
}
