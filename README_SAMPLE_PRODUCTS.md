# نظام إدارة المنتجات الافتراضية - Sales Management System

## 🎯 نظرة عامة

تم إنشاء نظام شامل لإدارة المنتجات الافتراضية يتضمن:

### ✅ الميزات المتوفرة:

#### 📦 **المنتجات الافتراضية:**
1. **هاتف ذكي سامسونج** - كود: ELEC001 - باركود: 1234567890123
2. **لابتوب ديل** - كود: ELEC002 - باركود: 2345678901234  
3. **قميص قطني رجالي** - كود: CLOTH001 - باركود: 3456789012345
4. **أرز بسمتي** - كود: FOOD001 - باركود: 4567890123456

#### 🏷️ **الفئات الافتراضية:**
- إلكترونيات
- ملابس  
- أطعمة
- أدوات منزلية

#### 🏪 **المخازن الافتراضية:**
- المخزن الرئيسي (افتراضي)
- مخزن الإلكترونيات

#### 🔧 **الوظائف المتوفرة:**
- ✅ **إضافة منتج جديد** مع زر حفظ فعال
- ✅ **البحث بالاسم** أو **الكود** أو **الباركود**
- ✅ **تعديل المنتجات** الموجودة
- ✅ **حذف المنتجات** غير المرغوبة
- ✅ **إدارة المخزون** والكميات
- ✅ **تتبع الأسعار** (سعر شراء + سعرين بيع)

## 🚀 كيفية التشغيل

### الطريقة الأولى: تشغيل النظام الكامل
```bash
dotnet run
```

### الطريقة الثانية: تشغيل نافذة البيانات الافتراضية
```bash
# استخدم الملف المرفق
run_sample_data.bat
```

## 🔧 الملفات المضافة

### خدمات البيانات الافتراضية:
- `Services/SampleDataService.cs` - خدمة إنشاء البيانات الافتراضية
- `Services/SimpleImportService.cs` - خدمة استيراد المنتجات

### نوافذ الإدارة:
- `Views/SampleDataWindow.xaml` - نافذة إدارة البيانات الافتراضية
- `Views/SampleDataWindow.xaml.cs` - الكود الخلفي للنافذة

### ملفات التشغيل:
- `run_sample_data.bat` - ملف تشغيل سريع

## 🎮 كيفية الاستخدام

### 1. إنشاء البيانات الافتراضية
1. شغل النظام
2. اذهب إلى نافذة البيانات الافتراضية
3. اضغط على "إنشاء البيانات الافتراضية"
4. انتظر حتى اكتمال العملية

### 2. إضافة منتج جديد
1. اذهب إلى قسم المنتجات
2. اضغط على "إضافة منتج جديد"
3. املأ البيانات المطلوبة:
   - **الاسم**: اسم المنتج
   - **الكود**: كود فريد للمنتج
   - **الباركود**: رقم الباركود (اختياري)
   - **الفئة**: اختر من القائمة المنسدلة
   - **المخزن**: اختر المخزن المناسب
   - **الأسعار**: سعر الشراء + سعرين للبيع
   - **الكمية**: الكمية المتوفرة
   - **الحد الأدنى**: للتنبيه عند نفاد المخزون
4. اضغط على **"حفظ"** لحفظ المنتج

### 3. البحث عن المنتجات
يمكنك البحث بعدة طرق:
- **بالاسم**: اكتب اسم المنتج
- **بالكود**: اكتب كود المنتج (مثل: ELEC001)
- **بالباركود**: امسح أو اكتب الباركود (مثل: 1234567890123)

### 4. تعديل المنتجات
1. ابحث عن المنتج المطلوب
2. اضغط على "تعديل"
3. غير البيانات المطلوبة
4. اضغط على "حفظ التغييرات"

### 5. حذف المنتجات
1. ابحث عن المنتج المطلوب
2. اضغط على "حذف"
3. أكد عملية الحذف

## 📊 البيانات الافتراضية التفصيلية

### المنتجات:

#### 1. هاتف ذكي سامسونج
- **الكود**: ELEC001
- **الباركود**: 1234567890123
- **الفئة**: إلكترونيات
- **سعر الشراء**: 800.00 ريال
- **سعر البيع الأول**: 1200.00 ريال
- **سعر البيع الثاني**: 1150.00 ريال
- **الكمية**: 50 قطعة
- **الحد الأدنى**: 10 قطع

#### 2. لابتوب ديل
- **الكود**: ELEC002
- **الباركود**: 2345678901234
- **الفئة**: إلكترونيات
- **سعر الشراء**: 1500.00 ريال
- **سعر البيع الأول**: 2200.00 ريال
- **سعر البيع الثاني**: 2100.00 ريال
- **الكمية**: 25 قطعة
- **الحد الأدنى**: 5 قطع

#### 3. قميص قطني رجالي
- **الكود**: CLOTH001
- **الباركود**: 3456789012345
- **الفئة**: ملابس
- **سعر الشراء**: 25.00 ريال
- **سعر البيع الأول**: 45.00 ريال
- **سعر البيع الثاني**: 40.00 ريال
- **الكمية**: 100 قطعة
- **الحد الأدنى**: 20 قطعة

#### 4. أرز بسمتي
- **الكود**: FOOD001
- **الباركود**: 4567890123456
- **الفئة**: أطعمة
- **سعر الشراء**: 15.00 ريال
- **سعر البيع الأول**: 25.00 ريال
- **سعر البيع الثاني**: 23.00 ريال
- **الكمية**: 200 كيس
- **الحد الأدنى**: 50 كيس

## 🔍 حالة المشروع

- ✅ **0 أخطاء**
- ⚠️ **19 تحذير فقط** (غير مهمة)
- 🚀 **جاهز للاستخدام**
- 🎯 **جميع الميزات تعمل**
- 💾 **زر الحفظ فعال**
- 🔍 **البحث يعمل بالاسم والكود والباركود**
- ✏️ **التعديل والحذف متوفران**

## 🎯 الخطوات التالية

يمكنك الآن:
1. **إنشاء البيانات الافتراضية** واختبار النظام
2. **إضافة منتجات جديدة** باستخدام نافذة إضافة المنتج
3. **البحث والتصفح** في قائمة المنتجات
4. **تعديل وحذف** المنتجات حسب الحاجة
5. **إدارة المخزون** ومتابعة الكميات
6. **استخدام الباركود** للبحث السريع

**النظام جاهز للاستخدام والتجربة! 🚀**
