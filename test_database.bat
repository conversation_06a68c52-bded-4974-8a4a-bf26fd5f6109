@echo off
echo ========================================
echo    اختبار قاعدة البيانات والمنتجات
echo ========================================
echo.
echo سيتم تشغيل نافذة اختبار شاملة لفحص:
echo 1. الاتصال بقاعدة البيانات
echo 2. وجود الجداول المطلوبة
echo 3. إنشاء البيانات الافتراضية
echo 4. تحميل المنتجات
echo 5. اختبار إضافة منتج
echo 6. اختبار نافذة إضافة المنتج
echo.
echo ========================================

cd /d "%~dp0"

echo بناء المشروع...
dotnet build --configuration Release --verbosity minimal

if %ERRORLEVEL% NEQ 0 (
    echo خطأ في بناء المشروع!
    pause
    exit /b 1
)

echo.
echo تشغيل نافذة الاختبار...
echo.

dotnet run --configuration Release

pause
