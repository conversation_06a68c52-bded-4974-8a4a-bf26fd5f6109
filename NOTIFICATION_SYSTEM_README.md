# نظام الإشعارات المتقدم - Sales Management System

## نظرة عامة

تم تطوير نظام إشعارات متقدم وشامل لنظام إدارة المبيعات يوفر تجربة مستخدم متميزة مع إشعارات ذكية ومرنة.

## الميزات الرئيسية

### 🔔 مركز الإشعارات (Notification Center)
- **عرض شامل**: جميع الإشعارات في مكان واحد
- **تصنيف ذكي**: تبويبات للكل، غير المقروءة، والمهمة
- **إدارة متقدمة**: تحديد كمقروء، حذف، تأجيل
- **واجهة تفاعلية**: تصميم Material Design مع RTL

### 🍞 إشعارات Toast
- **عرض فوري**: إشعارات منبثقة غير مزعجة
- **تصميم متجاوب**: ألوان وأيقونات حسب نوع الإشعار
- **تحكم تلقائي**: إغلاق تلقائي مع شريط تقدم
- **إجراءات سريعة**: عرض، تأجيل، إغلاق

### ⏰ الجدولة والتأجيل
- **إشعارات مجدولة**: تحديد وقت محدد للإشعار
- **تأجيل مرن**: خيارات سريعة أو وقت مخصص
- **فحص دوري**: مراقبة الإشعارات المجدولة

### ⚙️ إعدادات شاملة
- **تخصيص كامل**: تحكم في جميع جوانب الإشعارات
- **أولويات**: إعدادات مختلفة حسب الأولوية
- **ساعات العمل**: تحديد أوقات عرض الإشعارات
- **تنظيف تلقائي**: حذف الإشعارات القديمة

## البنية التقنية

### الخدمات (Services)
```
Services/
├── NotificationService.cs      # الخدمة الرئيسية للإشعارات
├── ToastManager.cs            # إدارة إشعارات Toast
└── SettingsService.cs         # إدارة الإعدادات
```

### العناصر المرئية (Controls)
```
Controls/
├── NotificationCenter.xaml    # مركز الإشعارات
├── ToastNotification.xaml     # عنصر Toast
└── [Related .cs files]
```

### النوافذ (Views)
```
Views/
├── NotificationSettingsWindow.xaml    # نافذة الإعدادات
├── SnoozeNotificationDialog.xaml      # نافذة التأجيل
└── [Related .cs files]
```

### النماذج (Models)
```
Models/
└── Notification.cs            # نموذج الإشعار المحسن
```

## قاعدة البيانات

### جدول الإشعارات
```sql
CREATE TABLE Notifications (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Title TEXT NOT NULL,
    Message TEXT NOT NULL,
    Type TEXT NOT NULL,
    Priority TEXT DEFAULT 'Normal',
    IsRead BOOLEAN DEFAULT 0,
    IsScheduled BOOLEAN DEFAULT 0,
    ScheduledTime TEXT,
    ActionUrl TEXT,
    CreatedAt TEXT NOT NULL
);
```

## أنواع الإشعارات

### 📦 إشعارات المخزون
- **مخزون منخفض**: تنبيه عند انخفاض الكمية
- **نفاد المخزون**: تحذير عند انتهاء المنتج
- **إعادة الطلب**: تذكير بإعادة طلب المنتجات

### 💰 إشعارات المالية
- **استحقاق دفع**: تذكير بالمدفوعات المستحقة
- **دفع للموردين**: تنبيه بمدفوعات الموردين
- **تقارير مالية**: إشعارات التقارير الدورية

### 🔧 إشعارات النظام
- **النسخ الاحتياطي**: حالة عمليات النسخ
- **التحديثات**: إشعارات التحديثات المتاحة
- **الأمان**: تنبيهات أمنية

## الاستخدام

### إضافة إشعار جديد
```csharp
await notificationService.AddNotificationAsync(new Notification
{
    Title = "مخزون منخفض",
    Message = "المنتج X أصبح مخزونه أقل من الحد الأدنى",
    Type = "LowStock",
    Priority = "High"
});
```

### عرض Toast سريع
```csharp
toastManager.ShowSuccess("تم الحفظ", "تم حفظ البيانات بنجاح");
toastManager.ShowError("خطأ", "فشل في حفظ البيانات");
```

### جدولة إشعار
```csharp
await notificationService.ScheduleNotificationAsync(
    notification, 
    DateTime.Now.AddHours(2)
);
```

## الإعدادات

### إعدادات عامة
- `EnableNotifications`: تفعيل/إلغاء الإشعارات
- `ShowToastNotifications`: عرض Toast
- `ToastDuration`: مدة عرض Toast
- `MaxToasts`: الحد الأقصى للإشعارات المعروضة

### إعدادات الأولوية
- `ShowLowPriorityToast`: عرض Toast للأولوية المنخفضة
- `PlaySoundForHighPriority`: صوت للأولوية العالية
- `NeverAutoDismissCritical`: عدم إغلاق الحرجة تلقائياً

### إعدادات الجدولة
- `EnableScheduledNotifications`: تفعيل الجدولة
- `WorkHoursStart/End`: ساعات العمل
- `AllowAfterHoursNotifications`: السماح خارج ساعات العمل

## التخصيص

### إضافة نوع إشعار جديد
1. إضافة النوع في `NotificationType` enum
2. تحديث `IconKind` و `ColorBrush` properties
3. إضافة معالج في `HandleNotificationAction`

### تخصيص مظهر Toast
- تعديل `ToastNotification.xaml` للتصميم
- تحديث `NotificationColorBrush` للألوان
- إضافة رسوم متحركة جديدة

## الأداء والتحسين

### التحسينات المطبقة
- **تحميل تدريجي**: تحميل الإشعارات حسب الحاجة
- **ذاكرة التخزين المؤقت**: تخزين الإعدادات مؤقتاً
- **فحص دوري محسن**: فحص الإشعارات المجدولة كل دقيقة
- **تنظيف تلقائي**: حذف الإشعارات القديمة

### مراقبة الأداء
- تسجيل الأخطاء والأحداث
- مراقبة استخدام الذاكرة
- قياس أوقات الاستجابة

## الأمان

### حماية البيانات
- تشفير الإشعارات الحساسة
- التحقق من صحة البيانات
- منع SQL Injection

### التحكم في الوصول
- صلاحيات عرض الإشعارات
- تحكم في إعدادات النظام
- تسجيل العمليات الحساسة

## استكشاف الأخطاء

### مشاكل شائعة
1. **عدم ظهور Toast**: تحقق من إعدادات العرض
2. **عدم عمل الجدولة**: تأكد من تفعيل الخدمة
3. **بطء في التحميل**: تحقق من حجم قاعدة البيانات

### سجلات الأخطاء
- جميع الأخطاء مسجلة في `LoggingService`
- تفاصيل كاملة للتشخيص
- إمكانية تصدير السجلات

## التطوير المستقبلي

### ميزات مخططة
- [ ] إشعارات push للهواتف المحمولة
- [ ] تكامل مع البريد الإلكتروني
- [ ] إشعارات صوتية متقدمة
- [ ] تحليلات الإشعارات
- [ ] API للتطبيقات الخارجية

### تحسينات مقترحة
- [ ] ضغط قاعدة البيانات
- [ ] تحسين الرسوم المتحركة
- [ ] دعم الثيمات المخصصة
- [ ] تصدير/استيراد الإعدادات

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من سجلات الأخطاء
3. تواصل مع فريق التطوير

---

**تم تطوير هذا النظام بعناية لتوفير أفضل تجربة إشعارات ممكنة للمستخدمين العرب.**
