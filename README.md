# نظام إدارة المبيعات - Sales Management System

نظام شامل لإدارة المبيعات والمخزون مبني بتقنية WPF و .NET 6

## المميزات الرئيسية

### 📊 لوحة التحكم
- عرض إحصائيات المبيعات والأرباح
- مراقبة المخزون المنخفض
- عرض أفضل المنتجات مبيعاً
- إحصائيات شهرية وسنوية

### 🛍️ إدارة المبيعات
- إنشاء فواتير البيع
- إدارة العملاء
- تتبع المدفوعات
- طباعة الفواتير

### 📦 إدارة المخزون
- إضافة وتعديل المنتجات
- تصنيف المنتجات
- تتبع الكميات
- تنبيهات المخزون المنخفض

### 🏪 إدارة المشتريات
- فواتير الشراء من الموردين
- إدارة الموردين
- تتبع المدفوعات للموردين

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء
- معلومات الاتصال والعناوين
- تتبع الأرصدة المستحقة

### 👨‍💼 إدارة الموظفين
- بيانات الموظفين
- الأدوار والصلاحيات
- تتبع الأداء

### 💰 إدارة المصاريف
- تسجيل المصاريف التشغيلية
- تصنيف المصاريف
- تقارير المصاريف

### 📈 التقارير والتحليلات
- تقارير المبيعات
- تقارير المخزون
- تقارير الأرباح والخسائر
- تقارير العملاء والموردين

### ⚙️ الإعدادات
- إعدادات الشركة
- إعدادات النظام
- إدارة المستخدمين
- النسخ الاحتياطي

## التقنيات المستخدمة

- **Framework**: .NET 6 Windows
- **UI**: WPF (Windows Presentation Foundation)
- **Database**: SQLite
- **ORM**: Dapper
- **UI Libraries**: 
  - MahApps.Metro
  - MaterialDesignThemes
- **Charts**: LiveChartsCore
- **Validation**: FluentValidation
- **Logging**: NLog
- **Architecture**: MVVM Pattern

## متطلبات النظام

- Windows 10 أو أحدث
- .NET 6 Runtime
- 100 MB مساحة فارغة على القرص الصلب
- 2 GB RAM (الحد الأدنى)

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd SalesManagementSystem
```

### 2. استعادة الحزم
```bash
dotnet restore
```

### 3. بناء المشروع
```bash
dotnet build
```

### 4. تشغيل التطبيق
```bash
dotnet run
```

## هيكل المشروع

```
SalesManagementSystem/
├── Models/                 # نماذج البيانات
├── Views/                  # واجهات المستخدم
├── ViewModels/            # نماذج العرض (MVVM)
├── Services/              # خدمات الأعمال
├── Resources/             # الموارد والترجمات
│   ├── Localization/      # ملفات الترجمة
│   └── Themes/           # ملفات المظاهر
├── Assets/               # الصور والأيقونات
└── Logs/                # ملفات السجلات
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:

- **Products**: المنتجات
- **Categories**: فئات المنتجات
- **Customers**: العملاء
- **Suppliers**: الموردين
- **Sales**: المبيعات
- **SaleItems**: عناصر المبيعات
- **Purchases**: المشتريات
- **PurchaseItems**: عناصر المشتريات
- **Employees**: الموظفين
- **Expenses**: المصاريف
- **Settings**: الإعدادات
- **Users**: المستخدمين

## المميزات المتقدمة

### 🌐 دعم متعدد اللغات
- العربية (افتراضي)
- الإنجليزية
- واجهة RTL للعربية

### 🎨 المظاهر
- المظهر الفاتح (افتراضي)
- المظهر الداكن
- ألوان Material Design

### 📝 نظام التسجيل
- تسجيل جميع العمليات
- ملفات سجل منفصلة للأخطاء
- تنظيف تلقائي للسجلات القديمة

### 💾 النسخ الاحتياطي
- نسخ احتياطي تلقائي
- نسخ احتياطي يدوي
- استعادة من النسخ الاحتياطية

### 🔒 الأمان
- تشفير كلمات المرور
- تسجيل العمليات الأمنية
- صلاحيات المستخدمين

## الاستخدام

### البدء السريع

1. **تشغيل التطبيق**: افتح التطبيق وستظهر لوحة التحكم
2. **إضافة منتجات**: انتقل إلى قسم المنتجات وأضف منتجاتك
3. **إضافة عملاء**: أضف بيانات عملائك في قسم العملاء
4. **إنشاء فاتورة**: انتقل إلى قسم المبيعات وأنشئ فاتورة جديدة

### نصائح الاستخدام

- استخدم البحث السريع للعثور على المنتجات والعملاء
- راجع تقارير المخزون بانتظام
- قم بعمل نسخة احتياطية دورية
- راجع الإعدادات لتخصيص النظام حسب احتياجاتك

## المساهمة في التطوير

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى البranch
5. إنشاء Pull Request

## الدعم والمساعدة

- **الوثائق**: راجع ملفات الوثائق في مجلد `docs/`
- **المشاكل**: أبلغ عن المشاكل في قسم Issues
- **الاقتراحات**: نرحب باقتراحاتكم لتحسين النظام

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## تاريخ الإصدارات

### الإصدار 1.0.0
- الإصدار الأولي
- جميع المميزات الأساسية
- دعم العربية والإنجليزية
- نظام التسجيل والنسخ الاحتياطي

---

**تم تطوير هذا النظام بعناية لتلبية احتياجات الشركات الصغيرة والمتوسطة في إدارة مبيعاتها ومخزونها بكفاءة عالية.**
