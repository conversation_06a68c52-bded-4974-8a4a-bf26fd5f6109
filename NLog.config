<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="c:\temp\internal-nlog-AspNetCore.txt">

  <!-- enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
  </extensions>

  <!-- the targets to write to -->
  <targets>
    <!-- File Target -->
    <target xsi:type="File" name="allfile"
            fileName="Logs/SalesSystem-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}"
            encoding="utf-8"
            createDirs="true"
            maxArchiveFiles="30"
            archiveEvery="Day"
            archiveNumbering="Date" />

    <!-- Console Target -->
    <target xsi:type="Console" name="console"
            layout="${time} [${level:uppercase=true}] ${message} ${exception:format=tostring}" />

    <!-- Error File Target -->
    <target xsi:type="File" name="errorfile"
            fileName="Logs/SalesSystem-errors-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}"
            encoding="utf-8"
            createDirs="true"
            maxArchiveFiles="30"
            archiveEvery="Day"
            archiveNumbering="Date" />

    <!-- Performance File Target -->
    <target xsi:type="File" name="performancefile"
            fileName="Logs/SalesSystem-performance-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message}"
            encoding="utf-8"
            createDirs="true"
            maxArchiveFiles="7"
            archiveEvery="Day"
            archiveNumbering="Date" />

    <!-- Security File Target -->
    <target xsi:type="File" name="securityfile"
            fileName="Logs/SalesSystem-security-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message}"
            encoding="utf-8"
            createDirs="true"
            maxArchiveFiles="90"
            archiveEvery="Day"
            archiveNumbering="Date" />

    <!-- Database Target (Optional) -->
    <target xsi:type="Database" name="database"
            connectionString="Data Source=SalesManagement.db"
            commandText="INSERT INTO Logs(Date, Level, Logger, Message, Exception) VALUES(@Date, @Level, @Logger, @Message, @Exception)">
      <parameter name="@Date" layout="${date}" />
      <parameter name="@Level" layout="${level}" />
      <parameter name="@Logger" layout="${logger}" />
      <parameter name="@Message" layout="${message}" />
      <parameter name="@Exception" layout="${exception:tostring}" />
    </target>
  </targets>

  <!-- rules to map from logger name to target -->
  <rules>
    <!-- All logs, including from Microsoft -->
    <logger name="*" minlevel="Debug" writeTo="allfile" />
    
    <!-- Console output for Info and above -->
    <logger name="*" minlevel="Info" writeTo="console" />
    
    <!-- Error logs only -->
    <logger name="*" minlevel="Error" writeTo="errorfile" />
    
    <!-- Performance logs -->
    <logger name="SalesManagementSystem.Services.LoggingService" level="Debug" writeTo="performancefile" final="true" />
    
    <!-- Security logs -->
    <logger name="Security.*" minlevel="Warn" writeTo="securityfile" />
    
    <!-- Database logs (uncomment if you want to log to database) -->
    <!-- <logger name="*" minlevel="Error" writeTo="database" /> -->
    
    <!-- Skip non-critical Microsoft logs and so log only own logs (BlackHole) -->
    <logger name="Microsoft.*" maxlevel="Info" final="true" />
    <logger name="System.Net.Http.*" maxlevel="Info" final="true" />
  </rules>
</nlog>
