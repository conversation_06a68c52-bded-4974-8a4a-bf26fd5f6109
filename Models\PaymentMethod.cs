using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج طريقة الدفع الإلكترونية
    /// </summary>
    public class PaymentMethod : INotifyPropertyChanged
    {
        private int _id;
        private string _name = string.Empty;
        private string _code = string.Empty;
        private string _description = string.Empty;
        private PaymentType _paymentType = PaymentType.Card;
        private PaymentProvider _provider = PaymentProvider.Local;
        private string _providerName = string.Empty;
        private string _apiEndpoint = string.Empty;
        private string _merchantId = string.Empty;
        private string _apiKey = string.Empty;
        private string _secretKey = string.Empty;
        private string _publicKey = string.Empty;
        private bool _isTestMode;
        private bool _isActive = true;
        private bool _requiresVerification;
        private decimal _transactionFee;
        private decimal _transactionFeePercentage;
        private decimal _minimumAmount;
        private decimal _maximumAmount;
        private string _supportedCurrencies = "SAR";
        private string _supportedCountries = "Saudi Arabia";
        private string _logoUrl = string.Empty;
        private string _instructions = string.Empty;
        private int _sortOrder;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private string _createdBy = string.Empty;
        private ObservableCollection<PaymentTransaction> _transactions = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public PaymentType PaymentType
        {
            get => _paymentType;
            set
            {
                if (_paymentType != value)
                {
                    _paymentType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PaymentTypeDisplay));
                    OnPropertyChanged(nameof(PaymentTypeIcon));
                }
            }
        }

        public PaymentProvider Provider
        {
            get => _provider;
            set
            {
                if (_provider != value)
                {
                    _provider = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ProviderDisplay));
                }
            }
        }

        public string ProviderName
        {
            get => _providerName;
            set
            {
                if (_providerName != value)
                {
                    _providerName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ApiEndpoint
        {
            get => _apiEndpoint;
            set
            {
                if (_apiEndpoint != value)
                {
                    _apiEndpoint = value;
                    OnPropertyChanged();
                }
            }
        }

        public string MerchantId
        {
            get => _merchantId;
            set
            {
                if (_merchantId != value)
                {
                    _merchantId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ApiKey
        {
            get => _apiKey;
            set
            {
                if (_apiKey != value)
                {
                    _apiKey = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SecretKey
        {
            get => _secretKey;
            set
            {
                if (_secretKey != value)
                {
                    _secretKey = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PublicKey
        {
            get => _publicKey;
            set
            {
                if (_publicKey != value)
                {
                    _publicKey = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsTestMode
        {
            get => _isTestMode;
            set
            {
                if (_isTestMode != value)
                {
                    _isTestMode = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool RequiresVerification
        {
            get => _requiresVerification;
            set
            {
                if (_requiresVerification != value)
                {
                    _requiresVerification = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal TransactionFee
        {
            get => _transactionFee;
            set
            {
                if (_transactionFee != value)
                {
                    _transactionFee = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTransactionFee));
                }
            }
        }

        public decimal TransactionFeePercentage
        {
            get => _transactionFeePercentage;
            set
            {
                if (_transactionFeePercentage != value)
                {
                    _transactionFeePercentage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTransactionFeePercentage));
                }
            }
        }

        public decimal MinimumAmount
        {
            get => _minimumAmount;
            set
            {
                if (_minimumAmount != value)
                {
                    _minimumAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedMinimumAmount));
                }
            }
        }

        public decimal MaximumAmount
        {
            get => _maximumAmount;
            set
            {
                if (_maximumAmount != value)
                {
                    _maximumAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedMaximumAmount));
                }
            }
        }

        public string SupportedCurrencies
        {
            get => _supportedCurrencies;
            set
            {
                if (_supportedCurrencies != value)
                {
                    _supportedCurrencies = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SupportedCountries
        {
            get => _supportedCountries;
            set
            {
                if (_supportedCountries != value)
                {
                    _supportedCountries = value;
                    OnPropertyChanged();
                }
            }
        }

        public string LogoUrl
        {
            get => _logoUrl;
            set
            {
                if (_logoUrl != value)
                {
                    _logoUrl = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasLogo));
                }
            }
        }

        public string Instructions
        {
            get => _instructions;
            set
            {
                if (_instructions != value)
                {
                    _instructions = value;
                    OnPropertyChanged();
                }
            }
        }

        public int SortOrder
        {
            get => _sortOrder;
            set
            {
                if (_sortOrder != value)
                {
                    _sortOrder = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<PaymentTransaction> Transactions
        {
            get => _transactions;
            set
            {
                if (_transactions != value)
                {
                    _transactions = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public string PaymentTypeDisplay
        {
            get
            {
                return PaymentType switch
                {
                    PaymentType.Cash => "نقدي",
                    PaymentType.Card => "بطاقة",
                    PaymentType.BankTransfer => "تحويل بنكي",
                    PaymentType.Check => "شيك",
                    PaymentType.Credit => "آجل",
                    PaymentType.Digital => "محفظة رقمية",
                    _ => "غير محدد"
                };
            }
        }

        public string ProviderDisplay
        {
            get
            {
                return Provider switch
                {
                    PaymentProvider.Local => "محلي",
                    PaymentProvider.Visa => "فيزا",
                    PaymentProvider.Mastercard => "ماستركارد",
                    PaymentProvider.Mada => "مدى",
                    PaymentProvider.ApplePay => "Apple Pay",
                    PaymentProvider.SamsungPay => "Samsung Pay",
                    PaymentProvider.PayPal => "PayPal",
                    PaymentProvider.Stripe => "Stripe",
                    PaymentProvider.Square => "Square",
                    PaymentProvider.Tabby => "Tabby",
                    PaymentProvider.Tamara => "Tamara",
                    PaymentProvider.STCPay => "STC Pay",
                    PaymentProvider.Urpay => "Urpay",
                    _ => ProviderName
                };
            }
        }

        public string PaymentTypeIcon
        {
            get
            {
                return PaymentType switch
                {
                    PaymentType.Cash => "Cash",
                    PaymentType.Card => "CreditCard",
                    PaymentType.BankTransfer => "Bank",
                    PaymentType.Check => "CheckboxMarked",
                    PaymentType.Credit => "ClockOutline",
                    PaymentType.Digital => "Wallet",
                    _ => "Payment"
                };
            }
        }

        public bool HasLogo => !string.IsNullOrEmpty(LogoUrl);

        // Formatted Properties
        public string FormattedTransactionFee => TransactionFee > 0 ? $"{TransactionFee:C}" : "مجاني";
        public string FormattedTransactionFeePercentage => TransactionFeePercentage > 0 ? $"{TransactionFeePercentage:P}" : "";
        public string FormattedMinimumAmount => MinimumAmount > 0 ? $"الحد الأدنى: {MinimumAmount:C}" : "";
        public string FormattedMaximumAmount => MaximumAmount > 0 ? $"الحد الأقصى: {MaximumAmount:C}" : "";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        /// <summary>
        /// حساب رسوم المعاملة
        /// </summary>
        public decimal CalculateTransactionFee(decimal amount)
        {
            decimal fee = TransactionFee;

            if (TransactionFeePercentage > 0)
                fee += amount * TransactionFeePercentage;

            return Math.Max(0, fee);
        }

        /// <summary>
        /// التحقق من صحة المبلغ
        /// </summary>
        public bool IsAmountValid(decimal amount)
        {
            if (MinimumAmount > 0 && amount < MinimumAmount)
                return false;

            if (MaximumAmount > 0 && amount > MaximumAmount)
                return false;

            return true;
        }

        /// <summary>
        /// التحقق من دعم العملة
        /// </summary>
        public bool SupportsCurrency(string currency)
        {
            if (string.IsNullOrEmpty(SupportedCurrencies))
                return true;

            var currencies = SupportedCurrencies.Split(',', StringSplitOptions.RemoveEmptyEntries);
            return currencies.Any(c => c.Trim().Equals(currency, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// التحقق من دعم البلد
        /// </summary>
        public bool SupportsCountry(string country)
        {
            if (string.IsNullOrEmpty(SupportedCountries))
                return true;

            var countries = SupportedCountries.Split(',', StringSplitOptions.RemoveEmptyEntries);
            return countries.Any(c => c.Trim().Equals(country, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// إنشاء معاملة دفع جديدة
        /// </summary>
        public PaymentTransaction CreateTransaction(decimal amount, string currency, string orderId)
        {
            return new PaymentTransaction
            {
                PaymentMethodId = Id,
                Amount = amount,
                Currency = currency,
                OrderId = orderId,
                Status = PaymentStatus.Pending,
                CreatedAt = DateTime.Now,
                TransactionFee = CalculateTransactionFee(amount)
            };
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// معاملة الدفع
    /// </summary>
    public class PaymentTransaction
    {
        public int Id { get; set; }
        public int PaymentMethodId { get; set; }
        public string TransactionId { get; set; } = string.Empty;
        public string OrderId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "SAR";
        public decimal TransactionFee { get; set; }
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;
        public string StatusMessage { get; set; } = string.Empty;
        public string GatewayResponse { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? ProcessedAt { get; set; }
        public string ProcessedBy { get; set; } = string.Empty;

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    PaymentStatus.Pending => "في الانتظار",
                    PaymentStatus.Paid => "مدفوع",
                    PaymentStatus.PartiallyPaid => "مدفوع جزئياً",
                    PaymentStatus.Overdue => "متأخر",
                    PaymentStatus.Cancelled => "ملغي",
                    PaymentStatus.Refunded => "مسترد",
                    _ => "غير محدد"
                };
            }
        }

        public string FormattedAmount => $"{Amount:C}";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");
    }

    #endregion

    #region Enums



    public enum PaymentProvider
    {
        Local,              // محلي
        Visa,               // فيزا
        Mastercard,         // ماستركارد
        Mada,               // مدى
        ApplePay,           // Apple Pay
        SamsungPay,         // Samsung Pay
        PayPal,             // PayPal
        Stripe,             // Stripe
        Square,             // Square
        Tabby,              // Tabby
        Tamara,             // Tamara
        STCPay,             // STC Pay
        Urpay               // Urpay
    }



    #endregion

    #region Validation

    public class PaymentMethodValidator : AbstractValidator<PaymentMethod>
    {
        public PaymentMethodValidator()
        {
            RuleFor(p => p.Name)
                .NotEmpty().WithMessage("اسم طريقة الدفع مطلوب")
                .MaximumLength(100).WithMessage("اسم طريقة الدفع لا يمكن أن يتجاوز 100 حرف");

            RuleFor(p => p.Code)
                .NotEmpty().WithMessage("كود طريقة الدفع مطلوب")
                .MaximumLength(50).WithMessage("كود طريقة الدفع لا يمكن أن يتجاوز 50 حرف");

            RuleFor(p => p.TransactionFee)
                .GreaterThanOrEqualTo(0).WithMessage("رسوم المعاملة يجب أن تكون أكبر من أو تساوي صفر");

            RuleFor(p => p.TransactionFeePercentage)
                .InclusiveBetween(0, 1).WithMessage("نسبة رسوم المعاملة يجب أن تكون بين 0 و 1");

            RuleFor(p => p.MinimumAmount)
                .GreaterThanOrEqualTo(0).WithMessage("الحد الأدنى للمبلغ يجب أن يكون أكبر من أو يساوي صفر");

            RuleFor(p => p.MaximumAmount)
                .GreaterThan(p => p.MinimumAmount).When(p => p.MaximumAmount > 0)
                .WithMessage("الحد الأقصى للمبلغ يجب أن يكون أكبر من الحد الأدنى");

            RuleFor(p => p.ApiEndpoint)
                .Must(BeValidUrl).When(p => !string.IsNullOrEmpty(p.ApiEndpoint))
                .WithMessage("رابط API غير صحيح");
        }

        private bool BeValidUrl(string url)
        {
            return Uri.TryCreate(url, UriKind.Absolute, out _);
        }
    }

    #endregion
}
