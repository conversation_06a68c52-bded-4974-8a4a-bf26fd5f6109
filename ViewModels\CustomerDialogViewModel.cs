using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class CustomerDialogViewModel : BaseViewModel
    {
        #region Services

        private readonly DatabaseService _dbService;
        private readonly CustomerService _customerService;

        #endregion

        #region Properties

        private Customer _customer = new();
        public Customer Customer
        {
            get => _customer;
            set
            {
                if (SetProperty(ref _customer, value))
                {
                    OnCustomerChanged();
                }
            }
        }

        private ObservableCollection<string> _paymentTerms = new();
        public ObservableCollection<string> PaymentTerms
        {
            get => _paymentTerms;
            set => SetProperty(ref _paymentTerms, value);
        }

        private ObservableCollection<string> _customerTypes = new();
        public ObservableCollection<string> CustomerTypes
        {
            get => _customerTypes;
            set => SetProperty(ref _customerTypes, value);
        }

        private string _windowTitle = "إضافة عميل جديد";
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }

        private string _headerIcon = "Account";
        public string HeaderIcon
        {
            get => _headerIcon;
            set => SetProperty(ref _headerIcon, value);
        }

        private bool _isCodeEditable = true;
        public bool IsCodeEditable
        {
            get => _isCodeEditable;
            set => SetProperty(ref _isCodeEditable, value);
        }

        private bool _isEditMode;
        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                if (SetProperty(ref _isEditMode, value))
                {
                    UpdateWindowTitle();
                    IsCodeEditable = !value;
                }
            }
        }

        public bool IsOverCreditLimit => Customer.Balance > Customer.CreditLimit && Customer.CreditLimit > 0;

        #endregion

        #region Commands

        private DelegateCommand? _saveCommand;
        public DelegateCommand SaveCommand => _saveCommand ??= new DelegateCommand(SaveCustomer, CanSaveCustomer);

        private DelegateCommand? _cancelCommand;
        public DelegateCommand CancelCommand => _cancelCommand ??= new DelegateCommand(Cancel);

        #endregion

        #region Constructor

        public CustomerDialogViewModel()
        {
            _dbService = new DatabaseService();
            _customerService = new CustomerService(_dbService);

            InitializeData();
        }

        public CustomerDialogViewModel(Customer customer) : this()
        {
            Customer = customer.Clone();
            IsEditMode = true;
        }

        #endregion

        #region Methods

        private void InitializeData()
        {
            InitializePaymentTerms();
            InitializeCustomerTypes();
        }

        private void InitializePaymentTerms()
        {
            PaymentTerms.Clear();
            PaymentTerms.Add("نقدي");
            PaymentTerms.Add("آجل 15 يوم");
            PaymentTerms.Add("آجل 30 يوم");
            PaymentTerms.Add("آجل 45 يوم");
            PaymentTerms.Add("آجل 60 يوم");
            PaymentTerms.Add("آجل 90 يوم");
        }

        private void InitializeCustomerTypes()
        {
            CustomerTypes.Clear();
            CustomerTypes.Add("فرد");
            CustomerTypes.Add("شركة");
            CustomerTypes.Add("مؤسسة");
            CustomerTypes.Add("جهة حكومية");
        }

        private void OnCustomerChanged()
        {
            OnPropertyChanged(nameof(IsOverCreditLimit));
            SaveCommand.RaiseCanExecuteChanged();
        }

        private void UpdateWindowTitle()
        {
            WindowTitle = IsEditMode ? "تعديل العميل" : "إضافة عميل جديد";
            HeaderIcon = IsEditMode ? "AccountEdit" : "Account";
        }

        #endregion

        #region Command Handlers

        private async void SaveCustomer()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = IsEditMode ? "جاري تحديث العميل..." : "جاري حفظ العميل...";
                ClearError();

                // Validate input
                if (!ValidateCustomer())
                    return;

                // Generate code if new customer
                if (!IsEditMode && string.IsNullOrWhiteSpace(Customer.Code))
                {
                    Customer.Code = await GenerateCustomerCodeAsync();
                }

                // Save customer
                if (IsEditMode)
                {
                    var success = await _customerService.UpdateCustomerAsync(Customer);
                    if (success)
                    {
                        LoggingService.LogSystemEvent("تحديث عميل", $"تم تحديث العميل: {Customer.Name}");
                        CustomerSaved?.Invoke(Customer);
                        RequestClose?.Invoke(true);
                    }
                    else
                    {
                        SetError("فشل في تحديث العميل");
                    }
                }
                else
                {
                    var result = await _customerService.AddCustomerAsync(Customer);
                    if (result != null)
                    {
                        Customer = result; // Update with the saved customer (including ID)
                        LoggingService.LogSystemEvent("إضافة عميل", $"تم إضافة العميل: {Customer.Name}");
                        CustomerSaved?.Invoke(Customer);
                        RequestClose?.Invoke(true);
                    }
                    else
                    {
                        SetError("فشل في حفظ العميل");
                    }
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ العميل: {ex.Message}");
                LoggingService.LogError(ex, $"خطأ في حفظ العميل: {Customer.Name}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanSaveCustomer()
        {
            return !string.IsNullOrWhiteSpace(Customer.Name) &&
                   !string.IsNullOrWhiteSpace(Customer.Phone) &&
                   !IsLoading;
        }

        private bool ValidateCustomer()
        {
            ClearAllValidationErrors();

            if (string.IsNullOrWhiteSpace(Customer.Name))
            {
                AddValidationError(nameof(Customer.Name), "اسم العميل مطلوب");
            }

            if (string.IsNullOrWhiteSpace(Customer.Phone))
            {
                AddValidationError(nameof(Customer.Phone), "رقم الهاتف مطلوب");
            }

            if (!string.IsNullOrWhiteSpace(Customer.Email) && !IsValidEmail(Customer.Email))
            {
                AddValidationError(nameof(Customer.Email), "البريد الإلكتروني غير صحيح");
            }

            if (Customer.CreditLimit < 0)
            {
                AddValidationError(nameof(Customer.CreditLimit), "الحد الائتماني لا يمكن أن يكون سالب");
            }

            if (HasValidationErrors)
            {
                var firstError = GetValidationErrors(nameof(Customer.Name)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Customer.Phone)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Customer.Email)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Customer.CreditLimit)).FirstOrDefault();

                SetError(firstError ?? "يرجى تصحيح الأخطاء المدخلة");
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private Task<string> GenerateCustomerCodeAsync()
        {
            try
            {
                // Generate code based on customer name and timestamp
                var namePrefix = Customer.Name.Length >= 3 
                    ? Customer.Name.Substring(0, 3).ToUpper() 
                    : Customer.Name.ToUpper();
                var timestamp = DateTime.Now.ToString("yyyyMMdd");
                var random = new Random().Next(100, 999);
                
                return Task.FromResult($"CUS-{namePrefix}-{timestamp}-{random}");
            }
            catch
            {
                return Task.FromResult($"CUS-{DateTime.Now:yyyyMMddHHmmss}");
            }
        }

        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }

        #endregion

        #region Events

        public event Action<Customer>? CustomerSaved;
        public event Action<bool>? RequestClose;

        #endregion
    }
}
