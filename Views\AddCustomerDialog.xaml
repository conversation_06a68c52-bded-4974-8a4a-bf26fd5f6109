<Window x:Class="SalesManagementSystem.Views.AddCustomerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة عميل جديد" Height="500" Width="400"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="15" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="AccountPlus" Width="24" Height="24" 
                                       Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="إضافة عميل جديد" FontSize="18" FontWeight="Bold" 
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <StackPanel Grid.Row="1">
            <!-- Name -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="اسم العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="NameTextBox" Height="35"/>
            </StackPanel>

            <!-- Phone -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="رقم الهاتف:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="PhoneTextBox" Height="35"/>
            </StackPanel>

            <!-- Email -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="البريد الإلكتروني:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="EmailTextBox" Height="35"/>
            </StackPanel>

            <!-- Address -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="العنوان:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="AddressTextBox" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
            </StackPanel>

            <!-- Notes -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
            </StackPanel>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Content="✅ حفظ" Width="100" Height="35" Margin="5"
                   Background="Green" Foreground="White" FontWeight="Bold"
                   Click="Save_Click"/>
            
            <Button Content="❌ إلغاء" Width="100" Height="35" Margin="5"
                   Background="Red" Foreground="White" FontWeight="Bold"
                   Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
