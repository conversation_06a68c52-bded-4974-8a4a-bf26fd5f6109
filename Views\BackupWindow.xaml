<Window x:Class="SalesManagementSystem.Views.BackupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="النسخ الاحتياطي"
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <Border Grid.Row="0" Background="DarkGreen">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="💾" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="النسخ الاحتياطي واستعادة البيانات" FontSize="18" 
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <TabControl Grid.Row="1" Margin="10">
            <TabItem Header="إنشاء نسخة احتياطية">
                <Grid Margin="20">
                    <StackPanel>
                        <GroupBox Header="خيارات النسخ الاحتياطي" Margin="0,0,0,20">
                            <StackPanel Margin="10">
                                <RadioButton Content="نسخة احتياطية كاملة (جميع البيانات)" IsChecked="True" Margin="0,5"/>
                                <RadioButton Content="نسخة احتياطية جزئية (البيانات المحددة)" Margin="0,5"/>
                                <RadioButton Content="نسخة احتياطية للإعدادات فقط" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="تفاصيل النسخة الاحتياطية">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم النسخة:" Margin="0,0,10,10"/>
                                <TextBox Grid.Row="0" Grid.Column="1" Text="نسخة_احتياطية_2024_01_20" Margin="0,0,0,10"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="مجلد الحفظ:" Margin="0,0,10,10"/>
                                <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                                    <TextBox Width="400" Text="C:\SalesSystem\Backup"/>
                                    <Button Content="..." Width="30" Margin="5,0,0,0"/>
                                </StackPanel>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="تنسيق الملف:" Margin="0,0,10,10"/>
                                <ComboBox Grid.Row="2" Grid.Column="1" Margin="0,0,0,10" SelectedIndex="0">
                                    <ComboBoxItem Content="ملف مضغوط (.zip)"/>
                                    <ComboBoxItem Content="قاعدة بيانات (.db)"/>
                                    <ComboBoxItem Content="ملف SQL (.sql)"/>
                                </ComboBox>

                                <CheckBox Grid.Row="3" Grid.Column="1" Content="تشفير النسخة الاحتياطية" Margin="0,10,0,0"/>
                            </Grid>
                        </GroupBox>

                        <Button Content="🚀 بدء النسخ الاحتياطي" Width="200" Height="40" 
                               Background="DarkGreen" Foreground="White" FontWeight="Bold" 
                               HorizontalAlignment="Center" Margin="0,20,0,0"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <TabItem Header="استعادة البيانات">
                <Grid Margin="20">
                    <StackPanel>
                        <GroupBox Header="اختيار ملف الاستعادة">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="ملف الاستعادة:" Margin="0,0,10,10"/>
                                <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                                    <TextBox Width="400" Text="اختر ملف النسخة الاحتياطية..."/>
                                    <Button Content="📂 تصفح" Width="80" Margin="5,0,0,0"/>
                                </StackPanel>

                                <CheckBox Grid.Row="1" Grid.Column="1" Content="استبدال البيانات الموجودة" Margin="0,10,0,0"/>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="النسخ الاحتياطية المتاحة" Margin="0,20,0,0">
                            <DataGrid Margin="10" Height="200" AutoGenerateColumns="False" CanUserAddRows="False">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="اسم الملف" Width="*"/>
                                    <DataGridTextColumn Header="التاريخ" Width="120"/>
                                    <DataGridTextColumn Header="الحجم" Width="80"/>
                                    <DataGridTextColumn Header="النوع" Width="100"/>
                                    <DataGridTemplateColumn Header="الإجراءات" Width="100">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="🔄" Width="25" Height="25" Margin="2" 
                                                           Background="Green" Foreground="White" ToolTip="استعادة"/>
                                                    <Button Content="🗑️" Width="25" Height="25" Margin="2" 
                                                           Background="Red" Foreground="White" ToolTip="حذف"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </GroupBox>

                        <Button Content="🔄 استعادة البيانات" Width="200" Height="40" 
                               Background="Blue" Foreground="White" FontWeight="Bold" 
                               HorizontalAlignment="Center" Margin="0,20,0,0"/>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>

        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="📊 سجل النسخ الاحتياطي" Width="150" Height="35" Margin="10" 
                       Background="DarkGreen" Foreground="White" FontWeight="Bold"/>
                <Button Content="⚙️ إعدادات متقدمة" Width="140" Height="35" Margin="10" 
                       Background="Orange" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إغلاق" Width="100" Height="35" Margin="10" 
                       Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
