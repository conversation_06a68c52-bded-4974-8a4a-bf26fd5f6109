<UserControl x:Class="SalesManagementSystem.Views.ProductsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="0" Padding="15" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox x:Name="SearchTextBox" Grid.Column="0"
                        materialDesign:HintAssist.Hint="{DynamicResource Search}"
                        materialDesign:TextFieldAssist.HasClearButton="True"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        Margin="0,0,15,0"
                        TextChanged="SearchTextBox_TextChanged"/>

                <!-- Category Filter -->
                <ComboBox x:Name="CategoryComboBox" Grid.Column="1"
                         materialDesign:HintAssist.Hint="{DynamicResource ProductCategory}"
                         Style="{StaticResource MaterialDesignOutlinedComboBox}"
                         Width="150" Margin="0,0,15,0"
                         SelectionChanged="CategoryComboBox_SelectionChanged"/>

                <!-- Add Product Button -->
                <Button x:Name="AddProductButton" Grid.Column="2"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Click="AddProductButton_Click"
                       Margin="0,0,10,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Margin="0,0,5,0"/>
                        <TextBlock Text="{DynamicResource AddProduct}"/>
                    </StackPanel>
                </Button>

                <!-- Refresh Button -->
                <Button x:Name="RefreshButton" Grid.Column="3"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Click="RefreshButton_Click">
                    <materialDesign:PackIcon Kind="Refresh"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Products DataGrid -->
        <materialDesign:Card Grid.Row="1" Padding="0">
            <DataGrid x:Name="ProductsDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     materialDesign:DataGridAssist.CellPadding="8"
                     materialDesign:DataGridAssist.ColumnHeaderPadding="8">

                <DataGrid.Columns>
                    <!-- Product Code -->
                    <DataGridTextColumn Header="{DynamicResource ProductCode}"
                                      Binding="{Binding Code}"
                                      Width="120"/>

                    <!-- Product Name -->
                    <DataGridTextColumn Header="{DynamicResource ProductName}"
                                      Binding="{Binding Name}"
                                      Width="*"/>

                    <!-- Category -->
                    <DataGridTextColumn Header="{DynamicResource ProductCategory}"
                                      Binding="{Binding CategoryName}"
                                      Width="150"/>

                    <!-- Purchase Price -->
                    <DataGridTextColumn Header="{DynamicResource PurchasePrice}"
                                      Binding="{Binding PurchasePrice, StringFormat=C}"
                                      Width="120"/>

                    <!-- Selling Price -->
                    <DataGridTextColumn Header="سعر البيع الأول"
                                      Binding="{Binding SellingPrice, StringFormat=C}"
                                      Width="120"/>

                    <!-- Selling Price 2 -->
                    <DataGridTextColumn Header="سعر البيع الثاني"
                                      Binding="{Binding SalePrice2, StringFormat=C}"
                                      Width="120"/>

                    <!-- Quantity -->
                    <DataGridTemplateColumn Header="{DynamicResource Quantity}" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding Quantity}" VerticalAlignment="Center"/>
                                    <materialDesign:PackIcon Kind="AlertCircle"
                                                           Width="16" Height="16"
                                                           Foreground="Orange"
                                                           Margin="5,0,0,0"
                                                           Visibility="{Binding IsLowStock, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- Actions -->
                    <DataGridTemplateColumn Header="{DynamicResource Actions}" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="{DynamicResource Edit}"
                                           Click="EditProduct_Click"
                                           Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Pencil" Foreground="{StaticResource PrimaryHueMidBrush}"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="{DynamicResource Delete}"
                                           Click="DeleteProduct_Click"
                                           Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Delete" Foreground="#F44336"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
