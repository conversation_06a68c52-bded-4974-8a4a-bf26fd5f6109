using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة اللغة
    /// </summary>
    public class LanguageService : INotifyPropertyChanged
    {
        private static LanguageService? _instance;
        private bool _isArabic;

        public static LanguageService Instance => _instance ??= new LanguageService();

        private LanguageService()
        {
            _isArabic = true; // البداية بالعربية
        }

        public bool IsArabic
        {
            get => _isArabic;
            set
            {
                if (_isArabic != value)
                {
                    _isArabic = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(LanguageIcon));
                    OnPropertyChanged(nameof(LanguageCode));
                    OnPropertyChanged(nameof(LanguageTooltip));
                    OnPropertyChanged(nameof(FlowDirection));
                    ApplyLanguage();
                }
            }
        }

        public string LanguageIcon => _isArabic ? "🇬🇧" : "🇩🇿";
        public string LanguageCode => _isArabic ? "EN" : "AR";
        public string LanguageTooltip => _isArabic ? "Switch to English" : "التبديل إلى العربية";
        public FlowDirection FlowDirection => _isArabic ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;

        // النصوص المترجمة
        public class Translations
        {
            // قائمة ملف
            public static string File => Instance._isArabic ? "ملف" : "File";
            public static string New => Instance._isArabic ? "جديد" : "New";
            public static string Open => Instance._isArabic ? "فتح" : "Open";
            public static string Save => Instance._isArabic ? "حفظ" : "Save";
            public static string Exit => Instance._isArabic ? "خروج" : "Exit";

            // قائمة المبيعات
            public static string Sales => Instance._isArabic ? "مبيعات" : "Sales";
            public static string NewSale => Instance._isArabic ? "بيع جديد" : "New Sale";
            public static string QuickSale => Instance._isArabic ? "بيع سريع" : "Quick Sale";
            public static string SalesList => Instance._isArabic ? "قائمة المبيعات" : "Sales List";

            // قائمة المشتريات
            public static string Purchases => Instance._isArabic ? "مشتريات" : "Purchases";
            public static string NewPurchase => Instance._isArabic ? "شراء جديد" : "New Purchase";
            public static string PurchasesList => Instance._isArabic ? "قائمة المشتريات" : "Purchases List";
            public static string Suppliers => Instance._isArabic ? "الموردين" : "Suppliers";

            // قائمة المنتجات
            public static string Products => Instance._isArabic ? "منتجات" : "Products";
            public static string NewProduct => Instance._isArabic ? "منتج جديد" : "New Product";
            public static string ProductsList => Instance._isArabic ? "قائمة المنتجات" : "Products List";
            public static string Inventory => Instance._isArabic ? "المخزون" : "Inventory";

            // قائمة العملاء
            public static string Customers => Instance._isArabic ? "عملاء" : "Customers";
            public static string NewCustomer => Instance._isArabic ? "عميل جديد" : "New Customer";
            public static string CustomersList => Instance._isArabic ? "قائمة العملاء" : "Customers List";

            // قائمة التقارير
            public static string Reports => Instance._isArabic ? "تقارير" : "Reports";
            public static string SalesReport => Instance._isArabic ? "تقرير المبيعات" : "Sales Report";
            public static string InventoryReport => Instance._isArabic ? "تقرير المخزون" : "Inventory Report";

            // قائمة الإعدادات
            public static string Settings => Instance._isArabic ? "الإعدادات" : "Settings";
            public static string SystemSettings => Instance._isArabic ? "إعدادات النظام" : "System Settings";
            public static string Backup => Instance._isArabic ? "النسخ الاحتياطي" : "Backup";
            public static string ShowCurrency => Instance._isArabic ? "عرض الدينار الجزائري" : "Show Algerian Dinar";

            // الأزرار الرئيسية
            public static string QuickSaleButton => Instance._isArabic ? "بيع سريع" : "Quick Sale";
            public static string ProductsButton => Instance._isArabic ? "المنتجات" : "Products";
            public static string CustomersButton => Instance._isArabic ? "العملاء" : "Customers";
            public static string ProfitsButton => Instance._isArabic ? "الأرباح" : "Profits";

            // الرسائل
            public static string LanguageChanged => Instance._isArabic ? "تم تغيير اللغة إلى العربية" : "Language changed to English";
            public static string LanguageChangeTitle => Instance._isArabic ? "تغيير اللغة" : "Language Change";
            public static string SystemCategory => Instance._isArabic ? "نظام" : "System";

            // العنوان الرئيسي
            public static string MainTitle => Instance._isArabic ? "نظام إدارة المبيعات المتكامل" : "Integrated Sales Management System";
            public static string WelcomeMessage => Instance._isArabic ? "مرحباً بك في نظام إدارة المبيعات" : "Welcome to Sales Management System";
        }

        public void ToggleLanguage()
        {
            IsArabic = !IsArabic;
        }

        private void ApplyLanguage()
        {
            // تطبيق اللغة على الموارد العامة
            var app = Application.Current;
            if (app != null)
            {
                app.Resources["CurrentLanguage"] = _isArabic ? "ar" : "en";
                app.Resources["FlowDirection"] = FlowDirection;
            }

            // إشعار بتغيير اللغة
            LanguageChanged?.Invoke(this, EventArgs.Empty);
        }

        public event EventHandler? LanguageChanged;
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // حفظ واستعادة إعدادات اللغة
        public void SaveLanguageSettings()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"تم حفظ إعدادات اللغة: {(_isArabic ? "عربي" : "إنجليزي")}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ إعدادات اللغة: {ex.Message}");
            }
        }

        public void LoadLanguageSettings()
        {
            try
            {
                IsArabic = true; // القيمة الافتراضية
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل إعدادات اللغة: {ex.Message}");
                IsArabic = true; // القيمة الافتراضية
            }
        }
    }
}
