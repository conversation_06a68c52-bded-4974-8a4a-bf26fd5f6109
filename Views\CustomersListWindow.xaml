<Window x:Class="SalesManagementSystem.Views.CustomersListWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="قائمة العملاء"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <Border Grid.Row="0" Background="Teal">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="👥" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="قائمة العملاء" FontSize="18" 
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <Border Grid.Row="1" Background="LightGray" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBox Width="200" Margin="0,0,10,0" Text="البحث في العملاء..."/>
                <Button Content="🔍 بحث" Width="80" Margin="0,0,10,0" Background="Blue" Foreground="White"/>
                <ComboBox Width="120" Margin="20,0,10,0">
                    <ComboBoxItem Content="جميع العملاء"/>
                    <ComboBoxItem Content="عملاء نشطين"/>
                    <ComboBoxItem Content="عملاء مميزين"/>
                </ComboBox>
                <Button Content="🔄 تحديث" Width="80" Margin="10,0,0,0" Background="Green" Foreground="White"/>
            </StackPanel>
        </Border>

        <DataGrid Grid.Row="2" Margin="10" AutoGenerateColumns="False" CanUserAddRows="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الاسم" Width="*"/>
                <DataGridTextColumn Header="الهاتف" Width="120"/>
                <DataGridTextColumn Header="البريد الإلكتروني" Width="150"/>
                <DataGridTextColumn Header="النوع" Width="100"/>
                <DataGridTextColumn Header="الرصيد" Width="100"/>
                <DataGridCheckBoxColumn Header="نشط" Width="60"/>
                <DataGridTemplateColumn Header="الإجراءات" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Content="✏️" Width="30" Height="25" Margin="2" 
                                       Background="Blue" Foreground="White" ToolTip="تعديل"/>
                                <Button Content="🗑️" Width="30" Height="25" Margin="2" 
                                       Background="Red" Foreground="White" ToolTip="حذف"/>
                                <Button Content="📋" Width="30" Height="25" Margin="2" 
                                       Background="Green" Foreground="White" ToolTip="تفاصيل"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <Border Grid.Row="3" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="➕ عميل جديد" Width="120" Height="35" Margin="10" 
                       Background="Teal" Foreground="White" FontWeight="Bold"/>
                <Button Content="📊 تقرير العملاء" Width="120" Height="35" Margin="10" 
                       Background="Blue" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إغلاق" Width="100" Height="35" Margin="10" 
                       Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
