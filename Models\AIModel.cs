using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج الذكاء الاصطناعي
    /// </summary>
    public class AIModel : INotifyPropertyChanged
    {
        private int _id;
        private string _modelCode = string.Empty;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private AIModelType _type = AIModelType.Prediction;
        private AIModelStatus _status = AIModelStatus.Training;
        private string _algorithm = string.Empty;
        private string _framework = string.Empty;
        private string _version = "1.0";
        private string _datasetPath = string.Empty;
        private string _modelPath = string.Empty;
        private string _configurationJson = string.Empty;
        private decimal _accuracy = 0;
        private decimal _precision = 0;
        private decimal _recall = 0;
        private decimal _f1Score = 0;
        private decimal _trainingLoss = 0;
        private decimal _validationLoss = 0;
        private int _epochs = 0;
        private int _batchSize = 32;
        private decimal _learningRate = 0.001m;
        private string _hyperparameters = string.Empty;
        private DateTime _trainingStarted = DateTime.Now;
        private DateTime? _trainingCompleted;
        private TimeSpan? _trainingDuration;
        private string _inputFeatures = string.Empty;
        private string _outputTargets = string.Empty;
        private string _preprocessingSteps = string.Empty;
        private string _evaluationMetrics = string.Empty;
        private string _notes = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private string _createdBy = string.Empty;
        private ObservableCollection<ModelPrediction> _predictions = new();
        private ObservableCollection<ModelEvaluation> _evaluations = new();
        private ObservableCollection<ModelDeployment> _deployments = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ModelCode
        {
            get => _modelCode;
            set
            {
                if (_modelCode != value)
                {
                    _modelCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public AIModelType Type
        {
            get => _type;
            set
            {
                if (_type != value)
                {
                    _type = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TypeDisplay));
                    OnPropertyChanged(nameof(TypeIcon));
                }
            }
        }

        public AIModelStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsDeployed));
                    OnPropertyChanged(nameof(IsTraining));
                }
            }
        }

        public string Algorithm
        {
            get => _algorithm;
            set
            {
                if (_algorithm != value)
                {
                    _algorithm = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Framework
        {
            get => _framework;
            set
            {
                if (_framework != value)
                {
                    _framework = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Version
        {
            get => _version;
            set
            {
                if (_version != value)
                {
                    _version = value;
                    OnPropertyChanged();
                }
            }
        }

        public string DatasetPath
        {
            get => _datasetPath;
            set
            {
                if (_datasetPath != value)
                {
                    _datasetPath = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ModelPath
        {
            get => _modelPath;
            set
            {
                if (_modelPath != value)
                {
                    _modelPath = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ConfigurationJson
        {
            get => _configurationJson;
            set
            {
                if (_configurationJson != value)
                {
                    _configurationJson = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Accuracy
        {
            get => _accuracy;
            set
            {
                if (_accuracy != value)
                {
                    _accuracy = Math.Max(0, Math.Min(100, value));
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAccuracy));
                    OnPropertyChanged(nameof(AccuracyColor));
                }
            }
        }

        public decimal Precision
        {
            get => _precision;
            set
            {
                if (_precision != value)
                {
                    _precision = Math.Max(0, Math.Min(100, value));
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedPrecision));
                }
            }
        }

        public decimal Recall
        {
            get => _recall;
            set
            {
                if (_recall != value)
                {
                    _recall = Math.Max(0, Math.Min(100, value));
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedRecall));
                }
            }
        }

        public decimal F1Score
        {
            get => _f1Score;
            set
            {
                if (_f1Score != value)
                {
                    _f1Score = Math.Max(0, Math.Min(100, value));
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedF1Score));
                }
            }
        }

        public decimal TrainingLoss
        {
            get => _trainingLoss;
            set
            {
                if (_trainingLoss != value)
                {
                    _trainingLoss = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTrainingLoss));
                }
            }
        }

        public decimal ValidationLoss
        {
            get => _validationLoss;
            set
            {
                if (_validationLoss != value)
                {
                    _validationLoss = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedValidationLoss));
                }
            }
        }

        public int Epochs
        {
            get => _epochs;
            set
            {
                if (_epochs != value)
                {
                    _epochs = value;
                    OnPropertyChanged();
                }
            }
        }

        public int BatchSize
        {
            get => _batchSize;
            set
            {
                if (_batchSize != value)
                {
                    _batchSize = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal LearningRate
        {
            get => _learningRate;
            set
            {
                if (_learningRate != value)
                {
                    _learningRate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLearningRate));
                }
            }
        }

        public string Hyperparameters
        {
            get => _hyperparameters;
            set
            {
                if (_hyperparameters != value)
                {
                    _hyperparameters = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime TrainingStarted
        {
            get => _trainingStarted;
            set
            {
                if (_trainingStarted != value)
                {
                    _trainingStarted = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTrainingStarted));
                    CalculateTrainingDuration();
                }
            }
        }

        public DateTime? TrainingCompleted
        {
            get => _trainingCompleted;
            set
            {
                if (_trainingCompleted != value)
                {
                    _trainingCompleted = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTrainingCompleted));
                    CalculateTrainingDuration();
                }
            }
        }

        public TimeSpan? TrainingDuration
        {
            get => _trainingDuration;
            set
            {
                if (_trainingDuration != value)
                {
                    _trainingDuration = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTrainingDuration));
                }
            }
        }

        public string InputFeatures
        {
            get => _inputFeatures;
            set
            {
                if (_inputFeatures != value)
                {
                    _inputFeatures = value;
                    OnPropertyChanged();
                }
            }
        }

        public string OutputTargets
        {
            get => _outputTargets;
            set
            {
                if (_outputTargets != value)
                {
                    _outputTargets = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PreprocessingSteps
        {
            get => _preprocessingSteps;
            set
            {
                if (_preprocessingSteps != value)
                {
                    _preprocessingSteps = value;
                    OnPropertyChanged();
                }
            }
        }

        public string EvaluationMetrics
        {
            get => _evaluationMetrics;
            set
            {
                if (_evaluationMetrics != value)
                {
                    _evaluationMetrics = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<ModelPrediction> Predictions
        {
            get => _predictions;
            set
            {
                if (_predictions != value)
                {
                    _predictions = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<ModelEvaluation> Evaluations
        {
            get => _evaluations;
            set
            {
                if (_evaluations != value)
                {
                    _evaluations = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<ModelDeployment> Deployments
        {
            get => _deployments;
            set
            {
                if (_deployments != value)
                {
                    _deployments = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsDeployed => Status == AIModelStatus.Deployed;
        public bool IsTraining => Status == AIModelStatus.Training;

        // Display Properties
        public string TypeDisplay
        {
            get
            {
                return Type switch
                {
                    AIModelType.Prediction => "تنبؤ",
                    AIModelType.Classification => "تصنيف",
                    AIModelType.Regression => "انحدار",
                    AIModelType.Clustering => "تجميع",
                    AIModelType.Recommendation => "توصية",
                    AIModelType.Anomaly => "كشف الشذوذ",
                    AIModelType.NLP => "معالجة اللغة",
                    AIModelType.TimeSeries => "السلاسل الزمنية",
                    AIModelType.DeepLearning => "التعلم العميق",
                    AIModelType.Reinforcement => "التعلم المعزز",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    AIModelStatus.Training => "قيد التدريب",
                    AIModelStatus.Trained => "مدرب",
                    AIModelStatus.Deployed => "منشور",
                    AIModelStatus.Failed => "فشل",
                    AIModelStatus.Deprecated => "مهجور",
                    _ => "غير محدد"
                };
            }
        }

        public string TypeIcon
        {
            get
            {
                return Type switch
                {
                    AIModelType.Prediction => "Crystal",
                    AIModelType.Classification => "SortVariant",
                    AIModelType.Regression => "TrendingUp",
                    AIModelType.Clustering => "Scatter",
                    AIModelType.Recommendation => "ThumbUp",
                    AIModelType.Anomaly => "AlertCircle",
                    AIModelType.NLP => "MessageText",
                    AIModelType.TimeSeries => "ChartTimeline",
                    AIModelType.DeepLearning => "Brain",
                    AIModelType.Reinforcement => "Robot",
                    _ => "ChartBox"
                };
            }
        }

        // Color Properties
        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    AIModelStatus.Training => "Orange",
                    AIModelStatus.Trained => "Blue",
                    AIModelStatus.Deployed => "Green",
                    AIModelStatus.Failed => "Red",
                    AIModelStatus.Deprecated => "Gray",
                    _ => "Gray"
                };
            }
        }

        public string AccuracyColor
        {
            get
            {
                if (Accuracy >= 90) return "Green";
                if (Accuracy >= 80) return "Orange";
                if (Accuracy >= 70) return "Yellow";
                return "Red";
            }
        }

        // Formatted Properties
        public string FormattedAccuracy => $"{Accuracy:F2}%";
        public string FormattedPrecision => $"{Precision:F2}%";
        public string FormattedRecall => $"{Recall:F2}%";
        public string FormattedF1Score => $"{F1Score:F2}%";
        public string FormattedTrainingLoss => $"{TrainingLoss:F4}";
        public string FormattedValidationLoss => $"{ValidationLoss:F4}";
        public string FormattedLearningRate => $"{LearningRate:F6}";
        public string FormattedTrainingStarted => TrainingStarted.ToString("dd/MM/yyyy HH:mm");
        public string FormattedTrainingCompleted => TrainingCompleted?.ToString("dd/MM/yyyy HH:mm") ?? "لم يكتمل";
        public string FormattedTrainingDuration => TrainingDuration?.ToString(@"hh\:mm\:ss") ?? "غير محدد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");

        #endregion

        #region Methods

        private void CalculateTrainingDuration()
        {
            if (TrainingCompleted.HasValue)
            {
                TrainingDuration = TrainingCompleted.Value - TrainingStarted;
            }
            else if (Status == AIModelStatus.Training)
            {
                TrainingDuration = DateTime.Now - TrainingStarted;
            }
        }

        public void StartTraining()
        {
            Status = AIModelStatus.Training;
            TrainingStarted = DateTime.Now;
            TrainingCompleted = null;
            UpdatedAt = DateTime.Now;
        }

        public void CompleteTraining(decimal accuracy, decimal precision, decimal recall)
        {
            Status = AIModelStatus.Trained;
            TrainingCompleted = DateTime.Now;
            Accuracy = accuracy;
            Precision = precision;
            Recall = recall;
            F1Score = 2 * (precision * recall) / (precision + recall);
            UpdatedAt = DateTime.Now;
        }

        public void Deploy(string deploymentEnvironment = "Production")
        {
            Status = AIModelStatus.Deployed;
            UpdatedAt = DateTime.Now;

            Deployments.Add(new ModelDeployment
            {
                ModelId = Id,
                Environment = deploymentEnvironment,
                DeployedAt = DateTime.Now,
                Version = Version,
                Status = "Active"
            });
        }

        public void AddPrediction(string inputData, string prediction, decimal confidence)
        {
            Predictions.Add(new ModelPrediction
            {
                ModelId = Id,
                InputData = inputData,
                Prediction = prediction,
                Confidence = confidence,
                PredictedAt = DateTime.Now
            });
        }

        public void MarkAsFailed(string errorMessage = "")
        {
            Status = AIModelStatus.Failed;
            Notes = string.IsNullOrEmpty(errorMessage) ? Notes : $"{Notes}\nError: {errorMessage}";
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// تنبؤ النموذج
    /// </summary>
    public class ModelPrediction
    {
        public int Id { get; set; }
        public int ModelId { get; set; }
        public string InputData { get; set; } = string.Empty;
        public string Prediction { get; set; } = string.Empty;
        public decimal Confidence { get; set; }
        public DateTime PredictedAt { get; set; } = DateTime.Now;
        public string ActualResult { get; set; } = string.Empty;
        public bool IsCorrect { get; set; }

        public string FormattedConfidence => $"{Confidence:F2}%";
        public string FormattedPredictedAt => PredictedAt.ToString("dd/MM/yyyy HH:mm");
    }

    /// <summary>
    /// تقييم النموذج
    /// </summary>
    public class ModelEvaluation
    {
        public int Id { get; set; }
        public int ModelId { get; set; }
        public string EvaluationType { get; set; } = string.Empty;
        public string TestDataset { get; set; } = string.Empty;
        public decimal Accuracy { get; set; }
        public decimal Precision { get; set; }
        public decimal Recall { get; set; }
        public decimal F1Score { get; set; }
        public string ConfusionMatrix { get; set; } = string.Empty;
        public DateTime EvaluatedAt { get; set; } = DateTime.Now;
        public string Notes { get; set; } = string.Empty;

        public string FormattedAccuracy => $"{Accuracy:F2}%";
        public string FormattedEvaluatedAt => EvaluatedAt.ToString("dd/MM/yyyy HH:mm");
    }

    /// <summary>
    /// نشر النموذج
    /// </summary>
    public class ModelDeployment
    {
        public int Id { get; set; }
        public int ModelId { get; set; }
        public string Environment { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public DateTime DeployedAt { get; set; } = DateTime.Now;
        public string Status { get; set; } = string.Empty;
        public string EndpointUrl { get; set; } = string.Empty;
        public int RequestCount { get; set; }
        public decimal AverageResponseTime { get; set; }
        public string Notes { get; set; } = string.Empty;

        public string FormattedDeployedAt => DeployedAt.ToString("dd/MM/yyyy HH:mm");
        public string FormattedResponseTime => $"{AverageResponseTime:F2} ms";
    }

    #endregion

    #region Enums

    public enum AIModelType
    {
        Prediction,         // تنبؤ
        Classification,     // تصنيف
        Regression,         // انحدار
        Clustering,         // تجميع
        Recommendation,     // توصية
        Anomaly,           // كشف الشذوذ
        NLP,               // معالجة اللغة الطبيعية
        TimeSeries,        // السلاسل الزمنية
        DeepLearning,      // التعلم العميق
        Reinforcement      // التعلم المعزز
    }

    public enum AIModelStatus
    {
        Training,          // قيد التدريب
        Trained,           // مدرب
        Deployed,          // منشور
        Failed,            // فشل
        Deprecated         // مهجور
    }

    #endregion

    #region Validation

    public class AIModelValidator : AbstractValidator<AIModel>
    {
        public AIModelValidator()
        {
            RuleFor(m => m.Name)
                .NotEmpty().WithMessage("اسم النموذج مطلوب")
                .MaximumLength(200).WithMessage("اسم النموذج لا يمكن أن يتجاوز 200 حرف");

            RuleFor(m => m.Algorithm)
                .NotEmpty().WithMessage("خوارزمية النموذج مطلوبة");

            RuleFor(m => m.Framework)
                .NotEmpty().WithMessage("إطار العمل مطلوب");

            RuleFor(m => m.Accuracy)
                .InclusiveBetween(0, 100).WithMessage("دقة النموذج يجب أن تكون بين 0 و 100");

            RuleFor(m => m.LearningRate)
                .GreaterThan(0).WithMessage("معدل التعلم يجب أن يكون أكبر من صفر");

            RuleFor(m => m.BatchSize)
                .GreaterThan(0).WithMessage("حجم الدفعة يجب أن يكون أكبر من صفر");

            RuleFor(m => m.Epochs)
                .GreaterThanOrEqualTo(0).WithMessage("عدد العصور يجب أن يكون أكبر من أو يساوي صفر");
        }
    }

    #endregion
}
