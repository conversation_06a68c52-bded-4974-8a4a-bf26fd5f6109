<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Animation Durations -->
    <Duration x:Key="AnimationFast">0:0:0.15</Duration>
    <Duration x:Key="AnimationNormal">0:0:0.3</Duration>
    <Duration x:Key="AnimationSlow">0:0:0.5</Duration>

    <!-- Easing Functions -->
    <CubicEase x:Key="EaseOut" EasingMode="EaseOut"/>
    <CubicEase x:Key="EaseIn" EasingMode="EaseIn"/>
    <CubicEase x:Key="EaseInOut" EasingMode="EaseInOut"/>
    <QuadraticEase x:Key="QuadEaseOut" EasingMode="EaseOut"/>
    <BackEase x:Key="BackEaseOut" EasingMode="EaseOut" Amplitude="0.3"/>

    <!-- Fade Animations -->
    <Storyboard x:Key="FadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="0" To="1" 
                        Duration="{StaticResource AnimationNormal}"
                        EasingFunction="{StaticResource EaseOut}"/>
    </Storyboard>

    <Storyboard x:Key="FadeOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="1" To="0" 
                        Duration="{StaticResource AnimationNormal}"
                        EasingFunction="{StaticResource EaseOut}"/>
    </Storyboard>

    <!-- Slide Animations -->
    <Storyboard x:Key="SlideInFromRightAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                        From="300" To="0" 
                        Duration="{StaticResource AnimationNormal}"
                        EasingFunction="{StaticResource EaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="0" To="1" 
                        Duration="{StaticResource AnimationNormal}"/>
    </Storyboard>

    <Storyboard x:Key="SlideInFromLeftAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                        From="-300" To="0" 
                        Duration="{StaticResource AnimationNormal}"
                        EasingFunction="{StaticResource EaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="0" To="1" 
                        Duration="{StaticResource AnimationNormal}"/>
    </Storyboard>

    <Storyboard x:Key="SlideInFromTopAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                        From="-100" To="0" 
                        Duration="{StaticResource AnimationNormal}"
                        EasingFunction="{StaticResource EaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="0" To="1" 
                        Duration="{StaticResource AnimationNormal}"/>
    </Storyboard>

    <Storyboard x:Key="SlideInFromBottomAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                        From="100" To="0" 
                        Duration="{StaticResource AnimationNormal}"
                        EasingFunction="{StaticResource EaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="0" To="1" 
                        Duration="{StaticResource AnimationNormal}"/>
    </Storyboard>

    <!-- Scale Animations -->
    <Storyboard x:Key="ScaleInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                        From="0.8" To="1" 
                        Duration="{StaticResource AnimationNormal}"
                        EasingFunction="{StaticResource BackEaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                        From="0.8" To="1" 
                        Duration="{StaticResource AnimationNormal}"
                        EasingFunction="{StaticResource BackEaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="0" To="1" 
                        Duration="{StaticResource AnimationNormal}"/>
    </Storyboard>

    <Storyboard x:Key="ScaleOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                        From="1" To="0.8" 
                        Duration="{StaticResource AnimationFast}"
                        EasingFunction="{StaticResource EaseIn}"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                        From="1" To="0.8" 
                        Duration="{StaticResource AnimationFast}"
                        EasingFunction="{StaticResource EaseIn}"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="1" To="0" 
                        Duration="{StaticResource AnimationFast}"/>
    </Storyboard>

    <!-- Hover Animations -->
    <Storyboard x:Key="HoverScaleUpAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                        To="1.05" 
                        Duration="{StaticResource AnimationFast}"
                        EasingFunction="{StaticResource EaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                        To="1.05" 
                        Duration="{StaticResource AnimationFast}"
                        EasingFunction="{StaticResource EaseOut}"/>
    </Storyboard>

    <Storyboard x:Key="HoverScaleDownAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                        To="1" 
                        Duration="{StaticResource AnimationFast}"
                        EasingFunction="{StaticResource EaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                        To="1" 
                        Duration="{StaticResource AnimationFast}"
                        EasingFunction="{StaticResource EaseOut}"/>
    </Storyboard>

    <!-- Ripple Effect Animation -->
    <Storyboard x:Key="RippleAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                        From="0" To="1" 
                        Duration="0:0:0.6"
                        EasingFunction="{StaticResource EaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                        From="0" To="1" 
                        Duration="0:0:0.6"
                        EasingFunction="{StaticResource EaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="0.3" To="0" 
                        Duration="0:0:0.6"
                        EasingFunction="{StaticResource EaseOut}"/>
    </Storyboard>

    <!-- Loading Spinner Animation -->
    <Storyboard x:Key="SpinAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                        From="0" To="360" 
                        Duration="0:0:1"
                        EasingFunction="{StaticResource EaseInOut}"/>
    </Storyboard>

    <!-- Pulse Animation -->
    <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever" AutoReverse="True">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="1" To="0.5" 
                        Duration="0:0:1"
                        EasingFunction="{StaticResource EaseInOut}"/>
    </Storyboard>

    <!-- Shake Animation -->
    <Storyboard x:Key="ShakeAnimation">
        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)">
            <LinearDoubleKeyFrame KeyTime="0:0:0.0" Value="0"/>
            <LinearDoubleKeyFrame KeyTime="0:0:0.1" Value="-10"/>
            <LinearDoubleKeyFrame KeyTime="0:0:0.2" Value="10"/>
            <LinearDoubleKeyFrame KeyTime="0:0:0.3" Value="-10"/>
            <LinearDoubleKeyFrame KeyTime="0:0:0.4" Value="10"/>
            <LinearDoubleKeyFrame KeyTime="0:0:0.5" Value="0"/>
        </DoubleAnimationUsingKeyFrames>
    </Storyboard>

    <!-- Page Transition Animations -->
    <Storyboard x:Key="PageSlideInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                        From="800" To="0" 
                        Duration="{StaticResource AnimationSlow}"
                        EasingFunction="{StaticResource EaseOut}"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="0" To="1" 
                        Duration="{StaticResource AnimationSlow}"/>
    </Storyboard>

    <Storyboard x:Key="PageSlideOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                        From="0" To="-800" 
                        Duration="{StaticResource AnimationSlow}"
                        EasingFunction="{StaticResource EaseIn}"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                        From="1" To="0" 
                        Duration="{StaticResource AnimationSlow}"/>
    </Storyboard>

</ResourceDictionary>
