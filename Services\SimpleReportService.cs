using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class SimpleReportService
    {
        private readonly DatabaseService _dbService;

        public SimpleReportService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<SalesReport> GetSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var totalSales = await GetTotalSalesAsync(startDate, endDate);

                var report = new SalesReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalSales = totalSales,
                    SalesByMonth = new List<Models.SaleSummaryByMonth>(),
                    TopProducts = new List<Models.SaleSummaryByProduct>(),
                    TopCustomers = new List<Models.CustomerSummary>()
                };

                return report;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير المبيعات");
                throw;
            }
        }

        public async Task<List<SimpleProductSalesReport>> GetProductSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var sql = @"
                    SELECT
                        p.Id as ProductId,
                        p.Name as ProductName,
                        p.Code as ProductCode,
                        p.Category,
                        COALESCE(SUM(si.Quantity), 0) as TotalQuantitySold,
                        COALESCE(SUM(si.Quantity * si.UnitPrice), 0) as TotalRevenue,
                        COALESCE(AVG(si.UnitPrice), 0) as AveragePrice,
                        COUNT(DISTINCT s.Id) as NumberOfOrders
                    FROM Products p
                    LEFT JOIN SaleItems si ON p.Id = si.ProductId
                    LEFT JOIN Sales s ON si.SaleId = s.Id AND s.SaleDate BETWEEN @StartDate AND @EndDate
                    GROUP BY p.Id, p.Name, p.Code, p.Category
                    HAVING COALESCE(SUM(si.Quantity * si.UnitPrice), 0) > 0
                    ORDER BY TotalRevenue DESC";

                var result = await _dbService.QueryAsync<SimpleProductSalesReport>(sql, new { StartDate = startDate, EndDate = endDate });
                return result.ToList();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير مبيعات المنتجات");
                return new List<SimpleProductSalesReport>();
            }
        }

        public async Task<List<SimpleCustomerSalesReport>> GetCustomerSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var sql = @"
                    SELECT
                        c.Id as CustomerId,
                        c.Name as CustomerName,
                        c.Phone as CustomerPhone,
                        c.Email as CustomerEmail,
                        COUNT(s.Id) as TotalOrders,
                        SUM(s.NetAmount) as TotalSpent,
                        AVG(s.NetAmount) as AverageOrderValue,
                        MAX(s.SaleDate) as LastOrderDate,
                        MIN(s.SaleDate) as FirstOrderDate
                    FROM Customers c
                    INNER JOIN Sales s ON c.Id = s.CustomerId
                    WHERE s.SaleDate BETWEEN @StartDate AND @EndDate
                    GROUP BY c.Id, c.Name, c.Phone, c.Email
                    ORDER BY TotalSpent DESC";

                var result = await _dbService.QueryAsync<SimpleCustomerSalesReport>(sql, new { StartDate = startDate, EndDate = endDate });
                return result.ToList();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير مبيعات العملاء");
                return new List<SimpleCustomerSalesReport>();
            }
        }

        public async Task<List<SimpleInventoryReportItem>> GetInventoryReportAsync()
        {
            try
            {
                var sql = @"
                    SELECT
                        p.Id as ProductId,
                        p.Name as ProductName,
                        p.Code as ProductCode,
                        p.Category,
                        p.Quantity as CurrentStock,
                        p.MinQuantity as MinimumStock,
                        p.PurchasePrice as CostPrice,
                        p.SalePrice as SalePrice
                    FROM Products p
                    ORDER BY p.Name";

                var result = await _dbService.QueryAsync<SimpleInventoryReportItem>(sql);
                return result.ToList();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير المخزون");
                return new List<SimpleInventoryReportItem>();
            }
        }

        public async Task<SimpleProfitLossReport> GetProfitLossReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var totalRevenue = await GetTotalSalesAsync(startDate, endDate);
                var totalExpenses = await GetTotalExpensesAsync(startDate, endDate);
                var totalCOGS = await GetTotalPurchasesAsync(startDate, endDate);

                var grossProfit = totalRevenue - totalCOGS;
                var netProfit = grossProfit - totalExpenses;

                return new SimpleProfitLossReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalRevenue = totalRevenue,
                    TotalCOGS = totalCOGS,
                    GrossProfit = grossProfit,
                    TotalExpenses = totalExpenses,
                    NetProfit = netProfit,
                    GrossProfitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0,
                    NetProfitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير الأرباح والخسائر");
                throw;
            }
        }

        public async Task<List<SimpleEmployeePerformanceReport>> GetEmployeePerformanceReportAsync(DateTime startDate, DateTime endDate)
        {
            await Task.CompletedTask; // To satisfy async requirement
            return new List<SimpleEmployeePerformanceReport>();
        }

        public async Task<SimplePeriodComparisonReport> GetPeriodComparisonReportAsync(DateTime period1Start, DateTime period1End, DateTime period2Start, DateTime period2End)
        {
            var period1Sales = await GetTotalSalesAsync(period1Start, period1End);
            var period2Sales = await GetTotalSalesAsync(period2Start, period2End);

            return new SimplePeriodComparisonReport
            {
                Period1 = new SimplePeriodData
                {
                    StartDate = period1Start,
                    EndDate = period1End,
                    Sales = period1Sales,
                    Purchases = 0,
                    Expenses = 0,
                    Profit = period1Sales
                },
                Period2 = new SimplePeriodData
                {
                    StartDate = period2Start,
                    EndDate = period2End,
                    Sales = period2Sales,
                    Purchases = 0,
                    Expenses = 0,
                    Profit = period2Sales
                }
            };
        }

        public async Task<SimpleTrendAnalysisReport> GetTrendAnalysisReportAsync(int months)
        {
            var salesTrend = new List<SimpleMonthlyTrendData>();
            var profitTrend = new List<SimpleMonthlyTrendData>();

            for (int i = 0; i < months; i++)
            {
                var monthStart = DateTime.Now.AddMonths(-months + i);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                var sales = await GetTotalSalesAsync(monthStart, monthEnd);

                salesTrend.Add(new SimpleMonthlyTrendData
                {
                    Month = monthStart.ToString("yyyy-MM"),
                    MonthName = monthStart.ToString("MMMM yyyy"),
                    Value = sales
                });

                profitTrend.Add(new SimpleMonthlyTrendData
                {
                    Month = monthStart.ToString("yyyy-MM"),
                    MonthName = monthStart.ToString("MMMM yyyy"),
                    Value = sales * 0.2m
                });
            }

            return new SimpleTrendAnalysisReport
            {
                SalesTrend = salesTrend,
                ProfitTrend = profitTrend,
                SalesGrowthRate = 15.5m,
                ProfitGrowthRate = 18.2m
            };
        }

        private async Task<decimal> GetTotalSalesAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var sql = "SELECT COALESCE(SUM(NetAmount), 0) FROM Sales WHERE SaleDate BETWEEN @StartDate AND @EndDate";
                return await _dbService.QuerySingleOrDefaultAsync<decimal>(sql, new { StartDate = startDate, EndDate = endDate });
            }
            catch
            {
                return 0;
            }
        }

        private async Task<decimal> GetTotalPurchasesAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var sql = "SELECT COALESCE(SUM(TotalAmount), 0) FROM Purchases WHERE PurchaseDate BETWEEN @StartDate AND @EndDate";
                return await _dbService.QuerySingleOrDefaultAsync<decimal>(sql, new { StartDate = startDate, EndDate = endDate });
            }
            catch
            {
                return 0;
            }
        }

        private async Task<decimal> GetTotalExpensesAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var sql = "SELECT COALESCE(SUM(Amount), 0) FROM Expenses WHERE ExpenseDate BETWEEN @StartDate AND @EndDate";
                return await _dbService.QuerySingleOrDefaultAsync<decimal>(sql, new { StartDate = startDate, EndDate = endDate });
            }
            catch
            {
                return 0;
            }
        }
    }

    #region Simple Report Models

    public class SimpleProductSalesReport
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int TotalQuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AveragePrice { get; set; }
        public int NumberOfOrders { get; set; }
    }

    public class SimpleCustomerSalesReport
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public string CustomerEmail { get; set; } = string.Empty;
        public int TotalOrders { get; set; }
        public decimal TotalSpent { get; set; }
        public decimal AverageOrderValue { get; set; }
        public DateTime LastOrderDate { get; set; }
        public DateTime FirstOrderDate { get; set; }
        public string CustomerType => TotalSpent > 10000 ? "VIP" : TotalSpent > 5000 ? "مميز" : "عادي";
    }

    public class SimpleInventoryReportItem
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public decimal CostPrice { get; set; }
        public decimal SalePrice { get; set; }
        public decimal ProfitMargin => SalePrice > 0 ? ((SalePrice - CostPrice) / SalePrice) * 100 : 0;
        public string StockStatus => CurrentStock <= MinimumStock ? "منخفض" : CurrentStock <= MinimumStock * 2 ? "متوسط" : "جيد";
    }

    public class SimpleProfitLossReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCOGS { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public decimal NetProfitMargin { get; set; }
    }

    public class SimpleEmployeePerformanceReport
    {
        public int EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public int TotalSales { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageOrderValue { get; set; }
        public decimal TotalDiscounts { get; set; }
    }

    public class SimplePeriodComparisonReport
    {
        public SimplePeriodData Period1 { get; set; } = new();
        public SimplePeriodData Period2 { get; set; } = new();
    }

    public class SimplePeriodData
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal Sales { get; set; }
        public decimal Purchases { get; set; }
        public decimal Expenses { get; set; }
        public decimal Profit { get; set; }
        public string PeriodName => $"{StartDate:MM/yyyy} - {EndDate:MM/yyyy}";
    }

    public class SimpleTrendAnalysisReport
    {
        public List<SimpleMonthlyTrendData> SalesTrend { get; set; } = new();
        public List<SimpleMonthlyTrendData> ProfitTrend { get; set; } = new();
        public decimal SalesGrowthRate { get; set; }
        public decimal ProfitGrowthRate { get; set; }
    }

    public class SimpleMonthlyTrendData
    {
        public string Month { get; set; } = string.Empty;
        public string MonthName { get; set; } = string.Empty;
        public decimal Value { get; set; }
    }

    #endregion
}
