<Window x:Class="SalesManagementSystem.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات النظام"
        Height="600" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <Border Grid.Row="0" Background="DarkSlateBlue">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="⚙️" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="إعدادات النظام" FontSize="18" 
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <TabControl Grid.Row="1" Margin="10">
            <TabItem Header="الإعدادات العامة">
                <ScrollViewer Margin="20">
                    <StackPanel>
                        <GroupBox Header="معلومات الشركة" Margin="0,0,0,20">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الشركة:" Margin="0,0,10,10"/>
                                <TextBox Grid.Row="0" Grid.Column="1" Text="شركة المبيعات المتقدمة" Margin="0,0,0,10"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="العنوان:" Margin="0,0,10,10"/>
                                <TextBox Grid.Row="1" Grid.Column="1" Text="الجزائر العاصمة" Margin="0,0,0,10"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="الهاتف:" Margin="0,0,10,10"/>
                                <TextBox Grid.Row="2" Grid.Column="1" Text="021-123456" Margin="0,0,0,10"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="البريد الإلكتروني:" Margin="0,0,10,10"/>
                                <TextBox Grid.Row="3" Grid.Column="1" Text="<EMAIL>" Margin="0,0,0,10"/>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="إعدادات العملة والضرائب">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="العملة الافتراضية:" Margin="0,0,10,10"/>
                                <ComboBox Grid.Row="0" Grid.Column="1" Margin="0,0,0,10" SelectedIndex="0">
                                    <ComboBoxItem Content="دينار جزائري (دج)"/>
                                    <ComboBoxItem Content="دولار أمريكي ($)"/>
                                    <ComboBoxItem Content="يورو (€)"/>
                                </ComboBox>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="نسبة الضريبة (%):" Margin="0,0,10,10"/>
                                <TextBox Grid.Row="1" Grid.Column="1" Text="19" Margin="0,0,0,10"/>

                                <CheckBox Grid.Row="2" Grid.Column="1" Content="تطبيق الضريبة تلقائياً" IsChecked="True" Margin="0,10,0,0"/>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <TabItem Header="إعدادات الطباعة">
                <Grid Margin="20">
                    <StackPanel>
                        <GroupBox Header="إعدادات الفواتير">
                            <StackPanel Margin="10">
                                <CheckBox Content="طباعة شعار الشركة" IsChecked="True" Margin="0,5"/>
                                <CheckBox Content="إظهار تفاصيل الضريبة" IsChecked="True" Margin="0,5"/>
                                <CheckBox Content="طباعة الباركود" Margin="0,5"/>
                                <CheckBox Content="طباعة ملاحظات إضافية" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="حجم الورق" Margin="0,20,0,0">
                            <StackPanel Margin="10">
                                <RadioButton Content="A4" IsChecked="True" Margin="0,5"/>
                                <RadioButton Content="A5" Margin="0,5"/>
                                <RadioButton Content="حجم مخصص" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </Grid>
            </TabItem>

            <TabItem Header="إعدادات النسخ الاحتياطي">
                <Grid Margin="20">
                    <StackPanel>
                        <GroupBox Header="النسخ الاحتياطي التلقائي">
                            <StackPanel Margin="10">
                                <CheckBox Content="تفعيل النسخ الاحتياطي التلقائي" IsChecked="True" Margin="0,5"/>
                                <StackPanel Orientation="Horizontal" Margin="0,10">
                                    <TextBlock Text="كل:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <ComboBox Width="100" SelectedIndex="0">
                                        <ComboBoxItem Content="يوم"/>
                                        <ComboBoxItem Content="أسبوع"/>
                                        <ComboBoxItem Content="شهر"/>
                                    </ComboBox>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,10">
                                    <TextBlock Text="مجلد الحفظ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox Width="300" Text="C:\SalesSystem\Backup"/>
                                    <Button Content="..." Width="30" Margin="5,0,0,0"/>
                                </StackPanel>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>

        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ الإعدادات" Width="130" Height="35" Margin="10" 
                       Background="DarkSlateBlue" Foreground="White" FontWeight="Bold"/>
                <Button Content="🔄 استعادة الافتراضي" Width="140" Height="35" Margin="10" 
                       Background="Orange" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إلغاء" Width="100" Height="35" Margin="10" 
                       Background="Red" Foreground="White" FontWeight="Bold" Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
