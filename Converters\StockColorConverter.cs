using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace SalesManagementSystem.Converters
{
    public class StockColorConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length != 2 || values[0] == null || values[1] == null)
                return Brushes.Black;

            if (values[0] is int currentStock && values[1] is int minStock)
            {
                if (currentStock <= 0)
                    return Brushes.Red;
                else if (currentStock <= minStock)
                    return Brushes.Orange;
                else
                    return Brushes.Green;
            }

            return Brushes.Black;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
