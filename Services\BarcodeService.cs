using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;
using ZXing;
using ZXing.Common;
using ZXing.Windows.Compatibility;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الباركود - توليد وقراءة الباركود
    /// </summary>
    public class BarcodeService
    {
        private readonly BarcodeWriter _barcodeWriter;
        private readonly BarcodeReader _barcodeReader;

        public BarcodeService()
        {
            // إعداد كاتب الباركود
            _barcodeWriter = new BarcodeWriter
            {
                Format = BarcodeFormat.CODE_128,
                Options = new EncodingOptions
                {
                    Width = 300,
                    Height = 100,
                    Margin = 10,
                    PureBarcode = false
                }
            };

            // إعداد قارئ الباركود
            _barcodeReader = new BarcodeReader
            {
                AutoRotate = true,
                Options = new DecodingOptions
                {
                    TryHarder = true,
                    TryInverted = true,
                    PossibleFormats = new[]
                    {
                        BarcodeFormat.CODE_128,
                        BarcodeFormat.CODE_39,
                        BarcodeFormat.EAN_13,
                        BarcodeFormat.EAN_8,
                        BarcodeFormat.UPC_A,
                        BarcodeFormat.UPC_E
                    }
                }
            };
        }

        /// <summary>
        /// توليد باركود من نص
        /// </summary>
        /// <param name="text">النص المراد تحويله لباركود</param>
        /// <param name="width">عرض الباركود</param>
        /// <param name="height">ارتفاع الباركود</param>
        /// <returns>صورة الباركود كـ BitmapImage</returns>
        public BitmapImage GenerateBarcode(string text, int width = 300, int height = 100)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(text))
                    throw new ArgumentException("النص لا يمكن أن يكون فارغاً", nameof(text));

                // تحديث إعدادات الحجم
                _barcodeWriter.Options.Width = width;
                _barcodeWriter.Options.Height = height;

                // توليد الباركود
                using var bitmap = _barcodeWriter.Write(text);

                // تحويل إلى BitmapImage
                return ConvertBitmapToBitmapImage(bitmap);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في توليد الباركود للنص: {text}");
                throw;
            }
        }

        /// <summary>
        /// توليد باركود للمنتج
        /// </summary>
        /// <param name="productCode">كود المنتج</param>
        /// <param name="width">عرض الباركود</param>
        /// <param name="height">ارتفاع الباركود</param>
        /// <returns>صورة الباركود</returns>
        public BitmapImage GenerateProductBarcode(string productCode, int width = 300, int height = 100)
        {
            if (string.IsNullOrWhiteSpace(productCode))
                throw new ArgumentException("كود المنتج لا يمكن أن يكون فارغاً", nameof(productCode));

            return GenerateBarcode(productCode, width, height);
        }

        /// <summary>
        /// قراءة الباركود من صورة
        /// </summary>
        /// <param name="imagePath">مسار الصورة</param>
        /// <returns>النص المقروء من الباركود</returns>
        public string? ReadBarcodeFromImage(string imagePath)
        {
            try
            {
                if (!File.Exists(imagePath))
                    throw new FileNotFoundException("الملف غير موجود", imagePath);

                using var bitmap = new Bitmap(imagePath);
                var result = _barcodeReader.Decode(bitmap);

                return result?.Text;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في قراءة الباركود من الصورة: {imagePath}");
                return null;
            }
        }

        /// <summary>
        /// قراءة الباركود من BitmapImage
        /// </summary>
        /// <param name="bitmapImage">صورة الباركود</param>
        /// <returns>النص المقروء من الباركود</returns>
        public string? ReadBarcodeFromBitmapImage(BitmapImage bitmapImage)
        {
            try
            {
                using var bitmap = ConvertBitmapImageToBitmap(bitmapImage);
                var result = _barcodeReader.Decode(bitmap);

                return result?.Text;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في قراءة الباركود من BitmapImage");
                return null;
            }
        }

        /// <summary>
        /// حفظ الباركود كصورة
        /// </summary>
        /// <param name="text">النص المراد تحويله لباركود</param>
        /// <param name="filePath">مسار حفظ الصورة</param>
        /// <param name="format">تنسيق الصورة</param>
        /// <param name="width">عرض الباركود</param>
        /// <param name="height">ارتفاع الباركود</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveBarcodeToFile(string text, string filePath, ImageFormat? format = null, int width = 300, int height = 100)
        {
            try
            {
                format ??= ImageFormat.Png;

                // تحديث إعدادات الحجم
                _barcodeWriter.Options.Width = width;
                _barcodeWriter.Options.Height = height;

                // توليد الباركود
                using var bitmap = _barcodeWriter.Write(text);

                // إنشاء المجلد إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // حفظ الصورة
                bitmap.Save(filePath, format);

                LoggingService.LogInfo($"تم حفظ الباركود بنجاح: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في حفظ الباركود: {filePath}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة الباركود
        /// </summary>
        /// <param name="barcode">الباركود المراد التحقق منه</param>
        /// <returns>true إذا كان الباركود صحيحاً</returns>
        public bool ValidateBarcode(string barcode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(barcode))
                    return false;

                // محاولة توليد الباركود للتحقق من صحته
                using var bitmap = _barcodeWriter.Write(barcode);

                // محاولة قراءة الباركود المولد
                var result = _barcodeReader.Decode(bitmap);

                return result?.Text == barcode;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تحويل Bitmap إلى BitmapImage
        /// </summary>
        private BitmapImage ConvertBitmapToBitmapImage(Bitmap bitmap)
        {
            using var memory = new MemoryStream();
            bitmap.Save(memory, ImageFormat.Png);
            memory.Position = 0;

            var bitmapImage = new BitmapImage();
            bitmapImage.BeginInit();
            bitmapImage.StreamSource = memory;
            bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
            bitmapImage.EndInit();
            bitmapImage.Freeze();

            return bitmapImage;
        }

        /// <summary>
        /// تحويل BitmapImage إلى Bitmap
        /// </summary>
        private Bitmap ConvertBitmapImageToBitmap(BitmapImage bitmapImage)
        {
            using var outStream = new MemoryStream();

            var encoder = new PngBitmapEncoder();
            encoder.Frames.Add(BitmapFrame.Create(bitmapImage));
            encoder.Save(outStream);

            return new Bitmap(outStream);
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            // BarcodeWriter و BarcodeReader لا يحتاجان إلى Dispose في ZXing.Net
        }
    }
}
