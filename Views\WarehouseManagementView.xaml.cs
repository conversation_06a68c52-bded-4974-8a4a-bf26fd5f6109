using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// واجهة إدارة المخازن المتعددة
    /// </summary>
    public partial class WarehouseManagementView : UserControl, INotifyPropertyChanged
    {
        private readonly WarehouseService? _warehouseService;
        private readonly InventoryService? _inventoryService;
        private readonly NotificationService? _notificationService;

        private ObservableCollection<Warehouse> _warehouses = new();
        private string _searchText = string.Empty;
        private WarehouseType? _selectedWarehouseType;
        private WarehouseStatus? _selectedWarehouseStatus;
        private bool _isLoading;

        public WarehouseManagementView()
        {
            try
            {
                InitializeComponent();
                DataContext = this;

                // Initialize services with error handling
                try
                {
                    var dbService = new DatabaseService();
                    var settingsService = new SettingsService(dbService);
                    _notificationService = new NotificationService(dbService, settingsService);
                    _inventoryService = new InventoryService(dbService, _notificationService);
                    _warehouseService = new WarehouseService(dbService, _notificationService);
                }
                catch (Exception ex)
                {
                    // في حالة فشل تهيئة الخدمات، استخدم خدمات وهمية
                    MessageBox.Show($"تحذير: فشل في تهيئة بعض الخدمات. سيتم استخدام البيانات التجريبية.\n{ex.Message}",
                        "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                }

                InitializeCommands();
                _ = LoadDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة واجهة إدارة المستودعات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region Properties

        public ObservableCollection<Warehouse> Warehouses
        {
            get => _warehouses;
            set
            {
                if (_warehouses != value)
                {
                    _warehouses = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged();
                }
            }
        }

        public WarehouseType? SelectedWarehouseType
        {
            get => _selectedWarehouseType;
            set
            {
                if (_selectedWarehouseType != value)
                {
                    _selectedWarehouseType = value;
                    OnPropertyChanged();
                }
            }
        }

        public WarehouseStatus? SelectedWarehouseStatus
        {
            get => _selectedWarehouseStatus;
            set
            {
                if (_selectedWarehouseStatus != value)
                {
                    _selectedWarehouseStatus = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }

        // Collections for ComboBoxes
        public Array WarehouseTypes => Enum.GetValues(typeof(WarehouseType));
        public Array WarehouseStatuses => Enum.GetValues(typeof(WarehouseStatus));

        #endregion

        #region Commands

        public ICommand AddWarehouseCommand { get; private set; } = null!;
        public ICommand EditWarehouseCommand { get; private set; } = null!;
        public ICommand DeleteWarehouseCommand { get; private set; } = null!;
        public ICommand ViewWarehouseDetailsCommand { get; private set; } = null!;
        public ICommand SearchCommand { get; private set; } = null!;
        public ICommand RefreshCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            AddWarehouseCommand = new RelayCommand(async () => await AddWarehouseAsync());
            EditWarehouseCommand = new RelayCommand<Warehouse>(async (warehouse) => await EditWarehouseAsync(warehouse));
            DeleteWarehouseCommand = new RelayCommand<Warehouse>(async (warehouse) => await DeleteWarehouseAsync(warehouse));
            ViewWarehouseDetailsCommand = new RelayCommand<Warehouse>(async (warehouse) => await ViewWarehouseDetailsAsync(warehouse));
            SearchCommand = new RelayCommand(async () => await SearchWarehousesAsync());
            RefreshCommand = new RelayCommand(async () => await LoadDataAsync());
        }

        #endregion

        #region Data Loading

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;

                // إنشاء بيانات تجريبية إذا لم تكن موجودة
                var warehouses = await GetSampleWarehousesAsync();

                // تحميل تفاصيل إضافية لكل مخزن
                foreach (var warehouse in warehouses)
                {
                    // تحميل المواقع
                    var locations = GetSampleLocations(warehouse.Id);
                    warehouse.Locations = new ObservableCollection<WarehouseLocation>(locations);

                    // تحميل المخزون
                    var inventory = GetSampleInventory(warehouse.Id);
                    warehouse.Inventory = new ObservableCollection<InventoryItem>(inventory);

                    // تحديث الإحصائيات
                    warehouse.UpdateInventory();
                }

                Warehouses = new ObservableCollection<Warehouse>(warehouses);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task<List<Warehouse>> GetSampleWarehousesAsync()
        {
            // محاولة تحميل البيانات من قاعدة البيانات أولاً
            try
            {
                if (_warehouseService != null)
                {
                    var warehouses = await _warehouseService.GetAllWarehousesAsync();
                    if (warehouses.Any())
                        return warehouses.ToList();
                }
            }
            catch
            {
                // في حالة فشل تحميل البيانات، استخدم البيانات التجريبية
            }

            // إنشاء بيانات تجريبية
            return new List<Warehouse>
            {
                new Warehouse
                {
                    Id = 1,
                    Code = "WH001",
                    Name = "المخزن الرئيسي",
                    Description = "المخزن الرئيسي للشركة",
                    Type = WarehouseType.Main,
                    Status = WarehouseStatus.Active,
                    Address = "شارع الملك فهد، الرياض",
                    City = "الرياض",
                    Phone = "011-1234567",
                    Email = "<EMAIL>",
                    ManagerName = "أحمد محمد",
                    TotalCapacity = 10000,
                    UsedCapacity = 6500,
                    IsDefault = true,
                    CreatedBy = "النظام",
                    CreatedAt = DateTime.Now.AddDays(-30)
                },
                new Warehouse
                {
                    Id = 2,
                    Code = "WH002",
                    Name = "مخزن الفرع الشرقي",
                    Description = "مخزن فرع المنطقة الشرقية",
                    Type = WarehouseType.Branch,
                    Status = WarehouseStatus.Active,
                    Address = "شارع الظهران، الدمام",
                    City = "الدمام",
                    Phone = "013-7654321",
                    Email = "<EMAIL>",
                    ManagerName = "سارة أحمد",
                    TotalCapacity = 5000,
                    UsedCapacity = 3200,
                    IsDefault = false,
                    CreatedBy = "النظام",
                    CreatedAt = DateTime.Now.AddDays(-20)
                },
                new Warehouse
                {
                    Id = 3,
                    Code = "WH003",
                    Name = "مخزن العبور",
                    Description = "مخزن مؤقت للبضائع العابرة",
                    Type = WarehouseType.Transit,
                    Status = WarehouseStatus.Active,
                    Address = "المنطقة الصناعية، جدة",
                    City = "جدة",
                    Phone = "012-9876543",
                    Email = "<EMAIL>",
                    ManagerName = "محمد علي",
                    TotalCapacity = 2000,
                    UsedCapacity = 800,
                    IsDefault = false,
                    CreatedBy = "النظام",
                    CreatedAt = DateTime.Now.AddDays(-15)
                }
            };
        }

        private List<WarehouseLocation> GetSampleLocations(int warehouseId)
        {
            return new List<WarehouseLocation>
            {
                new WarehouseLocation
                {
                    Id = warehouseId * 10 + 1,
                    WarehouseId = warehouseId,
                    Code = $"LOC{warehouseId:D3}-001",
                    Name = "الرف الأول",
                    Type = LocationType.Shelf,
                    Aisle = "A",
                    Rack = "01",
                    Shelf = "01",
                    Bin = "01",
                    Capacity = 100,
                    UsedCapacity = 75,
                    Status = LocationStatus.Available
                },
                new WarehouseLocation
                {
                    Id = warehouseId * 10 + 2,
                    WarehouseId = warehouseId,
                    Code = $"LOC{warehouseId:D3}-002",
                    Name = "الرف الثاني",
                    Type = LocationType.Shelf,
                    Aisle = "A",
                    Rack = "01",
                    Shelf = "02",
                    Bin = "01",
                    Capacity = 100,
                    UsedCapacity = 50,
                    Status = LocationStatus.Available
                }
            };
        }

        private List<InventoryItem> GetSampleInventory(int warehouseId)
        {
            // إنشاء بيانات مخزون متنوعة حسب نوع المخزن
            var baseItems = new List<InventoryItem>();

            if (warehouseId == 1) // المخزن الرئيسي
            {
                baseItems.AddRange(new[]
                {
                    new InventoryItem
                    {
                        Id = 101,
                        ProductId = 1,
                        WarehouseId = warehouseId,
                        Quantity = 150,
                        UnitCost = 25.50m,
                        Volume = 2.5m,
                        Weight = 1.2m
                    },
                    new InventoryItem
                    {
                        Id = 102,
                        ProductId = 2,
                        WarehouseId = warehouseId,
                        Quantity = 200,
                        UnitCost = 45.75m,
                        Volume = 3.0m,
                        Weight = 2.1m
                    },
                    new InventoryItem
                    {
                        Id = 103,
                        ProductId = 3,
                        WarehouseId = warehouseId,
                        Quantity = 75,
                        UnitCost = 120.00m,
                        Volume = 5.0m,
                        Weight = 3.5m
                    },
                    new InventoryItem
                    {
                        Id = 104,
                        ProductId = 4,
                        WarehouseId = warehouseId,
                        Quantity = 300,
                        UnitCost = 15.25m,
                        Volume = 1.0m,
                        Weight = 0.5m
                    }
                });
            }
            else if (warehouseId == 2) // مخزن الفرع الشرقي
            {
                baseItems.AddRange(new[]
                {
                    new InventoryItem
                    {
                        Id = 201,
                        ProductId = 1,
                        WarehouseId = warehouseId,
                        Quantity = 80,
                        UnitCost = 25.50m,
                        Volume = 2.5m,
                        Weight = 1.2m
                    },
                    new InventoryItem
                    {
                        Id = 202,
                        ProductId = 2,
                        WarehouseId = warehouseId,
                        Quantity = 60,
                        UnitCost = 45.75m,
                        Volume = 3.0m,
                        Weight = 2.1m
                    },
                    new InventoryItem
                    {
                        Id = 203,
                        ProductId = 5,
                        WarehouseId = warehouseId,
                        Quantity = 40,
                        UnitCost = 85.00m,
                        Volume = 4.0m,
                        Weight = 2.8m
                    }
                });
            }
            else if (warehouseId == 3) // مخزن العبور
            {
                baseItems.AddRange(new[]
                {
                    new InventoryItem
                    {
                        Id = 301,
                        ProductId = 6,
                        WarehouseId = warehouseId,
                        Quantity = 25,
                        UnitCost = 200.00m,
                        Volume = 8.0m,
                        Weight = 5.0m
                    },
                    new InventoryItem
                    {
                        Id = 302,
                        ProductId = 7,
                        WarehouseId = warehouseId,
                        Quantity = 15,
                        UnitCost = 350.00m,
                        Volume = 12.0m,
                        Weight = 8.5m
                    }
                });
            }

            return baseItems;
        }

        private async Task SearchWarehousesAsync()
        {
            try
            {
                IsLoading = true;

                if (string.IsNullOrWhiteSpace(SearchText) && !SelectedWarehouseType.HasValue && !SelectedWarehouseStatus.HasValue)
                {
                    await LoadDataAsync();
                    return;
                }

                // البحث في البيانات التجريبية
                var allWarehouses = await GetSampleWarehousesAsync();
                var filteredWarehouses = allWarehouses.Where(w =>
                    (string.IsNullOrWhiteSpace(SearchText) ||
                     w.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                     w.Code.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                     w.City.Contains(SearchText, StringComparison.OrdinalIgnoreCase)) &&
                    (!SelectedWarehouseType.HasValue || w.Type == SelectedWarehouseType.Value) &&
                    (!SelectedWarehouseStatus.HasValue || w.Status == SelectedWarehouseStatus.Value)
                ).ToList();

                // تحميل تفاصيل إضافية لكل مخزن
                foreach (var warehouse in filteredWarehouses)
                {
                    var locations = GetSampleLocations(warehouse.Id);
                    warehouse.Locations = new ObservableCollection<WarehouseLocation>(locations);

                    var inventory = GetSampleInventory(warehouse.Id);
                    warehouse.Inventory = new ObservableCollection<InventoryItem>(inventory);

                    warehouse.UpdateInventory();
                }

                Warehouses = new ObservableCollection<Warehouse>(filteredWarehouses);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Warehouse Operations

        private async Task AddWarehouseAsync()
        {
            try
            {
                MessageBox.Show("إضافة مخزن جديد - قيد التطوير", "معلومات",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                // يمكن إضافة المنطق هنا لاحقاً
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المخزن: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task EditWarehouseAsync(Warehouse? warehouse)
        {
            if (warehouse == null) return;

            try
            {
                MessageBox.Show($"تعديل المخزن '{warehouse.Name}' - قيد التطوير", "معلومات",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المخزن: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task DeleteWarehouseAsync(Warehouse? warehouse)
        {
            if (warehouse == null) return;

            try
            {
                var result = MessageBox.Show($"هل تريد حذف المخزن '{warehouse.Name}'؟\n\nتحذير: هذا الإجراء قيد التطوير.",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    MessageBox.Show("حذف المخزن - قيد التطوير", "معلومات",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المخزن: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ViewWarehouseDetailsAsync(Warehouse? warehouse)
        {
            if (warehouse == null) return;

            try
            {
                // عرض تفاصيل المخزن في رسالة
                var details = $"تفاصيل المخزن:\n\n" +
                             $"الكود: {warehouse.Code}\n" +
                             $"الاسم: {warehouse.Name}\n" +
                             $"النوع: {warehouse.TypeDisplay}\n" +
                             $"الحالة: {warehouse.StatusDisplay}\n" +
                             $"المدينة: {warehouse.City}\n" +
                             $"المدير: {warehouse.ManagerName}\n" +
                             $"السعة الإجمالية: {warehouse.FormattedTotalCapacity}\n" +
                             $"السعة المستخدمة: {warehouse.FormattedUsedCapacity}\n" +
                             $"السعة المتاحة: {warehouse.FormattedAvailableCapacity}\n" +
                             $"عدد المواقع: {warehouse.LocationCount}\n" +
                             $"عدد الأصناف: {warehouse.TotalItems}\n" +
                             $"القيمة الإجمالية: {warehouse.FormattedTotalValue}";

                MessageBox.Show(details, $"تفاصيل المخزن - {warehouse.Name}",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التفاصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }


}
