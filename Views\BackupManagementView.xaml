<UserControl x:Class="SalesManagementSystem.Views.BackupManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- تحويل حالة النسخ الاحتياطي إلى لون -->
        <Style x:Key="BackupStatusCard" TargetType="Border">
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Scheduled">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Running">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Completed">
                    <Setter Property="Background" Value="#E8F5E8"/>
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Failed">
                    <Setter Property="Background" Value="#FFEBEE"/>
                    <Setter Property="BorderBrush" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Cancelled">
                    <Setter Property="Background" Value="#FAFAFA"/>
                    <Setter Property="BorderBrush" Value="#9E9E9E"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل حالة النسخ الاحتياطي إلى لون النص -->
        <Style x:Key="StatusIndicator" TargetType="Ellipse">
            <Setter Property="Fill" Value="Gray"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Scheduled">
                    <Setter Property="Fill" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Running">
                    <Setter Property="Fill" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Completed">
                    <Setter Property="Fill" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Failed">
                    <Setter Property="Fill" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Cancelled">
                    <Setter Property="Fill" Value="#9E9E9E"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="DatabaseExport" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="إدارة النسخ الاحتياطي المتقدم" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding CreateBackupJobCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إنشاء مهمة نسخ"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding CreateRestoreJobCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="DatabaseImport" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="استعادة البيانات"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات سريعة -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي المهام -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="DatabaseExport" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalJobs}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي المهام" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- المهام النشطة -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Play" Width="32" Height="32" 
                                           Foreground="Orange"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding RunningJobs}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Orange"/>
                    <TextBlock Text="قيد التشغيل" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- المهام المكتملة -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CheckCircle" Width="32" Height="32" 
                                           Foreground="Green"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding CompletedJobs}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Green"/>
                    <TextBlock Text="مكتملة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- المهام الفاشلة -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AlertCircle" Width="32" Height="32" 
                                           Foreground="Red"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding FailedJobs}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Red"/>
                    <TextBlock Text="فاشلة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- معدل النجاح -->
                <StackPanel Grid.Column="4" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ChartLine" Width="32" Height="32" 
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding SuccessRate, StringFormat='{}{0:F1}%'}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="معدل النجاح" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- قائمة مهام النسخ الاحتياطي -->
        <ScrollViewer Grid.Row="2" Margin="16,8,16,16" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding BackupJobs}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="1" Margin="0,0,0,16"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <materialDesign:Card Margin="8" 
                                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
                            <Border BorderThickness="2" CornerRadius="4" Style="{StaticResource BackupStatusCard}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- رأس البطاقة -->
                                    <Grid Grid.Row="0" Margin="16,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- أيقونة نوع النسخ -->
                                        <materialDesign:PackIcon Grid.Column="0" 
                                                               Kind="{Binding BackupTypeIcon}" 
                                                               Width="24" Height="24"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,12,0"/>

                                        <!-- اسم المهمة -->
                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding JobName}" 
                                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                     FontWeight="Bold"/>
                                            <TextBlock Text="{Binding JobCode}" 
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     Opacity="0.7"/>
                                        </StackPanel>

                                        <!-- مؤشر الحالة -->
                                        <Ellipse Grid.Column="2" 
                                               Width="12" Height="12" 
                                               Style="{StaticResource StatusIndicator}"
                                               VerticalAlignment="Center"
                                               Margin="8,0"/>

                                        <!-- نوع النسخ -->
                                        <materialDesign:Chip Grid.Column="3"
                                                           Content="{Binding BackupTypeDisplay}"
                                                           VerticalAlignment="Center"
                                                           FontSize="10"
                                                           Margin="8,0"/>

                                        <!-- التكرار -->
                                        <materialDesign:Chip Grid.Column="4"
                                                           Content="{Binding FrequencyDisplay}"
                                                           VerticalAlignment="Center"
                                                           FontSize="10"/>
                                    </Grid>

                                    <!-- محتوى البطاقة -->
                                    <Grid Grid.Row="1" Margin="16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- معلومات المسار -->
                                        <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                            <TextBlock Text="المصدر" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding SourcePath}" Style="{StaticResource MaterialDesignBody2TextBlock}" FontSize="11" TextWrapping="Wrap"/>
                                            
                                            <TextBlock Text="الوجهة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7" Margin="0,8,0,0"/>
                                            <TextBlock Text="{Binding DestinationPath}" Style="{StaticResource MaterialDesignBody2TextBlock}" FontSize="11" TextWrapping="Wrap"/>
                                        </StackPanel>

                                        <!-- معلومات التوقيت -->
                                        <StackPanel Grid.Column="1" Margin="8,0">
                                            <TextBlock Text="التشغيل التالي" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding FormattedNextRunTime}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            
                                            <TextBlock Text="آخر تشغيل" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7" Margin="0,8,0,0"/>
                                            <TextBlock Text="{Binding FormattedLastRunTime}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            
                                            <TextBlock Text="آخر نجاح" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7" Margin="0,8,0,0"/>
                                            <TextBlock Text="{Binding FormattedLastSuccessTime}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                        </StackPanel>

                                        <!-- معلومات الأداء -->
                                        <StackPanel Grid.Column="2" Margin="8,0,0,0">
                                            <TextBlock Text="معدل النجاح" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding FormattedSuccessRate}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            
                                            <TextBlock Text="حجم آخر نسخة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7" Margin="0,8,0,0"/>
                                            <TextBlock Text="{Binding FormattedActualSize}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            
                                            <TextBlock Text="المدة الفعلية" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7" Margin="0,8,0,0"/>
                                            <TextBlock Text="{Binding FormattedActualDuration}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- شريط التقدم للمهام قيد التشغيل -->
                                    <ProgressBar Grid.Row="2" 
                                               Margin="16,8"
                                               Height="4"
                                               IsIndeterminate="True"
                                               Visibility="{Binding IsRunning, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                                    <!-- أزرار الإجراءات -->
                                    <Grid Grid.Row="3" Margin="16,8,16,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- رسالة الخطأ -->
                                        <TextBlock Grid.Column="0" 
                                                 Text="{Binding ErrorMessage}"
                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                 Foreground="Red"
                                                 VerticalAlignment="Center"
                                                 TextWrapping="Wrap"
                                                 Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                                        <!-- زر التشغيل -->
                                        <Button Grid.Column="1" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.RunBackupJobCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="تشغيل"
                                              Visibility="{Binding CanStart, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <materialDesign:PackIcon Kind="Play" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر الإيقاف -->
                                        <Button Grid.Column="2" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.StopBackupJobCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="إيقاف"
                                              Visibility="{Binding CanStop, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <materialDesign:PackIcon Kind="Stop" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر التحرير -->
                                        <Button Grid.Column="3" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.EditBackupJobCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="تحرير">
                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر السجل -->
                                        <Button Grid.Column="4" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.ViewBackupHistoryCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="عرض السجل">
                                            <materialDesign:PackIcon Kind="History" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر الحذف -->
                                        <Button Grid.Column="5" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.DeleteBackupJobCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="حذف">
                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Border>
                        </materialDesign:Card>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Grid Grid.Row="2" 
              Background="White" 
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري التحميل..." 
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
