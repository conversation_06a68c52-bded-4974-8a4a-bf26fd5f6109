using System;
using System.Windows;
using System.Windows.Input;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// نافذة تسجيل الدخول
    /// </summary>
    public partial class LoginWindow : Window
    {
        private readonly LoginAuthService _authService;
        private readonly EnhancedNotificationService _notificationService;

        public LoginWindow()
        {
            InitializeComponent();
            _authService = LoginAuthService.Instance;
            _notificationService = EnhancedNotificationService.Instance;

            // تمكين السحب للنافذة
            MouseDown += (s, e) => { if (e.LeftButton == MouseButtonState.Pressed) DragMove(); };

            // تسجيل الدخول بالضغط على Enter
            KeyDown += LoginWindow_KeyDown;

            // تركيز على حقل اسم المستخدم
            Loaded += (s, e) => UsernameTextBox.Focus();
        }

        private void LoginWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var username = UsernameTextBox.Text.Trim();
                var password = PasswordBox.Password;

                // إخفاء رسالة الخطأ السابقة
                ErrorMessage.Visibility = Visibility.Collapsed;

                // التحقق من صحة البيانات
                if (string.IsNullOrEmpty(username))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    UsernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrEmpty(password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    PasswordBox.Focus();
                    return;
                }

                // محاولة تسجيل الدخول
                var loginResult = _authService.Login(username, password);

                if (loginResult.IsSuccess)
                {
                    // تسجيل دخول ناجح
                    var currentUser = CurrentUserService.Instance;
                    currentUser.LoginUser(loginResult.User?.FullName ?? "مستخدم", loginResult.User?.Role ?? "مستخدم");

                    // إشعار بنجاح تسجيل الدخول
                    _notificationService.AddNotification(
                        "تسجيل دخول ناجح",
                        $"مرحباً بك {loginResult.User?.FullName ?? "مستخدم"}",
                        Models.EnhancedNotificationType.Success,
                        Models.EnhancedNotificationPriority.High,
                        "نظام");

                    // إغلاق نافذة تسجيل الدخول وفتح النافذة الرئيسية
                    var mainWindow = new MainWindow();
                    Application.Current.MainWindow = mainWindow;
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    // فشل تسجيل الدخول
                    ShowError(loginResult.ErrorMessage);
                    PasswordBox.Clear();
                    PasswordBox.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
            }
        }

        private void ShowError(string message)
        {
            ErrorMessage.Text = message;
            ErrorMessage.Visibility = Visibility.Visible;
        }
    }
}
