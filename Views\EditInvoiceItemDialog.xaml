<Window x:Class="SalesManagementSystem.Views.EditInvoiceItemDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تعديل عنصر الفاتورة" Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <Style x:Key="CenterAlignedTextBlock" TargetType="TextBlock">
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="15" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Edit" Width="24" Height="24"
                                       Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="تعديل عنصر الفاتورة" FontSize="18" FontWeight="Bold"
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <StackPanel Grid.Row="1">
            <!-- Product Name -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="اسم المنتج:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="ProductNameTextBox" Height="35" IsReadOnly="True"
                        Background="LightGray" FontWeight="Bold"/>
            </StackPanel>

            <!-- Quantity -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="الكمية:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="QuantityTextBox" Height="35" TextAlignment="Center"/>
            </StackPanel>

            <!-- Unit Price -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="سعر الوحدة:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="UnitPriceTextBox" Height="35" TextAlignment="Center"/>
            </StackPanel>

            <!-- Discount -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="الخصم:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="DiscountTextBox" Height="35" TextAlignment="Center" Text="0"/>
            </StackPanel>

            <!-- Discount Percentage -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="نسبة الخصم (%):" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="DiscountPercentageTextBox" Height="35" TextAlignment="Center" Text="0"
                        TextChanged="DiscountPercentageTextBox_TextChanged"/>
            </StackPanel>

            <!-- Total -->
            <StackPanel Margin="0,0,0,15">
                <TextBlock Text="الإجمالي:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TotalTextBox" Height="35" TextAlignment="Center"
                        IsReadOnly="True" Background="LightYellow" FontWeight="Bold"/>
            </StackPanel>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Content="✅ حفظ" Width="100" Height="35" Margin="5"
                   Background="Green" Foreground="White" FontWeight="Bold"
                   Click="Save_Click"/>

            <Button Content="❌ إلغاء" Width="100" Height="35" Margin="5"
                   Background="Red" Foreground="White" FontWeight="Bold"
                   Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
