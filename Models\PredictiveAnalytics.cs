using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج التحليلات التنبؤية والذكاء الاصطناعي
    /// </summary>
    public class PredictiveAnalytics : INotifyPropertyChanged
    {
        private int _id;
        private string _predictionName = string.Empty;
        private string _predictionCode = string.Empty;
        private PredictionType _predictionType = PredictionType.SalesForecast;
        private PredictionModel _model = PredictionModel.LinearRegression;
        private PredictionPeriod _predictionPeriod = PredictionPeriod.NextMonth;
        private DateTime _baseDate = DateTime.Now;
        private DateTime _predictionDate = DateTime.Now.AddMonths(1);
        private string _inputFeatures = string.Empty;
        private string _historicalData = string.Empty;
        private decimal _predictedValue;
        private decimal _confidenceLevel = 85.0m;
        private decimal _accuracyScore = 90.0m;
        private string _predictionRange = string.Empty;
        private string _seasonalFactors = string.Empty;
        private string _trendAnalysis = string.Empty;
        private string _riskFactors = string.Empty;
        private string _assumptions = string.Empty;
        private string _methodology = string.Empty;
        private string _dataQuality = string.Empty;
        private PredictionStatus _status = PredictionStatus.Generated;
        private DateTime _generatedAt = DateTime.Now;
        private string _generatedBy = string.Empty;
        private DateTime? _lastUpdated;
        private DateTime? _validUntil;
        private ObservableCollection<PredictionDataPoint> _dataPoints = new();
        private ObservableCollection<PredictionScenario> _scenarios = new();
        private ObservableCollection<PredictionAlert> _alerts = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PredictionName
        {
            get => _predictionName;
            set
            {
                if (_predictionName != value)
                {
                    _predictionName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PredictionCode
        {
            get => _predictionCode;
            set
            {
                if (_predictionCode != value)
                {
                    _predictionCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public PredictionType PredictionType
        {
            get => _predictionType;
            set
            {
                if (_predictionType != value)
                {
                    _predictionType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PredictionTypeDisplay));
                    OnPropertyChanged(nameof(PredictionTypeIcon));
                }
            }
        }

        public PredictionModel Model
        {
            get => _model;
            set
            {
                if (_model != value)
                {
                    _model = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ModelDisplay));
                }
            }
        }

        public PredictionPeriod PredictionPeriod
        {
            get => _predictionPeriod;
            set
            {
                if (_predictionPeriod != value)
                {
                    _predictionPeriod = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PredictionPeriodDisplay));
                }
            }
        }

        public DateTime BaseDate
        {
            get => _baseDate;
            set
            {
                if (_baseDate != value)
                {
                    _baseDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedBaseDate));
                }
            }
        }

        public DateTime PredictionDate
        {
            get => _predictionDate;
            set
            {
                if (_predictionDate != value)
                {
                    _predictionDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedPredictionDate));
                    OnPropertyChanged(nameof(DaysAhead));
                }
            }
        }

        public string InputFeatures
        {
            get => _inputFeatures;
            set
            {
                if (_inputFeatures != value)
                {
                    _inputFeatures = value;
                    OnPropertyChanged();
                }
            }
        }

        public string HistoricalData
        {
            get => _historicalData;
            set
            {
                if (_historicalData != value)
                {
                    _historicalData = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal PredictedValue
        {
            get => _predictedValue;
            set
            {
                if (_predictedValue != value)
                {
                    _predictedValue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedPredictedValue));
                }
            }
        }

        public decimal ConfidenceLevel
        {
            get => _confidenceLevel;
            set
            {
                if (_confidenceLevel != value)
                {
                    _confidenceLevel = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedConfidenceLevel));
                    OnPropertyChanged(nameof(ConfidenceColor));
                }
            }
        }

        public decimal AccuracyScore
        {
            get => _accuracyScore;
            set
            {
                if (_accuracyScore != value)
                {
                    _accuracyScore = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAccuracyScore));
                    OnPropertyChanged(nameof(AccuracyColor));
                }
            }
        }

        public string PredictionRange
        {
            get => _predictionRange;
            set
            {
                if (_predictionRange != value)
                {
                    _predictionRange = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SeasonalFactors
        {
            get => _seasonalFactors;
            set
            {
                if (_seasonalFactors != value)
                {
                    _seasonalFactors = value;
                    OnPropertyChanged();
                }
            }
        }

        public string TrendAnalysis
        {
            get => _trendAnalysis;
            set
            {
                if (_trendAnalysis != value)
                {
                    _trendAnalysis = value;
                    OnPropertyChanged();
                }
            }
        }

        public string RiskFactors
        {
            get => _riskFactors;
            set
            {
                if (_riskFactors != value)
                {
                    _riskFactors = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Assumptions
        {
            get => _assumptions;
            set
            {
                if (_assumptions != value)
                {
                    _assumptions = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Methodology
        {
            get => _methodology;
            set
            {
                if (_methodology != value)
                {
                    _methodology = value;
                    OnPropertyChanged();
                }
            }
        }

        public string DataQuality
        {
            get => _dataQuality;
            set
            {
                if (_dataQuality != value)
                {
                    _dataQuality = value;
                    OnPropertyChanged();
                }
            }
        }

        public PredictionStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                }
            }
        }

        public DateTime GeneratedAt
        {
            get => _generatedAt;
            set
            {
                if (_generatedAt != value)
                {
                    _generatedAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedGeneratedAt));
                }
            }
        }

        public string GeneratedBy
        {
            get => _generatedBy;
            set
            {
                if (_generatedBy != value)
                {
                    _generatedBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? LastUpdated
        {
            get => _lastUpdated;
            set
            {
                if (_lastUpdated != value)
                {
                    _lastUpdated = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLastUpdated));
                }
            }
        }

        public DateTime? ValidUntil
        {
            get => _validUntil;
            set
            {
                if (_validUntil != value)
                {
                    _validUntil = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedValidUntil));
                    OnPropertyChanged(nameof(IsValid));
                }
            }
        }

        public ObservableCollection<PredictionDataPoint> DataPoints
        {
            get => _dataPoints;
            set
            {
                if (_dataPoints != value)
                {
                    _dataPoints = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<PredictionScenario> Scenarios
        {
            get => _scenarios;
            set
            {
                if (_scenarios != value)
                {
                    _scenarios = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<PredictionAlert> Alerts
        {
            get => _alerts;
            set
            {
                if (_alerts != value)
                {
                    _alerts = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasAlerts));
                    OnPropertyChanged(nameof(AlertsCount));
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsValid => !ValidUntil.HasValue || ValidUntil > DateTime.Now;
        public bool HasAlerts => Alerts?.Any() == true;
        public int AlertsCount => Alerts?.Count ?? 0;
        public int DaysAhead => (int)(PredictionDate - BaseDate).TotalDays;

        // Display Properties
        public string PredictionTypeDisplay
        {
            get
            {
                return PredictionType switch
                {
                    PredictionType.SalesForecast => "توقع المبيعات",
                    PredictionType.DemandForecast => "توقع الطلب",
                    PredictionType.CustomerBehavior => "سلوك العملاء",
                    PredictionType.InventoryOptimization => "تحسين المخزون",
                    PredictionType.PriceOptimization => "تحسين الأسعار",
                    PredictionType.ChurnPrediction => "توقع فقدان العملاء",
                    PredictionType.RevenueProjection => "إسقاط الإيرادات",
                    PredictionType.MarketTrends => "اتجاهات السوق",
                    _ => "غير محدد"
                };
            }
        }

        public string ModelDisplay
        {
            get
            {
                return Model switch
                {
                    PredictionModel.LinearRegression => "الانحدار الخطي",
                    PredictionModel.RandomForest => "الغابة العشوائية",
                    PredictionModel.NeuralNetwork => "الشبكة العصبية",
                    PredictionModel.ARIMA => "نموذج ARIMA",
                    PredictionModel.SVM => "آلة الدعم الشعاعي",
                    PredictionModel.DecisionTree => "شجرة القرار",
                    PredictionModel.Ensemble => "النموذج المجمع",
                    PredictionModel.DeepLearning => "التعلم العميق",
                    _ => "غير محدد"
                };
            }
        }

        public string PredictionPeriodDisplay
        {
            get
            {
                return PredictionPeriod switch
                {
                    PredictionPeriod.NextWeek => "الأسبوع القادم",
                    PredictionPeriod.NextMonth => "الشهر القادم",
                    PredictionPeriod.NextQuarter => "الربع القادم",
                    PredictionPeriod.NextYear => "السنة القادمة",
                    PredictionPeriod.Custom => "فترة مخصصة",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    PredictionStatus.Generated => "تم الإنشاء",
                    PredictionStatus.Training => "قيد التدريب",
                    PredictionStatus.Validated => "تم التحقق",
                    PredictionStatus.Active => "نشط",
                    PredictionStatus.Expired => "منتهي الصلاحية",
                    PredictionStatus.Failed => "فشل",
                    _ => "غير محدد"
                };
            }
        }

        public string PredictionTypeIcon
        {
            get
            {
                return PredictionType switch
                {
                    PredictionType.SalesForecast => "TrendingUp",
                    PredictionType.DemandForecast => "ChartLine",
                    PredictionType.CustomerBehavior => "AccountGroup",
                    PredictionType.InventoryOptimization => "Warehouse",
                    PredictionType.PriceOptimization => "CurrencyUsd",
                    PredictionType.ChurnPrediction => "AccountMinus",
                    PredictionType.RevenueProjection => "ChartAreaspline",
                    PredictionType.MarketTrends => "Finance",
                    _ => "Crystal"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    PredictionStatus.Generated => "Blue",
                    PredictionStatus.Training => "Orange",
                    PredictionStatus.Validated => "Purple",
                    PredictionStatus.Active => "Green",
                    PredictionStatus.Expired => "Gray",
                    PredictionStatus.Failed => "Red",
                    _ => "Gray"
                };
            }
        }

        public string ConfidenceColor
        {
            get
            {
                return ConfidenceLevel switch
                {
                    >= 90 => "Green",
                    >= 75 => "Orange",
                    _ => "Red"
                };
            }
        }

        public string AccuracyColor
        {
            get
            {
                return AccuracyScore switch
                {
                    >= 90 => "Green",
                    >= 75 => "Orange",
                    _ => "Red"
                };
            }
        }

        // Formatted Properties
        public string FormattedPredictedValue => PredictedValue.ToString("N2");
        public string FormattedConfidenceLevel => $"{ConfidenceLevel:F1}%";
        public string FormattedAccuracyScore => $"{AccuracyScore:F1}%";
        public string FormattedBaseDate => BaseDate.ToString("dd/MM/yyyy");
        public string FormattedPredictionDate => PredictionDate.ToString("dd/MM/yyyy");
        public string FormattedGeneratedAt => GeneratedAt.ToString("dd/MM/yyyy HH:mm");
        public string FormattedLastUpdated => LastUpdated?.ToString("dd/MM/yyyy HH:mm") ?? "لم يتم التحديث";
        public string FormattedValidUntil => ValidUntil?.ToString("dd/MM/yyyy") ?? "غير محدد";

        #endregion

        #region Methods

        public void AddDataPoint(PredictionDataPoint dataPoint)
        {
            DataPoints.Add(dataPoint);
            LastUpdated = DateTime.Now;
        }

        public void AddScenario(PredictionScenario scenario)
        {
            Scenarios.Add(scenario);
            LastUpdated = DateTime.Now;
        }

        public void AddAlert(PredictionAlert alert)
        {
            Alerts.Add(alert);
            LastUpdated = DateTime.Now;
        }

        public void UpdateStatus(PredictionStatus newStatus)
        {
            Status = newStatus;
            LastUpdated = DateTime.Now;
        }

        public void UpdateAccuracy(decimal newAccuracy)
        {
            AccuracyScore = newAccuracy;
            LastUpdated = DateTime.Now;
        }

        public void Validate()
        {
            Status = PredictionStatus.Validated;
            LastUpdated = DateTime.Now;
        }

        public void Activate()
        {
            Status = PredictionStatus.Active;
            LastUpdated = DateTime.Now;
        }

        public void Expire()
        {
            Status = PredictionStatus.Expired;
            LastUpdated = DateTime.Now;
        }

        public decimal CalculateVariance()
        {
            if (!DataPoints.Any()) return 0;

            var values = DataPoints.Select(dp => dp.ActualValue).Where(v => v.HasValue).Select(v => v!.Value);
            if (!values.Any()) return 0;

            var mean = values.Average();
            var variance = values.Select(v => Math.Pow((double)(v - mean), 2)).Average();
            return (decimal)variance;
        }

        public decimal CalculateStandardDeviation()
        {
            return (decimal)Math.Sqrt((double)CalculateVariance());
        }

        public void GenerateScenarios()
        {
            // سيناريو متفائل
            Scenarios.Add(new PredictionScenario
            {
                PredictionId = Id,
                ScenarioName = "السيناريو المتفائل",
                ScenarioType = ScenarioType.Optimistic,
                PredictedValue = PredictedValue * 1.2m,
                Probability = 25,
                Description = "توقعات في حالة الظروف المثلى"
            });

            // السيناريو الأساسي
            Scenarios.Add(new PredictionScenario
            {
                PredictionId = Id,
                ScenarioName = "السيناريو الأساسي",
                ScenarioType = ScenarioType.Base,
                PredictedValue = PredictedValue,
                Probability = 50,
                Description = "التوقعات الأكثر احتمالاً"
            });

            // السيناريو المتشائم
            Scenarios.Add(new PredictionScenario
            {
                PredictionId = Id,
                ScenarioName = "السيناريو المتشائم",
                ScenarioType = ScenarioType.Pessimistic,
                PredictedValue = PredictedValue * 0.8m,
                Probability = 25,
                Description = "توقعات في حالة الظروف الصعبة"
            });
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// نقطة بيانات التنبؤ
    /// </summary>
    public class PredictionDataPoint
    {
        public int Id { get; set; }
        public int PredictionId { get; set; }
        public DateTime Date { get; set; } = DateTime.Now;
        public decimal PredictedValue { get; set; }
        public decimal? ActualValue { get; set; }
        public decimal? ErrorMargin { get; set; }
        public string Features { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;

        public decimal? Accuracy
        {
            get
            {
                if (!ActualValue.HasValue) return null;
                if (ActualValue == 0) return PredictedValue == 0 ? 100 : 0;
                return 100 - Math.Abs((PredictedValue - ActualValue.Value) / ActualValue.Value * 100);
            }
        }

        public string FormattedPredictedValue => PredictedValue.ToString("N2");
        public string FormattedActualValue => ActualValue?.ToString("N2") ?? "غير متوفر";
        public string FormattedAccuracy => Accuracy?.ToString("F1") + "%" ?? "غير محسوب";
    }

    /// <summary>
    /// سيناريو التنبؤ
    /// </summary>
    public class PredictionScenario
    {
        public int Id { get; set; }
        public int PredictionId { get; set; }
        public string ScenarioName { get; set; } = string.Empty;
        public ScenarioType ScenarioType { get; set; } = ScenarioType.Base;
        public decimal PredictedValue { get; set; }
        public decimal Probability { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Assumptions { get; set; } = string.Empty;
        public string RiskFactors { get; set; } = string.Empty;

        public string FormattedPredictedValue => PredictedValue.ToString("N2");
        public string FormattedProbability => $"{Probability:F0}%";

        public string ScenarioTypeDisplay
        {
            get
            {
                return ScenarioType switch
                {
                    ScenarioType.Optimistic => "متفائل",
                    ScenarioType.Base => "أساسي",
                    ScenarioType.Pessimistic => "متشائم",
                    ScenarioType.Custom => "مخصص",
                    _ => "غير محدد"
                };
            }
        }

        public string ScenarioColor
        {
            get
            {
                return ScenarioType switch
                {
                    ScenarioType.Optimistic => "Green",
                    ScenarioType.Base => "Blue",
                    ScenarioType.Pessimistic => "Red",
                    ScenarioType.Custom => "Purple",
                    _ => "Gray"
                };
            }
        }
    }

    /// <summary>
    /// تنبيه التنبؤ
    /// </summary>
    public class PredictionAlert
    {
        public int Id { get; set; }
        public int PredictionId { get; set; }
        public AlertType AlertType { get; set; } = AlertType.Accuracy;
        public AlertSeverity Severity { get; set; } = AlertSeverity.Medium;
        public string Message { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public bool IsRead { get; set; }
        public bool IsResolved { get; set; }

        public string AlertTypeDisplay
        {
            get
            {
                return AlertType switch
                {
                    AlertType.Accuracy => "دقة التنبؤ",
                    AlertType.DataQuality => "جودة البيانات",
                    AlertType.ModelDrift => "انحراف النموذج",
                    AlertType.Outlier => "قيم شاذة",
                    AlertType.Threshold => "تجاوز الحد",
                    _ => "غير محدد"
                };
            }
        }

        public string SeverityDisplay
        {
            get
            {
                return Severity switch
                {
                    AlertSeverity.Low => "منخفض",
                    AlertSeverity.Medium => "متوسط",
                    AlertSeverity.High => "عالي",
                    AlertSeverity.Critical => "حرج",
                    _ => "غير محدد"
                };
            }
        }

        public string SeverityColor
        {
            get
            {
                return Severity switch
                {
                    AlertSeverity.Low => "Green",
                    AlertSeverity.Medium => "Orange",
                    AlertSeverity.High => "Red",
                    AlertSeverity.Critical => "Purple",
                    _ => "Gray"
                };
            }
        }

        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");
    }

    #endregion

    #region Enums

    public enum PredictionType
    {
        SalesForecast,          // توقع المبيعات
        DemandForecast,         // توقع الطلب
        CustomerBehavior,       // سلوك العملاء
        InventoryOptimization,  // تحسين المخزون
        PriceOptimization,      // تحسين الأسعار
        ChurnPrediction,        // توقع فقدان العملاء
        RevenueProjection,      // إسقاط الإيرادات
        MarketTrends           // اتجاهات السوق
    }

    public enum PredictionModel
    {
        LinearRegression,       // الانحدار الخطي
        RandomForest,          // الغابة العشوائية
        NeuralNetwork,         // الشبكة العصبية
        ARIMA,                 // نموذج ARIMA
        SVM,                   // آلة الدعم الشعاعي
        DecisionTree,          // شجرة القرار
        Ensemble,              // النموذج المجمع
        DeepLearning          // التعلم العميق
    }

    public enum PredictionPeriod
    {
        NextWeek,              // الأسبوع القادم
        NextMonth,             // الشهر القادم
        NextQuarter,           // الربع القادم
        NextYear,              // السنة القادمة
        Custom                 // فترة مخصصة
    }

    public enum PredictionStatus
    {
        Generated,             // تم الإنشاء
        Training,              // قيد التدريب
        Validated,             // تم التحقق
        Active,                // نشط
        Expired,               // منتهي الصلاحية
        Failed                 // فشل
    }

    public enum ScenarioType
    {
        Optimistic,            // متفائل
        Base,                  // أساسي
        Pessimistic,           // متشائم
        Custom                 // مخصص
    }

    public enum AlertType
    {
        Accuracy,              // دقة التنبؤ
        DataQuality,           // جودة البيانات
        ModelDrift,            // انحراف النموذج
        Outlier,               // قيم شاذة
        Threshold              // تجاوز الحد
    }



    #endregion

    #region Validation

    public class PredictiveAnalyticsValidator : AbstractValidator<PredictiveAnalytics>
    {
        public PredictiveAnalyticsValidator()
        {
            RuleFor(p => p.PredictionName)
                .NotEmpty().WithMessage("اسم التنبؤ مطلوب")
                .MaximumLength(200).WithMessage("اسم التنبؤ لا يمكن أن يتجاوز 200 حرف");

            RuleFor(p => p.PredictionCode)
                .NotEmpty().WithMessage("كود التنبؤ مطلوب")
                .MaximumLength(50).WithMessage("كود التنبؤ لا يمكن أن يتجاوز 50 حرف");

            RuleFor(p => p.BaseDate)
                .LessThanOrEqualTo(p => p.PredictionDate).WithMessage("تاريخ الأساس يجب أن يكون قبل تاريخ التنبؤ");

            RuleFor(p => p.ConfidenceLevel)
                .InclusiveBetween(0, 100).WithMessage("مستوى الثقة يجب أن يكون بين 0 و 100");

            RuleFor(p => p.AccuracyScore)
                .InclusiveBetween(0, 100).WithMessage("درجة الدقة يجب أن تكون بين 0 و 100");

            RuleFor(p => p.PredictedValue)
                .GreaterThanOrEqualTo(0).WithMessage("القيمة المتوقعة لا يمكن أن تكون سالبة");
        }
    }

    #endregion
}
