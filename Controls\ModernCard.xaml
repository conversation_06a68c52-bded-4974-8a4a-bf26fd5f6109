<UserControl x:Class="SalesManagementSystem.Controls.ModernCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <UserControl.Resources>
        <!-- Card Hover Animation -->
        <Storyboard x:Key="CardHoverIn">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1.02" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1.02" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.BlurRadius)"
                           To="24" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.ShadowDepth)"
                           To="8" Duration="0:0:0.2"/>
        </Storyboard>

        <Storyboard x:Key="CardHoverOut">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.BlurRadius)"
                           To="16" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.ShadowDepth)"
                           To="4" Duration="0:0:0.2"/>
        </Storyboard>

        <!-- Click Animation -->
        <Storyboard x:Key="CardClick">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="0.98" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="0.98" Duration="0:0:0.1"/>
        </Storyboard>

        <Storyboard x:Key="CardClickRelease">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1.02" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1.02" Duration="0:0:0.1"/>
        </Storyboard>
    </UserControl.Resources>

    <Border x:Name="CardBorder"
            Background="{DynamicResource MaterialDesignCardBackground}"
            CornerRadius="12"
            Margin="8"
            Cursor="{Binding IsClickable, RelativeSource={RelativeSource AncestorType=UserControl},
                           Converter={StaticResource BooleanToCursorConverter}}"
            RenderTransformOrigin="0.5,0.5">

        <Border.RenderTransform>
            <ScaleTransform ScaleX="1" ScaleY="1"/>
        </Border.RenderTransform>

        <Border.Effect>
            <DropShadowEffect BlurRadius="16"
                            ShadowDepth="4"
                            Direction="270"
                            Opacity="0.1"
                            Color="#000000"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header Section -->
            <Border Grid.Row="0"
                    Background="{Binding HeaderBackground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    CornerRadius="12,12,0,0"
                    Visibility="{Binding HasHeader, RelativeSource={RelativeSource AncestorType=UserControl},
                               Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid Margin="20,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Header Icon -->
                    <materialDesign:PackIcon Grid.Column="0"
                                           Kind="{Binding HeaderIcon, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           Width="24" Height="24"
                                           Foreground="{Binding HeaderForeground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,12,0"
                                           Visibility="{Binding HeaderIcon, RelativeSource={RelativeSource AncestorType=UserControl},
                                                      Converter={StaticResource NullToVisibilityConverter}}"/>

                    <!-- Header Content -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock Text="{Binding HeaderTitle, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                 FontSize="18"
                                 FontWeight="Medium"
                                 Foreground="{Binding HeaderForeground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                 Visibility="{Binding HeaderTitle, RelativeSource={RelativeSource AncestorType=UserControl},
                                            Converter={StaticResource NullToVisibilityConverter}}"/>

                        <TextBlock Text="{Binding HeaderSubtitle, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                 FontSize="14"
                                 Opacity="0.7"
                                 Foreground="{Binding HeaderForeground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                 Margin="0,2,0,0"
                                 Visibility="{Binding HeaderSubtitle, RelativeSource={RelativeSource AncestorType=UserControl},
                                            Converter={StaticResource NullToVisibilityConverter}}"/>
                    </StackPanel>

                    <!-- Header Action -->
                    <ContentPresenter Grid.Column="2"
                                    Content="{Binding HeaderAction, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                    VerticalAlignment="Center"/>
                </Grid>
            </Border>

            <!-- Content Section -->
            <Border Grid.Row="1" Padding="20">
                <ContentPresenter Content="{Binding CardContent, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
            </Border>

            <!-- Footer Section -->
            <Border Grid.Row="2"
                    BorderThickness="0,1,0,0"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    Padding="20,16"
                    Visibility="{Binding HasFooter, RelativeSource={RelativeSource AncestorType=UserControl},
                               Converter={StaticResource BooleanToVisibilityConverter}}">
                <ContentPresenter Content="{Binding FooterContent, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
            </Border>

            <!-- Ripple Effect Overlay -->
            <Border x:Name="RippleOverlay"
                    Background="Transparent"
                    CornerRadius="12"
                    Visibility="{Binding IsClickable, RelativeSource={RelativeSource AncestorType=UserControl},
                               Converter={StaticResource BooleanToVisibilityConverter}}">
                <Ellipse x:Name="RippleEllipse"
                         Fill="{DynamicResource PrimaryHueMidBrush}"
                         Opacity="0"
                         RenderTransformOrigin="0.5,0.5">
                    <Ellipse.RenderTransform>
                        <ScaleTransform ScaleX="0" ScaleY="0"/>
                    </Ellipse.RenderTransform>
                </Ellipse>
            </Border>
        </Grid>
    </Border>

    <!-- Event Triggers -->
    <UserControl.Triggers>
        <EventTrigger RoutedEvent="MouseEnter">
            <BeginStoryboard Storyboard="{StaticResource CardHoverIn}"/>
        </EventTrigger>

        <EventTrigger RoutedEvent="MouseLeave">
            <BeginStoryboard Storyboard="{StaticResource CardHoverOut}"/>
        </EventTrigger>

        <EventTrigger RoutedEvent="PreviewMouseLeftButtonDown">
            <BeginStoryboard Storyboard="{StaticResource CardClick}"/>
        </EventTrigger>

        <EventTrigger RoutedEvent="PreviewMouseLeftButtonUp">
            <BeginStoryboard Storyboard="{StaticResource CardClickRelease}"/>
        </EventTrigger>
    </UserControl.Triggers>

</UserControl>
