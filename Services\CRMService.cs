using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة علاقات العملاء المتكاملة
    /// </summary>
    public class CRMService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;
        private readonly EmailService _emailService;

        public CRMService(
            DatabaseService dbService,
            NotificationService notificationService,
            EmailService emailService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
        }

        #region Customer Management

        /// <summary>
        /// الحصول على جميع العملاء
        /// </summary>
        public async Task<IEnumerable<CrmCustomer>> GetAllCustomersAsync()
        {
            try
            {
                const string sql = "SELECT * FROM CrmCustomers ORDER BY CreatedAt DESC";
                var customersData = await _dbService.QueryAsync<dynamic>(sql);
                return customersData.Select(MapToCrmCustomer);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على العملاء");
                throw;
            }
        }

        /// <summary>
        /// البحث عن العملاء
        /// </summary>
        public async Task<IEnumerable<CrmCustomer>> SearchCustomersAsync(string searchTerm, CustomerStatus? status = null, CustomerCategory? category = null)
        {
            try
            {
                var sql = @"
                    SELECT * FROM CrmCustomers
                    WHERE (CompanyName LIKE @SearchTerm OR ContactPerson LIKE @SearchTerm OR PrimaryEmail LIKE @SearchTerm)";

                var parameters = new Dictionary<string, object>
                {
                    ["SearchTerm"] = $"%{searchTerm}%"
                };

                if (status.HasValue)
                {
                    sql += " AND Status = @Status";
                    parameters["Status"] = status?.ToString() ?? string.Empty;
                }

                if (category.HasValue)
                {
                    sql += " AND Category = @Category";
                    parameters["Category"] = category?.ToString() ?? string.Empty;
                }

                sql += " ORDER BY CreatedAt DESC";

                var customersData = await _dbService.QueryAsync<dynamic>(sql, parameters);
                return customersData.Select(MapToCrmCustomer);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في البحث عن العملاء: {searchTerm}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء عميل جديد
        /// </summary>
        public async Task<int> CreateCustomerAsync(CrmCustomer customer)
        {
            try
            {
                // توليد رقم العميل إذا لم يكن موجود
                if (string.IsNullOrEmpty(customer.CustomerNumber))
                {
                    customer.CustomerNumber = await GenerateCustomerNumberAsync();
                }

                const string sql = @"
                    INSERT INTO CrmCustomers (
                        CustomerNumber, CompanyName, ContactPerson, CustomerType, Status, Category,
                        Industry, Website, TaxNumber, CommercialRegister, PrimaryEmail, PrimaryPhone,
                        SecondaryPhone, Fax, Address, City, State, Country, PostalCode, CreditLimit,
                        CurrentBalance, Source, AssignedSalesperson, DiscountPercentage, Notes, Tags,
                        CreatedAt, CreatedBy
                    ) VALUES (
                        @CustomerNumber, @CompanyName, @ContactPerson, @CustomerType, @Status, @Category,
                        @Industry, @Website, @TaxNumber, @CommercialRegister, @PrimaryEmail, @PrimaryPhone,
                        @SecondaryPhone, @Fax, @Address, @City, @State, @Country, @PostalCode, @CreditLimit,
                        @CurrentBalance, @Source, @AssignedSalesperson, @DiscountPercentage, @Notes, @Tags,
                        @CreatedAt, @CreatedBy
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    customer.CustomerNumber,
                    customer.CompanyName,
                    customer.ContactPerson,
                    CustomerType = customer.CustomerType.ToString(),
                    Status = customer.Status.ToString(),
                    Category = customer.Category.ToString(),
                    customer.Industry,
                    customer.Website,
                    customer.TaxNumber,
                    customer.CommercialRegister,
                    customer.PrimaryEmail,
                    customer.PrimaryPhone,
                    customer.SecondaryPhone,
                    customer.Fax,
                    customer.Address,
                    customer.City,
                    customer.State,
                    customer.Country,
                    customer.PostalCode,
                    customer.CreditLimit,
                    customer.CurrentBalance,
                    Source = customer.Source.ToString(),
                    customer.AssignedSalesperson,
                    customer.DiscountPercentage,
                    customer.Notes,
                    customer.Tags,
                    CreatedAt = customer.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    customer.CreatedBy
                };

                var customerId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                customer.Id = customerId;

                // إضافة نشاط إنشاء العميل
                await AddCustomerActivityAsync(customerId, "إنشاء عميل", $"تم إنشاء العميل: {customer.DisplayName}", customer.CreatedBy);

                // إرسال إشعار
                await _notificationService.SendNotificationAsync(
                    "عميل جديد",
                    $"تم إضافة عميل جديد: {customer.DisplayName}",
                    Models.NotificationType.Info
                );

                LoggingService.LogInfo($"تم إنشاء عميل جديد: {customer.DisplayName}");
                return customerId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء العميل: {customer?.DisplayName}");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات العميل
        /// </summary>
        public async Task UpdateCustomerAsync(CrmCustomer customer)
        {
            try
            {
                customer.UpdatedAt = DateTime.Now;

                const string sql = @"
                    UPDATE CrmCustomers SET
                        CompanyName = @CompanyName,
                        ContactPerson = @ContactPerson,
                        Status = @Status,
                        Category = @Category,
                        Industry = @Industry,
                        Website = @Website,
                        TaxNumber = @TaxNumber,
                        CommercialRegister = @CommercialRegister,
                        PrimaryEmail = @PrimaryEmail,
                        PrimaryPhone = @PrimaryPhone,
                        SecondaryPhone = @SecondaryPhone,
                        Fax = @Fax,
                        Address = @Address,
                        City = @City,
                        State = @State,
                        Country = @Country,
                        PostalCode = @PostalCode,
                        CreditLimit = @CreditLimit,
                        AssignedSalesperson = @AssignedSalesperson,
                        DiscountPercentage = @DiscountPercentage,
                        Notes = @Notes,
                        Tags = @Tags,
                        UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                await _dbService.ExecuteAsync(sql, new
                {
                    customer.Id,
                    customer.CompanyName,
                    customer.ContactPerson,
                    Status = customer.Status.ToString(),
                    Category = customer.Category.ToString(),
                    customer.Industry,
                    customer.Website,
                    customer.TaxNumber,
                    customer.CommercialRegister,
                    customer.PrimaryEmail,
                    customer.PrimaryPhone,
                    customer.SecondaryPhone,
                    customer.Fax,
                    customer.Address,
                    customer.City,
                    customer.State,
                    customer.Country,
                    customer.PostalCode,
                    customer.CreditLimit,
                    customer.AssignedSalesperson,
                    customer.DiscountPercentage,
                    customer.Notes,
                    customer.Tags,
                    UpdatedAt = customer.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss")
                });

                // إضافة نشاط تحديث العميل
                await AddCustomerActivityAsync(customer.Id, "تحديث بيانات", $"تم تحديث بيانات العميل: {customer.DisplayName}");

                LoggingService.LogInfo($"تم تحديث العميل: {customer.DisplayName}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث العميل: {customer?.DisplayName}");
                throw;
            }
        }

        /// <summary>
        /// حذف العميل
        /// </summary>
        public async Task<bool> DeleteCustomerAsync(int customerId)
        {
            try
            {
                // التحقق من وجود طلبات مرتبطة
                const string checkOrdersSql = "SELECT COUNT(*) FROM Orders WHERE CustomerId = @CustomerId";
                var ordersCount = await _dbService.QuerySingleAsync<int>(checkOrdersSql, new { CustomerId = customerId });

                if (ordersCount > 0)
                {
                    throw new InvalidOperationException("لا يمكن حذف العميل لوجود طلبات مرتبطة به");
                }

                const string sql = "DELETE FROM CrmCustomers WHERE Id = @Id";
                var rowsAffected = await _dbService.ExecuteAsync(sql, new { Id = customerId });

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم حذف العميل: {customerId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في حذف العميل: {customerId}");
                throw;
            }
        }

        #endregion

        #region Opportunity Management

        /// <summary>
        /// الحصول على جميع الفرص التجارية
        /// </summary>
        public async Task<IEnumerable<SalesOpportunity>> GetAllOpportunitiesAsync()
        {
            try
            {
                const string sql = @"
                    SELECT o.*, c.CompanyName as CustomerName
                    FROM SalesOpportunities o
                    LEFT JOIN CrmCustomers c ON o.CustomerId = c.Id
                    ORDER BY o.CreatedAt DESC";

                var opportunitiesData = await _dbService.QueryAsync<dynamic>(sql);
                return opportunitiesData.Select(MapToSalesOpportunity);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على الفرص التجارية");
                throw;
            }
        }

        /// <summary>
        /// إنشاء فرصة تجارية جديدة
        /// </summary>
        public async Task<int> CreateOpportunityAsync(SalesOpportunity opportunity)
        {
            try
            {
                // توليد كود الفرصة إذا لم يكن موجود
                if (string.IsNullOrEmpty(opportunity.OpportunityCode))
                {
                    opportunity.OpportunityCode = await GenerateOpportunityCodeAsync();
                }

                const string sql = @"
                    INSERT INTO SalesOpportunities (
                        OpportunityCode, Title, Description, CustomerId, AssignedSalesRepId,
                        Stage, Status, Type, Priority, Source, EstimatedValue, ProbabilityPercentage,
                        ExpectedCloseDate, CompetitorInfo, KeyDecisionMakers, PainPoints,
                        ProposedSolution, NextSteps, Tags, Notes, CreatedAt, CreatedBy
                    ) VALUES (
                        @OpportunityCode, @Title, @Description, @CustomerId, @AssignedSalesRepId,
                        @Stage, @Status, @Type, @Priority, @Source, @EstimatedValue, @ProbabilityPercentage,
                        @ExpectedCloseDate, @CompetitorInfo, @KeyDecisionMakers, @PainPoints,
                        @ProposedSolution, @NextSteps, @Tags, @Notes, @CreatedAt, @CreatedBy
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    opportunity.OpportunityCode,
                    opportunity.Title,
                    opportunity.Description,
                    opportunity.CustomerId,
                    opportunity.AssignedSalesRepId,
                    Stage = opportunity.Stage.ToString(),
                    Status = opportunity.Status.ToString(),
                    Type = opportunity.Type.ToString(),
                    Priority = opportunity.Priority.ToString(),
                    Source = opportunity.Source.ToString(),
                    opportunity.EstimatedValue,
                    opportunity.ProbabilityPercentage,
                    ExpectedCloseDate = opportunity.ExpectedCloseDate.ToString("yyyy-MM-dd"),
                    opportunity.CompetitorInfo,
                    opportunity.KeyDecisionMakers,
                    opportunity.PainPoints,
                    opportunity.ProposedSolution,
                    opportunity.NextSteps,
                    opportunity.Tags,
                    opportunity.Notes,
                    CreatedAt = opportunity.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    opportunity.CreatedBy
                };

                var opportunityId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                opportunity.Id = opportunityId;

                // إضافة نشاط إنشاء الفرصة
                await AddOpportunityActivityAsync(opportunityId, "إنشاء فرصة", $"تم إنشاء فرصة تجارية جديدة: {opportunity.Title}", opportunity.CreatedBy);

                // إرسال إشعار
                await _notificationService.SendNotificationAsync(
                    "فرصة تجارية جديدة",
                    $"تم إضافة فرصة تجارية جديدة: {opportunity.Title}",
                    Models.NotificationType.Info
                );

                LoggingService.LogInfo($"تم إنشاء فرصة تجارية جديدة: {opportunity.Title}");
                return opportunityId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء الفرصة التجارية: {opportunity?.Title}");
                throw;
            }
        }

        /// <summary>
        /// تحديث مرحلة الفرصة التجارية
        /// </summary>
        public async Task UpdateOpportunityStageAsync(int opportunityId, OpportunityStage newStage, string notes = "")
        {
            try
            {
                const string sql = @"
                    UPDATE SalesOpportunities SET
                        Stage = @Stage,
                        UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                await _dbService.ExecuteAsync(sql, new
                {
                    Id = opportunityId,
                    Stage = newStage.ToString(),
                    UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });

                // إضافة نشاط تحديث المرحلة
                var stageDisplay = GetStageDisplay(newStage);
                await AddOpportunityActivityAsync(opportunityId, "تحديث المرحلة", $"تم تحديث المرحلة إلى: {stageDisplay}. {notes}");

                LoggingService.LogInfo($"تم تحديث مرحلة الفرصة {opportunityId} إلى {stageDisplay}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث مرحلة الفرصة: {opportunityId}");
                throw;
            }
        }

        #endregion

        #region Campaign Management

        /// <summary>
        /// الحصول على جميع الحملات التسويقية
        /// </summary>
        public async Task<IEnumerable<MarketingCampaign>> GetAllCampaignsAsync()
        {
            try
            {
                const string sql = "SELECT * FROM MarketingCampaigns ORDER BY CreatedAt DESC";
                var campaignsData = await _dbService.QueryAsync<dynamic>(sql);
                return campaignsData.Select(MapToMarketingCampaign);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على الحملات التسويقية");
                throw;
            }
        }

        /// <summary>
        /// إنشاء حملة تسويقية جديدة
        /// </summary>
        public async Task<int> CreateCampaignAsync(MarketingCampaign campaign)
        {
            try
            {
                // توليد كود الحملة إذا لم يكن موجود
                if (string.IsNullOrEmpty(campaign.CampaignCode))
                {
                    campaign.CampaignCode = await GenerateCampaignCodeAsync();
                }

                const string sql = @"
                    INSERT INTO MarketingCampaigns (
                        CampaignCode, Name, Description, Type, Status, Priority, TargetAudience,
                        Objectives, Budget, StartDate, EndDate, Channels, Content, CallToAction,
                        LandingPageUrl, TrackingCode, TargetReach, Tags, Notes, CreatedAt, CreatedBy,
                        AssignedManagerId
                    ) VALUES (
                        @CampaignCode, @Name, @Description, @Type, @Status, @Priority, @TargetAudience,
                        @Objectives, @Budget, @StartDate, @EndDate, @Channels, @Content, @CallToAction,
                        @LandingPageUrl, @TrackingCode, @TargetReach, @Tags, @Notes, @CreatedAt, @CreatedBy,
                        @AssignedManagerId
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    campaign.CampaignCode,
                    campaign.Name,
                    campaign.Description,
                    Type = campaign.Type.ToString(),
                    Status = campaign.Status.ToString(),
                    Priority = campaign.Priority.ToString(),
                    campaign.TargetAudience,
                    campaign.Objectives,
                    campaign.Budget,
                    StartDate = campaign.StartDate.ToString("yyyy-MM-dd"),
                    EndDate = campaign.EndDate.ToString("yyyy-MM-dd"),
                    campaign.Channels,
                    campaign.Content,
                    campaign.CallToAction,
                    campaign.LandingPageUrl,
                    campaign.TrackingCode,
                    campaign.TargetReach,
                    campaign.Tags,
                    campaign.Notes,
                    CreatedAt = campaign.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    campaign.CreatedBy,
                    campaign.AssignedManagerId
                };

                var campaignId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                campaign.Id = campaignId;

                // إرسال إشعار
                await _notificationService.SendNotificationAsync(
                    "حملة تسويقية جديدة",
                    $"تم إنشاء حملة تسويقية جديدة: {campaign.Name}",
                    Models.NotificationType.Info
                );

                LoggingService.LogInfo($"تم إنشاء حملة تسويقية جديدة: {campaign.Name}");
                return campaignId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء الحملة التسويقية: {campaign?.Name}");
                throw;
            }
        }

        #endregion

        #region Activity Management

        /// <summary>
        /// إضافة نشاط للعميل
        /// </summary>
        public async Task AddCustomerActivityAsync(int customerId, string activityType, string description, string performedBy = "")
        {
            try
            {
                const string sql = @"
                    INSERT INTO CustomerActivities (
                        CustomerId, ActivityType, Description, ActivityDate, PerformedBy
                    ) VALUES (
                        @CustomerId, @ActivityType, @Description, @ActivityDate, @PerformedBy
                    )";

                await _dbService.ExecuteAsync(sql, new
                {
                    CustomerId = customerId,
                    ActivityType = activityType,
                    Description = description,
                    ActivityDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    PerformedBy = performedBy
                });

                // تحديث تاريخ آخر اتصال للعميل
                const string updateCustomerSql = @"
                    UPDATE CrmCustomers SET
                        LastContactDate = @LastContactDate,
                        UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                await _dbService.ExecuteAsync(updateCustomerSql, new
                {
                    Id = customerId,
                    LastContactDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إضافة نشاط للعميل: {customerId}");
                throw;
            }
        }

        /// <summary>
        /// إضافة نشاط للفرصة التجارية
        /// </summary>
        public async Task AddOpportunityActivityAsync(int opportunityId, string activityType, string description, string performedBy = "")
        {
            try
            {
                const string sql = @"
                    INSERT INTO OpportunityActivities (
                        OpportunityId, ActivityType, Description, ActivityDate, PerformedBy
                    ) VALUES (
                        @OpportunityId, @ActivityType, @Description, @ActivityDate, @PerformedBy
                    )";

                await _dbService.ExecuteAsync(sql, new
                {
                    OpportunityId = opportunityId,
                    ActivityType = activityType,
                    Description = description,
                    ActivityDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    PerformedBy = performedBy
                });

                // تحديث تاريخ آخر نشاط للفرصة
                const string updateOpportunitySql = @"
                    UPDATE SalesOpportunities SET
                        LastActivityDate = @LastActivityDate,
                        UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                await _dbService.ExecuteAsync(updateOpportunitySql, new
                {
                    Id = opportunityId,
                    LastActivityDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إضافة نشاط للفرصة: {opportunityId}");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        private async Task<string> GenerateCustomerNumberAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM CrmCustomers WHERE DATE(CreatedAt) = DATE('now')";
                var todayCount = await _dbService.QuerySingleAsync<int>(sql);

                var today = DateTime.Now;
                return $"CUS{today:yyyyMMdd}{(todayCount + 1):D4}";
            }
            catch
            {
                return $"CUS{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        private async Task<string> GenerateOpportunityCodeAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM SalesOpportunities WHERE DATE(CreatedAt) = DATE('now')";
                var todayCount = await _dbService.QuerySingleAsync<int>(sql);

                var today = DateTime.Now;
                return $"OPP{today:yyyyMMdd}{(todayCount + 1):D4}";
            }
            catch
            {
                return $"OPP{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        private async Task<string> GenerateCampaignCodeAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM MarketingCampaigns WHERE DATE(CreatedAt) = DATE('now')";
                var todayCount = await _dbService.QuerySingleAsync<int>(sql);

                var today = DateTime.Now;
                return $"CAM{today:yyyyMMdd}{(todayCount + 1):D4}";
            }
            catch
            {
                return $"CAM{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        private string GetStageDisplay(OpportunityStage stage)
        {
            return stage switch
            {
                OpportunityStage.Prospecting => "البحث عن العملاء",
                OpportunityStage.Qualification => "التأهيل",
                OpportunityStage.NeedsAnalysis => "تحليل الاحتياجات",
                OpportunityStage.Proposal => "تقديم العرض",
                OpportunityStage.Negotiation => "التفاوض",
                OpportunityStage.Closing => "الإغلاق",
                _ => "غير محدد"
            };
        }

        // Mapping methods would be implemented here
        private CrmCustomer MapToCrmCustomer(dynamic data)
        {
            // Implementation for mapping database data to CrmCustomer object
            // This would include parsing enums and dates
            return new CrmCustomer(); // Placeholder
        }

        private SalesOpportunity MapToSalesOpportunity(dynamic data)
        {
            // Implementation for mapping database data to SalesOpportunity object
            return new SalesOpportunity(); // Placeholder
        }

        private MarketingCampaign MapToMarketingCampaign(dynamic data)
        {
            // Implementation for mapping database data to MarketingCampaign object
            return new MarketingCampaign(); // Placeholder
        }

        #endregion
    }
}
