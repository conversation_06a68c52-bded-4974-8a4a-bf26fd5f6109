<Window x:Class="SalesManagementSystem.Views.Dialogs.CustomerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding WindowTitle}"
        Height="750"
        Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>

        <Style x:Key="FormTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="FormComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="{Binding HeaderIcon}"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="{Binding WindowTitle}"
                      Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                      VerticalAlignment="Center"
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Customer Code -->
                <TextBox x:Name="CodeTextBox"
                        Style="{StaticResource FormTextBox}"
                        materialDesign:HintAssist.Hint="كود العميل *"
                        Text="{Binding Customer.Code, UpdateSourceTrigger=PropertyChanged}"
                        IsEnabled="{Binding IsCodeEditable}"/>

                <!-- Customer Name -->
                <TextBox x:Name="NameTextBox"
                        Style="{StaticResource FormTextBox}"
                        materialDesign:HintAssist.Hint="اسم العميل *"
                        Text="{Binding Customer.Name, UpdateSourceTrigger=PropertyChanged}"/>

                <!-- Contact Information -->
                <GroupBox Header="معلومات الاتصال"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,16,0,8">
                    <StackPanel Margin="16">
                        <!-- Phone -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="رقم الهاتف *"
                                Text="{Binding Customer.Phone, UpdateSourceTrigger=PropertyChanged}"/>

                        <!-- Email -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="البريد الإلكتروني"
                                Text="{Binding Customer.Email, UpdateSourceTrigger=PropertyChanged}"/>

                        <!-- Address -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="العنوان"
                                Text="{Binding Customer.Address, UpdateSourceTrigger=PropertyChanged}"
                                Height="80"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </GroupBox>

                <!-- Financial Information -->
                <GroupBox Header="المعلومات المالية"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,8">
                    <StackPanel Margin="16">
                        <!-- Credit Limit -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="الحد الائتماني"
                                Text="{Binding Customer.CreditLimit, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"/>

                        <!-- Current Balance -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="الرصيد الحالي"
                                Text="{Binding Customer.Balance, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"
                                IsReadOnly="{Binding IsEditMode}"/>

                        <!-- Payment Terms -->
                        <ComboBox Style="{StaticResource FormComboBox}"
                                 materialDesign:HintAssist.Hint="شروط الدفع"
                                 ItemsSource="{Binding PaymentTerms}"
                                 SelectedItem="{Binding Customer.PaymentTerms}"
                                 IsEditable="True"/>

                        <!-- Credit Status Display -->
                        <Border Background="{DynamicResource MaterialDesignSelection}"
                               CornerRadius="4"
                               Padding="12,8"
                               Margin="0,8"
                               Visibility="{Binding IsOverCreditLimit, Converter={StaticResource BoolToVisConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                       Foreground="Red"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="تحذير: تجاوز الحد الائتماني"
                                          Foreground="Red"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </GroupBox>

                <!-- Additional Information -->
                <GroupBox Header="معلومات إضافية"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,8">
                    <StackPanel Margin="16">
                        <!-- Customer Type -->
                        <ComboBox Style="{StaticResource FormComboBox}"
                                 materialDesign:HintAssist.Hint="نوع العميل"
                                 ItemsSource="{Binding CustomerTypes}"
                                 SelectedItem="{Binding Customer.CustomerType}"/>

                        <!-- Tax Number -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="الرقم الضريبي"
                                Text="{Binding Customer.TaxNumber, UpdateSourceTrigger=PropertyChanged}"/>

                        <!-- Notes -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="ملاحظات"
                                Text="{Binding Customer.Notes, UpdateSourceTrigger=PropertyChanged}"
                                Height="80"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                VerticalScrollBarVisibility="Auto"/>

                        <!-- Is Active -->
                        <CheckBox Content="العميل نشط"
                                 IsChecked="{Binding Customer.IsActive}"
                                 Margin="0,8"
                                 Style="{DynamicResource MaterialDesignCheckBox}"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2"
                   Orientation="Horizontal"
                   HorizontalAlignment="Left"
                   Margin="0,24,0,0">

            <Button Command="{Binding SaveCommand}"
                   Style="{DynamicResource MaterialDesignRaisedButton}"
                   Width="100"
                   Margin="0,0,12,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding CancelCommand}"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Width="100">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cancel"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button.Content>
            </Button>
        </StackPanel>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}">
            <StackPanel HorizontalAlignment="Center"
                       VerticalAlignment="Center">
                <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="48" Height="48"/>
                <TextBlock Text="{Binding LoadingMessage}"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
