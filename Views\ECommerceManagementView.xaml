<UserControl x:Class="SalesManagementSystem.Views.ECommerceManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- تحويل حالة المتجر إلى لون -->
        <Style x:Key="StoreStatusCard" TargetType="Border">
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Active">
                    <Setter Property="Background" Value="#E8F5E8"/>
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Inactive">
                    <Setter Property="Background" Value="#FAFAFA"/>
                    <Setter Property="BorderBrush" Value="#9E9E9E"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Maintenance">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Suspended">
                    <Setter Property="Background" Value="#FFEBEE"/>
                    <Setter Property="BorderBrush" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل حالة المتجر إلى لون النص -->
        <Style x:Key="StatusIndicator" TargetType="Ellipse">
            <Setter Property="Fill" Value="Gray"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Active">
                    <Setter Property="Fill" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Inactive">
                    <Setter Property="Fill" Value="#9E9E9E"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Maintenance">
                    <Setter Property="Fill" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Suspended">
                    <Setter Property="Fill" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Store" Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="إدارة التجارة الإلكترونية"
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding CreateStoreCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إنشاء متجر"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding ManagePaymentMethodsCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CreditCard" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="طرق الدفع"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding ManageShippingMethodsCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Truck" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="طرق الشحن"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات سريعة -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي المتاجر -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Store" Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalStores}"
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي المتاجر"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- المتاجر النشطة -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CheckCircle" Width="32" Height="32"
                                           Foreground="Green"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ActiveStores}"
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Green"/>
                    <TextBlock Text="متاجر نشطة"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- إجمالي المنتجات -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Package" Width="32" Height="32"
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalProducts}"
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي المنتجات"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- إجمالي الطلبات -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ShoppingCart" Width="32" Height="32"
                                           Foreground="Orange"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalOrders}"
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Orange"/>
                    <TextBlock Text="إجمالي الطلبات"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- قائمة المتاجر -->
        <ScrollViewer Grid.Row="2" Margin="16,8,16,16" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding Stores}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="2" Margin="0,0,0,16"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>

                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <materialDesign:Card Margin="8"
                                           Style="{StaticResource StoreStatusCard}"
                                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
                            <Border BorderThickness="2" CornerRadius="4">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- رأس البطاقة -->
                                    <Grid Grid.Row="0" Background="{Binding StatusColor, Converter={StaticResource ColorToBrushConverter}}"
                                          Opacity="0.1">
                                        <Grid Margin="16,12">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- شعار المتجر -->
                                            <Border Grid.Column="0"
                                                  Width="48" Height="48"
                                                  CornerRadius="24"
                                                  Background="White"
                                                  VerticalAlignment="Center"
                                                  Margin="0,0,12,0">
                                                <Image Source="{Binding Logo}"
                                                     Stretch="UniformToFill"
                                                     Width="40" Height="40"/>
                                                <Border.Effect>
                                                    <DropShadowEffect ShadowDepth="2" BlurRadius="4" Opacity="0.3"/>
                                                </Border.Effect>
                                            </Border>

                                            <!-- اسم المتجر -->
                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding StoreName}"
                                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                         FontWeight="Bold"/>
                                                <TextBlock Text="{Binding StoreCode}"
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         Opacity="0.7"/>
                                                <TextBlock Text="{Binding Domain}"
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         Opacity="0.7"/>
                                            </StackPanel>

                                            <!-- مؤشر الحالة -->
                                            <Ellipse Grid.Column="2"
                                                   Width="12" Height="12"
                                                   Style="{StaticResource StatusIndicator}"
                                                   VerticalAlignment="Center"
                                                   Margin="8,0"/>

                                            <!-- نوع المتجر -->
                                            <materialDesign:Chip Grid.Column="3"
                                                               Content="{Binding StoreTypeDisplay}"
                                                               VerticalAlignment="Center"
                                                               FontSize="10"/>
                                        </Grid>
                                    </Grid>

                                    <!-- محتوى البطاقة -->
                                    <StackPanel Grid.Row="1" Margin="16">
                                        <!-- الوصف -->
                                        <TextBlock Text="{Binding Description}"
                                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                 TextWrapping="Wrap"
                                                 MaxHeight="60"
                                                 Margin="0,0,0,12"/>

                                        <!-- معلومات الاتصال -->
                                        <Grid Margin="0,0,0,8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="البريد الإلكتروني" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding ContactEmail}" Style="{StaticResource MaterialDesignBody2TextBlock}" FontSize="11"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="الهاتف" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding ContactPhone}" Style="{StaticResource MaterialDesignBody2TextBlock}" FontSize="11"/>
                                            </StackPanel>
                                        </Grid>

                                        <!-- العملة واللغة -->
                                        <Grid Margin="0,0,0,8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="العملة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding Currency}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="اللغة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding Language}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            </StackPanel>
                                        </Grid>
                                    </StackPanel>

                                    <!-- إحصائيات سريعة -->
                                    <Grid Grid.Row="2" Margin="16,0,16,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding CategoriesCount}"
                                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                            <TextBlock Text="فئة"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Opacity="0.7"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding ProductsCount}"
                                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                                            <TextBlock Text="منتج"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Opacity="0.7"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding OrdersCount}"
                                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Foreground="Orange"/>
                                            <TextBlock Text="طلب"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Opacity="0.7"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding FormattedTotalRevenue}"
                                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Foreground="Green"
                                                     FontSize="12"/>
                                            <TextBlock Text="إيرادات"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Opacity="0.7"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- أزرار الإجراءات -->
                                    <Grid Grid.Row="3" Margin="16,8,16,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- زر زيارة المتجر -->
                                        <Button Grid.Column="1"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.VisitStoreCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="زيارة المتجر">
                                            <materialDesign:PackIcon Kind="Web" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر إدارة المنتجات -->
                                        <Button Grid.Column="2"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.ManageProductsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="إدارة المنتجات">
                                            <materialDesign:PackIcon Kind="Package" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر إدارة الطلبات -->
                                        <Button Grid.Column="3"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.ManageOrdersCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="إدارة الطلبات">
                                            <materialDesign:PackIcon Kind="ShoppingCart" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر الإعدادات -->
                                        <Button Grid.Column="4"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.StoreSettingsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="إعدادات المتجر">
                                            <materialDesign:PackIcon Kind="Settings" Width="18" Height="18"/>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Border>
                        </materialDesign:Card>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Grid Grid.Row="2"
              Background="White"
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري التحميل..."
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
