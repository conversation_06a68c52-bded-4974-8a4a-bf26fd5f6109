<Window x:Class="SalesManagementSystem.Views.PurchaseWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="فاتورة شراء جديدة"
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="80"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="DarkBlue">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="📦" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="فاتورة شراء جديدة - ملء المخزون" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <TabControl Grid.Row="1" Margin="10">
            <TabItem Header="بيانات المورد">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Supplier Info -->
                    <GroupBox Grid.Row="0" Header="بيانات المورد" Margin="0,0,0,20">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المورد:" Margin="0,0,0,5"/>
                            <ComboBox Grid.Row="0" Grid.Column="0" Margin="0,20,10,10" IsEditable="True">
                                <ComboBoxItem Content="شركة التقنية المتقدمة"/>
                                <ComboBoxItem Content="مؤسسة الحاسوب"/>
                                <ComboBoxItem Content="شركة الإلكترونيات"/>
                                <ComboBoxItem Content="مورد جديد"/>
                            </ComboBox>

                            <TextBlock Grid.Row="0" Grid.Column="1" Text="رقم الهاتف:" Margin="0,0,0,5"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Margin="10,20,0,10"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="العنوان:" Margin="0,0,0,5"/>
                            <TextBox Grid.Row="1" Grid.Column="0" Margin="0,20,10,10"/>

                            <TextBlock Grid.Row="1" Grid.Column="1" Text="تاريخ الفاتورة:" Margin="0,0,0,5"/>
                            <DatePicker Grid.Row="1" Grid.Column="1" Margin="10,20,0,10" SelectedDate="{x:Static sys:DateTime.Now}"
                                       xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                        </Grid>
                    </GroupBox>

                    <!-- Products -->
                    <GroupBox Grid.Row="2" Header="المنتجات المشتراة">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Add Product -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                                <TextBox Width="200" Margin="0,0,10,0" Text="اسم المنتج الجديد"/>
                                <TextBox Width="80" Margin="0,0,10,0" Text="الكمية"/>
                                <TextBox Width="100" Margin="0,0,10,0" Text="سعر الشراء"/>
                                <TextBox Width="100" Margin="0,0,10,0" Text="سعر البيع"/>
                                <Button Content="إضافة للمخزون" Width="120" Background="DarkBlue" Foreground="White"/>
                            </StackPanel>

                            <!-- Products List -->
                            <DataGrid Grid.Row="1" AutoGenerateColumns="False" CanUserAddRows="False">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="المنتج" Width="*"/>
                                    <DataGridTextColumn Header="الكمية" Width="100"/>
                                    <DataGridTextColumn Header="سعر الشراء" Width="120"/>
                                    <DataGridTextColumn Header="سعر البيع" Width="120"/>
                                    <DataGridTextColumn Header="الإجمالي" Width="100"/>
                                    <DataGridTemplateColumn Header="حذف" Width="80">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Content="حذف" Background="Red" Foreground="White"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>

                            <!-- Total -->
                            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,10,0,0">
                                <TextBlock Text="إجمالي تكلفة الشراء: " FontSize="16" FontWeight="Bold"/>
                                <TextBlock Text="0.00 دج" FontSize="16" FontWeight="Bold" Foreground="DarkBlue"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <TabItem Header="الدفع والتوريد">
                <Grid Margin="20">
                    <StackPanel>
                        <GroupBox Header="طريقة الدفع للمورد" Margin="0,0,0,20">
                            <StackPanel Margin="10">
                                <RadioButton Content="نقدي" IsChecked="True" Margin="0,5"/>
                                <RadioButton Content="شيك" Margin="0,5"/>
                                <RadioButton Content="تحويل بنكي" Margin="0,5"/>
                                <RadioButton Content="آجل" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="تفاصيل التوريد">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم فاتورة المورد:" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="0" Grid.Column="0" Margin="0,20,10,10"/>

                                <TextBlock Grid.Row="0" Grid.Column="1" Text="تاريخ الاستحقاق:" Margin="0,0,0,5"/>
                                <DatePicker Grid.Row="0" Grid.Column="1" Margin="10,20,0,10"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="ملاحظات:" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,20,0,10"
                                        Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ وإضافة للمخزون" Width="150" Height="35" Margin="10"
                       Background="DarkBlue" Foreground="White" FontWeight="Bold"/>
                <Button Content="🖨️ طباعة" Width="120" Height="35" Margin="10"
                       Background="Green" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إلغاء" Width="120" Height="35" Margin="10"
                       Background="Red" Foreground="White" FontWeight="Bold" Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
