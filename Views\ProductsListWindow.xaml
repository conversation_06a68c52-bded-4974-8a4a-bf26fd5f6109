<Window x:Class="SalesManagementSystem.Views.ProductsListWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="قائمة المنتجات والمخزون"
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="DarkGreen">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="📋" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="قائمة المنتجات والمخزون" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Search and Filters -->
        <Border Grid.Row="1" Background="LightGray" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBox Width="200" Margin="0,0,10,0" Text="البحث في المنتجات..."/>
                <Button Content="🔍 بحث" Width="80" Margin="0,0,10,0" Background="Blue" Foreground="White"/>

                <TextBlock Text="الفئة:" VerticalAlignment="Center" Margin="20,0,5,0"/>
                <ComboBox Width="120" Margin="0,0,10,0">
                    <ComboBoxItem Content="جميع الفئات"/>
                    <ComboBoxItem Content="إلكترونيات"/>
                    <ComboBoxItem Content="أجهزة كمبيوتر"/>
                    <ComboBoxItem Content="اكسسوارات"/>
                </ComboBox>

                <TextBlock Text="الحالة:" VerticalAlignment="Center" Margin="20,0,5,0"/>
                <ComboBox Width="120" Margin="0,0,10,0">
                    <ComboBoxItem Content="جميع المنتجات"/>
                    <ComboBoxItem Content="متوفر"/>
                    <ComboBoxItem Content="نفد المخزون"/>
                    <ComboBoxItem Content="تحت الحد الأدنى"/>
                </ComboBox>

                <Button Content="🔄 تحديث" Width="80" Margin="10,0,0,0" Background="Green" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Products DataGrid -->
        <DataGrid Grid.Row="2" Margin="10" AutoGenerateColumns="False" CanUserAddRows="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الباركود" Width="100"/>
                <DataGridTextColumn Header="اسم المنتج" Width="*"/>
                <DataGridTextColumn Header="الفئة" Width="120"/>
                <DataGridTextColumn Header="الكمية المتاحة" Width="100"/>
                <DataGridTextColumn Header="الحد الأدنى" Width="100"/>
                <DataGridTextColumn Header="سعر الشراء" Width="100"/>
                <DataGridTextColumn Header="سعر البيع" Width="100"/>
                <DataGridTextColumn Header="الحالة" Width="100"/>
                <DataGridTemplateColumn Header="الإجراءات" Width="200">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Content="✏️" Width="30" Height="25" Margin="2"
                                       Background="Blue" Foreground="White" ToolTip="تعديل"/>
                                <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                       Background="Red" Foreground="White" ToolTip="حذف"/>
                                <Button Content="📦" Width="30" Height="25" Margin="2"
                                       Background="Orange" Foreground="White" ToolTip="تعديل المخزون"/>
                                <Button Content="📋" Width="30" Height="25" Margin="2"
                                       Background="Green" Foreground="White" ToolTip="تفاصيل"/>
                                <Button Content="🖨️" Width="30" Height="25" Margin="2"
                                       Background="Purple" Foreground="White" ToolTip="طباعة"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>

            <!-- البيانات ستُحمل من قاعدة البيانات -->
        </DataGrid>

        <!-- Footer with Statistics -->
        <Border Grid.Row="3" Background="LightGray">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Statistics -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="إجمالي المنتجات: " FontWeight="Bold"/>
                    <TextBlock Text="3" Foreground="Blue" FontWeight="Bold" Margin="0,0,20,0"/>

                    <TextBlock Text="متوفر: " FontWeight="Bold"/>
                    <TextBlock Text="1" Foreground="Green" FontWeight="Bold" Margin="0,0,20,0"/>

                    <TextBlock Text="تحت الحد الأدنى: " FontWeight="Bold"/>
                    <TextBlock Text="1" Foreground="Orange" FontWeight="Bold" Margin="0,0,20,0"/>

                    <TextBlock Text="نفد المخزون: " FontWeight="Bold"/>
                    <TextBlock Text="1" Foreground="Red" FontWeight="Bold"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="➕ منتج جديد" Width="100" Height="35" Margin="5"
                           Background="Green" Foreground="White" FontWeight="Bold"/>
                    <Button Content="📊 تقرير المخزون" Width="120" Height="35" Margin="5"
                           Background="Blue" Foreground="White" FontWeight="Bold"/>
                    <Button Content="📤 تصدير" Width="80" Height="35" Margin="5"
                           Background="Orange" Foreground="White" FontWeight="Bold"/>
                    <Button Content="❌ إغلاق" Width="80" Height="35" Margin="5"
                           Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
