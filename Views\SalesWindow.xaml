<Window x:Class="SalesManagementSystem.Views.SalesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="فاتورة مبيعات جديدة"
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="80"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Green">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="🧾" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="فاتورة مبيعات جديدة" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <TabControl Grid.Row="1" Margin="10">
            <TabItem Header="بيانات الفاتورة">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Customer Info -->
                    <GroupBox Grid.Row="0" Header="بيانات العميل" Margin="0,0,0,20">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم العميل:" Margin="0,0,0,5"/>
                            <ComboBox Grid.Row="0" Grid.Column="0" Margin="0,20,10,10" IsEditable="True">
                                <ComboBoxItem Content="عميل نقدي"/>
                                <ComboBoxItem Content="أحمد محمد"/>
                                <ComboBoxItem Content="فاطمة علي"/>
                                <ComboBoxItem Content="محمد حسن"/>
                            </ComboBox>

                            <TextBlock Grid.Row="0" Grid.Column="1" Text="رقم الهاتف:" Margin="0,0,0,5"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Margin="10,20,0,10"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="العنوان:" Margin="0,0,0,5"/>
                            <TextBox Grid.Row="1" Grid.Column="0" Margin="0,20,10,10"/>

                            <TextBlock Grid.Row="1" Grid.Column="1" Text="تاريخ الفاتورة:" Margin="0,0,0,5"/>
                            <DatePicker Grid.Row="1" Grid.Column="1" Margin="10,20,0,10" SelectedDate="{x:Static sys:DateTime.Now}"/>
                        </Grid>
                    </GroupBox>

                    <!-- Products -->
                    <GroupBox Grid.Row="2" Header="المنتجات">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Add Product -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                                <ComboBox Width="200" Margin="0,0,10,0">
                                    <ComboBoxItem Content="لابتوب ديل"/>
                                    <ComboBoxItem Content="ماوس لوجيتك"/>
                                    <ComboBoxItem Content="كيبورد ميكانيكي"/>
                                    <ComboBoxItem Content="شاشة سامسونج"/>
                                </ComboBox>
                                <TextBox Width="80" Margin="0,0,10,0" Text="1"/>
                                <TextBox Width="100" Margin="0,0,10,0" Text="1000.00 دج"/>
                                <Button Content="إضافة" Width="80" Background="Blue" Foreground="White"/>
                            </StackPanel>

                            <!-- Products List -->
                            <DataGrid Grid.Row="1" AutoGenerateColumns="False" CanUserAddRows="False">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="المنتج" Width="*"/>
                                    <DataGridTextColumn Header="الكمية" Width="100"/>
                                    <DataGridTextColumn Header="السعر" Width="100"/>
                                    <DataGridTextColumn Header="الإجمالي" Width="100"/>
                                    <DataGridTemplateColumn Header="حذف" Width="80">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Content="حذف" Background="Red" Foreground="White"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>

                            <!-- Total -->
                            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,10,0,0">
                                <TextBlock Text="الإجمالي: " FontSize="16" FontWeight="Bold"/>
                                <TextBlock Text="0.00 دج" FontSize="16" FontWeight="Bold" Foreground="Green"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <TabItem Header="الدفع والخصومات">
                <Grid Margin="20">
                    <StackPanel>
                        <GroupBox Header="طريقة الدفع" Margin="0,0,0,20">
                            <StackPanel Margin="10">
                                <RadioButton Content="نقدي" IsChecked="True" Margin="0,5"/>
                                <RadioButton Content="بطاقة ائتمان" Margin="0,5"/>
                                <RadioButton Content="تحويل بنكي" Margin="0,5"/>
                                <RadioButton Content="آجل" Margin="0,5"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="الخصومات والضرائب">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="نسبة الخصم (%):" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="0" Grid.Column="0" Margin="0,20,10,10" Text="0%"/>

                                <TextBlock Grid.Row="0" Grid.Column="1" Text="قيمة الخصم:" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="0" Grid.Column="1" Margin="10,20,0,10" Text="0.00 دج"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="نسبة الضريبة (%):" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" Grid.Column="0" Margin="0,20,10,10" Text="19%"/>

                                <TextBlock Grid.Row="1" Grid.Column="1" Text="قيمة الضريبة:" Margin="0,0,0,5"/>
                                <TextBox Grid.Row="1" Grid.Column="1" Margin="10,20,0,10" Text="0.00 دج"/>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ الفاتورة" Width="120" Height="35" Margin="10"
                       Background="Green" Foreground="White" FontWeight="Bold"/>
                <Button Content="🖨️ طباعة" Width="120" Height="35" Margin="10"
                       Background="Blue" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إلغاء" Width="120" Height="35" Margin="10"
                       Background="Red" Foreground="White" FontWeight="Bold" Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
