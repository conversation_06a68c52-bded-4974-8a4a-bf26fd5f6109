using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using MaterialDesignThemes.Wpf;

namespace SalesManagementSystem.Controls
{
    /// <summary>
    /// مؤشر تقدم متقدم مع أنواع مختلفة من العرض
    /// </summary>
    public partial class ProgressIndicator : UserControl
    {
        #region Dependency Properties

        public static readonly DependencyProperty IsCircularProperty =
            DependencyProperty.Register(nameof(IsCircular), typeof(bool), typeof(ProgressIndicator),
                new PropertyMetadata(true));

        public static readonly DependencyProperty CircularSizeProperty =
            DependencyProperty.Register(nameof(CircularSize), typeof(double), typeof(ProgressIndicator),
                new PropertyMetadata(80.0));

        public static readonly DependencyProperty LinearHeightProperty =
            DependencyProperty.Register(nameof(LinearHeight), typeof(double), typeof(ProgressIndicator),
                new PropertyMetadata(8.0));

        public static readonly DependencyProperty LinearCornerRadiusProperty =
            DependencyProperty.Register(nameof(LinearCornerRadius), typeof(CornerRadius), typeof(ProgressIndicator),
                new PropertyMetadata(new CornerRadius(4)));

        public static readonly DependencyProperty StrokeThicknessProperty =
            DependencyProperty.Register(nameof(StrokeThickness), typeof(double), typeof(ProgressIndicator),
                new PropertyMetadata(6.0));

        public static readonly DependencyProperty ProgressValueProperty =
            DependencyProperty.Register(nameof(ProgressValue), typeof(double), typeof(ProgressIndicator),
                new PropertyMetadata(0.0, OnProgressValueChanged));

        public static readonly DependencyProperty IsIndeterminateProperty =
            DependencyProperty.Register(nameof(IsIndeterminate), typeof(bool), typeof(ProgressIndicator),
                new PropertyMetadata(false, OnIsIndeterminateChanged));

        public static readonly DependencyProperty ProgressColorProperty =
            DependencyProperty.Register(nameof(ProgressColor), typeof(Brush), typeof(ProgressIndicator),
                new PropertyMetadata(Application.Current.Resources["PrimaryHueMidBrush"]));

        public static readonly DependencyProperty TrackColorProperty =
            DependencyProperty.Register(nameof(TrackColor), typeof(Brush), typeof(ProgressIndicator),
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(224, 224, 224))));

        public static readonly DependencyProperty TextColorProperty =
            DependencyProperty.Register(nameof(TextColor), typeof(Brush), typeof(ProgressIndicator),
                new PropertyMetadata(Application.Current.Resources["MaterialDesignBody"]));

        public static readonly DependencyProperty ShowTextProperty =
            DependencyProperty.Register(nameof(ShowText), typeof(bool), typeof(ProgressIndicator),
                new PropertyMetadata(true));

        public static readonly DependencyProperty ProgressTextProperty =
            DependencyProperty.Register(nameof(ProgressText), typeof(string), typeof(ProgressIndicator),
                new PropertyMetadata("0%"));

        public static readonly DependencyProperty TextSizeProperty =
            DependencyProperty.Register(nameof(TextSize), typeof(double), typeof(ProgressIndicator),
                new PropertyMetadata(14.0));

        public static readonly DependencyProperty CenterIconProperty =
            DependencyProperty.Register(nameof(CenterIcon), typeof(PackIconKind?), typeof(ProgressIndicator));

        public static readonly DependencyProperty IconSizeProperty =
            DependencyProperty.Register(nameof(IconSize), typeof(double), typeof(ProgressIndicator),
                new PropertyMetadata(24.0));

        public static readonly DependencyProperty IconColorProperty =
            DependencyProperty.Register(nameof(IconColor), typeof(Brush), typeof(ProgressIndicator),
                new PropertyMetadata(Application.Current.Resources["PrimaryHueMidBrush"]));

        public static readonly DependencyProperty ShowDotsProperty =
            DependencyProperty.Register(nameof(ShowDots), typeof(bool), typeof(ProgressIndicator),
                new PropertyMetadata(false));

        public static readonly DependencyProperty LoadingMessageProperty =
            DependencyProperty.Register(nameof(LoadingMessage), typeof(string), typeof(ProgressIndicator));

        public static readonly DependencyProperty LoadingSubMessageProperty =
            DependencyProperty.Register(nameof(LoadingSubMessage), typeof(string), typeof(ProgressIndicator));

        #endregion

        #region Properties

        public bool IsCircular
        {
            get => (bool)GetValue(IsCircularProperty);
            set => SetValue(IsCircularProperty, value);
        }

        public double CircularSize
        {
            get => (double)GetValue(CircularSizeProperty);
            set => SetValue(CircularSizeProperty, value);
        }

        public double LinearHeight
        {
            get => (double)GetValue(LinearHeightProperty);
            set => SetValue(LinearHeightProperty, value);
        }

        public CornerRadius LinearCornerRadius
        {
            get => (CornerRadius)GetValue(LinearCornerRadiusProperty);
            set => SetValue(LinearCornerRadiusProperty, value);
        }

        public double StrokeThickness
        {
            get => (double)GetValue(StrokeThicknessProperty);
            set => SetValue(StrokeThicknessProperty, value);
        }

        public double ProgressValue
        {
            get => (double)GetValue(ProgressValueProperty);
            set => SetValue(ProgressValueProperty, value);
        }

        public bool IsIndeterminate
        {
            get => (bool)GetValue(IsIndeterminateProperty);
            set => SetValue(IsIndeterminateProperty, value);
        }

        public Brush ProgressColor
        {
            get => (Brush)GetValue(ProgressColorProperty);
            set => SetValue(ProgressColorProperty, value);
        }

        public Brush TrackColor
        {
            get => (Brush)GetValue(TrackColorProperty);
            set => SetValue(TrackColorProperty, value);
        }

        public Brush TextColor
        {
            get => (Brush)GetValue(TextColorProperty);
            set => SetValue(TextColorProperty, value);
        }

        public bool ShowText
        {
            get => (bool)GetValue(ShowTextProperty);
            set => SetValue(ShowTextProperty, value);
        }

        public string ProgressText
        {
            get => (string)GetValue(ProgressTextProperty);
            set => SetValue(ProgressTextProperty, value);
        }

        public double TextSize
        {
            get => (double)GetValue(TextSizeProperty);
            set => SetValue(TextSizeProperty, value);
        }

        public PackIconKind? CenterIcon
        {
            get => (PackIconKind?)GetValue(CenterIconProperty);
            set => SetValue(CenterIconProperty, value);
        }

        public double IconSize
        {
            get => (double)GetValue(IconSizeProperty);
            set => SetValue(IconSizeProperty, value);
        }

        public Brush IconColor
        {
            get => (Brush)GetValue(IconColorProperty);
            set => SetValue(IconColorProperty, value);
        }

        public bool ShowDots
        {
            get => (bool)GetValue(ShowDotsProperty);
            set => SetValue(ShowDotsProperty, value);
        }

        public string LoadingMessage
        {
            get => (string)GetValue(LoadingMessageProperty);
            set => SetValue(LoadingMessageProperty, value);
        }

        public string LoadingSubMessage
        {
            get => (string)GetValue(LoadingSubMessageProperty);
            set => SetValue(LoadingSubMessageProperty, value);
        }

        #endregion

        #region Constructor

        public ProgressIndicator()
        {
            InitializeComponent();
            DataContext = this;
        }

        #endregion

        #region Event Handlers

        private static void OnProgressValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ProgressIndicator indicator)
            {
                indicator.UpdateProgress((double)e.NewValue);
            }
        }

        private static void OnIsIndeterminateChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ProgressIndicator indicator)
            {
                indicator.UpdateIndeterminateState((bool)e.NewValue);
            }
        }

        #endregion

        #region Private Methods

        private void UpdateProgress(double value)
        {
            // تحديث النص
            if (ShowText && !IsIndeterminate)
            {
                ProgressText = $"{value:F0}%";
            }

            // تحديث العرض المرئي
            if (IsCircular)
            {
                UpdateCircularProgress(value);
            }
            else
            {
                UpdateLinearProgress(value);
            }
        }

        private void UpdateCircularProgress(double value)
        {
            // تحديث مسار الدائرة (يمكن تحسينه لاحقاً)
            // هذا يتطلب حسابات هندسية معقدة لرسم القوس
        }

        private void UpdateLinearProgress(double value)
        {
            if (ProgressFill != null && !IsIndeterminate)
            {
                var targetWidth = (value / 100.0) * ActualWidth;
                
                var animation = new DoubleAnimation
                {
                    To = targetWidth,
                    Duration = TimeSpan.FromMilliseconds(300),
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                };

                ProgressFill.BeginAnimation(WidthProperty, animation);
            }
        }

        private void UpdateIndeterminateState(bool isIndeterminate)
        {
            if (isIndeterminate)
            {
                StartIndeterminateAnimation();
            }
            else
            {
                StopIndeterminateAnimation();
                UpdateProgress(ProgressValue);
            }
        }

        private void StartIndeterminateAnimation()
        {
            if (IsCircular && IndeterminateSpinner != null)
            {
                var storyboard = FindResource("CircularProgressAnimation") as Storyboard;
                storyboard?.Begin(IndeterminateSpinner);
            }
            else if (!IsCircular && IndeterminateBar != null)
            {
                var storyboard = FindResource("LinearProgressAnimation") as Storyboard;
                storyboard?.Begin(IndeterminateBar);
            }

            if (ShowDots)
            {
                StartDotsAnimation();
            }
        }

        private void StopIndeterminateAnimation()
        {
            if (IsCircular && IndeterminateSpinner != null)
            {
                IndeterminateSpinner.BeginAnimation(UIElement.RenderTransformProperty, null);
            }
            else if (!IsCircular && IndeterminateBar != null)
            {
                IndeterminateBar.BeginAnimation(UIElement.RenderTransformProperty, null);
            }

            StopDotsAnimation();
        }

        private void StartDotsAnimation()
        {
            var dots = new[] { Dot1, Dot2, Dot3 };
            
            for (int i = 0; i < dots.Length; i++)
            {
                if (dots[i] != null)
                {
                    var animation = new DoubleAnimation
                    {
                        From = 0.3,
                        To = 1.0,
                        Duration = TimeSpan.FromMilliseconds(600),
                        AutoReverse = true,
                        RepeatBehavior = RepeatBehavior.Forever,
                        BeginTime = TimeSpan.FromMilliseconds(i * 200)
                    };

                    dots[i].BeginAnimation(OpacityProperty, animation);
                }
            }
        }

        private void StopDotsAnimation()
        {
            var dots = new[] { Dot1, Dot2, Dot3 };
            
            foreach (var dot in dots)
            {
                dot?.BeginAnimation(OpacityProperty, null);
                if (dot != null) dot.Opacity = 1.0;
            }
        }

        #endregion
    }
}
