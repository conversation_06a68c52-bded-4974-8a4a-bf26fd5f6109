using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Threading.Tasks;
using Dapper;
using System.Reflection;
using System.Linq;

namespace SalesManagementSystem.Services
{
    public class DatabaseService
    {
        private readonly string _dbPath;
        private readonly string _connectionString;

        public DatabaseService()
        {
            string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SalesManagementSystem");

            // Create directory if it doesn't exist
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            _dbPath = Path.Combine(appDataPath, "SalesManagement.db");
            _connectionString = $"Data Source={_dbPath};Version=3;";

            // Initialize database if it doesn't exist
            if (!File.Exists(_dbPath))
            {
                InitializeDatabase();
            }
        }

        private void InitializeDatabase()
        {
            try
            {
                SQLiteConnection.CreateFile(_dbPath);

                using (var connection = new SQLiteConnection(_connectionString))
                {
                    connection.Open();

                    // Create tables with error handling
                    CreateTablesWithErrorHandling(connection);

                    // Update database schema for existing databases
                    UpdateDatabaseSchema(connection);

                    // Insert default data
                    InsertDefaultData(connection);
                }

                LoggingService.LogSystemEvent("قاعدة البيانات", "تم إنشاء قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء قاعدة البيانات");
                throw new Exception("فشل في إنشاء قاعدة البيانات", ex);
            }
        }

        private void CreateTablesWithErrorHandling(SQLiteConnection connection)
        {
            var tables = new Dictionary<string, string>
            {
                ["Products"] = CreateProductsTableSql,
                ["Categories"] = CreateCategoriesTableSql,
                ["Customers"] = CreateCustomersTableSql,
                ["Suppliers"] = CreateSuppliersTableSql,
                ["Employees"] = CreateEmployeesTableSql,
                ["Expenses"] = CreateExpensesTableSql,
                ["ExpenseCategories"] = CreateExpenseCategoriesTableSql,
                ["Sales"] = CreateSalesTableSql,
                ["SaleItems"] = CreateSaleItemsTableSql,
                ["Purchases"] = CreatePurchasesTableSql,
                ["PurchaseItems"] = CreatePurchaseItemsTableSql,
                ["Users"] = CreateUsersTableSql,
                ["Settings"] = CreateSettingsTableSql,
                ["Notifications"] = CreateNotificationsTableSql,
                ["Payments"] = CreatePaymentsTableSql,
                ["Invoices"] = CreateInvoicesTableSql,
                ["InvoiceItems"] = CreateInvoiceItemsTableSql,
                ["InstallmentPlans"] = CreateInstallmentPlansTableSql,
                ["InstallmentPayments"] = CreateInstallmentPaymentsTableSql,
                ["Warehouses"] = CreateWarehousesTableSql,
                ["WarehouseLocations"] = CreateWarehouseLocationsTableSql,
                ["InventoryItems"] = CreateInventoryItemsTableSql,
                ["InventoryTransfers"] = CreateInventoryTransfersTableSql,
                ["InventoryTransferItems"] = CreateInventoryTransferItemsTableSql
            };

            foreach (var table in tables)
            {
                try
                {
                    ExecuteNonQuery(connection, table.Value);
                }
                catch (Exception ex)
                {
                    LoggingService.LogError(ex, $"خطأ في إنشاء جدول {table.Key}");
                    // Continue with other tables
                }
            }
        }

        private void UpdateDatabaseSchema(SQLiteConnection connection)
        {
            try
            {
                // Check which columns exist in Products table
                var checkColumnSql = "PRAGMA table_info(Products)";
                using (var command = new SQLiteCommand(checkColumnSql, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        var existingColumns = new HashSet<string>();
                        while (reader.Read())
                        {
                            existingColumns.Add(reader["name"].ToString());
                        }

                        // Add missing columns
                        var columnsToAdd = new Dictionary<string, string>
                        {
                            { "SalePrice2", "REAL DEFAULT 0" },
                            { "Unit", "TEXT DEFAULT 'قطعة'" },
                            { "Barcode", "TEXT" },
                            { "IsActive", "INTEGER DEFAULT 1" },
                            { "TrackStock", "INTEGER DEFAULT 1" },
                            { "WarehouseId", "INTEGER" },
                            { "UpdatedAt", "TEXT" }
                        };

                        foreach (var column in columnsToAdd)
                        {
                            if (!existingColumns.Contains(column.Key))
                            {
                                ExecuteNonQuery(connection, $"ALTER TABLE Products ADD COLUMN {column.Key} {column.Value}");
                                LoggingService.LogInfo($"تم إضافة عمود {column.Key} إلى جدول Products");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحديث مخطط قاعدة البيانات");
            }
        }

        private void ExecuteNonQuery(SQLiteConnection connection, string sql)
        {
            using (var command = new SQLiteCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private void InsertDefaultData(SQLiteConnection connection)
        {
            try
            {
                // Insert default product categories
                string[] defaultCategories = { "إلكترونيات", "ملابس", "طعام", "مشروبات", "قرطاسية", "أخرى" };
                foreach (var category in defaultCategories)
                {
                    try
                    {
                        ExecuteNonQuery(connection, $"INSERT OR IGNORE INTO Categories (Name) VALUES ('{category}')");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في إدراج فئة المنتج: {category}");
                    }
                }

                // Insert default expense categories
                string[] defaultExpenseCategories = { "إيجار", "مرافق", "رواتب", "صيانة", "مستلزمات مكتبية", "تسويق", "أخرى" };
                foreach (var category in defaultExpenseCategories)
                {
                    try
                    {
                        ExecuteNonQuery(connection, $"INSERT OR IGNORE INTO ExpenseCategories (Name) VALUES ('{category}')");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في إدراج فئة المصروف: {category}");
                    }
                }

                // Insert default admin user
                try
                {
                    ExecuteNonQuery(connection, "INSERT OR IGNORE INTO Users (Username, PasswordHash, FullName, Role, IsActive) VALUES ('admin', 'admin', 'مدير النظام', 'Admin', 1)");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError(ex, "خطأ في إدراج المستخدم الافتراضي");
                }

                // Insert default settings
                var defaultSettings = new Dictionary<string, string>
                {
                    ["CompanyName"] = "نظام إدارة المبيعات",
                    ["Language"] = "ar-SA",
                    ["Theme"] = "Light",
                    ["Currency"] = "ريال",
                    ["TaxRate"] = "15"
                };

                foreach (var setting in defaultSettings)
                {
                    try
                    {
                        ExecuteNonQuery(connection, $"INSERT OR IGNORE INTO Settings (Key, Value) VALUES ('{setting.Key}', '{setting.Value}')");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError(ex, $"خطأ في إدراج الإعداد: {setting.Key}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إدراج البيانات الافتراضية");
            }
        }

        public async Task<T> QuerySingleAsync<T>(string sql, object? param = null)
        {
            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                return await db.QuerySingleAsync<T>(sql, param);
            }
        }

        public async Task<T> QuerySingleOrDefaultAsync<T>(string sql, object? param = null)
        {
            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                return await db.QuerySingleOrDefaultAsync<T>(sql, param);
            }
        }

        public async Task<IEnumerable<T>> QueryAsync<T>(string sql, object? param = null)
        {
            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                return await db.QueryAsync<T>(sql, param);
            }
        }

        public async Task<int> ExecuteAsync(string sql, object? param = null)
        {
            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                return await db.ExecuteAsync(sql, param);
            }
        }

        public async Task<T> InsertAsync<T>(string tableName, object entity)
        {
            var properties = entity.GetType().GetProperties()
                .Where(p => p.Name.ToLower() != "id" && p.GetValue(entity) != null)
                .ToList();

            var columnNames = string.Join(", ", properties.Select(p => p.Name));
            var paramNames = string.Join(", ", properties.Select(p => "@" + p.Name));

            var sql = $"INSERT INTO {tableName} ({columnNames}) VALUES ({paramNames}); SELECT last_insert_rowid()";

            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                var id = await db.ExecuteScalarAsync<long>(sql, entity);
                var idProperty = entity.GetType().GetProperty("Id");
                if (idProperty != null)
                {
                    idProperty.SetValue(entity, Convert.ChangeType(id, idProperty.PropertyType));
                }
                return (T)entity;
            }
        }

        public async Task<bool> UpdateAsync<T>(string tableName, T entity)
        {
            var properties = entity?.GetType().GetProperties()
                .Where(p => p.Name.ToLower() != "id" && p.GetValue(entity) != null)
                .ToList() ?? new List<System.Reflection.PropertyInfo>();

            var setClause = string.Join(", ", properties.Select(p => $"{p.Name} = @{p.Name}"));
            var idProperty = entity?.GetType().GetProperty("Id");
            var id = idProperty?.GetValue(entity);

            var sql = $"UPDATE {tableName} SET {setClause} WHERE Id = @Id";

            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                var result = await db.ExecuteAsync(sql, entity);
                return result > 0;
            }
        }

        public async Task<bool> DeleteAsync(string tableName, int id)
        {
            var sql = $"DELETE FROM {tableName} WHERE Id = @Id";

            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                var result = await db.ExecuteAsync(sql, new { Id = id });
                return result > 0;
            }
        }

        #region SQL Create Table Statements

        private const string CreateProductsTableSql = @"
            CREATE TABLE IF NOT EXISTS Products (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Code TEXT NOT NULL,
                Name TEXT NOT NULL,
                Description TEXT,
                CategoryId INTEGER,
                PurchasePrice REAL NOT NULL,
                SalePrice REAL NOT NULL,
                SalePrice2 REAL DEFAULT 0,
                Unit TEXT DEFAULT 'قطعة',
                Barcode TEXT,
                IsActive INTEGER DEFAULT 1,
                TrackStock INTEGER DEFAULT 1,
                Quantity INTEGER NOT NULL,
                MinQuantity INTEGER NOT NULL,
                WarehouseId INTEGER,
                WarehouseName TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
            );
            CREATE INDEX idx_products_code ON Products(Code);
            CREATE INDEX idx_products_category ON Products(CategoryId);
            CREATE INDEX idx_products_barcode ON Products(Barcode);
        ";

        private const string CreateCategoriesTableSql = @"
            CREATE TABLE Categories (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Description TEXT
            );
        ";

        private const string CreateCustomersTableSql = @"
            CREATE TABLE Customers (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Phone TEXT,
                Email TEXT,
                Address TEXT,
                Balance REAL NOT NULL DEFAULT 0,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT
            );
            CREATE INDEX idx_customers_name ON Customers(Name);
        ";

        private const string CreateSuppliersTableSql = @"
            CREATE TABLE Suppliers (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Phone TEXT,
                Email TEXT,
                Address TEXT,
                Balance REAL NOT NULL DEFAULT 0,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT
            );
            CREATE INDEX idx_suppliers_name ON Suppliers(Name);
        ";

        private const string CreateEmployeesTableSql = @"
            CREATE TABLE Employees (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Phone TEXT,
                Email TEXT,
                Address TEXT,
                Position TEXT NOT NULL,
                Salary REAL NOT NULL,
                HireDate TEXT NOT NULL,
                Status TEXT NOT NULL,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT
            );
            CREATE INDEX idx_employees_name ON Employees(Name);
        ";

        private const string CreateExpensesTableSql = @"
            CREATE TABLE Expenses (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Title TEXT NOT NULL,
                CategoryId INTEGER,
                Amount REAL NOT NULL,
                Date TEXT NOT NULL,
                Notes TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (CategoryId) REFERENCES ExpenseCategories(Id)
            );
            CREATE INDEX idx_expenses_category ON Expenses(CategoryId);
            CREATE INDEX idx_expenses_date ON Expenses(Date);
        ";

        private const string CreateExpenseCategoriesTableSql = @"
            CREATE TABLE ExpenseCategories (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Description TEXT
            );
        ";

        private const string CreateSalesTableSql = @"
            CREATE TABLE Sales (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                InvoiceNumber TEXT NOT NULL,
                CustomerId INTEGER,
                Date TEXT NOT NULL,
                Subtotal REAL NOT NULL,
                Discount REAL NOT NULL DEFAULT 0,
                Tax REAL NOT NULL DEFAULT 0,
                Total REAL NOT NULL,
                PaymentMethod TEXT NOT NULL,
                PaymentStatus TEXT NOT NULL,
                Notes TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
            );
            CREATE INDEX idx_sales_invoice ON Sales(InvoiceNumber);
            CREATE INDEX idx_sales_customer ON Sales(CustomerId);
            CREATE INDEX idx_sales_date ON Sales(Date);
        ";

        private const string CreateSaleItemsTableSql = @"
            CREATE TABLE SaleItems (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                SaleId INTEGER NOT NULL,
                ProductId INTEGER NOT NULL,
                Quantity INTEGER NOT NULL,
                UnitPrice REAL NOT NULL,
                Discount REAL NOT NULL DEFAULT 0,
                Total REAL NOT NULL,
                FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE,
                FOREIGN KEY (ProductId) REFERENCES Products(Id)
            );
            CREATE INDEX idx_saleitems_sale ON SaleItems(SaleId);
            CREATE INDEX idx_saleitems_product ON SaleItems(ProductId);
        ";

        private const string CreatePurchasesTableSql = @"
            CREATE TABLE Purchases (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                InvoiceNumber TEXT NOT NULL,
                SupplierId INTEGER,
                Date TEXT NOT NULL,
                Subtotal REAL NOT NULL,
                Discount REAL NOT NULL DEFAULT 0,
                Tax REAL NOT NULL DEFAULT 0,
                Total REAL NOT NULL,
                PaymentMethod TEXT NOT NULL,
                PaymentStatus TEXT NOT NULL,
                Notes TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
            );
            CREATE INDEX idx_purchases_invoice ON Purchases(InvoiceNumber);
            CREATE INDEX idx_purchases_supplier ON Purchases(SupplierId);
            CREATE INDEX idx_purchases_date ON Purchases(Date);
        ";

        private const string CreatePurchaseItemsTableSql = @"
            CREATE TABLE PurchaseItems (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                PurchaseId INTEGER NOT NULL,
                ProductId INTEGER NOT NULL,
                Quantity INTEGER NOT NULL,
                UnitPrice REAL NOT NULL,
                Discount REAL NOT NULL DEFAULT 0,
                Total REAL NOT NULL,
                FOREIGN KEY (PurchaseId) REFERENCES Purchases(Id) ON DELETE CASCADE,
                FOREIGN KEY (ProductId) REFERENCES Products(Id)
            );
            CREATE INDEX idx_purchaseitems_purchase ON PurchaseItems(PurchaseId);
            CREATE INDEX idx_purchaseitems_product ON PurchaseItems(ProductId);
        ";

        private const string CreateUsersTableSql = @"
            CREATE TABLE Users (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Username TEXT NOT NULL,
                PasswordHash TEXT NOT NULL,
                FullName TEXT NOT NULL,
                Email TEXT,
                Role TEXT NOT NULL,
                IsActive INTEGER NOT NULL DEFAULT 1,
                LastLogin TEXT,
                CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UpdatedAt TEXT
            );
            CREATE UNIQUE INDEX idx_users_username ON Users(Username);
        ";

        private const string CreateSettingsTableSql = @"
            CREATE TABLE Settings (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Key TEXT NOT NULL,
                Value TEXT,
                Description TEXT
            );
            CREATE UNIQUE INDEX idx_settings_key ON Settings(Key);
        ";

        private const string CreateNotificationsTableSql = @"
            CREATE TABLE IF NOT EXISTS Notifications (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Title TEXT NOT NULL,
                Message TEXT NOT NULL,
                Type TEXT NOT NULL,
                Priority TEXT DEFAULT 'Normal',
                IsRead INTEGER DEFAULT 0,
                IsScheduled INTEGER DEFAULT 0,
                ScheduledTime TEXT,
                ActionUrl TEXT,
                CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
            );
            CREATE INDEX IF NOT EXISTS idx_notifications_type ON Notifications(Type);
            CREATE INDEX IF NOT EXISTS idx_notifications_created ON Notifications(CreatedAt);
        ";

        private const string CreatePaymentsTableSql = @"
            CREATE TABLE Payments (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                PaymentNumber TEXT NOT NULL UNIQUE,
                Amount DECIMAL(10,2) NOT NULL,
                PaymentType TEXT NOT NULL,
                Status TEXT NOT NULL DEFAULT 'Pending',
                Direction TEXT NOT NULL DEFAULT 'Incoming',
                CustomerId INTEGER,
                SupplierId INTEGER,
                InvoiceId INTEGER,
                SaleId INTEGER,
                Description TEXT NOT NULL,
                Reference TEXT,
                PaymentDate TEXT NOT NULL,
                DueDate TEXT,
                Notes TEXT,
                CreatedBy TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                IsRecurring INTEGER DEFAULT 0,
                RecurringType TEXT,
                RecurringInterval INTEGER,
                NextPaymentDate TEXT,
                PaidAmount DECIMAL(10,2) DEFAULT 0,
                RemainingAmount DECIMAL(10,2) DEFAULT 0,
                FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id),
                FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id),
                FOREIGN KEY (SaleId) REFERENCES Sales(Id)
            );
            CREATE INDEX idx_payments_status ON Payments(Status);
            CREATE INDEX idx_payments_date ON Payments(PaymentDate);
            CREATE INDEX idx_payments_customer ON Payments(CustomerId);
            CREATE INDEX idx_payments_supplier ON Payments(SupplierId);
        ";

        private const string CreateInvoicesTableSql = @"
            CREATE TABLE Invoices (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                InvoiceNumber TEXT NOT NULL UNIQUE,
                InvoiceType TEXT NOT NULL DEFAULT 'Sales',
                Status TEXT NOT NULL DEFAULT 'Draft',
                CustomerId INTEGER,
                SupplierId INTEGER,
                InvoiceDate TEXT NOT NULL,
                DueDate TEXT,
                Subtotal DECIMAL(10,2) DEFAULT 0,
                TaxAmount DECIMAL(10,2) DEFAULT 0,
                DiscountAmount DECIMAL(10,2) DEFAULT 0,
                TotalAmount DECIMAL(10,2) DEFAULT 0,
                PaidAmount DECIMAL(10,2) DEFAULT 0,
                RemainingAmount DECIMAL(10,2) DEFAULT 0,
                Notes TEXT,
                Terms TEXT,
                CreatedBy TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
            );
            CREATE INDEX idx_invoices_status ON Invoices(Status);
            CREATE INDEX idx_invoices_date ON Invoices(InvoiceDate);
            CREATE INDEX idx_invoices_customer ON Invoices(CustomerId);
            CREATE INDEX idx_invoices_supplier ON Invoices(SupplierId);
        ";

        private const string CreateInvoiceItemsTableSql = @"
            CREATE TABLE InvoiceItems (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                InvoiceId INTEGER NOT NULL,
                ProductId INTEGER NOT NULL,
                ProductName TEXT NOT NULL,
                Description TEXT,
                Quantity DECIMAL(10,3) NOT NULL,
                UnitPrice DECIMAL(10,2) NOT NULL,
                Discount DECIMAL(10,2) DEFAULT 0,
                Total DECIMAL(10,2) NOT NULL,
                FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id) ON DELETE CASCADE,
                FOREIGN KEY (ProductId) REFERENCES Products(Id)
            );
            CREATE INDEX idx_invoiceitems_invoice ON InvoiceItems(InvoiceId);
            CREATE INDEX idx_invoiceitems_product ON InvoiceItems(ProductId);
        ";

        private const string CreateInstallmentPlansTableSql = @"
            CREATE TABLE InstallmentPlans (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                PlanNumber TEXT NOT NULL UNIQUE,
                CustomerId INTEGER,
                InvoiceId INTEGER,
                SaleId INTEGER,
                TotalAmount DECIMAL(10,2) NOT NULL,
                DownPayment DECIMAL(10,2) DEFAULT 0,
                InstallmentAmount DECIMAL(10,2) NOT NULL,
                NumberOfInstallments INTEGER NOT NULL,
                Frequency TEXT NOT NULL DEFAULT 'Monthly',
                InterestRate DECIMAL(5,2) DEFAULT 0,
                StartDate TEXT NOT NULL,
                EndDate TEXT NOT NULL,
                Status TEXT NOT NULL DEFAULT 'Pending',
                Notes TEXT,
                CreatedBy TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id),
                FOREIGN KEY (SaleId) REFERENCES Sales(Id)
            );
            CREATE INDEX idx_installmentplans_customer ON InstallmentPlans(CustomerId);
            CREATE INDEX idx_installmentplans_status ON InstallmentPlans(Status);
        ";

        private const string CreateInstallmentPaymentsTableSql = @"
            CREATE TABLE InstallmentPayments (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                InstallmentPlanId INTEGER NOT NULL,
                PaymentNumber INTEGER NOT NULL,
                Amount DECIMAL(10,2) NOT NULL,
                DueDate TEXT NOT NULL,
                PaidDate TEXT,
                Status TEXT NOT NULL DEFAULT 'Pending',
                LateFee DECIMAL(10,2) DEFAULT 0,
                Notes TEXT,
                FOREIGN KEY (InstallmentPlanId) REFERENCES InstallmentPlans(Id) ON DELETE CASCADE
            );
            CREATE INDEX idx_installmentpayments_plan ON InstallmentPayments(InstallmentPlanId);
            CREATE INDEX idx_installmentpayments_due ON InstallmentPayments(DueDate);
            CREATE INDEX idx_installmentpayments_status ON InstallmentPayments(Status);
        ";

        private const string CreateWarehousesTableSql = @"
            CREATE TABLE Warehouses (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Code TEXT NOT NULL UNIQUE,
                Name TEXT NOT NULL,
                Description TEXT,
                Type TEXT NOT NULL DEFAULT 'Main',
                Status TEXT NOT NULL DEFAULT 'Active',
                Address TEXT,
                City TEXT,
                Phone TEXT,
                Email TEXT,
                ManagerName TEXT,
                TotalCapacity DECIMAL(10,2) DEFAULT 0,
                UsedCapacity DECIMAL(10,2) DEFAULT 0,
                AvailableCapacity DECIMAL(10,2) DEFAULT 0,
                IsDefault INTEGER DEFAULT 0,
                Notes TEXT,
                CreatedBy TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT
            );
            CREATE INDEX idx_warehouses_code ON Warehouses(Code);
            CREATE INDEX idx_warehouses_type ON Warehouses(Type);
            CREATE INDEX idx_warehouses_status ON Warehouses(Status);
            CREATE INDEX idx_warehouses_city ON Warehouses(City);
        ";

        private const string CreateWarehouseLocationsTableSql = @"
            CREATE TABLE WarehouseLocations (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                WarehouseId INTEGER NOT NULL,
                Code TEXT NOT NULL,
                Name TEXT NOT NULL,
                Description TEXT,
                Type TEXT NOT NULL DEFAULT 'Shelf',
                Status TEXT NOT NULL DEFAULT 'Available',
                Aisle TEXT,
                Rack TEXT,
                Shelf TEXT,
                Bin TEXT,
                Capacity DECIMAL(10,2) DEFAULT 0,
                UsedCapacity DECIMAL(10,2) DEFAULT 0,
                AvailableCapacity DECIMAL(10,2) DEFAULT 0,
                Barcode TEXT,
                Notes TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (WarehouseId) REFERENCES Warehouses(Id) ON DELETE CASCADE
            );
            CREATE INDEX idx_warehouselocations_warehouse ON WarehouseLocations(WarehouseId);
            CREATE INDEX idx_warehouselocations_code ON WarehouseLocations(Code);
            CREATE INDEX idx_warehouselocations_type ON WarehouseLocations(Type);
            CREATE INDEX idx_warehouselocations_status ON WarehouseLocations(Status);
            CREATE INDEX idx_warehouselocations_barcode ON WarehouseLocations(Barcode);
        ";

        private const string CreateInventoryItemsTableSql = @"
            CREATE TABLE InventoryItems (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                ProductId INTEGER NOT NULL,
                WarehouseId INTEGER NOT NULL,
                LocationId INTEGER,
                Quantity DECIMAL(10,3) NOT NULL DEFAULT 0,
                UnitCost DECIMAL(10,2) NOT NULL DEFAULT 0,
                TotalValue DECIMAL(10,2) NOT NULL DEFAULT 0,
                ReservedQuantity DECIMAL(10,3) DEFAULT 0,
                AvailableQuantity DECIMAL(10,3) DEFAULT 0,
                MinStockLevel DECIMAL(10,3) DEFAULT 0,
                MaxStockLevel DECIMAL(10,3) DEFAULT 0,
                ReorderPoint DECIMAL(10,3) DEFAULT 0,
                Volume DECIMAL(10,3) DEFAULT 0,
                Weight DECIMAL(10,3) DEFAULT 0,
                BatchNumber TEXT,
                SerialNumber TEXT,
                ExpiryDate TEXT,
                LastUpdated TEXT NOT NULL,
                Notes TEXT,
                FOREIGN KEY (ProductId) REFERENCES Products(Id),
                FOREIGN KEY (WarehouseId) REFERENCES Warehouses(Id),
                FOREIGN KEY (LocationId) REFERENCES WarehouseLocations(Id)
            );
            CREATE INDEX idx_inventoryitems_product ON InventoryItems(ProductId);
            CREATE INDEX idx_inventoryitems_warehouse ON InventoryItems(WarehouseId);
            CREATE INDEX idx_inventoryitems_location ON InventoryItems(LocationId);
            CREATE INDEX idx_inventoryitems_batch ON InventoryItems(BatchNumber);
            CREATE INDEX idx_inventoryitems_serial ON InventoryItems(SerialNumber);
            CREATE INDEX idx_inventoryitems_expiry ON InventoryItems(ExpiryDate);
        ";

        private const string CreateInventoryTransfersTableSql = @"
            CREATE TABLE InventoryTransfers (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                TransferNumber TEXT NOT NULL UNIQUE,
                TransferType TEXT NOT NULL DEFAULT 'WarehouseToWarehouse',
                Status TEXT NOT NULL DEFAULT 'Pending',
                FromWarehouseId INTEGER NOT NULL,
                ToWarehouseId INTEGER NOT NULL,
                FromLocationId INTEGER,
                ToLocationId INTEGER,
                RequestDate TEXT NOT NULL,
                ScheduledDate TEXT,
                ShippedDate TEXT,
                ReceivedDate TEXT,
                Reason TEXT NOT NULL,
                Notes TEXT,
                RequestedBy TEXT,
                ApprovedBy TEXT,
                ShippedBy TEXT,
                ReceivedBy TEXT,
                TotalValue DECIMAL(10,2) DEFAULT 0,
                TrackingNumber TEXT,
                CarrierName TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (FromWarehouseId) REFERENCES Warehouses(Id),
                FOREIGN KEY (ToWarehouseId) REFERENCES Warehouses(Id),
                FOREIGN KEY (FromLocationId) REFERENCES WarehouseLocations(Id),
                FOREIGN KEY (ToLocationId) REFERENCES WarehouseLocations(Id)
            );
            CREATE INDEX idx_inventorytransfers_number ON InventoryTransfers(TransferNumber);
            CREATE INDEX idx_inventorytransfers_type ON InventoryTransfers(TransferType);
            CREATE INDEX idx_inventorytransfers_status ON InventoryTransfers(Status);
            CREATE INDEX idx_inventorytransfers_from ON InventoryTransfers(FromWarehouseId);
            CREATE INDEX idx_inventorytransfers_to ON InventoryTransfers(ToWarehouseId);
            CREATE INDEX idx_inventorytransfers_date ON InventoryTransfers(RequestDate);
        ";

        private const string CreateInventoryTransferItemsTableSql = @"
            CREATE TABLE InventoryTransferItems (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                TransferId INTEGER NOT NULL,
                ProductId INTEGER NOT NULL,
                ProductName TEXT NOT NULL,
                ProductCode TEXT,
                Quantity DECIMAL(10,3) NOT NULL,
                UnitCost DECIMAL(10,2) NOT NULL DEFAULT 0,
                TotalValue DECIMAL(10,2) NOT NULL DEFAULT 0,
                Notes TEXT,
                FOREIGN KEY (TransferId) REFERENCES InventoryTransfers(Id) ON DELETE CASCADE,
                FOREIGN KEY (ProductId) REFERENCES Products(Id)
            );
            CREATE INDEX idx_inventorytransferitems_transfer ON InventoryTransferItems(TransferId);
            CREATE INDEX idx_inventorytransferitems_product ON InventoryTransferItems(ProductId);
        ";

        #endregion

        // Additional methods for BackupService compatibility
        public async Task OpenConnectionAsync()
        {
            // This method is for compatibility with BackupService
            // In this implementation, connections are opened per operation
            await Task.CompletedTask;
        }

        public async Task CloseConnectionAsync()
        {
            // This method is for compatibility with BackupService
            // In this implementation, connections are closed per operation
            await Task.CompletedTask;
        }

        public async Task<string> GetDatabaseVersionAsync()
        {
            const string sql = "PRAGMA user_version";
            var version = await QuerySingleOrDefaultAsync<int>(sql);
            return version.ToString();
        }

        public async Task InitializeDatabaseAsync()
        {
            // Initialize database if it doesn't exist
            if (!File.Exists(_dbPath))
            {
                InitializeDatabase();
            }
            await Task.CompletedTask;
        }
    }
}