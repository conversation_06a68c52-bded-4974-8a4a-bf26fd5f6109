using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج عنصر المخزون المتقدم للمخازن المتعددة
    /// </summary>
    public class InventoryItem : INotifyPropertyChanged
    {
        private int _id;
        private int _productId;
        private int _warehouseId;
        private int? _locationId;
        private decimal _quantity;
        private decimal _unitCost;
        private decimal _totalValue;
        private decimal _reservedQuantity;
        private decimal _availableQuantity;
        private decimal _minStockLevel;
        private decimal _maxStockLevel;
        private decimal _reorderPoint;
        private decimal _volume;
        private decimal _weight;
        private string _batchNumber = string.Empty;
        private string _serialNumber = string.Empty;
        private DateTime? _expiryDate;
        private DateTime _lastUpdated = DateTime.Now;
        private string _notes = string.Empty;

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ProductId
        {
            get => _productId;
            set
            {
                if (_productId != value)
                {
                    _productId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int WarehouseId
        {
            get => _warehouseId;
            set
            {
                if (_warehouseId != value)
                {
                    _warehouseId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? LocationId
        {
            get => _locationId;
            set
            {
                if (_locationId != value)
                {
                    _locationId = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedQuantity));
                    UpdateAvailableQuantity();
                    CalculateTotalValue();
                }
            }
        }

        public decimal UnitCost
        {
            get => _unitCost;
            set
            {
                if (_unitCost != value)
                {
                    _unitCost = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedUnitCost));
                    CalculateTotalValue();
                }
            }
        }

        public decimal TotalValue
        {
            get => _totalValue;
            set
            {
                if (_totalValue != value)
                {
                    _totalValue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalValue));
                }
            }
        }

        public decimal ReservedQuantity
        {
            get => _reservedQuantity;
            set
            {
                if (_reservedQuantity != value)
                {
                    _reservedQuantity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedReservedQuantity));
                    UpdateAvailableQuantity();
                }
            }
        }

        public decimal AvailableQuantity
        {
            get => _availableQuantity;
            set
            {
                if (_availableQuantity != value)
                {
                    _availableQuantity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAvailableQuantity));
                    OnPropertyChanged(nameof(IsLowStock));
                    OnPropertyChanged(nameof(IsOutOfStock));
                    OnPropertyChanged(nameof(StockStatus));
                }
            }
        }

        public decimal MinStockLevel
        {
            get => _minStockLevel;
            set
            {
                if (_minStockLevel != value)
                {
                    _minStockLevel = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsLowStock));
                    OnPropertyChanged(nameof(StockStatus));
                }
            }
        }

        public decimal MaxStockLevel
        {
            get => _maxStockLevel;
            set
            {
                if (_maxStockLevel != value)
                {
                    _maxStockLevel = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsOverStock));
                    OnPropertyChanged(nameof(StockStatus));
                }
            }
        }

        public decimal ReorderPoint
        {
            get => _reorderPoint;
            set
            {
                if (_reorderPoint != value)
                {
                    _reorderPoint = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(NeedsReorder));
                }
            }
        }

        public decimal Volume
        {
            get => _volume;
            set
            {
                if (_volume != value)
                {
                    _volume = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedVolume));
                    OnPropertyChanged(nameof(TotalVolume));
                }
            }
        }

        public decimal Weight
        {
            get => _weight;
            set
            {
                if (_weight != value)
                {
                    _weight = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedWeight));
                    OnPropertyChanged(nameof(TotalWeight));
                }
            }
        }

        public string BatchNumber
        {
            get => _batchNumber;
            set
            {
                if (_batchNumber != value)
                {
                    _batchNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SerialNumber
        {
            get => _serialNumber;
            set
            {
                if (_serialNumber != value)
                {
                    _serialNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? ExpiryDate
        {
            get => _expiryDate;
            set
            {
                if (_expiryDate != value)
                {
                    _expiryDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedExpiryDate));
                    OnPropertyChanged(nameof(IsExpired));
                    OnPropertyChanged(nameof(IsNearExpiry));
                    OnPropertyChanged(nameof(DaysToExpiry));
                }
            }
        }

        public DateTime LastUpdated
        {
            get => _lastUpdated;
            set
            {
                if (_lastUpdated != value)
                {
                    _lastUpdated = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLastUpdated));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsLowStock => AvailableQuantity <= MinStockLevel && MinStockLevel > 0;
        public bool IsOutOfStock => AvailableQuantity <= 0;
        public bool IsOverStock => AvailableQuantity > MaxStockLevel && MaxStockLevel > 0;
        public bool NeedsReorder => AvailableQuantity <= ReorderPoint && ReorderPoint > 0;
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;
        public bool IsNearExpiry => ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.Now.AddDays(30) && !IsExpired;

        public decimal TotalVolume => Volume * Quantity;
        public decimal TotalWeight => Weight * Quantity;

        public int DaysToExpiry
        {
            get
            {
                if (!ExpiryDate.HasValue) return int.MaxValue;
                return (int)(ExpiryDate.Value - DateTime.Now).TotalDays;
            }
        }

        public string StockStatus
        {
            get
            {
                if (IsOutOfStock) return "نفد المخزون";
                if (IsExpired) return "منتهي الصلاحية";
                if (IsNearExpiry) return "قريب الانتهاء";
                if (IsLowStock) return "مخزون منخفض";
                if (IsOverStock) return "مخزون زائد";
                if (NeedsReorder) return "يحتاج إعادة طلب";
                return "طبيعي";
            }
        }

        public string StockStatusColor
        {
            get
            {
                if (IsOutOfStock || IsExpired) return "Red";
                if (IsLowStock || IsNearExpiry || NeedsReorder) return "Orange";
                if (IsOverStock) return "Blue";
                return "Green";
            }
        }

        public string FormattedQuantity => $"{Quantity:N2}";
        public string FormattedReservedQuantity => $"{ReservedQuantity:N2}";
        public string FormattedAvailableQuantity => $"{AvailableQuantity:N2}";
        public string FormattedUnitCost => UnitCost.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalValue => TotalValue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedVolume => $"{Volume:N2} م³";
        public string FormattedWeight => $"{Weight:N2} كجم";
        public string FormattedExpiryDate => ExpiryDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedLastUpdated => LastUpdated.ToString("dd/MM/yyyy HH:mm");

        #endregion

        #region Methods

        private void UpdateAvailableQuantity()
        {
            AvailableQuantity = Quantity - ReservedQuantity;
        }

        private void CalculateTotalValue()
        {
            TotalValue = Quantity * UnitCost;
        }

        public void AddQuantity(decimal quantity, decimal? unitCost = null)
        {
            Quantity += quantity;
            if (unitCost.HasValue)
            {
                // حساب متوسط التكلفة المرجح
                var totalCost = (TotalValue + (quantity * unitCost.Value));
                UnitCost = totalCost / Quantity;
            }
            LastUpdated = DateTime.Now;
        }

        public void RemoveQuantity(decimal quantity)
        {
            if (quantity > AvailableQuantity)
                throw new InvalidOperationException("الكمية المطلوبة أكبر من الكمية المتاحة");
            
            Quantity -= quantity;
            LastUpdated = DateTime.Now;
        }

        public void ReserveQuantity(decimal quantity)
        {
            if (quantity > AvailableQuantity)
                throw new InvalidOperationException("الكمية المطلوبة للحجز أكبر من الكمية المتاحة");
            
            ReservedQuantity += quantity;
            LastUpdated = DateTime.Now;
        }

        public void ReleaseReservedQuantity(decimal quantity)
        {
            if (quantity > ReservedQuantity)
                throw new InvalidOperationException("الكمية المطلوبة لإلغاء الحجز أكبر من الكمية المحجوزة");
            
            ReservedQuantity -= quantity;
            LastUpdated = DateTime.Now;
        }

        public void AdjustQuantity(decimal newQuantity, string reason = "")
        {
            var oldQuantity = Quantity;
            Quantity = newQuantity;
            LastUpdated = DateTime.Now;
            
            if (!string.IsNullOrEmpty(reason))
            {
                Notes += $"\nتسوية من {oldQuantity} إلى {newQuantity} - {reason} - {DateTime.Now:dd/MM/yyyy HH:mm}";
            }
        }

        public void UpdateCost(decimal newUnitCost, string reason = "")
        {
            var oldCost = UnitCost;
            UnitCost = newUnitCost;
            LastUpdated = DateTime.Now;
            
            if (!string.IsNullOrEmpty(reason))
            {
                Notes += $"\nتحديث التكلفة من {oldCost:C} إلى {newUnitCost:C} - {reason} - {DateTime.Now:dd/MM/yyyy HH:mm}";
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Validation

    public class InventoryItemValidator : AbstractValidator<InventoryItem>
    {
        public InventoryItemValidator()
        {
            RuleFor(i => i.ProductId)
                .GreaterThan(0).WithMessage("المنتج مطلوب");

            RuleFor(i => i.WarehouseId)
                .GreaterThan(0).WithMessage("المخزن مطلوب");

            RuleFor(i => i.Quantity)
                .GreaterThanOrEqualTo(0).WithMessage("الكمية لا يمكن أن تكون سالبة");

            RuleFor(i => i.UnitCost)
                .GreaterThanOrEqualTo(0).WithMessage("تكلفة الوحدة لا يمكن أن تكون سالبة");

            RuleFor(i => i.ReservedQuantity)
                .GreaterThanOrEqualTo(0).WithMessage("الكمية المحجوزة لا يمكن أن تكون سالبة")
                .LessThanOrEqualTo(i => i.Quantity).WithMessage("الكمية المحجوزة لا يمكن أن تتجاوز الكمية الإجمالية");

            RuleFor(i => i.MinStockLevel)
                .GreaterThanOrEqualTo(0).WithMessage("الحد الأدنى للمخزون لا يمكن أن يكون سالب");

            RuleFor(i => i.MaxStockLevel)
                .GreaterThanOrEqualTo(i => i.MinStockLevel)
                .When(i => i.MaxStockLevel > 0)
                .WithMessage("الحد الأقصى للمخزون يجب أن يكون أكبر من أو يساوي الحد الأدنى");

            RuleFor(i => i.Volume)
                .GreaterThanOrEqualTo(0).WithMessage("الحجم لا يمكن أن يكون سالب");

            RuleFor(i => i.Weight)
                .GreaterThanOrEqualTo(0).WithMessage("الوزن لا يمكن أن يكون سالب");

            RuleFor(i => i.ExpiryDate)
                .GreaterThan(DateTime.Now)
                .When(i => i.ExpiryDate.HasValue)
                .WithMessage("تاريخ انتهاء الصلاحية يجب أن يكون في المستقبل");
        }
    }

    #endregion
}
