<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Light Theme Colors -->
    <SolidColorBrush x:Key="PrimaryHueMidBrush" Color="#2196F3"/>
    <SolidColorBrush x:Key="PrimaryHueLightBrush" Color="#64B5F6"/>
    <SolidColorBrush x:Key="PrimaryHueDarkBrush" Color="#1976D2"/>
    
    <SolidColorBrush x:Key="AccentColorBrush" Color="#4CAF50"/>
    
    <SolidColorBrush x:Key="MaterialDesignPaper" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="MaterialDesignBackground" Color="#FAFAFA"/>
    <SolidColorBrush x:Key="MaterialDesignCardBackground" Color="#FFFFFF"/>
    
    <SolidColorBrush x:Key="MaterialDesignBody" Color="#DD000000"/>
    <SolidColorBrush x:Key="MaterialDesignBodyLight" Color="#89000000"/>
    <SolidColorBrush x:Key="MaterialDesignCaption" Color="#61000000"/>
    
    <SolidColorBrush x:Key="MaterialDesignDivider" Color="#1F000000"/>
    <SolidColorBrush x:Key="MaterialDesignSelection" Color="#DEDEDE"/>
    
    <!-- Button Styles -->
    <Style x:Key="MaterialDesignRaisedButton" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryHueMidBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryHueDarkBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Card Style -->
    <Style x:Key="MaterialDesignCard" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource MaterialDesignCardBackground}"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="8" ShadowDepth="2" Direction="270" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
