using System;
using System.ComponentModel;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using SalesManagementSystem.Services;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Controls
{
    /// <summary>
    /// عنصر تحكم عرض الباركود
    /// </summary>
    public partial class BarcodeDisplay : UserControl, INotifyPropertyChanged
    {
        private readonly BarcodeService _barcodeService;

        private string _barcodeText = string.Empty;
        private BitmapImage? _barcodeImage;
        private int _barcodeWidth = 300;
        private int _barcodeHeight = 100;
        private bool _isGenerating;
        private bool _hasBarcode;
        private bool _hasError;
        private string _errorMessage = string.Empty;

        public BarcodeDisplay()
        {
            InitializeComponent();
            DataContext = this;

            _barcodeService = new BarcodeService();

            InitializeCommands();
        }

        #region Properties

        public string BarcodeText
        {
            get => _barcodeText;
            set
            {
                if (_barcodeText != value)
                {
                    _barcodeText = value;
                    OnPropertyChanged();
                    ClearError();
                }
            }
        }

        public BitmapImage? BarcodeImage
        {
            get => _barcodeImage;
            set
            {
                if (_barcodeImage != value)
                {
                    _barcodeImage = value;
                    OnPropertyChanged();
                }
            }
        }

        public int BarcodeWidth
        {
            get => _barcodeWidth;
            set
            {
                if (_barcodeWidth != value)
                {
                    _barcodeWidth = value;
                    OnPropertyChanged();
                    if (HasBarcode && !string.IsNullOrWhiteSpace(BarcodeText))
                    {
                        _ = GenerateBarcodeAsync();
                    }
                }
            }
        }

        public int BarcodeHeight
        {
            get => _barcodeHeight;
            set
            {
                if (_barcodeHeight != value)
                {
                    _barcodeHeight = value;
                    OnPropertyChanged();
                    if (HasBarcode && !string.IsNullOrWhiteSpace(BarcodeText))
                    {
                        _ = GenerateBarcodeAsync();
                    }
                }
            }
        }

        public bool IsGenerating
        {
            get => _isGenerating;
            set
            {
                if (_isGenerating != value)
                {
                    _isGenerating = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool HasBarcode
        {
            get => _hasBarcode;
            set
            {
                if (_hasBarcode != value)
                {
                    _hasBarcode = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool HasError
        {
            get => _hasError;
            set
            {
                if (_hasError != value)
                {
                    _hasError = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Commands

        public ICommand GenerateBarcodeCommand { get; private set; } = null!;
        public ICommand SaveBarcodeCommand { get; private set; } = null!;
        public ICommand PrintBarcodeCommand { get; private set; } = null!;
        public ICommand CopyBarcodeCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            GenerateBarcodeCommand = new RelayCommand(async () => await GenerateBarcodeAsync(),
                () => !string.IsNullOrWhiteSpace(BarcodeText) && !IsGenerating);
            SaveBarcodeCommand = new RelayCommand(async () => await SaveBarcodeAsync(),
                () => HasBarcode && BarcodeImage != null);
            PrintBarcodeCommand = new RelayCommand(async () => await PrintBarcodeAsync(),
                () => HasBarcode && BarcodeImage != null);
            CopyBarcodeCommand = new RelayCommand(() => CopyBarcode(),
                () => HasBarcode && BarcodeImage != null);
        }

        #endregion

        #region Methods

        /// <summary>
        /// توليد الباركود
        /// </summary>
        private async Task GenerateBarcodeAsync()
        {
            if (string.IsNullOrWhiteSpace(BarcodeText))
                return;

            try
            {
                IsGenerating = true;
                ClearError();

                await Task.Run(() =>
                {
                    var barcodeImage = _barcodeService.GenerateBarcode(BarcodeText, BarcodeWidth, BarcodeHeight);

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        BarcodeImage = barcodeImage;
                        HasBarcode = true;
                    });
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في توليد الباركود: {BarcodeText}");
                ShowError($"خطأ في توليد الباركود: {ex.Message}");
            }
            finally
            {
                IsGenerating = false;
            }
        }

        /// <summary>
        /// حفظ الباركود كصورة
        /// </summary>
        private async Task SaveBarcodeAsync()
        {
            if (BarcodeImage == null || string.IsNullOrWhiteSpace(BarcodeText))
                return;

            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "حفظ الباركود",
                    Filter = "PNG Files|*.png|JPEG Files|*.jpg|Bitmap Files|*.bmp|All Files|*.*",
                    DefaultExt = "png",
                    FileName = $"Barcode_{BarcodeText}_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    await Task.Run(() =>
                    {
                        var success = _barcodeService.SaveBarcodeToFile(BarcodeText, saveFileDialog.FileName,
                            null, BarcodeWidth, BarcodeHeight);

                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            if (success)
                            {
                                MessageBox.Show("تم حفظ الباركود بنجاح", "نجح الحفظ",
                                    MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                            else
                            {
                                MessageBox.Show("فشل في حفظ الباركود", "خطأ",
                                    MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        });
                    });
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في حفظ الباركود");
                MessageBox.Show($"خطأ في حفظ الباركود: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة الباركود
        /// </summary>
        private async Task PrintBarcodeAsync()
        {
            if (string.IsNullOrWhiteSpace(BarcodeText))
                return;

            try
            {
                // هنا يمكن إضافة منطق الطباعة
                await Task.Delay(100); // محاكاة عملية الطباعة
                MessageBox.Show("ميزة طباعة الباركود ستكون متاحة قريباً", "قيد التطوير",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في طباعة الباركود");
                MessageBox.Show($"خطأ في طباعة الباركود: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// نسخ الباركود للحافظة
        /// </summary>
        private void CopyBarcode()
        {
            try
            {
                if (BarcodeImage != null)
                {
                    Clipboard.SetImage(BarcodeImage);
                    MessageBox.Show("تم نسخ الباركود للحافظة", "تم النسخ",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في نسخ الباركود");
                MessageBox.Show($"خطأ في نسخ الباركود: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعيين نص الباركود من الخارج
        /// </summary>
        /// <param name="text">النص</param>
        public async Task SetBarcodeTextAsync(string text)
        {
            BarcodeText = text;
            if (!string.IsNullOrWhiteSpace(text))
            {
                await GenerateBarcodeAsync();
            }
        }

        /// <summary>
        /// مسح الباركود
        /// </summary>
        public void ClearBarcode()
        {
            BarcodeText = string.Empty;
            BarcodeImage = null;
            HasBarcode = false;
            ClearError();
        }

        /// <summary>
        /// عرض رسالة خطأ
        /// </summary>
        private void ShowError(string message)
        {
            ErrorMessage = message;
            HasError = true;
        }

        /// <summary>
        /// مسح رسالة الخطأ
        /// </summary>
        private void ClearError()
        {
            ErrorMessage = string.Empty;
            HasError = false;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
