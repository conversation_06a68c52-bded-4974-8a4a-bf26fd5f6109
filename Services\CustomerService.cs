using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class CustomerService
    {
        private readonly DatabaseService _dbService;

        public CustomerService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<IEnumerable<Customer>> GetAllCustomersAsync()
        {
            const string sql = "SELECT * FROM Customers ORDER BY Name";
            return await _dbService.QueryAsync<Customer>(sql);
        }

        public async Task<IEnumerable<Customer>> GetCustomersWithOutstandingBalanceAsync()
        {
            const string sql = "SELECT * FROM Customers WHERE Balance > 0 ORDER BY Balance DESC";
            return await _dbService.QueryAsync<Customer>(sql);
        }

        public async Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm)
        {
            const string sql = @"
                SELECT * FROM Customers
                WHERE Name LIKE @SearchTerm OR Phone LIKE @SearchTerm OR Email LIKE @SearchTerm
                ORDER BY Name";

            return await _dbService.QueryAsync<Customer>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<Customer> GetCustomerByIdAsync(int id)
        {
            const string sql = "SELECT * FROM Customers WHERE Id = @Id";
            return await _dbService.QuerySingleOrDefaultAsync<Customer>(sql, new { Id = id });
        }

        public async Task<Customer> AddCustomerAsync(Customer customer)
        {
            customer.CreatedAt = DateTime.Now;

            // First check what columns exist in the Customers table
            try
            {
                // Try with basic columns first (compatible with existing schema)
                const string sql = @"
                    INSERT INTO Customers (Name, Phone, Email, Address)
                    VALUES (@Name, @Phone, @Email, @Address);
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    Name = customer.Name,
                    Phone = customer.Phone,
                    Email = customer.Email ?? "",
                    Address = customer.Address ?? ""
                };

                var newId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                customer.Id = newId;

                return customer;
            }
            catch (Exception)
            {
                // If that fails, try with even more basic columns
                const string basicSql = @"
                    INSERT INTO Customers (Name, Phone)
                    VALUES (@Name, @Phone);
                    SELECT last_insert_rowid();";

                var basicParameters = new
                {
                    Name = customer.Name,
                    Phone = customer.Phone
                };

                var newId = await _dbService.QuerySingleAsync<int>(basicSql, basicParameters);
                customer.Id = newId;

                return customer;
            }
        }

        public async Task<bool> UpdateCustomerAsync(Customer customer)
        {
            customer.UpdatedAt = DateTime.Now;

            // Use basic columns that should exist in most schemas
            try
            {
                const string sql = @"
                    UPDATE Customers SET
                        Name = @Name,
                        Phone = @Phone,
                        Email = @Email,
                        Address = @Address
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = customer.Id,
                    Name = customer.Name,
                    Phone = customer.Phone,
                    Email = customer.Email ?? "",
                    Address = customer.Address ?? ""
                };

                var result = await _dbService.ExecuteAsync(sql, parameters);
                return result > 0;
            }
            catch (Exception)
            {
                // Fallback to even more basic update
                const string basicSql = @"
                    UPDATE Customers SET
                        Name = @Name,
                        Phone = @Phone
                    WHERE Id = @Id";

                var basicParameters = new
                {
                    Id = customer.Id,
                    Name = customer.Name,
                    Phone = customer.Phone
                };

                var result = await _dbService.ExecuteAsync(basicSql, basicParameters);
                return result > 0;
            }
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            return await _dbService.DeleteAsync("Customers", id);
        }

        public async Task<bool> UpdateCustomerBalanceAsync(int customerId, decimal amount)
        {
            const string sql = @"
                UPDATE Customers
                SET Balance = Balance + @Amount, UpdatedAt = @UpdatedAt
                WHERE Id = @CustomerId";

            var result = await _dbService.ExecuteAsync(sql, new
            {
                CustomerId = customerId,
                Amount = amount,
                UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });

            return result > 0;
        }

        public async Task<IEnumerable<CustomerSalesHistory>> GetCustomerSalesHistoryAsync(int customerId)
        {
            const string sql = @"
                SELECT s.Id, s.InvoiceNumber, s.Date, s.Total, s.PaymentStatus,
                       COUNT(si.Id) as ItemCount
                FROM Sales s
                LEFT JOIN SaleItems si ON s.Id = si.SaleId
                WHERE s.CustomerId = @CustomerId
                GROUP BY s.Id
                ORDER BY s.Date DESC";

            return await _dbService.QueryAsync<CustomerSalesHistory>(sql, new { CustomerId = customerId });
        }

        public async Task<IEnumerable<Customer>> GetTopCustomersAsync(int count = 10, DateTime? startDate = null, DateTime? endDate = null)
        {
            string dateFilter = "";
            if (startDate.HasValue && endDate.HasValue)
            {
                dateFilter = "AND s.Date BETWEEN @StartDate AND @EndDate";
            }

            string sql = $@"
                SELECT c.*, SUM(s.Total) as TotalPurchases
                FROM Customers c
                INNER JOIN Sales s ON c.Id = s.CustomerId
                WHERE 1=1 {dateFilter}
                GROUP BY c.Id
                ORDER BY TotalPurchases DESC
                LIMIT @Count";

            return await _dbService.QueryAsync<Customer>(sql, new
            {
                Count = count,
                StartDate = startDate?.ToString("yyyy-MM-dd"),
                EndDate = endDate?.ToString("yyyy-MM-dd")
            });
        }

        // Additional methods for ReportService
        public async Task<int> GetCustomerCountAsync()
        {
            const string sql = "SELECT COUNT(*) FROM Customers";
            return await _dbService.QuerySingleAsync<int>(sql);
        }

        public async Task<decimal> GetTotalCustomerBalanceAsync()
        {
            const string sql = "SELECT SUM(Balance) FROM Customers WHERE Balance > 0";
            var result = await _dbService.QuerySingleOrDefaultAsync<decimal?>(sql);
            return result ?? 0;
        }

        public async Task<Customer> GetOrCreateDefaultCustomerAsync()
        {
            try
            {
                // البحث عن العميل الافتراضي
                var customers = await GetAllCustomersAsync();
                var defaultCustomer = customers.FirstOrDefault(c => c.Name.Equals("CLIENT", StringComparison.OrdinalIgnoreCase));

                if (defaultCustomer == null)
                {
                    // إنشاء العميل الافتراضي
                    defaultCustomer = new Customer
                    {
                        Name = "CLIENT",
                        Phone = "",
                        Email = "",
                        Address = "عميل افتراضي للبيع السريع",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        Notes = "عميل افتراضي للبيع السريع - لا يحذف"
                    };

                    defaultCustomer = await AddCustomerAsync(defaultCustomer);
                }

                return defaultCustomer;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء العميل الافتراضي: {ex.Message}", ex);
            }
        }
    }

    public class CustomerSalesHistory
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public decimal Total { get; set; }
        public string PaymentStatus { get; set; } = string.Empty;
        public int ItemCount { get; set; }
    }
}