<UserControl x:Class="SalesManagementSystem.Views.WarehouseManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- المحولات -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- محول النص إلى رؤية -->
        <Style x:Key="StringToVisibilityConverter" TargetType="FrameworkElement">
            <Style.Triggers>
                <DataTrigger Binding="{Binding}" Value="">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding}" Value="{x:Null}">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>



        <!-- تحويل نوع المخزن إلى لون -->
        <Style x:Key="WarehouseTypeCard" TargetType="Border">
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Type}" Value="Main">
                    <Setter Property="Background" Value="#E8F5E8"/>
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Branch">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Transit">
                    <Setter Property="Background" Value="#F3E5F5"/>
                    <Setter Property="BorderBrush" Value="#9C27B0"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Returns">
                    <Setter Property="Background" Value="#FFEBEE"/>
                    <Setter Property="BorderBrush" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Damaged">
                    <Setter Property="Background" Value="#FAFAFA"/>
                    <Setter Property="BorderBrush" Value="#9E9E9E"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل حالة المخزن إلى لون -->
        <Style x:Key="StatusIndicator" TargetType="Ellipse">
            <Setter Property="Fill" Value="Gray"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Active">
                    <Setter Property="Fill" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Inactive">
                    <Setter Property="Fill" Value="#9E9E9E"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Maintenance">
                    <Setter Property="Fill" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Closed">
                    <Setter Property="Fill" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Warehouse" Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="إدارة المخازن المتعددة"
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding AddWarehouseCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة مخزن"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط البحث والفلاتر -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- مربع البحث -->
                <TextBox Grid.Column="0"
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="البحث في المخازن..."
                         materialDesign:TextFieldAssist.HasLeadingIcon="True"
                         materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,16,0"/>

                <!-- فلتر نوع المخزن -->
                <ComboBox Grid.Column="1"
                          ItemsSource="{Binding WarehouseTypes}"
                          SelectedItem="{Binding SelectedWarehouseType}"
                          materialDesign:HintAssist.Hint="نوع المخزن"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="0,0,16,0"/>

                <!-- فلتر حالة المخزن -->
                <ComboBox Grid.Column="2"
                          ItemsSource="{Binding WarehouseStatuses}"
                          SelectedItem="{Binding SelectedWarehouseStatus}"
                          materialDesign:HintAssist.Hint="حالة المخزن"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="0,0,16,0"/>

                <!-- زر البحث -->
                <Button Grid.Column="3"
                        Style="{StaticResource MaterialDesignIconButton}"
                        Command="{Binding SearchCommand}"
                        ToolTip="بحث">
                    <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- قائمة المخازن -->
        <ScrollViewer Grid.Row="2" Margin="16,8,16,16" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding Warehouses}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="3" Margin="0,0,0,16"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>

                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <materialDesign:Card Margin="8"
                                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
                            <Border BorderThickness="2" CornerRadius="4"
                                    Style="{StaticResource WarehouseTypeCard}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- رأس البطاقة -->
                                    <Grid Grid.Row="0" Background="LightGray"
                                          Opacity="0.1">
                                        <Grid Margin="16,12">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- أيقونة نوع المخزن -->
                                            <materialDesign:PackIcon Grid.Column="0"
                                                                   Kind="{Binding TypeIcon}"
                                                                   Width="24" Height="24"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,12,0"/>

                                            <!-- اسم المخزن -->
                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding Name}"
                                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                         FontWeight="Bold"/>
                                                <TextBlock Text="{Binding Code}"
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         Opacity="0.7"/>
                                            </StackPanel>

                                            <!-- مؤشر الحالة -->
                                            <Ellipse Grid.Column="2"
                                                   Width="12" Height="12"
                                                   Style="{StaticResource StatusIndicator}"
                                                   VerticalAlignment="Center"
                                                   Margin="8,0"/>

                                            <!-- أيقونة المخزن الافتراضي -->
                                            <materialDesign:PackIcon Grid.Column="3"
                                                                   Kind="Star"
                                                                   Width="16" Height="16"
                                                                   Foreground="Gold"
                                                                   VerticalAlignment="Center"
                                                                   Visibility="{Binding IsDefault, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                        </Grid>
                                    </Grid>

                                    <!-- محتوى البطاقة -->
                                    <StackPanel Grid.Row="1" Margin="16">
                                        <!-- نوع وحالة المخزن -->
                                        <Grid Margin="0,0,0,8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="النوع" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding TypeDisplay}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="الحالة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding StatusDisplay}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            </StackPanel>
                                        </Grid>

                                        <!-- الموقع -->
                                        <StackPanel Margin="0,0,0,8">
                                            <TextBlock Text="الموقع" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding City}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                        </StackPanel>

                                        <!-- المدير -->
                                        <StackPanel Margin="0,0,0,8">
                                            <TextBlock Text="المدير" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding ManagerName}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                        </StackPanel>

                                        <!-- السعة -->
                                        <StackPanel Margin="0,0,0,8">
                                            <TextBlock Text="السعة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <ProgressBar Value="{Binding CapacityUsagePercentage, Mode=OneWay}"
                                                       Maximum="100"
                                                       Height="6"
                                                       Margin="0,4,0,4"/>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0"
                                                         Text="{Binding FormattedUsedCapacity}"
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                                <TextBlock Grid.Column="1"
                                                         Text="{Binding FormattedTotalCapacity}"
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                            </Grid>
                                        </StackPanel>
                                    </StackPanel>

                                    <!-- إحصائيات سريعة -->
                                    <Grid Grid.Row="2" Margin="16,0,16,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding LocationCount}"
                                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                            <TextBlock Text="موقع"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Opacity="0.7"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding TotalItems}"
                                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                                            <TextBlock Text="صنف"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Opacity="0.7"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding FormattedTotalValue}"
                                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Foreground="Green"/>
                                            <TextBlock Text="قيمة"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Opacity="0.7"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- أزرار الإجراءات -->
                                    <Grid Grid.Row="3" Margin="16,8,16,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- زر التفاصيل -->
                                        <Button Grid.Column="1"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.ViewWarehouseDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="عرض التفاصيل">
                                            <materialDesign:PackIcon Kind="Eye" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر التعديل -->
                                        <Button Grid.Column="2"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.EditWarehouseCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="تعديل">
                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر الحذف -->
                                        <Button Grid.Column="3"
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.DeleteWarehouseCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="حذف"
                                              Foreground="Red">
                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Border>
                        </materialDesign:Card>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Grid Grid.Row="2"
              Background="White"
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري التحميل..."
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
