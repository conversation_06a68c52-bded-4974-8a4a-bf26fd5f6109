using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    public class Expense : INotifyPropertyChanged
    {
        private int _id;
        private string _title = string.Empty;
        private int _categoryId;
        private string _categoryName = string.Empty;
        private decimal _amount;
        private DateTime _date;
        private string _notes = string.Empty;
        private DateTime _createdAt;
        private DateTime? _updatedAt;
        private string _employeeName = string.Empty;
        private string _productName = string.Empty;
        private string _description = string.Empty;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CategoryId
        {
            get => _categoryId;
            set
            {
                if (_categoryId != value)
                {
                    _categoryId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CategoryName
        {
            get => _categoryName;
            set
            {
                if (_categoryName != value)
                {
                    _categoryName = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Amount
        {
            get => _amount;
            set
            {
                if (_amount != value)
                {
                    _amount = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime Date
        {
            get => _date;
            set
            {
                if (_date != value)
                {
                    _date = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string EmployeeName
        {
            get => _employeeName;
            set
            {
                if (_employeeName != value)
                {
                    _employeeName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductName
        {
            get => _productName;
            set
            {
                if (_productName != value)
                {
                    _productName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        // Calculated properties
        public bool IsCurrentMonth => Date.Month == DateTime.Now.Month && Date.Year == DateTime.Now.Year;
        public string FormattedAmount => $"{Amount:F2} دج";
        public string FormattedDate => Date.ToString("yyyy/MM/dd");
        public string CategoryIcon => GetCategoryIcon(CategoryId);

        public static string GetCategoryIcon(int categoryId)
        {
            return categoryId switch
            {
                1 => "👨‍💼", // راتب موظف
                2 => "📅", // منتج منتهي الصلاحية
                3 => "💔", // منتج تالف
                4 => "💡", // فواتير ومرافق
                5 => "🏢", // إيجار
                6 => "🔧", // صيانة
                7 => "📢", // تسويق وإعلان
                8 => "🚚", // نقل ومواصلات
                9 => "📋", // مصاريف مكتبية
                10 => "💰", // مصاريف أخرى
                _ => "❓"
            };
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName ?? string.Empty));
        }
    }

    public class ExpenseValidator : AbstractValidator<Expense>
    {
        public ExpenseValidator()
        {
            RuleFor(e => e.Title).NotEmpty().WithMessage("Expense title is required")
                .MaximumLength(100).WithMessage("Expense title cannot exceed 100 characters");

            RuleFor(e => e.CategoryId).NotEmpty().WithMessage("Category is required");

            RuleFor(e => e.Amount).GreaterThan(0).WithMessage("Amount must be greater than 0");

            RuleFor(e => e.Date).NotEmpty().WithMessage("Date is required")
                .LessThanOrEqualTo(DateTime.Now).WithMessage("Date cannot be in the future");

            RuleFor(e => e.Notes).MaximumLength(500).WithMessage("Notes cannot exceed 500 characters");
        }
    }

    public class ExpenseCategory : INotifyPropertyChanged
    {
        private int _id;
        private string _name = string.Empty;
        private string _description = string.Empty;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName ?? string.Empty));
        }
    }

    public class ExpenseCategoryValidator : AbstractValidator<ExpenseCategory>
    {
        public ExpenseCategoryValidator()
        {
            RuleFor(c => c.Name).NotEmpty().WithMessage("Category name is required")
                .MaximumLength(50).WithMessage("Category name cannot exceed 50 characters");

            RuleFor(c => c.Description).MaximumLength(200).WithMessage("Description cannot exceed 200 characters");
        }
    }
}