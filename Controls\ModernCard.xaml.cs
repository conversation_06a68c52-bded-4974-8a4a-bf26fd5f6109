using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using MaterialDesignThemes.Wpf;

namespace SalesManagementSystem.Controls
{
    /// <summary>
    /// بطاقة حديثة مع تأثيرات بصرية متقدمة
    /// </summary>
    public partial class ModernCard : UserControl
    {
        #region Dependency Properties

        public static readonly DependencyProperty HeaderTitleProperty =
            DependencyProperty.Register(nameof(HeaderTitle), typeof(string), typeof(ModernCard));

        public static readonly DependencyProperty HeaderSubtitleProperty =
            DependencyProperty.Register(nameof(HeaderSubtitle), typeof(string), typeof(ModernCard));

        public static readonly DependencyProperty HeaderIconProperty =
            DependencyProperty.Register(nameof(HeaderIcon), typeof(PackIconKind?), typeof(ModernCard));

        public static readonly DependencyProperty HeaderBackgroundProperty =
            DependencyProperty.Register(nameof(HeaderBackground), typeof(Brush), typeof(ModernCard),
                new PropertyMetadata(Brushes.Transparent));

        public static readonly DependencyProperty HeaderForegroundProperty =
            DependencyProperty.Register(nameof(HeaderForeground), typeof(Brush), typeof(ModernCard),
                new PropertyMetadata(Application.Current.Resources["MaterialDesignBody"]));

        public static readonly DependencyProperty HeaderActionProperty =
            DependencyProperty.Register(nameof(HeaderAction), typeof(object), typeof(ModernCard));

        public static readonly DependencyProperty CardContentProperty =
            DependencyProperty.Register(nameof(CardContent), typeof(object), typeof(ModernCard));

        public static readonly DependencyProperty FooterContentProperty =
            DependencyProperty.Register(nameof(FooterContent), typeof(object), typeof(ModernCard));

        public static readonly DependencyProperty IsClickableProperty =
            DependencyProperty.Register(nameof(IsClickable), typeof(bool), typeof(ModernCard),
                new PropertyMetadata(false));

        public static readonly DependencyProperty HasHeaderProperty =
            DependencyProperty.Register(nameof(HasHeader), typeof(bool), typeof(ModernCard),
                new PropertyMetadata(false));

        public static readonly DependencyProperty HasFooterProperty =
            DependencyProperty.Register(nameof(HasFooter), typeof(bool), typeof(ModernCard),
                new PropertyMetadata(false));

        #endregion

        #region Properties

        public string HeaderTitle
        {
            get => (string)GetValue(HeaderTitleProperty);
            set => SetValue(HeaderTitleProperty, value);
        }

        public string HeaderSubtitle
        {
            get => (string)GetValue(HeaderSubtitleProperty);
            set => SetValue(HeaderSubtitleProperty, value);
        }

        public PackIconKind? HeaderIcon
        {
            get => (PackIconKind?)GetValue(HeaderIconProperty);
            set => SetValue(HeaderIconProperty, value);
        }

        public Brush HeaderBackground
        {
            get => (Brush)GetValue(HeaderBackgroundProperty);
            set => SetValue(HeaderBackgroundProperty, value);
        }

        public Brush HeaderForeground
        {
            get => (Brush)GetValue(HeaderForegroundProperty);
            set => SetValue(HeaderForegroundProperty, value);
        }

        public object HeaderAction
        {
            get => GetValue(HeaderActionProperty);
            set => SetValue(HeaderActionProperty, value);
        }

        public object CardContent
        {
            get => GetValue(CardContentProperty);
            set => SetValue(CardContentProperty, value);
        }

        public object FooterContent
        {
            get => GetValue(FooterContentProperty);
            set => SetValue(FooterContentProperty, value);
        }

        public bool IsClickable
        {
            get => (bool)GetValue(IsClickableProperty);
            set => SetValue(IsClickableProperty, value);
        }

        public bool HasHeader
        {
            get => (bool)GetValue(HasHeaderProperty);
            set => SetValue(HasHeaderProperty, value);
        }

        public bool HasFooter
        {
            get => (bool)GetValue(HasFooterProperty);
            set => SetValue(HasFooterProperty, value);
        }

        #endregion

        #region Events

        public event EventHandler<RoutedEventArgs>? CardClicked;

        #endregion

        #region Constructor

        public ModernCard()
        {
            InitializeComponent();
            DataContext = this;
        }

        #endregion

        #region Event Handlers

        protected override void OnPreviewMouseLeftButtonDown(MouseButtonEventArgs e)
        {
            base.OnPreviewMouseLeftButtonDown(e);

            if (IsClickable)
            {
                CreateRippleEffect(e.GetPosition(RippleOverlay));
            }
        }

        protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonUp(e);

            if (IsClickable)
            {
                CardClicked?.Invoke(this, e);
            }
        }

        #endregion

        #region Private Methods

        private void CreateRippleEffect(Point position)
        {
            try
            {
                var ripple = RippleEllipse;
                if (ripple == null) return;

                // حساب حجم التأثير
                var maxRadius = Math.Max(RippleOverlay.ActualWidth, RippleOverlay.ActualHeight);

                // تعيين موقع وحجم الدائرة
                ripple.Width = ripple.Height = maxRadius * 2;
                Canvas.SetLeft(ripple, position.X - maxRadius);
                Canvas.SetTop(ripple, position.Y - maxRadius);

                // إنشاء الرسوم المتحركة
                var scaleAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = TimeSpan.FromMilliseconds(600),
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                };

                var opacityAnimation = new DoubleAnimation
                {
                    From = 0.3,
                    To = 0,
                    Duration = TimeSpan.FromMilliseconds(600),
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                };

                var storyboard = new Storyboard();

                Storyboard.SetTarget(scaleAnimation, ripple);
                Storyboard.SetTargetProperty(scaleAnimation,
                    new PropertyPath("(UIElement.RenderTransform).(ScaleTransform.ScaleX)"));

                var scaleYAnimation = scaleAnimation.Clone();
                Storyboard.SetTarget(scaleYAnimation, ripple);
                Storyboard.SetTargetProperty(scaleYAnimation,
                    new PropertyPath("(UIElement.RenderTransform).(ScaleTransform.ScaleY)"));

                Storyboard.SetTarget(opacityAnimation, ripple);
                Storyboard.SetTargetProperty(opacityAnimation, new PropertyPath("Opacity"));

                storyboard.Children.Add(scaleAnimation);
                storyboard.Children.Add(scaleYAnimation);
                storyboard.Children.Add(opacityAnimation);

                storyboard.Begin();
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ دون إيقاف التطبيق
                System.Diagnostics.Debug.WriteLine($"خطأ في تأثير الريبل: {ex.Message}");
            }
        }

        #endregion
    }
}
