<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <!-- تعطيل تحذيرات الحزم القديمة -->
    <NoWarn>$(NoWarn);NU1701</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="8.0.5" />
    <PackageReference Include="itext7" Version="9.2.0" />
    <!-- PDF export temporarily disabled - will be updated to iText7 later -->
    <!-- <PackageReference Include="itext7" Version="8.0.2" /> -->
    <!-- LiveCharts - حزمة قديمة ولكن مطلوبة للواجهات الحالية -->
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />
    <!-- LiveChartsCore - الإصدار الحديث للمستقبل -->
    <PackageReference Include="LiveChartsCore.SkiaSharpView.WPF" Version="2.0.0-rc2" />
    <PackageReference Include="MahApps.Metro" Version="2.4.9" />
    <PackageReference Include="MaterialDesignThemes" Version="4.6.1" />
    <PackageReference Include="Dapper" Version="2.0.123" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.116" />
    <PackageReference Include="Prism.Core" Version="8.1.97" />
    <PackageReference Include="FluentValidation" Version="11.2.2" />
    <PackageReference Include="NLog" Version="5.0.4" />
    <PackageReference Include="ZXing.Net" Version="0.16.9" />
    <PackageReference Include="ZXing.Net.Bindings.Windows.Compatibility" Version="0.16.12" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
    <PackageReference Include="Microsoft.VisualBasic" Version="10.3.0" />
  </ItemGroup>



  <ItemGroup>
    <Folder Include="Assets\Fonts\" />
    <Folder Include="Assets\Styles\" />
  </ItemGroup>

</Project>