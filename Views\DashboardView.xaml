<UserControl x:Class="SalesManagementSystem.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Statistics Cards -->
            <Grid Grid.Row="0" Margin="0,0,0,32">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Sales Card -->
                <Border Grid.Column="0" Style="{StaticResource ModernCard}" Margin="0,0,12,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <DockPanel Grid.Row="0" Margin="0,0,0,16">
                            <Border DockPanel.Dock="Right" Background="{StaticResource Primary50}"
                                   CornerRadius="12" Width="48" Height="48">
                                <materialDesign:PackIcon Kind="CashMultiple" Width="24" Height="24"
                                                       Foreground="{StaticResource PrimaryBrush}"
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="{DynamicResource TotalSales}" FontSize="14"
                                         Foreground="{StaticResource TextSecondaryBrush}" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalSalesText" Text="$0.00" FontSize="28"
                                         FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}"/>
                            </StackPanel>
                        </DockPanel>

                        <StackPanel Grid.Row="1" Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="TrendingUp" Width="16" Height="16"
                                                   Foreground="{StaticResource SuccessBrush}" VerticalAlignment="Center"/>
                            <TextBlock Text="+12.5% من الشهر الماضي" FontSize="12"
                                     Foreground="{StaticResource SuccessBrush}" Margin="4,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Total Expenses Card -->
                <materialDesign:Card Grid.Column="1" Margin="5,0,5,0" Padding="20">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="TrendingDown" Width="32" Height="32"
                                                   Foreground="#F44336"
                                                   DockPanel.Dock="Right"/>
                            <StackPanel>
                                <TextBlock Text="{DynamicResource TotalExpenses}" FontSize="14"
                                         Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                <TextBlock x:Name="TotalExpensesText" Text="$0.00" FontSize="24"
                                         FontWeight="Bold" Foreground="#F44336"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Net Profit Card -->
                <materialDesign:Card Grid.Column="2" Margin="5,0,5,0" Padding="20">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="TrendingUp" Width="32" Height="32"
                                                   Foreground="#4CAF50"
                                                   DockPanel.Dock="Right"/>
                            <StackPanel>
                                <TextBlock Text="{DynamicResource NetProfit}" FontSize="14"
                                         Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                <TextBlock x:Name="NetProfitText" Text="$0.00" FontSize="24"
                                         FontWeight="Bold" Foreground="#4CAF50"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Low Stock Items Card -->
                <materialDesign:Card Grid.Column="3" Margin="10,0,0,0" Padding="20">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="AlertCircle" Width="32" Height="32"
                                                   Foreground="#FF9800"
                                                   DockPanel.Dock="Right"/>
                            <StackPanel>
                                <TextBlock Text="{DynamicResource LowStockItems}" FontSize="14"
                                         Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                <TextBlock x:Name="LowStockText" Text="0" FontSize="24"
                                         FontWeight="Bold" Foreground="#FF9800"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Charts Section -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sales Chart -->
                <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" Padding="20">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource SalesOverview}" FontSize="18" FontWeight="Medium"
                                 Margin="0,0,0,15"/>
                        <Border Height="200" Background="#F5F5F5" CornerRadius="4">
                            <TextBlock Text="📊 رسم بياني للمبيعات" HorizontalAlignment="Center"
                                     VerticalAlignment="Center" FontSize="16"
                                     Foreground="{StaticResource MaterialDesignBodyLight}"/>
                        </Border>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Top Products -->
                <materialDesign:Card Grid.Column="1" Margin="10,0,0,0" Padding="20">
                    <StackPanel>
                        <TextBlock Text="{DynamicResource TopSellingProducts}" FontSize="18" FontWeight="Medium"
                                 Margin="0,0,0,15"/>
                        <ListBox x:Name="TopProductsList" Height="200" BorderThickness="0">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="{Binding Name}" FontSize="14"/>
                                        <TextBlock Grid.Column="1" Text="{Binding TotalSold}" FontSize="14"
                                                 FontWeight="Bold" Foreground="{StaticResource PrimaryHueMidBrush}"/>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Recent Activities -->
            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Recent Sales -->
                <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" Padding="20">
                    <StackPanel>
                        <DockPanel Margin="0,0,0,15">
                            <Button DockPanel.Dock="Right" Style="{StaticResource MaterialDesignFlatButton}"
                                  Content="{DynamicResource ViewAll}" Click="ViewAllSales_Click"/>
                            <TextBlock Text="{DynamicResource RecentSales}" FontSize="18" FontWeight="Medium"/>
                        </DockPanel>
                        <DataGrid x:Name="RecentSalesGrid" Height="250" AutoGenerateColumns="False"
                                CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="{DynamicResource InvoiceNumber}" Binding="{Binding InvoiceNumber}" Width="*"/>
                                <DataGridTextColumn Header="{DynamicResource Customer}" Binding="{Binding CustomerName}" Width="*"/>
                                <DataGridTextColumn Header="{DynamicResource Total}" Binding="{Binding Total, StringFormat=C}" Width="Auto"/>
                                <DataGridTextColumn Header="{DynamicResource Date}" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="Auto"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Low Stock Products -->
                <materialDesign:Card Grid.Column="1" Margin="10,0,0,0" Padding="20">
                    <StackPanel>
                        <DockPanel Margin="0,0,0,15">
                            <Button DockPanel.Dock="Right" Style="{StaticResource MaterialDesignFlatButton}"
                                  Content="{DynamicResource ViewAll}" Click="ViewAllProducts_Click"/>
                            <TextBlock Text="{DynamicResource LowStockItems}" FontSize="18" FontWeight="Medium"/>
                        </DockPanel>
                        <DataGrid x:Name="LowStockGrid" Height="250" AutoGenerateColumns="False"
                                CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="{DynamicResource ProductName}" Binding="{Binding Name}" Width="*"/>
                                <DataGridTextColumn Header="{DynamicResource Quantity}" Binding="{Binding Quantity}" Width="Auto"/>
                                <DataGridTextColumn Header="{DynamicResource MinQuantity}" Binding="{Binding MinQuantity}" Width="Auto"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
