using System;
using System.IO;
using System.Text.Json;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إعدادات الأمان
    /// </summary>
    public class SecuritySettingsService
    {
        private static SecuritySettingsService? _instance;
        private SecuritySettings _currentSettings = new();
        private readonly string _settingsFilePath;

        public static SecuritySettingsService Instance => _instance ??= new SecuritySettingsService();

        private SecuritySettingsService()
        {
            _settingsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "SalesManagementSystem", "SecuritySettings.json");

            // إنشاء المجلد إذا لم يكن موجوداً
            var directory = Path.GetDirectoryName(_settingsFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            LoadSettings();
        }

        // نموذج إعدادات الأمان
        public class SecuritySettings
        {
            // إعدادات كلمات المرور
            public int MinPasswordLength { get; set; } = 4;
            public bool RequireMixedCase { get; set; } = false;
            public bool RequireNumbers { get; set; } = false;
            public bool PreventWeakPasswords { get; set; } = true;

            // إعدادات الجلسات
            public int SessionTimeoutMinutes { get; set; } = 30;
            public bool EnableSessionTimeout { get; set; } = false;
            public bool SessionWarning { get; set; } = true;

            // إعدادات المحاولات الفاشلة
            public int MaxLoginAttempts { get; set; } = 3;
            public int LockoutDurationMinutes { get; set; } = 15;
            public bool EnableLockout { get; set; } = true;

            // إعدادات التسجيل والمراقبة
            public bool LogLoginAttempts { get; set; } = true;
            public bool LogSensitiveOperations { get; set; } = true;
            public bool LogDataChanges { get; set; } = false;
            public int LogRetentionDays { get; set; } = 90;

            // إعدادات النسخ الاحتياطي
            public bool AutoBackup { get; set; } = true;
            public int BackupIntervalHours { get; set; } = 24;
            public bool EncryptBackups { get; set; } = true;
            public string BackupFolderPath { get; set; } = "";

            // إعدادات متقدمة
            public bool DeveloperMode { get; set; } = false;
            public bool RemoteAccess { get; set; } = false;

            // معلومات إضافية
            public DateTime LastModified { get; set; } = DateTime.Now;
            public string ModifiedBy { get; set; } = "النظام";
        }

        // تحميل الإعدادات
        private void LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsFilePath))
                {
                    var json = File.ReadAllText(_settingsFilePath);
                    _currentSettings = JsonSerializer.Deserialize<SecuritySettings>(json) ?? new SecuritySettings();
                }
                else
                {
                    _currentSettings = new SecuritySettings();
                    SaveSettings(_currentSettings); // حفظ الإعدادات الافتراضية
                }
            }
            catch
            {
                _currentSettings = new SecuritySettings();
            }
        }

        // حفظ الإعدادات
        public bool SaveSettings(SecuritySettings settings)
        {
            try
            {
                settings.LastModified = DateTime.Now;
                settings.ModifiedBy = CurrentUserService.Instance.CurrentUserName;

                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_settingsFilePath, json);

                _currentSettings = settings;
                return true;
            }
            catch
            {
                return false;
            }
        }

        // الحصول على الإعدادات الحالية
        public SecuritySettings GetCurrentSettings()
        {
            return _currentSettings;
        }

        // إعادة تعيين إلى القيم الافتراضية
        public void ResetToDefaults()
        {
            _currentSettings = new SecuritySettings();
            SaveSettings(_currentSettings);
        }

        // تحديد مجلد النسخ الاحتياطي
        public void SetBackupFolder(string folderPath)
        {
            _currentSettings.BackupFolderPath = folderPath;
            SaveSettings(_currentSettings);
        }

        // التحقق من قوة كلمة المرور حسب الإعدادات
        public (bool IsValid, string ErrorMessage) ValidatePassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return (false, "كلمة المرور مطلوبة");

            if (password.Length < _currentSettings.MinPasswordLength)
                return (false, $"كلمة المرور يجب أن تكون {_currentSettings.MinPasswordLength} أحرف على الأقل");

            if (_currentSettings.RequireMixedCase)
            {
                bool hasUpper = false, hasLower = false;
                foreach (char c in password)
                {
                    if (char.IsUpper(c)) hasUpper = true;
                    if (char.IsLower(c)) hasLower = true;
                }
                if (!hasUpper || !hasLower)
                    return (false, "كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة");
            }

            if (_currentSettings.RequireNumbers)
            {
                bool hasNumber = false;
                foreach (char c in password)
                {
                    if (char.IsDigit(c)) hasNumber = true;
                }
                if (!hasNumber)
                    return (false, "كلمة المرور يجب أن تحتوي على أرقام");
            }

            if (_currentSettings.PreventWeakPasswords)
            {
                var weakPasswords = new[] { "1234", "password", "admin", "123456", "qwerty", "abc123" };
                foreach (var weak in weakPasswords)
                {
                    if (password.ToLower().Contains(weak.ToLower()))
                        return (false, "كلمة المرور ضعيفة جداً، يرجى اختيار كلمة مرور أقوى");
                }
            }

            return (true, "");
        }

        // التحقق من انتهاء الجلسة
        public bool IsSessionExpired(DateTime lastActivity)
        {
            if (!_currentSettings.EnableSessionTimeout)
                return false;

            var timeElapsed = DateTime.Now - lastActivity;
            return timeElapsed.TotalMinutes > _currentSettings.SessionTimeoutMinutes;
        }

        // التحقق من حظر المستخدم
        public bool IsUserLockedOut(string username, int failedAttempts, DateTime lastFailedAttempt)
        {
            if (!_currentSettings.EnableLockout)
                return false;

            if (failedAttempts < _currentSettings.MaxLoginAttempts)
                return false;

            var timeSinceLastAttempt = DateTime.Now - lastFailedAttempt;
            return timeSinceLastAttempt.TotalMinutes < _currentSettings.LockoutDurationMinutes;
        }

        // تسجيل حدث أمني
        public void LogSecurityEvent(string eventType, string description, string username = "")
        {
            try
            {
                if (!ShouldLogEvent(eventType))
                    return;

                var logEntry = new
                {
                    Timestamp = DateTime.Now,
                    EventType = eventType,
                    Description = description,
                    Username = string.IsNullOrEmpty(username) ? CurrentUserService.Instance.CurrentUserName : username,
                    IPAddress = "127.0.0.1", // يمكن تحسينه لاحقاً
                    UserAgent = "SalesManagementSystem"
                };

                var logPath = Path.Combine(Path.GetDirectoryName(_settingsFilePath) ?? "", "SecurityLog.txt");
                var logText = $"{logEntry.Timestamp:yyyy-MM-dd HH:mm:ss} | {logEntry.EventType} | {logEntry.Username} | {logEntry.Description}\n";

                File.AppendAllText(logPath, logText);

                // تنظيف السجلات القديمة
                CleanOldLogs();
            }
            catch
            {
                // تجاهل أخطاء التسجيل لتجنب تعطيل النظام
            }
        }

        // تحديد ما إذا كان يجب تسجيل الحدث
        private bool ShouldLogEvent(string eventType)
        {
            return eventType.ToLower() switch
            {
                "login_attempt" => _currentSettings.LogLoginAttempts,
                "sensitive_operation" => _currentSettings.LogSensitiveOperations,
                "data_change" => _currentSettings.LogDataChanges,
                _ => true
            };
        }

        // تنظيف السجلات القديمة
        private void CleanOldLogs()
        {
            try
            {
                var logPath = Path.Combine(Path.GetDirectoryName(_settingsFilePath) ?? "", "SecurityLog.txt");
                if (!File.Exists(logPath))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-_currentSettings.LogRetentionDays);
                var lines = File.ReadAllLines(logPath);
                var filteredLines = new System.Collections.Generic.List<string>();

                foreach (var line in lines)
                {
                    if (line.Length > 19 && DateTime.TryParse(line.Substring(0, 19), out var logDate))
                    {
                        if (logDate >= cutoffDate)
                            filteredLines.Add(line);
                    }
                }

                File.WriteAllLines(logPath, filteredLines);
            }
            catch
            {
                // تجاهل أخطاء التنظيف
            }
        }

        // الحصول على إحصائيات الأمان
        public SecurityStats GetSecurityStats()
        {
            return new SecurityStats
            {
                PasswordPolicyEnabled = _currentSettings.RequireMixedCase || _currentSettings.RequireNumbers,
                SessionTimeoutEnabled = _currentSettings.EnableSessionTimeout,
                LockoutEnabled = _currentSettings.EnableLockout,
                LoggingEnabled = _currentSettings.LogLoginAttempts || _currentSettings.LogSensitiveOperations,
                BackupEnabled = _currentSettings.AutoBackup,
                LastModified = _currentSettings.LastModified,
                ModifiedBy = _currentSettings.ModifiedBy
            };
        }

        // نموذج إحصائيات الأمان
        public class SecurityStats
        {
            public bool PasswordPolicyEnabled { get; set; }
            public bool SessionTimeoutEnabled { get; set; }
            public bool LockoutEnabled { get; set; }
            public bool LoggingEnabled { get; set; }
            public bool BackupEnabled { get; set; }
            public DateTime LastModified { get; set; }
            public string ModifiedBy { get; set; } = "";
        }
    }
}
