using System;
using System.IO;
using System.Windows;
using Microsoft.Win32;
using SalesManagementSystem.Services;
using System.Text.Json;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// نافذة إعدادات الأمان
    /// </summary>
    public partial class SecuritySettingsWindow : Window
    {
        private readonly SecuritySettingsService _securityService;
        private readonly EnhancedNotificationService _notificationService;

        public SecuritySettingsWindow()
        {
            InitializeComponent();
            _securityService = SecuritySettingsService.Instance;
            _notificationService = EnhancedNotificationService.Instance;

            LoadCurrentSettings();
        }

        // تحميل الإعدادات الحالية
        private void LoadCurrentSettings()
        {
            try
            {
                var settings = _securityService.GetCurrentSettings();

                // إعدادات كلمات المرور
                MinPasswordLengthTextBox.Text = settings.MinPasswordLength.ToString();
                RequireMixedCaseCheckBox.IsChecked = settings.RequireMixedCase;
                RequireNumbersCheckBox.IsChecked = settings.RequireNumbers;
                PreventWeakPasswordsCheckBox.IsChecked = settings.PreventWeakPasswords;

                // إعدادات الجلسات
                SessionTimeoutTextBox.Text = settings.SessionTimeoutMinutes.ToString();
                EnableSessionTimeoutCheckBox.IsChecked = settings.EnableSessionTimeout;
                SessionWarningCheckBox.IsChecked = settings.SessionWarning;

                // إعدادات المحاولات
                MaxLoginAttemptsTextBox.Text = settings.MaxLoginAttempts.ToString();
                LockoutDurationTextBox.Text = settings.LockoutDurationMinutes.ToString();
                EnableLockoutCheckBox.IsChecked = settings.EnableLockout;

                // إعدادات التسجيل
                LogLoginAttemptsCheckBox.IsChecked = settings.LogLoginAttempts;
                LogSensitiveOperationsCheckBox.IsChecked = settings.LogSensitiveOperations;
                LogDataChangesCheckBox.IsChecked = settings.LogDataChanges;
                LogRetentionDaysTextBox.Text = settings.LogRetentionDays.ToString();

                // إعدادات النسخ الاحتياطي
                AutoBackupCheckBox.IsChecked = settings.AutoBackup;
                BackupIntervalTextBox.Text = settings.BackupIntervalHours.ToString();
                EncryptBackupsCheckBox.IsChecked = settings.EncryptBackups;

                // إعدادات متقدمة
                DeveloperModeCheckBox.IsChecked = settings.DeveloperMode;
                RemoteAccessCheckBox.IsChecked = settings.RemoteAccess;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // حفظ الإعدادات
        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateSettings())
                    return;

                var settings = new SecuritySettingsService.SecuritySettings
                {
                    // إعدادات كلمات المرور
                    MinPasswordLength = int.Parse(MinPasswordLengthTextBox.Text),
                    RequireMixedCase = RequireMixedCaseCheckBox.IsChecked ?? false,
                    RequireNumbers = RequireNumbersCheckBox.IsChecked ?? false,
                    PreventWeakPasswords = PreventWeakPasswordsCheckBox.IsChecked ?? true,

                    // إعدادات الجلسات
                    SessionTimeoutMinutes = int.Parse(SessionTimeoutTextBox.Text),
                    EnableSessionTimeout = EnableSessionTimeoutCheckBox.IsChecked ?? false,
                    SessionWarning = SessionWarningCheckBox.IsChecked ?? true,

                    // إعدادات المحاولات
                    MaxLoginAttempts = int.Parse(MaxLoginAttemptsTextBox.Text),
                    LockoutDurationMinutes = int.Parse(LockoutDurationTextBox.Text),
                    EnableLockout = EnableLockoutCheckBox.IsChecked ?? true,

                    // إعدادات التسجيل
                    LogLoginAttempts = LogLoginAttemptsCheckBox.IsChecked ?? true,
                    LogSensitiveOperations = LogSensitiveOperationsCheckBox.IsChecked ?? true,
                    LogDataChanges = LogDataChangesCheckBox.IsChecked ?? false,
                    LogRetentionDays = int.Parse(LogRetentionDaysTextBox.Text),

                    // إعدادات النسخ الاحتياطي
                    AutoBackup = AutoBackupCheckBox.IsChecked ?? true,
                    BackupIntervalHours = int.Parse(BackupIntervalTextBox.Text),
                    EncryptBackups = EncryptBackupsCheckBox.IsChecked ?? true,

                    // إعدادات متقدمة
                    DeveloperMode = DeveloperModeCheckBox.IsChecked ?? false,
                    RemoteAccess = RemoteAccessCheckBox.IsChecked ?? false
                };

                if (_securityService.SaveSettings(settings))
                {
                    MessageBox.Show("تم حفظ إعدادات الأمان بنجاح", "نجح الحفظ",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    _notificationService.AddNotification(
                        "إعدادات الأمان",
                        "تم تحديث إعدادات الأمان بنجاح",
                        Models.EnhancedNotificationType.Success,
                        Models.EnhancedNotificationPriority.Normal,
                        "نظام");
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الإعدادات", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // التحقق من صحة الإعدادات
        private bool ValidateSettings()
        {
            // التحقق من طول كلمة المرور
            if (!int.TryParse(MinPasswordLengthTextBox.Text, out int minLength) || minLength < 1 || minLength > 50)
            {
                MessageBox.Show("الحد الأدنى لطول كلمة المرور يجب أن يكون بين 1 و 50", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                MinPasswordLengthTextBox.Focus();
                return false;
            }

            // التحقق من مهلة الجلسة
            if (!int.TryParse(SessionTimeoutTextBox.Text, out int sessionTimeout) || sessionTimeout < 1 || sessionTimeout > 1440)
            {
                MessageBox.Show("مهلة الجلسة يجب أن تكون بين 1 و 1440 دقيقة", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                SessionTimeoutTextBox.Focus();
                return false;
            }

            // التحقق من عدد المحاولات
            if (!int.TryParse(MaxLoginAttemptsTextBox.Text, out int maxAttempts) || maxAttempts < 1 || maxAttempts > 10)
            {
                MessageBox.Show("عدد المحاولات المسموحة يجب أن يكون بين 1 و 10", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                MaxLoginAttemptsTextBox.Focus();
                return false;
            }

            // التحقق من مدة الحظر
            if (!int.TryParse(LockoutDurationTextBox.Text, out int lockoutDuration) || lockoutDuration < 1 || lockoutDuration > 1440)
            {
                MessageBox.Show("مدة الحظر يجب أن تكون بين 1 و 1440 دقيقة", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                LockoutDurationTextBox.Focus();
                return false;
            }

            return true;
        }

        // تصدير الإعدادات
        private void Export_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "ملفات إعدادات الأمان (*.json)|*.json",
                    DefaultExt = "json",
                    FileName = $"SecuritySettings_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var settings = _securityService.GetCurrentSettings();
                    var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
                    File.WriteAllText(saveDialog.FileName, json);

                    MessageBox.Show("تم تصدير الإعدادات بنجاح", "نجح التصدير",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // استيراد الإعدادات
        private void Import_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Filter = "ملفات إعدادات الأمان (*.json)|*.json",
                    DefaultExt = "json"
                };

                if (openDialog.ShowDialog() == true)
                {
                    var json = File.ReadAllText(openDialog.FileName);
                    var settings = JsonSerializer.Deserialize<SecuritySettingsService.SecuritySettings>(json);

                    if (settings != null)
                    {
                        if (_securityService.SaveSettings(settings))
                        {
                            LoadCurrentSettings(); // إعادة تحميل الواجهة
                            MessageBox.Show("تم استيراد الإعدادات بنجاح", "نجح الاستيراد",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // تحديد مجلد النسخ الاحتياطي
        private void SelectBackupFolder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var folderDialog = new SaveFileDialog
                {
                    Title = "اختر مجلد النسخ الاحتياطي",
                    Filter = "مجلدات|*.",
                    FileName = "BackupFolder"
                };

                if (folderDialog.ShowDialog() == true)
                {
                    var folderPath = Path.GetDirectoryName(folderDialog.FileName) ?? "";
                    _securityService.SetBackupFolder(folderPath);
                    MessageBox.Show($"تم تحديد مجلد النسخ الاحتياطي:\n{folderPath}",
                        "تم التحديد", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد المجلد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // إعادة تعيين جميع الإعدادات
        private void ResetAllSettings_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إعادة تعيين جميع إعدادات الأمان إلى القيم الافتراضية؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد إعادة التعيين", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _securityService.ResetToDefaults();
                    LoadCurrentSettings();

                    MessageBox.Show("تم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية", "تم بنجاح",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    _notificationService.AddNotification(
                        "إعادة تعيين الإعدادات",
                        "تم إعادة تعيين إعدادات الأمان إلى القيم الافتراضية",
                        Models.EnhancedNotificationType.Warning,
                        Models.EnhancedNotificationPriority.Normal,
                        "نظام");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة التعيين: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // إغلاق النافذة
        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
