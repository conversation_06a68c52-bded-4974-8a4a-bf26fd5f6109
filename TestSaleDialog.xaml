<Window x:Class="SalesManagementSystem.TestSaleDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="اختبار حوار المبيعات" 
        Height="400" 
        Width="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <materialDesign:PackIcon Kind="ShoppingCart" 
                                   Width="64" Height="64" 
                                   HorizontalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   Margin="0,0,0,24"/>
            
            <TextBlock Text="اختبار حوار فاتورة المبيعات" 
                      Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,32"/>
            
            <Button x:Name="NewSaleButton"
                   Click="NewSaleButton_Click"
                   Style="{DynamicResource MaterialDesignRaisedButton}"
                   Width="200"
                   Height="50"
                   Margin="0,8">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Plus" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="فاتورة مبيعات جديدة" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            
            <Button x:Name="EditSaleButton"
                   Click="EditSaleButton_Click"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Width="200"
                   Height="50"
                   Margin="0,8">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Edit" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="تعديل فاتورة موجودة" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            
            <TextBlock x:Name="ResultTextBlock"
                      Text=""
                      Style="{DynamicResource MaterialDesignBody1TextBlock}"
                      HorizontalAlignment="Center"
                      Margin="0,24,0,0"
                      TextWrapping="Wrap"
                      MaxWidth="400"/>
        </StackPanel>
    </Grid>
</Window>
