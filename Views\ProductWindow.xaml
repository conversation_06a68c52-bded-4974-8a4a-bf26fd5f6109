<Window x:Class="SalesManagementSystem.Views.ProductWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة منتج جديد"
        Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Orange">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="📦" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="إضافة منتج جديد للمخزون" FontSize="18" 
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel>
                <GroupBox Header="بيانات المنتج الأساسية" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المنتج:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="0" Margin="0,20,10,10"/>

                        <TextBlock Grid.Row="0" Grid.Column="1" Text="الباركود:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Margin="10,20,0,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="الفئة:" Margin="0,0,0,5"/>
                        <ComboBox Grid.Row="1" Grid.Column="0" Margin="0,20,10,10">
                            <ComboBoxItem Content="إلكترونيات"/>
                            <ComboBoxItem Content="أجهزة كمبيوتر"/>
                            <ComboBoxItem Content="اكسسوارات"/>
                            <ComboBoxItem Content="برمجيات"/>
                        </ComboBox>

                        <TextBlock Grid.Row="1" Grid.Column="1" Text="الوحدة:" Margin="0,0,0,5"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" Margin="10,20,0,10">
                            <ComboBoxItem Content="قطعة"/>
                            <ComboBoxItem Content="كيلو"/>
                            <ComboBoxItem Content="متر"/>
                            <ComboBoxItem Content="لتر"/>
                        </ComboBox>

                        <TextBlock Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Text="الوصف:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,20,0,10" 
                                Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                    </Grid>
                </GroupBox>

                <GroupBox Header="الأسعار والمخزون" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="سعر الشراء:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="0" Margin="0,20,10,10"/>

                        <TextBlock Grid.Row="0" Grid.Column="1" Text="سعر البيع:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Margin="10,20,0,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="الكمية الحالية:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="0" Margin="0,20,10,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="1" Text="الحد الأدنى للتنبيه:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Margin="10,20,0,10"/>

                        <CheckBox Grid.Row="2" Grid.Column="0" Content="منتج نشط" IsChecked="True" Margin="0,10,0,0"/>
                        <CheckBox Grid.Row="2" Grid.Column="1" Content="يخضع للضريبة" IsChecked="True" Margin="10,10,0,0"/>
                    </Grid>
                </GroupBox>

                <GroupBox Header="معلومات إضافية">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="الشركة المصنعة:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="0" Margin="0,20,10,10"/>

                        <TextBlock Grid.Row="0" Grid.Column="1" Text="بلد المنشأ:" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Margin="10,20,0,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ الانتهاء:" Margin="0,0,0,5"/>
                        <DatePicker Grid.Row="1" Grid.Column="0" Margin="0,20,10,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="1" Text="الضمان (بالأشهر):" Margin="0,0,0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Margin="10,20,0,10"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ المنتج" Width="120" Height="35" Margin="10" 
                       Background="Orange" Foreground="White" FontWeight="Bold"/>
                <Button Content="🔄 جديد" Width="100" Height="35" Margin="10" 
                       Background="Blue" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إلغاء" Width="100" Height="35" Margin="10" 
                       Background="Red" Foreground="White" FontWeight="Bold" Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
