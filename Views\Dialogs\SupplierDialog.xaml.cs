using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views.Dialogs
{
    public partial class SupplierDialog : Window
    {
        private readonly SupplierDialogViewModel _viewModel;

        public Supplier? Result { get; private set; }

        public SupplierDialog(Supplier? existingSupplier = null)
        {
            InitializeComponent();
            
            _viewModel = existingSupplier != null 
                ? new SupplierDialogViewModel(existingSupplier) 
                : new SupplierDialogViewModel();
            
            DataContext = _viewModel;
            
            // Subscribe to events
            _viewModel.SupplierSaved += OnSupplierSaved;
            _viewModel.RequestClose += OnRequestClose;
        }

        #region Event Handlers

        private void OnSupplierSaved(Supplier supplier)
        {
            Result = supplier;
            DialogResult = true;
        }

        private void OnRequestClose(bool result)
        {
            DialogResult = result;
            Close();
        }

        #endregion

        protected override void OnClosed(System.EventArgs e)
        {
            // Unsubscribe from events
            _viewModel.SupplierSaved -= OnSupplierSaved;
            _viewModel.RequestClose -= OnRequestClose;
            
            base.OnClosed(e);
        }
    }
}
