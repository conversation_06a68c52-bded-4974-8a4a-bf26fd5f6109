using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class DashboardViewModel : BaseViewModel
    {
        #region Services

        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private readonly SaleService _saleService;
        private readonly CustomerService _customerService;
        private readonly ExpenseService _expenseService;

        #endregion

        #region Properties

        private decimal _totalSales;
        public decimal TotalSales
        {
            get => _totalSales;
            set => SetProperty(ref _totalSales, value);
        }

        private decimal _totalExpenses;
        public decimal TotalExpenses
        {
            get => _totalExpenses;
            set => SetProperty(ref _totalExpenses, value);
        }

        private decimal _netProfit;
        public decimal NetProfit
        {
            get => _netProfit;
            set => SetProperty(ref _netProfit, value);
        }

        private int _lowStockCount;
        public int LowStockCount
        {
            get => _lowStockCount;
            set => SetProperty(ref _lowStockCount, value);
        }

        private ObservableCollection<DashboardSale> _recentSales = new();
        public ObservableCollection<DashboardSale> RecentSales
        {
            get => _recentSales;
            set => SetProperty(ref _recentSales, value);
        }

        private ObservableCollection<Product> _lowStockProducts = new();
        public ObservableCollection<Product> LowStockProducts
        {
            get => _lowStockProducts;
            set => SetProperty(ref _lowStockProducts, value);
        }

        private ObservableCollection<TopProduct> _topProducts = new();
        public ObservableCollection<TopProduct> TopProducts
        {
            get => _topProducts;
            set => SetProperty(ref _topProducts, value);
        }

        private DateTime _selectedDate = DateTime.Now;
        public DateTime SelectedDate
        {
            get => _selectedDate;
            set
            {
                if (SetProperty(ref _selectedDate, value))
                {
                    _ = LoadDashboardDataAsync();
                }
            }
        }

        #endregion

        #region Commands

        private ICommand? _viewAllSalesCommand;
        public ICommand ViewAllSalesCommand => _viewAllSalesCommand ??= new DelegateCommand(ViewAllSales);

        private ICommand? _viewAllProductsCommand;
        public ICommand ViewAllProductsCommand => _viewAllProductsCommand ??= new DelegateCommand(ViewAllProducts);

        private ICommand? _viewLowStockCommand;
        public ICommand ViewLowStockCommand => _viewLowStockCommand ??= new DelegateCommand(ViewLowStock);

        private ICommand? _addSaleCommand;
        public ICommand AddSaleCommand => _addSaleCommand ??= new DelegateCommand(AddSale);

        #endregion

        #region Constructor

        public DashboardViewModel()
        {
            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);
            _customerService = new CustomerService(_dbService);
            _saleService = new SaleService(_dbService, _productService, _customerService);
            _expenseService = new ExpenseService(_dbService);

            _ = LoadDashboardDataAsync();
        }

        #endregion

        #region Methods

        protected override async Task RefreshAsync()
        {
            await LoadDashboardDataAsync();
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                IsLoading = true;
                ClearError();

                // Get date range for current month
                var startDate = new DateTime(SelectedDate.Year, SelectedDate.Month, 1);
                var endDate = startDate.AddMonths(1).AddDays(-1);

                // Load statistics
                await LoadStatisticsAsync(startDate, endDate);

                // Load recent sales
                await LoadRecentSalesAsync();

                // Load low stock products
                await LoadLowStockProductsAsync();

                // Load top selling products
                await LoadTopSellingProductsAsync(startDate, endDate);
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            // Load total sales
            TotalSales = await _saleService.GetTotalSalesAsync(startDate, endDate);

            // Load total expenses
            TotalExpenses = await _expenseService.GetTotalExpensesAsync(startDate, endDate);

            // Calculate net profit
            NetProfit = TotalSales - TotalExpenses;

            // Load low stock count
            var lowStockProducts = await _productService.GetLowStockProductsAsync();
            LowStockCount = lowStockProducts.Count();
        }

        private async Task LoadRecentSalesAsync()
        {
            var recentSales = await _saleService.GetRecentSalesAsync(10);
            RecentSales.Clear();

            foreach (var sale in recentSales)
            {
                RecentSales.Add(new DashboardSale
                {
                    InvoiceNumber = sale.InvoiceNumber,
                    CustomerName = sale.CustomerName ?? "عميل نقدي",
                    Total = sale.Total,
                    Date = sale.Date
                });
            }
        }

        private async Task LoadLowStockProductsAsync()
        {
            var lowStockProducts = await _productService.GetLowStockProductsAsync();
            LowStockProducts.Clear();

            foreach (var product in lowStockProducts.Take(10))
            {
                LowStockProducts.Add(product);
            }
        }

        private async Task LoadTopSellingProductsAsync(DateTime startDate, DateTime endDate)
        {
            var topProducts = await _productService.GetTopSellingProductsAsync(5, startDate, endDate);
            TopProducts.Clear();

            foreach (var product in topProducts)
            {
                TopProducts.Add(new TopProduct
                {
                    Name = product.Name,
                    TotalSold = product.TotalSold ?? 0
                });
            }
        }

        private DateTime TryParseDate(string dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return DateTime.Now;

            if (DateTime.TryParse(dateString, out DateTime result))
                return result;

            return DateTime.Now;
        }

        #endregion

        #region Command Handlers

        private void ViewAllSales()
        {
            // Navigate to sales page
            NavigationRequested?.Invoke("Sales");
        }

        private void ViewAllProducts()
        {
            // Navigate to products page
            NavigationRequested?.Invoke("Products");
        }

        private void ViewLowStock()
        {
            // Navigate to products page with low stock filter
            NavigationRequested?.Invoke("Products");
        }

        private void AddSale()
        {
            // Navigate to sales page to add new sale
            NavigationRequested?.Invoke("Sales");
        }

        #endregion

        #region Events

        public event Action<string>? NavigationRequested;

        #endregion
    }

    #region Helper Classes

    public class DashboardSale
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public decimal Total { get; set; }
        public DateTime Date { get; set; }
    }

    public class TopProduct
    {
        public string Name { get; set; } = string.Empty;
        public int TotalSold { get; set; }
    }

    #endregion
}
