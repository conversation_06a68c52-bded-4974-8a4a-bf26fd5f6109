<Window x:Class="SalesManagementSystem.Views.CurrencySettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات العملة والأرقام"
        Height="400" Width="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="DarkBlue">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="🇩🇿" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="إعدادات العملة والأرقام" FontSize="18" 
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel>
                <GroupBox Header="إعدادات العملة" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="العملة الحالية:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="الدينار الجزائري (دج)" FontSize="16" Foreground="Green" FontWeight="Bold" Margin="0,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="رمز العملة:" FontWeight="Bold" Margin="0,5"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="دج" Margin="0,5" IsReadOnly="True" Background="LightGray"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="اسم العملة بالكامل:" FontWeight="Bold" Margin="0,5"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Text="دينار جزائري" Margin="0,5" IsReadOnly="True" Background="LightGray"/>
                    </Grid>
                </GroupBox>

                <GroupBox Header="إعدادات الأرقام" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="نوع الأرقام:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="الأرقام الإنجليزية (0123456789)" FontSize="16" Foreground="Blue" FontWeight="Bold" Margin="0,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="مثال على الأرقام:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="12345.67 دج" FontSize="16" Margin="0,5"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="تنسيق التاريخ:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="2024/01/15" FontSize="16" Margin="0,5"/>
                    </Grid>
                </GroupBox>

                <GroupBox Header="أمثلة على الاستخدام">
                    <StackPanel Margin="10">
                        <Border Background="LightBlue" Padding="10" Margin="0,5">
                            <StackPanel>
                                <TextBlock Text="فاتورة مبيعات:" FontWeight="Bold"/>
                                <TextBlock Text="المبلغ: 2500.50 دج"/>
                                <TextBlock Text="الخصم: 10%"/>
                                <TextBlock Text="المبلغ النهائي: 2250.45 دج"/>
                            </StackPanel>
                        </Border>

                        <Border Background="LightGreen" Padding="10" Margin="0,5">
                            <StackPanel>
                                <TextBlock Text="بيانات المنتج:" FontWeight="Bold"/>
                                <TextBlock Text="سعر الشراء: 150.00 دج"/>
                                <TextBlock Text="سعر البيع: 200.00 دج"/>
                                <TextBlock Text="الربح: 50.00 دج"/>
                            </StackPanel>
                        </Border>

                        <Border Background="LightYellow" Padding="10" Margin="0,5">
                            <StackPanel>
                                <TextBlock Text="إحصائيات المبيعات:" FontWeight="Bold"/>
                                <TextBlock Text="إجمالي المبيعات اليوم: 15750.25 دج"/>
                                <TextBlock Text="عدد الفواتير: 42"/>
                                <TextBlock Text="متوسط الفاتورة: 375.00 دج"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="✅ تم تطبيق إعدادات الدينار الجزائري والأرقام الإنجليزية بنجاح!" 
                          FontSize="12" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
                <Button Content="❌ إغلاق" Width="100" Height="35" 
                       Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
