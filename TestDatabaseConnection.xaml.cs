using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.Views.Dialogs;

namespace SalesManagementSystem
{
    public partial class TestDatabaseConnection : Window
    {
        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private readonly SampleDataService _sampleDataService;
        private readonly DatabaseRepairService _repairService;

        public TestDatabaseConnection()
        {
            InitializeComponent();

            // Initialize services
            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);
            _sampleDataService = new SampleDataService(_dbService);
            _repairService = new DatabaseRepairService(_dbService);

            AddResult("🚀 تم تهيئة خدمات قاعدة البيانات");
            AddResult("📊 جاهز لاختبار النظام...");
        }

        private async void TestDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 اختبار الاتصال بقاعدة البيانات...");

                // Test database connection
                var tables = await _dbService.QueryAsync<string>("SELECT name FROM sqlite_master WHERE type='table'");
                var tableList = tables.ToList();

                AddResult($"✅ تم الاتصال بقاعدة البيانات بنجاح");
                AddResult($"   عدد الجداول: {tableList.Count}");

                foreach (var table in tableList)
                {
                    AddResult($"   - {table}");
                }

                // Test Products table specifically
                if (tableList.Contains("Products"))
                {
                    var productCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Products");
                    AddResult($"✅ جدول المنتجات موجود - عدد المنتجات: {productCount}");
                }
                else
                {
                    AddResult("❌ جدول المنتجات غير موجود!");
                }

                // Test Categories table
                if (tableList.Contains("Categories"))
                {
                    var categoryCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Categories");
                    AddResult($"✅ جدول الفئات موجود - عدد الفئات: {categoryCount}");
                }
                else
                {
                    AddResult("❌ جدول الفئات غير موجود!");
                }

                // Test Warehouses table
                if (tableList.Contains("Warehouses"))
                {
                    var warehouseCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Warehouses");
                    AddResult($"✅ جدول المخازن موجود - عدد المخازن: {warehouseCount}");
                }
                else
                {
                    AddResult("❌ جدول المخازن غير موجود!");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في اختبار قاعدة البيانات: {ex.Message}");
            }
        }

        private async void RepairDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔧 بدء إصلاح قاعدة البيانات...");

                var success = await _repairService.CheckAndRepairDatabaseAsync();

                if (success)
                {
                    AddResult("✅ تم إصلاح قاعدة البيانات بنجاح!");
                    AddResult("   يمكنك الآن اختبار قاعدة البيانات مرة أخرى");
                }
                else
                {
                    AddResult("❌ فشل في إصلاح قاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في إصلاح قاعدة البيانات: {ex.Message}");
            }
        }

        private async void ShowTableStructureButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 عرض هيكل الجداول...");

                // Show Products table structure
                AddResult("📋 هيكل جدول Products:");
                var productsColumns = await _dbService.QueryAsync<dynamic>("PRAGMA table_info(Products)");
                foreach (var column in productsColumns)
                {
                    AddResult($"   - {column.name} ({column.type}) {(column.notnull == 1 ? "NOT NULL" : "")} {(!string.IsNullOrEmpty(column.dflt_value?.ToString()) ? $"DEFAULT {column.dflt_value}" : "")}");
                }

                // Show Categories table structure
                AddResult("📋 هيكل جدول Categories:");
                var categoriesColumns = await _dbService.QueryAsync<dynamic>("PRAGMA table_info(Categories)");
                foreach (var column in categoriesColumns)
                {
                    AddResult($"   - {column.name} ({column.type}) {(column.notnull == 1 ? "NOT NULL" : "")} {(!string.IsNullOrEmpty(column.dflt_value?.ToString()) ? $"DEFAULT {column.dflt_value}" : "")}");
                }

                // Show Warehouses table structure if exists
                var warehousesTables = await _dbService.QueryAsync<string>("SELECT name FROM sqlite_master WHERE type='table' AND name='Warehouses'");
                if (warehousesTables.Any())
                {
                    AddResult("📋 هيكل جدول Warehouses:");
                    var warehousesColumns = await _dbService.QueryAsync<dynamic>("PRAGMA table_info(Warehouses)");
                    foreach (var column in warehousesColumns)
                    {
                        AddResult($"   - {column.name} ({column.type}) {(column.notnull == 1 ? "NOT NULL" : "")} {(!string.IsNullOrEmpty(column.dflt_value?.ToString()) ? $"DEFAULT {column.dflt_value}" : "")}");
                    }
                }
                else
                {
                    AddResult("❌ جدول Warehouses غير موجود");
                }

                // Show Customers table structure if exists
                var customersTables = await _dbService.QueryAsync<string>("SELECT name FROM sqlite_master WHERE type='table' AND name='Customers'");
                if (customersTables.Any())
                {
                    AddResult("📋 هيكل جدول Customers:");
                    var customersColumns = await _dbService.QueryAsync<dynamic>("PRAGMA table_info(Customers)");
                    foreach (var column in customersColumns)
                    {
                        AddResult($"   - {column.name} ({column.type}) {(column.notnull == 1 ? "NOT NULL" : "")} {(!string.IsNullOrEmpty(column.dflt_value?.ToString()) ? $"DEFAULT {column.dflt_value}" : "")}");
                    }
                }
                else
                {
                    AddResult("❌ جدول Customers غير موجود");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في عرض هيكل الجداول: {ex.Message}");
            }
        }

        private async void CreateWarehousesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔧 إنشاء جدول المخازن...");

                const string createWarehousesSql = @"
                    CREATE TABLE IF NOT EXISTS Warehouses (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Code TEXT NOT NULL UNIQUE,
                        Name TEXT NOT NULL,
                        Description TEXT,
                        Type TEXT NOT NULL DEFAULT 'Main',
                        Status TEXT NOT NULL DEFAULT 'Active',
                        Address TEXT,
                        City TEXT,
                        Phone TEXT,
                        Email TEXT,
                        ManagerName TEXT,
                        IsDefault INTEGER DEFAULT 0,
                        CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        UpdatedAt TEXT
                    );
                    CREATE INDEX IF NOT EXISTS idx_warehouses_code ON Warehouses(Code);
                    CREATE INDEX IF NOT EXISTS idx_warehouses_type ON Warehouses(Type);";

                await _dbService.ExecuteAsync(createWarehousesSql);
                AddResult("✅ تم إنشاء جدول Warehouses بنجاح!");

                // Create default warehouses
                const string insertDefaultWarehouses = @"
                    INSERT OR IGNORE INTO Warehouses (Code, Name, Description, Type, IsDefault, CreatedAt)
                    VALUES
                    ('MAIN', 'المخزن الرئيسي', 'المخزن الرئيسي للشركة', 'Main', 1, datetime('now')),
                    ('ELEC', 'مخزن الإلكترونيات', 'مخزن متخصص للإلكترونيات', 'Branch', 0, datetime('now'));";

                await _dbService.ExecuteAsync(insertDefaultWarehouses);
                AddResult("✅ تم إنشاء المخازن الافتراضية!");
                AddResult("   - المخزن الرئيسي (MAIN)");
                AddResult("   - مخزن الإلكترونيات (ELEC)");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في إنشاء جدول المخازن: {ex.Message}");
            }
        }

        private async void CreateCustomersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔧 إنشاء جدول العملاء...");

                const string createCustomersSql = @"
                    CREATE TABLE IF NOT EXISTS Customers (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Code TEXT NOT NULL UNIQUE,
                        Name TEXT NOT NULL,
                        Phone TEXT NOT NULL,
                        Email TEXT,
                        Address TEXT,
                        Balance REAL NOT NULL DEFAULT 0,
                        CreditLimit REAL NOT NULL DEFAULT 0,
                        PaymentTerms TEXT NOT NULL DEFAULT 'نقدي',
                        CustomerType TEXT NOT NULL DEFAULT 'فرد',
                        TaxNumber TEXT,
                        Notes TEXT,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        UpdatedAt TEXT
                    );
                    CREATE INDEX IF NOT EXISTS idx_customers_code ON Customers(Code);
                    CREATE INDEX IF NOT EXISTS idx_customers_name ON Customers(Name);
                    CREATE INDEX IF NOT EXISTS idx_customers_phone ON Customers(Phone);";

                await _dbService.ExecuteAsync(createCustomersSql);
                AddResult("✅ تم إنشاء جدول Customers بنجاح!");

                // Create default customers
                const string insertDefaultCustomers = @"
                    INSERT OR IGNORE INTO Customers (Code, Name, Phone, Email, Address, Balance, CreditLimit, PaymentTerms, CustomerType, IsActive, CreatedAt)
                    VALUES
                    ('CUS-001', 'أحمد محمد علي', '0501234567', '<EMAIL>', 'الرياض، المملكة العربية السعودية', 0, 5000, 'آجل 30 يوم', 'فرد', 1, datetime('now')),
                    ('CUS-002', 'شركة النور للتجارة', '0509876543', '<EMAIL>', 'جدة، المملكة العربية السعودية', 1500, 10000, 'آجل 45 يوم', 'شركة', 1, datetime('now')),
                    ('CUS-003', 'فاطمة عبدالله', '0555555555', '<EMAIL>', 'الدمام، المملكة العربية السعودية', 0, 3000, 'نقدي', 'فرد', 1, datetime('now')),
                    ('CUS-004', 'مؤسسة الخليج', '0544444444', '<EMAIL>', 'الخبر، المملكة العربية السعودية', 2500, 15000, 'آجل 60 يوم', 'مؤسسة', 1, datetime('now'));";

                await _dbService.ExecuteAsync(insertDefaultCustomers);
                AddResult("✅ تم إنشاء العملاء الافتراضيين!");
                AddResult("   - أحمد محمد علي (CUS-001)");
                AddResult("   - شركة النور للتجارة (CUS-002)");
                AddResult("   - فاطمة عبدالله (CUS-003)");
                AddResult("   - مؤسسة الخليج (CUS-004)");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في إنشاء جدول العملاء: {ex.Message}");
            }
        }

        private async void CreateSampleDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 إنشاء البيانات الافتراضية...");

                var success = await _sampleDataService.CreateSampleProductsAsync();

                if (success)
                {
                    AddResult("✅ تم إنشاء البيانات الافتراضية بنجاح!");

                    // Check what was created
                    var productCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Products");
                    var categoryCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Categories");
                    var warehouseCount = await _dbService.QuerySingleAsync<int>("SELECT COUNT(*) FROM Warehouses");

                    AddResult($"   المنتجات: {productCount}");
                    AddResult($"   الفئات: {categoryCount}");
                    AddResult($"   المخازن: {warehouseCount}");
                }
                else
                {
                    AddResult("❌ فشل في إنشاء البيانات الافتراضية");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في إنشاء البيانات الافتراضية: {ex.Message}");
            }
        }

        private async void LoadProductsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 تحميل المنتجات من قاعدة البيانات...");

                var products = await _productService.GetAllProductsAsync();
                var productList = products.ToList();

                AddResult($"✅ تم تحميل {productList.Count} منتج");

                if (productList.Any())
                {
                    AddResult("   قائمة المنتجات:");
                    foreach (var product in productList.Take(10)) // Show first 10
                    {
                        AddResult($"   - {product.Name} (كود: {product.Code}, باركود: {product.Barcode})");
                    }

                    if (productList.Count > 10)
                    {
                        AddResult($"   ... و {productList.Count - 10} منتج آخر");
                    }
                }
                else
                {
                    AddResult("   لا توجد منتجات في قاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تحميل المنتجات: {ex.Message}");
            }
        }

        private async void TestAddProductButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 اختبار إضافة منتج جديد...");

                // First ensure we have a category
                var categories = await _productService.GetAllCategoriesAsync();
                var categoryList = categories.ToList();

                int? categoryId = null;
                if (categoryList.Any())
                {
                    categoryId = categoryList.First().Id;
                    AddResult($"   استخدام الفئة: {categoryList.First().Name} (ID: {categoryId})");
                }
                else
                {
                    AddResult("   لا توجد فئات - سيتم إنشاء المنتج بدون فئة");
                }

                var testProduct = new Product
                {
                    Code = "TEST001",
                    Name = "منتج تجريبي",
                    Description = "منتج لاختبار النظام",
                    CategoryId = categoryId,
                    PurchasePrice = 10.00m,
                    SalePrice = 15.00m,
                    SalePrice2 = 14.00m,
                    Quantity = 100,
                    MinQuantity = 10,
                    Unit = "قطعة",
                    Barcode = "1111111111111",
                    IsActive = true,
                    TrackStock = true,
                    CreatedAt = DateTime.Now
                };

                var result = await _productService.AddProductAsync(testProduct);

                if (result != null)
                {
                    AddResult($"✅ تم إضافة المنتج التجريبي بنجاح - ID: {result.Id}");
                    AddResult($"   الاسم: {result.Name}");
                    AddResult($"   الكود: {result.Code}");
                    AddResult($"   الباركود: {result.Barcode}");
                    AddResult($"   سعر البيع: {result.SalePrice:C}");
                }
                else
                {
                    AddResult("❌ فشل في إضافة المنتج التجريبي");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في إضافة المنتج التجريبي: {ex.Message}");
                AddResult($"   تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        private void OpenProductDialogButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 فتح نافذة إضافة منتج...");

                var dialog = new ProductDialog();
                var result = dialog.ShowDialog();

                if (result == true && dialog.Result != null)
                {
                    AddResult($"✅ تم حفظ المنتج من النافذة بنجاح!");
                    AddResult($"   الاسم: {dialog.Result.Name}");
                    AddResult($"   الكود: {dialog.Result.Code}");
                    AddResult($"   الباركود: {dialog.Result.Barcode}");
                    AddResult($"   ID: {dialog.Result.Id}");
                }
                else
                {
                    AddResult("❌ تم إلغاء إضافة المنتج أو فشل الحفظ");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في فتح نافذة إضافة المنتج: {ex.Message}");
            }
        }

        private async void TestCustomersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔧 اختبار العملاء...");

                var customerService = new CustomerService(_dbService);

                // Test loading customers
                var customers = await customerService.GetAllCustomersAsync();
                AddResult($"✅ تم تحميل {customers.Count()} عميل");

                foreach (var customer in customers.Take(3))
                {
                    AddResult($"   - {customer.Name} (ID: {customer.Id}) - {customer.Phone}");
                }

                // Test adding a customer with basic data
                var newCustomer = new Customer
                {
                    Name = "عميل تجريبي",
                    Phone = "0500000000",
                    Email = "<EMAIL>",
                    Address = "عنوان تجريبي"
                };

                var savedCustomer = await customerService.AddCustomerAsync(newCustomer);
                if (savedCustomer != null)
                {
                    AddResult($"✅ تم إضافة العميل التجريبي: {savedCustomer.Name} (ID: {savedCustomer.Id})");
                }
                else
                {
                    AddResult("❌ فشل في إضافة العميل التجريبي");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في اختبار العملاء: {ex.Message}");
                AddResult($"   تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        private void OpenCustomersWindowButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔧 فتح نافذة العملاء...");

                var customersWindow = new Window
                {
                    Title = "إدارة العملاء",
                    Width = 1200,
                    Height = 800,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    Content = new Views.CustomersView()
                };
                customersWindow.ShowDialog();

                AddResult("✅ تم إغلاق نافذة العملاء");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في فتح نافذة العملاء: {ex.Message}");
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            ResultsTextBox.Text = "";
            AddResult("🧹 تم مسح النتائج");
            AddResult("📊 جاهز لاختبار النظام...");
        }

        private void AddResult(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var newText = $"[{timestamp}] {message}\n";

            ResultsTextBox.Text += newText;
            ResultsTextBox.ScrollToEnd();
        }
    }
}
