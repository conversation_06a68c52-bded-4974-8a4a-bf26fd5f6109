<Window x:Class="SalesManagementSystem.Views.Dialogs.PurchaseDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding WindowTitle}" 
        Height="750" 
        Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
        
        <Style x:Key="FormTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="FormComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="{Binding HeaderIcon}" 
                                   Width="32" Height="32" 
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="{Binding WindowTitle}" 
                      Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                      VerticalAlignment="Center" 
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Purchase Header Information -->
        <GroupBox Grid.Row="1" Header="معلومات فاتورة الشراء" 
                 Style="{DynamicResource MaterialDesignCardGroupBox}"
                 Margin="0,0,0,16">
            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Invoice Number -->
                <TextBox Grid.Row="0" Grid.Column="0"
                        Style="{StaticResource FormTextBox}"
                        materialDesign:HintAssist.Hint="رقم الفاتورة"
                        Text="{Binding Purchase.InvoiceNumber, UpdateSourceTrigger=PropertyChanged}"
                        IsReadOnly="True"/>

                <!-- Supplier -->
                <ComboBox Grid.Row="0" Grid.Column="2"
                         Style="{StaticResource FormComboBox}"
                         materialDesign:HintAssist.Hint="المورد *"
                         ItemsSource="{Binding Suppliers}"
                         SelectedItem="{Binding SelectedSupplier}"
                         DisplayMemberPath="Name"/>

                <!-- Date -->
                <DatePicker Grid.Row="0" Grid.Column="4"
                           Style="{DynamicResource MaterialDesignOutlinedDatePicker}"
                           materialDesign:HintAssist.Hint="التاريخ"
                           SelectedDate="{Binding Purchase.Date}"
                           Margin="0,8"
                           Height="56"/>

                <!-- Payment Method -->
                <ComboBox Grid.Row="1" Grid.Column="0"
                         Style="{StaticResource FormComboBox}"
                         materialDesign:HintAssist.Hint="طريقة الدفع"
                         ItemsSource="{Binding PaymentMethods}"
                         SelectedItem="{Binding Purchase.PaymentMethod}"/>

                <!-- Payment Status -->
                <ComboBox Grid.Row="1" Grid.Column="2"
                         Style="{StaticResource FormComboBox}"
                         materialDesign:HintAssist.Hint="حالة الدفع"
                         ItemsSource="{Binding PaymentStatuses}"
                         SelectedItem="{Binding Purchase.PaymentStatus}"/>

                <!-- Notes -->
                <TextBox Grid.Row="1" Grid.Column="4"
                        Style="{StaticResource FormTextBox}"
                        materialDesign:HintAssist.Hint="ملاحظات"
                        Text="{Binding Purchase.Notes, UpdateSourceTrigger=PropertyChanged}"/>
            </Grid>
        </GroupBox>

        <!-- Purchase Items -->
        <GroupBox Grid.Row="2" Header="أصناف فاتورة الشراء" 
                 Style="{DynamicResource MaterialDesignCardGroupBox}">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Add Item Section -->
                <GroupBox Grid.Row="0" Header="إضافة صنف" 
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <Grid Margin="16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="8"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="8"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="8"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="8"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Product -->
                        <ComboBox Grid.Column="0"
                                 Style="{StaticResource FormComboBox}"
                                 materialDesign:HintAssist.Hint="المنتج"
                                 ItemsSource="{Binding Products}"
                                 SelectedItem="{Binding SelectedProduct}"
                                 DisplayMemberPath="Name"/>

                        <!-- Quantity -->
                        <TextBox Grid.Column="2"
                                Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="الكمية"
                                Text="{Binding ItemQuantity, UpdateSourceTrigger=PropertyChanged}"/>

                        <!-- Unit Price -->
                        <TextBox Grid.Column="4"
                                Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="سعر الوحدة"
                                Text="{Binding ItemUnitPrice, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"/>

                        <!-- Total -->
                        <TextBox Grid.Column="6"
                                Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="المجموع"
                                Text="{Binding ItemTotal, StringFormat=F2}"
                                IsReadOnly="True"/>

                        <!-- Add Button -->
                        <Button Grid.Column="8"
                               Command="{Binding AddItemCommand}"
                               Style="{DynamicResource MaterialDesignRaisedButton}"
                               Width="100"
                               Height="40"
                               VerticalAlignment="Center">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" 
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="إضافة" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </GroupBox>

                <!-- Items List -->
                <DataGrid Grid.Row="1"
                         ItemsSource="{Binding PurchaseItems}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         Style="{DynamicResource MaterialDesignDataGrid}"
                         HeadersVisibility="All">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="المنتج" 
                                          Binding="{Binding ProductName}" 
                                          Width="2*"
                                          IsReadOnly="True"/>
                        
                        <DataGridTextColumn Header="الكمية" 
                                          Binding="{Binding Quantity}" 
                                          Width="*"/>
                        
                        <DataGridTextColumn Header="سعر الوحدة" 
                                          Binding="{Binding UnitPrice, StringFormat=F2}" 
                                          Width="*"/>
                        
                        <DataGridTextColumn Header="المجموع" 
                                          Binding="{Binding Total, StringFormat=F2}" 
                                          Width="*"
                                          IsReadOnly="True"/>
                        
                        <DataGridTemplateColumn Header="إجراءات" Width="Auto">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Command="{Binding DataContext.RemoveItemCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                           CommandParameter="{Binding}"
                                           Style="{DynamicResource MaterialDesignIconButton}"
                                           ToolTip="حذف">
                                        <materialDesign:PackIcon Kind="Delete" Foreground="Red"/>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </GroupBox>

        <!-- Totals Section -->
        <GroupBox Grid.Row="3" Header="المجاميع" 
                 Style="{DynamicResource MaterialDesignCardGroupBox}"
                 Margin="0,16,0,16">
            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Subtotal -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="المجموع الفرعي" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Purchase.Subtotal, StringFormat=F2}" 
                              Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                              HorizontalAlignment="Center"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </StackPanel>

                <!-- Tax -->
                <StackPanel Grid.Column="1">
                    <TextBlock Text="الضريبة" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Purchase.Tax, StringFormat=F2}" 
                              Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                              HorizontalAlignment="Center"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </StackPanel>

                <!-- Discount -->
                <StackPanel Grid.Column="2">
                    <TextBlock Text="الخصم" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              HorizontalAlignment="Center"/>
                    <TextBox Text="{Binding Purchase.Discount, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"
                            Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                            Width="100"
                            HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Total -->
                <StackPanel Grid.Column="3">
                    <TextBlock Text="المجموع الكلي" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Purchase.Total, StringFormat=F2}" 
                              Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                              HorizontalAlignment="Center"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                              FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </GroupBox>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="4" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Left">
            
            <Button Command="{Binding SaveCommand}"
                   Style="{DynamicResource MaterialDesignRaisedButton}"
                   Width="120"
                   Margin="0,0,12,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ContentSave" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="حفظ الفاتورة" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <Button Command="{Binding CancelCommand}"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Width="100"
                   Margin="0,0,12,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Cancel" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="إلغاء" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </StackPanel>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="5" 
              Background="#80000000" 
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}">
            <StackPanel HorizontalAlignment="Center" 
                       VerticalAlignment="Center">
                <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="48" Height="48"/>
                <TextBlock Text="{Binding LoadingMessage}" 
                          Foreground="White"
                          HorizontalAlignment="Center"
                          Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
