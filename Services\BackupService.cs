using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using System.Windows;

namespace SalesManagementSystem.Services
{
    public class BackupService
    {
        private readonly DatabaseService _dbService;
        private readonly string _databasePath;
        private readonly string _backupDirectory;

        public BackupService(DatabaseService dbService, string databasePath)
        {
            _dbService = dbService;
            _databasePath = databasePath;
            _backupDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "SalesManagementSystem",
                "Backups");

            // Ensure backup directory exists
            if (!Directory.Exists(_backupDirectory))
            {
                Directory.CreateDirectory(_backupDirectory);
            }
        }

        public async Task<string> CreateBackupAsync(string description = "")
        {
            try
            {
                // Close database connection to ensure all data is written
                await _dbService.CloseConnectionAsync();

                // Generate backup filename with timestamp
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string backupFilename = $"Backup_{timestamp}.zip";
                string backupPath = Path.Combine(_backupDirectory, backupFilename);

                // Create a temporary directory for backup files
                string tempDir = Path.Combine(Path.GetTempPath(), $"SalesManagementSystem_Backup_{timestamp}");
                Directory.CreateDirectory(tempDir);

                try
                {
                    // Copy database file to temp directory
                    string tempDbFile = Path.Combine(tempDir, Path.GetFileName(_databasePath));
                    File.Copy(_databasePath, tempDbFile);

                    // Create backup info file
                    string infoFile = Path.Combine(tempDir, "backup_info.txt");
                    using (StreamWriter writer = new StreamWriter(infoFile))
                    {
                        await writer.WriteLineAsync($"Backup Date: {DateTime.Now}");
                        await writer.WriteLineAsync($"Description: {description}");
                        await writer.WriteLineAsync($"Database Version: {await _dbService.GetDatabaseVersionAsync()}");
                        await writer.WriteLineAsync($"Application Version: {GetApplicationVersion()}");
                    }

                    // Create zip archive
                    ZipFile.CreateFromDirectory(tempDir, backupPath);

                    // Log backup creation
                    await LogBackupOperationAsync("Create", backupFilename, description);

                    return backupPath;
                }
                finally
                {
                    // Clean up temp directory
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }

                    // Reopen database connection
                    await _dbService.OpenConnectionAsync();
                }
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error creating backup: {ex.Message}");
                throw new BackupException("Failed to create backup", ex);
            }
        }

        public async Task<bool> RestoreBackupAsync(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    throw new FileNotFoundException("Backup file not found", backupPath);
                }

                // Close database connection
                await _dbService.CloseConnectionAsync();

                // Create a temporary directory for extracted files
                string tempDir = Path.Combine(Path.GetTempPath(), $"SalesManagementSystem_Restore_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}");
                Directory.CreateDirectory(tempDir);

                try
                {
                    // Extract backup archive
                    ZipFile.ExtractToDirectory(backupPath, tempDir);

                    // Verify backup contains database file
                    string extractedDbFile = Path.Combine(tempDir, Path.GetFileName(_databasePath));
                    if (!File.Exists(extractedDbFile))
                    {
                        throw new BackupException("Invalid backup file: Database file not found in backup");
                    }

                    // Create backup of current database before restoring
                    string currentBackup = Path.Combine(
                        Path.GetDirectoryName(_databasePath) ?? Environment.CurrentDirectory,
                        $"pre_restore_backup_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.db");
                    File.Copy(_databasePath, currentBackup, true);

                    // Copy restored database file to original location
                    File.Copy(extractedDbFile, _databasePath, true);

                    // Log restore operation
                    await LogBackupOperationAsync("Restore", Path.GetFileName(backupPath));

                    return true;
                }
                finally
                {
                    // Clean up temp directory
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }

                    // Reopen database connection
                    await _dbService.OpenConnectionAsync();
                }
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error restoring backup: {ex.Message}");
                throw new BackupException("Failed to restore backup", ex);
            }
        }

        public async Task<List<BackupInfo>> GetBackupListAsync()
        {
            List<BackupInfo> backups = new List<BackupInfo>();

            try
            {
                if (!Directory.Exists(_backupDirectory))
                {
                    return backups;
                }

                string[] backupFiles = Directory.GetFiles(_backupDirectory, "Backup_*.zip");
                foreach (string backupFile in backupFiles)
                {
                    try
                    {
                        BackupInfo info = await GetBackupInfoAsync(backupFile);
                        backups.Add(info);
                    }
                    catch
                    {
                        // Skip invalid backup files
                        continue;
                    }
                }

                // Sort by date (newest first)
                backups.Sort((a, b) => b.CreatedAt.CompareTo(a.CreatedAt));

                return backups;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting backup list: {ex.Message}");
                return backups;
            }
        }

        public async Task<BackupInfo> GetBackupInfoAsync(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    throw new FileNotFoundException("Backup file not found", backupPath);
                }

                BackupInfo info = new BackupInfo
                {
                    FilePath = backupPath,
                    FileName = Path.GetFileName(backupPath),
                    FileSize = new FileInfo(backupPath).Length
                };

                // Try to parse date from filename
                string filename = Path.GetFileNameWithoutExtension(backupPath);
                if (filename.StartsWith("Backup_") && filename.Length > 16)
                {
                    string dateStr = filename.Substring(7, 8); // yyyyMMdd
                    string timeStr = filename.Substring(16, 6); // HHmmss

                    if (DateTime.TryParseExact(
                        $"{dateStr}_{timeStr}",
                        "yyyyMMdd_HHmmss",
                        null,
                        System.Globalization.DateTimeStyles.None,
                        out DateTime date))
                    {
                        info.CreatedAt = date;
                    }
                }

                // Extract backup info file if exists
                using (ZipArchive archive = ZipFile.OpenRead(backupPath))
                {
                    ZipArchiveEntry? infoEntry = archive.GetEntry("backup_info.txt");
                    if (infoEntry != null)
                    {
                        using (StreamReader reader = new StreamReader(infoEntry.Open()))
                        {
                            string? line;
                            while ((line = await reader.ReadLineAsync()) != null)
                            {
                                if (line.StartsWith("Description: "))
                                {
                                    info.Description = line.Substring("Description: ".Length);
                                }
                                else if (line.StartsWith("Database Version: "))
                                {
                                    info.DatabaseVersion = line.Substring("Database Version: ".Length);
                                }
                                else if (line.StartsWith("Application Version: "))
                                {
                                    info.ApplicationVersion = line.Substring("Application Version: ".Length);
                                }
                            }
                        }
                    }
                }

                return info;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting backup info: {ex.Message}");
                throw new BackupException("Failed to get backup information", ex);
            }
        }

        public async Task<bool> DeleteBackupAsync(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    return false;
                }

                // Log deletion
                await LogBackupOperationAsync("Delete", Path.GetFileName(backupPath));

                // Delete file
                File.Delete(backupPath);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting backup: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ScheduleAutomaticBackupsAsync(int intervalDays, string description = "Automatic backup")
        {
            try
            {
                // Save backup schedule settings
                await SaveBackupScheduleSettingsAsync(intervalDays, description);

                // Create initial backup if none exists
                var backups = await GetBackupListAsync();
                if (backups.Count == 0)
                {
                    await CreateBackupAsync(description);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error scheduling automatic backups: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DisableAutomaticBackupsAsync()
        {
            try
            {
                // Save backup schedule settings with interval 0 (disabled)
                await SaveBackupScheduleSettingsAsync(0, "");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error disabling automatic backups: {ex.Message}");
                return false;
            }
        }

        public async Task<(bool Enabled, int IntervalDays, string Description, DateTime? NextBackupDate)> GetBackupScheduleAsync()
        {
            try
            {
                const string sql = "SELECT Value FROM Settings WHERE Key = @Key";
                string intervalStr = await _dbService.QuerySingleOrDefaultAsync<string>(sql, new { Key = "BackupIntervalDays" }) ?? "0";
                string description = await _dbService.QuerySingleOrDefaultAsync<string>(sql, new { Key = "BackupDescription" }) ?? "";
                string lastBackupStr = await _dbService.QuerySingleOrDefaultAsync<string>(sql, new { Key = "LastBackupDate" }) ?? "";

                int intervalDays = int.TryParse(intervalStr, out int interval) ? interval : 0;
                bool enabled = intervalDays > 0;

                DateTime? nextBackupDate = null;
                if (enabled && DateTime.TryParse(lastBackupStr, out DateTime lastBackup))
                {
                    nextBackupDate = lastBackup.AddDays(intervalDays);
                }

                return (enabled, intervalDays, description, nextBackupDate);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting backup schedule: {ex.Message}");
                return (false, 0, "", null);
            }
        }

        public async Task CheckAndPerformScheduledBackupAsync()
        {
            try
            {
                var schedule = await GetBackupScheduleAsync();
                if (!schedule.Enabled || !schedule.NextBackupDate.HasValue)
                {
                    return;
                }

                // Check if it's time for a backup
                if (DateTime.Now >= schedule.NextBackupDate.Value)
                {
                    // Perform backup
                    await CreateBackupAsync(schedule.Description);

                    // Update last backup date
                    const string sql = "UPDATE Settings SET Value = @Value WHERE Key = @Key";
                    await _dbService.ExecuteAsync(sql, new { Key = "LastBackupDate", Value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });

                    // Clean up old backups if needed
                    await CleanupOldBackupsAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error performing scheduled backup: {ex.Message}");
            }
        }

        private async Task SaveBackupScheduleSettingsAsync(int intervalDays, string description)
        {
            const string updateSql = "UPDATE Settings SET Value = @Value WHERE Key = @Key";
            const string insertSql = "INSERT INTO Settings (Key, Value) VALUES (@Key, @Value)";

            // Save interval days
            string intervalKey = "BackupIntervalDays";
            int count = await _dbService.QuerySingleOrDefaultAsync<int>("SELECT COUNT(*) FROM Settings WHERE Key = @Key", new { Key = intervalKey });
            if (count > 0)
            {
                await _dbService.ExecuteAsync(updateSql, new { Key = intervalKey, Value = intervalDays.ToString() });
            }
            else
            {
                await _dbService.ExecuteAsync(insertSql, new { Key = intervalKey, Value = intervalDays.ToString() });
            }

            // Save description
            string descKey = "BackupDescription";
            count = await _dbService.QuerySingleOrDefaultAsync<int>("SELECT COUNT(*) FROM Settings WHERE Key = @Key", new { Key = descKey });
            if (count > 0)
            {
                await _dbService.ExecuteAsync(updateSql, new { Key = descKey, Value = description });
            }
            else
            {
                await _dbService.ExecuteAsync(insertSql, new { Key = descKey, Value = description });
            }

            // Save last backup date if not exists
            string dateKey = "LastBackupDate";
            count = await _dbService.QuerySingleOrDefaultAsync<int>("SELECT COUNT(*) FROM Settings WHERE Key = @Key", new { Key = dateKey });
            if (count == 0)
            {
                await _dbService.ExecuteAsync(insertSql, new { Key = dateKey, Value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
            }
        }

        private async Task CleanupOldBackupsAsync(int maxBackups = 10)
        {
            try
            {
                var backups = await GetBackupListAsync();
                if (backups.Count <= maxBackups)
                {
                    return;
                }

                // Delete oldest backups
                for (int i = maxBackups; i < backups.Count; i++)
                {
                    await DeleteBackupAsync(backups[i].FilePath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error cleaning up old backups: {ex.Message}");
            }
        }

        private async Task LogBackupOperationAsync(string operation, string filename, string description = "")
        {
            try
            {
                const string sql = @"INSERT INTO BackupLogs (Operation, Filename, Description, PerformedAt)
                                   VALUES (@Operation, @Filename, @Description, @PerformedAt)";

                await _dbService.ExecuteAsync(sql, new
                {
                    Operation = operation,
                    Filename = filename,
                    Description = description,
                    PerformedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error logging backup operation: {ex.Message}");
            }
        }

        private string GetApplicationVersion()
        {
            try
            {
                return System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "*******";
            }
            catch
            {
                return "*******";
            }
        }
    }

    public class BackupInfo
    {
        public string FilePath { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public string Description { get; set; } = "";
        public string DatabaseVersion { get; set; } = "";
        public string ApplicationVersion { get; set; } = "";

        public string FormattedSize
        {
            get
            {
                if (FileSize < 1024)
                    return $"{FileSize} B";
                else if (FileSize < 1024 * 1024)
                    return $"{FileSize / 1024.0:F2} KB";
                else if (FileSize < 1024 * 1024 * 1024)
                    return $"{FileSize / (1024.0 * 1024.0):F2} MB";
                else
                    return $"{FileSize / (1024.0 * 1024.0 * 1024.0):F2} GB";
            }
        }

        public string FormattedDate => CreatedAt.ToString("yyyy-MM-dd HH:mm:ss");
    }

    public class BackupException : Exception
    {
        public BackupException(string message) : base(message) { }

        public BackupException(string message, Exception innerException) : base(message, innerException) { }
    }
}