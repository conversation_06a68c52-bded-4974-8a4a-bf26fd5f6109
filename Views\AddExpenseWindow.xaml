<Window x:Class="SalesManagementSystem.Views.AddExpenseWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="➕ إضافة مصروف جديد"
        Height="600" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                               Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF673AB7">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="💰" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock x:Name="HeaderTitle" Text="إضافة مصروف جديد" FontSize="16" 
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel>
                <!-- العنوان -->
                <TextBlock Text="عنوان المصروف:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TitleTextBox" Height="30" Margin="0,0,0,15" FontSize="12"/>

                <!-- التصنيف -->
                <TextBlock Text="تصنيف المصروف:" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="CategoryComboBox" Height="30" Margin="0,0,0,15" FontSize="12"
                         SelectionChanged="CategoryComboBox_SelectionChanged"/>

                <!-- المبلغ -->
                <TextBlock Text="المبلغ (دج):" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="AmountTextBox" Height="30" Margin="0,0,0,15" FontSize="12"/>

                <!-- التاريخ -->
                <TextBlock Text="تاريخ المصروف:" FontWeight="Bold" Margin="0,0,0,5"/>
                <DatePicker x:Name="DatePicker" Height="30" Margin="0,0,0,15" FontSize="12"/>

                <!-- اسم الموظف (يظهر عند اختيار راتب موظف) -->
                <TextBlock x:Name="EmployeeLabel" Text="اسم الموظف:" FontWeight="Bold" Margin="0,0,0,5" Visibility="Collapsed"/>
                <TextBox x:Name="EmployeeTextBox" Height="30" Margin="0,0,0,15" FontSize="12" Visibility="Collapsed"/>

                <!-- اسم المنتج (يظهر عند اختيار منتج تالف أو منتهي الصلاحية) -->
                <TextBlock x:Name="ProductLabel" Text="اسم المنتج:" FontWeight="Bold" Margin="0,0,0,5" Visibility="Collapsed"/>
                <TextBox x:Name="ProductTextBox" Height="30" Margin="0,0,0,15" FontSize="12" Visibility="Collapsed"/>

                <!-- الوصف -->
                <TextBlock Text="وصف المصروف:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox" Height="60" Margin="0,0,0,15" FontSize="12" 
                        TextWrapping="Wrap" AcceptsReturn="True"/>

                <!-- ملاحظات -->
                <TextBlock Text="ملاحظات إضافية:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox" Height="60" Margin="0,0,0,15" FontSize="12" 
                        TextWrapping="Wrap" AcceptsReturn="True"/>

                <!-- معاينة المبلغ -->
                <Border Background="LightYellow" Padding="10" Margin="0,10,0,0" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="معاينة المصروف:" FontWeight="Bold" FontSize="14"/>
                        <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                            <TextBlock x:Name="PreviewIcon" Text="💰" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock x:Name="PreviewText" Text="لم يتم تحديد المصروف بعد" FontSize="12"/>
                        </StackPanel>
                        <TextBlock x:Name="PreviewAmount" Text="المبلغ: 0.00 دج" FontSize="12" FontWeight="Bold" 
                                  Foreground="DarkGreen" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ المصروف" Width="120" Height="40" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF4CAF50" Click="Save_Click"/>
                <Button Content="❌ إلغاء" Width="80" Height="40" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FFF44336" Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
