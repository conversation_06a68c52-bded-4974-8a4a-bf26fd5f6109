<Window x:Class="SalesManagementSystem.Views.ProfitsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="📈 إدارة الأرباح والتحليل المالي"
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                               Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Chart Bar Style -->
        <Style x:Key="ChartBarStyle" TargetType="Rectangle">
            <Setter Property="Fill" Value="#FF4CAF50"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="RadiusX" Value="2"/>
            <Setter Property="RadiusY" Value="2"/>
        </Style>
    </Window.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="200"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF2E7D32">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="📈" FontSize="28" Foreground="White" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة الأرباح والتحليل المالي" FontSize="20" 
                                  FontWeight="Bold" Foreground="White"/>
                        <TextBlock x:Name="PeriodLabel" Text="اليوم" FontSize="12" 
                                  Foreground="LightGreen" Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>
                
                <!-- Net Profit Display -->
                <Border Grid.Column="1" Background="Black" CornerRadius="15" Padding="20,10" 
                       BorderBrush="Lime" BorderThickness="3" MinWidth="250">
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock x:Name="ProfitIcon" Text="📈" FontSize="24" Foreground="Lime" Margin="0,0,10,0"/>
                        <StackPanel>
                            <TextBlock Text="صافي الربح" FontSize="12" Foreground="LightGray" 
                                      HorizontalAlignment="Center" Margin="0,0,0,3"/>
                            <TextBlock x:Name="NetProfitLabel" Text="0.00 دج" 
                                      FontSize="28" FontWeight="Bold" Foreground="Lime" 
                                      FontFamily="Consolas, 'Courier New', monospace"
                                      HorizontalAlignment="Center">
                                <TextBlock.Effect>
                                    <DropShadowEffect Color="Lime" BlurRadius="15" ShadowDepth="0" Opacity="0.8"/>
                                </TextBlock.Effect>
                            </TextBlock>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Controls Section -->
        <Border Grid.Row="1" Background="LightGray" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Period Selection -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="📅 الفترة الزمنية:" FontWeight="Bold" FontSize="14" 
                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox x:Name="PeriodComboBox" Width="120" Height="30" Margin="0,0,10,0"
                             SelectionChanged="PeriodComboBox_SelectionChanged"/>
                    <DatePicker x:Name="StartDatePicker" Width="120" Height="30" Margin="0,0,5,0"
                               SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                    <TextBlock Text="إلى" VerticalAlignment="Center" Margin="5,0"/>
                    <DatePicker x:Name="EndDatePicker" Width="120" Height="30" Margin="5,0,10,0"
                               SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                    <Button Content="🔄 تحديث" Width="80" Height="30" 
                           Style="{StaticResource ModernButtonStyle}"
                           Background="#FF2196F3" Click="Refresh_Click"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="📊 تقرير مفصل" Width="120" Height="30" 
                           Style="{StaticResource ModernButtonStyle}"
                           Background="#FFFF9800" Click="DetailedReport_Click"/>
                    <Button Content="📤 تصدير" Width="80" Height="30" 
                           Style="{StaticResource ModernButtonStyle}"
                           Background="#FF9C27B0" Click="Export_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Summary Cards -->
        <Grid Grid.Row="2" Margin="15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Sales Card -->
            <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="10" Padding="15" Margin="5">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <TextBlock Text="💰" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="إجمالي المبيعات" FontSize="14" FontWeight="Bold" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalSalesLabel" Text="0.00 دج" FontSize="20" FontWeight="Bold" 
                              Foreground="#1976D2" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="SalesCountLabel" Text="0 عملية بيع" FontSize="12" 
                              Foreground="Gray" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- Total Expenses Card -->
            <Border Grid.Column="1" Background="#FFEBEE" CornerRadius="10" Padding="15" Margin="5">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <TextBlock Text="💸" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="إجمالي المصاريف" FontSize="14" FontWeight="Bold" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalExpensesLabel" Text="0.00 دج" FontSize="20" FontWeight="Bold" 
                              Foreground="#D32F2F" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="ExpensesCountLabel" Text="0 مصروف" FontSize="12" 
                              Foreground="Gray" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- Profit Margin Card -->
            <Border Grid.Column="2" Background="#E8F5E8" CornerRadius="10" Padding="15" Margin="5">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <TextBlock Text="📊" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="هامش الربح" FontSize="14" FontWeight="Bold" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock x:Name="ProfitMarginLabel" Text="0.0%" FontSize="20" FontWeight="Bold" 
                              Foreground="#388E3C" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="MarginStatusLabel" Text="ممتاز" FontSize="12" 
                              Foreground="Gray" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- Average Sale Card -->
            <Border Grid.Column="3" Background="#FFF3E0" CornerRadius="10" Padding="15" Margin="5">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <TextBlock Text="📈" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="متوسط البيع" FontSize="14" FontWeight="Bold" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock x:Name="AverageSaleLabel" Text="0.00 دج" FontSize="20" FontWeight="Bold" 
                              Foreground="#F57C00" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="AverageExpenseLabel" Text="متوسط المصروف: 0.00 دج" FontSize="10" 
                              Foreground="Gray" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Chart Section -->
        <Border Grid.Row="3" Background="White" BorderBrush="LightGray" BorderThickness="1" Margin="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="30"/>
                </Grid.RowDefinitions>

                <!-- Chart Header -->
                <Border Grid.Row="0" Background="#F5F5F5" Padding="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📊" FontSize="16" Margin="0,0,10,0"/>
                        <TextBlock x:Name="ChartTitle" Text="الأرباح حسب الساعات - اليوم" 
                                  FontSize="14" FontWeight="Bold" VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Chart Area -->
                <Canvas Grid.Row="1" x:Name="ChartCanvas" Background="White" Margin="10"/>

                <!-- Chart Legend -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Rectangle Width="15" Height="15" Fill="#4CAF50" Margin="5,0"/>
                    <TextBlock Text="ربح" FontSize="10" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <Rectangle Width="15" Height="15" Fill="#F44336" Margin="5,0"/>
                    <TextBlock Text="خسارة" FontSize="10" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="4" Background="DarkGray" Height="30">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                <TextBlock Text="📊" FontSize="12" Foreground="White" Margin="0,0,5,0"/>
                <TextBlock x:Name="StatusLabel" Text="جاهز" FontSize="12" Foreground="White" Margin="0,0,20,0"/>
                <TextBlock Text="⏰" FontSize="12" Foreground="White" Margin="0,0,5,0"/>
                <TextBlock x:Name="LastUpdateLabel" Text="آخر تحديث: الآن" FontSize="12" Foreground="LightGray"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
