<UserControl x:Class="SalesManagementSystem.Controls.ToastNotification"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             MaxWidth="400"
             MinWidth="300">

    <UserControl.Resources>
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                           From="400" To="0" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.3"/>
        </Storyboard>
        
        <Storyboard x:Key="SlideOutAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                           From="0" To="400" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.3"/>
        </Storyboard>
    </UserControl.Resources>

    <UserControl.RenderTransform>
        <TranslateTransform/>
    </UserControl.RenderTransform>

    <Border Background="{DynamicResource MaterialDesignPaper}"
            CornerRadius="8"
            Effect="{DynamicResource MaterialDesignShadowDepth3}"
            BorderBrush="{Binding NotificationColorBrush}"
            BorderThickness="0,0,4,0"
            Margin="16,8">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Grid Grid.Row="0" Background="{Binding NotificationColorBrush}" Margin="0,0,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Icon -->
                <materialDesign:PackIcon Grid.Column="0"
                                       Kind="{Binding IconKind}"
                                       Width="20" Height="20"
                                       Foreground="White"
                                       VerticalAlignment="Center"
                                       Margin="12,8,8,8"/>

                <!-- Title -->
                <TextBlock Grid.Column="1"
                          Text="{Binding Title}"
                          Style="{DynamicResource MaterialDesignSubtitle2TextBlock}"
                          Foreground="White"
                          FontWeight="Medium"
                          VerticalAlignment="Center"
                          TextTrimming="CharacterEllipsis"/>

                <!-- Close Button -->
                <Button Grid.Column="2"
                       Click="CloseButton_Click"
                       Style="{DynamicResource MaterialDesignIconButton}"
                       Width="24" Height="24"
                       Foreground="White"
                       Margin="4">
                    <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                </Button>
            </Grid>

            <!-- Content -->
            <StackPanel Grid.Row="1" Margin="16,12">
                <!-- Message -->
                <TextBlock Text="{Binding Message}"
                          Style="{DynamicResource MaterialDesignBody2TextBlock}"
                          TextWrapping="Wrap"
                          MaxHeight="80"
                          Margin="0,0,0,8"/>

                <!-- Time -->
                <TextBlock Text="{Binding TimeAgo}"
                          Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                          Opacity="0.6"
                          HorizontalAlignment="Left"/>
            </StackPanel>

            <!-- Actions -->
            <StackPanel Grid.Row="2" 
                       Orientation="Horizontal" 
                       HorizontalAlignment="Right"
                       Margin="16,0,16,12"
                       Visibility="{Binding HasActions, Converter={StaticResource BooleanToVisibilityConverter}}">
                
                <Button Content="عرض"
                       Style="{DynamicResource MaterialDesignFlatButton}"
                       Click="ViewButton_Click"
                       Margin="0,0,8,0"
                       Visibility="{Binding HasAction, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                
                <Button Content="تأجيل"
                       Style="{DynamicResource MaterialDesignOutlinedButton}"
                       Click="SnoozeButton_Click"
                       Margin="0,0,8,0"/>
                
                <Button Content="إغلاق"
                       Style="{DynamicResource MaterialDesignOutlinedButton}"
                       Click="CloseButton_Click"/>
            </StackPanel>

            <!-- Progress Bar (Auto-dismiss timer) -->
            <ProgressBar Grid.Row="2"
                        x:Name="AutoDismissProgressBar"
                        Height="2"
                        VerticalAlignment="Bottom"
                        Background="Transparent"
                        Foreground="{Binding NotificationColorBrush}"
                        Minimum="0"
                        Maximum="100"
                        Value="0"
                        Visibility="{Binding ShowProgressBar, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>
    </Border>
</UserControl>
