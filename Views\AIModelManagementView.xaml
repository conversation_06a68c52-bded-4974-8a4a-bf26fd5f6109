<UserControl x:Class="SalesManagementSystem.Views.AIModelManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- تحويل حالة النموذج إلى لون -->
        <Style x:Key="ModelStatusCard" TargetType="Border">
            <Setter Property="Background" Value="#FFF3E0"/>
            <Setter Property="BorderBrush" Value="#FF9800"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Training">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Trained">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Deployed">
                    <Setter Property="Background" Value="#E8F5E8"/>
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Failed">
                    <Setter Property="Background" Value="#FFEBEE"/>
                    <Setter Property="BorderBrush" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Deprecated">
                    <Setter Property="Background" Value="#FAFAFA"/>
                    <Setter Property="BorderBrush" Value="#9E9E9E"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل نوع النموذج إلى أيقونة -->
        <Style x:Key="ModelTypeIcon" TargetType="materialDesign:PackIcon">
            <Setter Property="Kind" Value="Brain"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Type}" Value="Prediction">
                    <Setter Property="Kind" Value="Crystal"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Classification">
                    <Setter Property="Kind" Value="SortVariant"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Regression">
                    <Setter Property="Kind" Value="TrendingUp"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Clustering">
                    <Setter Property="Kind" Value="Scatter"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Recommendation">
                    <Setter Property="Kind" Value="ThumbUp"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Anomaly">
                    <Setter Property="Kind" Value="AlertCircle"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="NLP">
                    <Setter Property="Kind" Value="MessageText"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="TimeSeries">
                    <Setter Property="Kind" Value="ChartTimeline"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="DeepLearning">
                    <Setter Property="Kind" Value="Brain"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Reinforcement">
                    <Setter Property="Kind" Value="Robot"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Brain" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="إدارة نماذج الذكاء الاصطناعي" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding CreateModelCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="نموذج جديد"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding ImportModelCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Import" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="استيراد نموذج"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding ModelTemplatesCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileTemplate" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="قوالب النماذج"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات النماذج -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي النماذج -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Brain" Width="28" Height="28" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalModels}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي النماذج" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- النماذج المدربة -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="School" Width="28" Height="28" 
                                           Foreground="Blue"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TrainedModels}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Blue"/>
                    <TextBlock Text="نماذج مدربة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- النماذج المنشورة -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CloudUpload" Width="28" Height="28" 
                                           Foreground="Green"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding DeployedModels}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Green"/>
                    <TextBlock Text="نماذج منشورة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- متوسط الدقة -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Target" Width="28" Height="28" 
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding AverageAccuracy, StringFormat='{}{0:F1}%'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="متوسط الدقة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- النماذج قيد التدريب -->
                <StackPanel Grid.Column="4" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Cog" Width="28" Height="28" 
                                           Foreground="Orange"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TrainingModels}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Orange"/>
                    <TextBlock Text="قيد التدريب" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- إجمالي التنبؤات -->
                <StackPanel Grid.Column="5" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Crystal" Width="28" Height="28" 
                                           Foreground="Purple"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalPredictions, StringFormat='{}{0:N0}'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Purple"/>
                    <TextBlock Text="إجمالي التنبؤات" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط البحث والفلترة -->
        <materialDesign:Card Grid.Row="2" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- مربع البحث -->
                <TextBox Grid.Column="0" 
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="البحث في النماذج..."
                         materialDesign:TextFieldAssist.HasLeadingIcon="True"
                         materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,8,0"/>

                <!-- فلتر النوع -->
                <ComboBox Grid.Column="1" 
                          SelectedItem="{Binding SelectedType}"
                          ItemsSource="{Binding ModelTypes}"
                          materialDesign:HintAssist.Hint="نوع النموذج"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Display}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- فلتر الحالة -->
                <ComboBox Grid.Column="2" 
                          SelectedItem="{Binding SelectedStatus}"
                          ItemsSource="{Binding ModelStatuses}"
                          materialDesign:HintAssist.Hint="حالة النموذج"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Display}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- فلتر الخوارزمية -->
                <ComboBox Grid.Column="3" 
                          SelectedItem="{Binding SelectedAlgorithm}"
                          ItemsSource="{Binding Algorithms}"
                          materialDesign:HintAssist.Hint="الخوارزمية"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- أزرار الفلترة -->
                <StackPanel Grid.Column="4" Orientation="Horizontal" Margin="8,0,0,0">
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            Command="{Binding ApplyFiltersCommand}"
                            ToolTip="تطبيق الفلاتر">
                        <materialDesign:PackIcon Kind="FilterVariant" Width="20" Height="20"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            Command="{Binding ClearFiltersCommand}"
                            ToolTip="مسح الفلاتر">
                        <materialDesign:PackIcon Kind="FilterVariantRemove" Width="20" Height="20"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- قائمة النماذج -->
        <ScrollViewer Grid.Row="3" Margin="16,8,16,16" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding FilteredModels}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="2" Margin="0,0,0,16"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <materialDesign:Card Margin="8" 
                                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
                            <Border BorderThickness="2" CornerRadius="4" Style="{StaticResource ModelStatusCard}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- رأس البطاقة -->
                                    <Grid Grid.Row="0" Margin="16,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- أيقونة نوع النموذج -->
                                        <materialDesign:PackIcon Grid.Column="0" 
                                                               Style="{StaticResource ModelTypeIcon}"
                                                               Width="24" Height="24"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,12,0"/>

                                        <!-- اسم النموذج -->
                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding Name}" 
                                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                     FontWeight="Bold"/>
                                            <TextBlock Text="{Binding ModelCode}" 
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     Opacity="0.7"/>
                                        </StackPanel>

                                        <!-- نوع النموذج -->
                                        <materialDesign:Chip Grid.Column="2"
                                                           Content="{Binding TypeDisplay}"
                                                           VerticalAlignment="Center"
                                                           FontSize="10"
                                                           Margin="8,0"/>

                                        <!-- حالة النموذج -->
                                        <materialDesign:Chip Grid.Column="3"
                                                           Content="{Binding StatusDisplay}"
                                                           VerticalAlignment="Center"
                                                           FontSize="10"
                                                           Background="{Binding StatusColor}"/>
                                    </Grid>

                                    <!-- محتوى البطاقة -->
                                    <Grid Grid.Row="1" Margin="16">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- الوصف -->
                                        <TextBlock Grid.Row="0" 
                                                 Text="{Binding Description}"
                                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                 TextWrapping="Wrap"
                                                 MaxHeight="60"
                                                 Margin="0,0,0,8"/>

                                        <!-- الخوارزمية والإطار -->
                                        <Grid Grid.Row="1" Margin="0,0,0,8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                <TextBlock Text="الخوارزمية" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding Algorithm}" 
                                                         Style="{StaticResource MaterialDesignBody2TextBlock}" 
                                                         FontWeight="Bold"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                <TextBlock Text="الإطار" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding Framework}" 
                                                         Style="{StaticResource MaterialDesignBody2TextBlock}" 
                                                         FontWeight="Bold"/>
                                            </StackPanel>
                                        </Grid>

                                        <!-- مقاييس الأداء -->
                                        <Grid Grid.Row="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                <TextBlock Text="الدقة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding FormattedAccuracy}" 
                                                             Style="{StaticResource MaterialDesignBody2TextBlock}" 
                                                             Foreground="{Binding AccuracyColor}"
                                                             FontWeight="Bold"/>
                                                    <ProgressBar Value="{Binding Accuracy}" 
                                                               Maximum="100" 
                                                               Width="50" 
                                                               Height="4" 
                                                               VerticalAlignment="Center"
                                                               Margin="8,0,0,0"/>
                                                </StackPanel>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                <TextBlock Text="الإصدار" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding Version}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            </StackPanel>
                                        </Grid>
                                    </Grid>

                                    <!-- معلومات التدريب -->
                                    <Grid Grid.Row="2" Margin="16,0,16,8" Visibility="{Binding IsTraining, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <StackPanel>
                                            <TextBlock Text="جاري التدريب..." 
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Margin="0,0,0,4"/>
                                            <ProgressBar IsIndeterminate="True" Height="4"/>
                                            <TextBlock Text="{Binding FormattedTrainingDuration}" 
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     HorizontalAlignment="Center"
                                                     Margin="0,4,0,0"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- أزرار الإجراءات -->
                                    <Grid Grid.Row="3" Margin="16,8,16,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- تاريخ الإنشاء -->
                                        <TextBlock Grid.Column="0" 
                                                 Text="{Binding FormattedCreatedAt}" 
                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                 VerticalAlignment="Center"
                                                 Opacity="0.7"/>

                                        <!-- زر التدريب -->
                                        <Button Grid.Column="1" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.TrainModelCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="تدريب النموذج"
                                              Visibility="{Binding CanTrain, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <materialDesign:PackIcon Kind="School" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر النشر -->
                                        <Button Grid.Column="2" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.DeployModelCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="نشر النموذج"
                                              Visibility="{Binding CanDeploy, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <materialDesign:PackIcon Kind="CloudUpload" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر التشغيل -->
                                        <Button Grid.Column="3" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.RunModelCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="تشغيل النموذج"
                                              Visibility="{Binding IsDeployed, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <materialDesign:PackIcon Kind="Play" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر التقييم -->
                                        <Button Grid.Column="4" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.EvaluateModelCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="تقييم النموذج">
                                            <materialDesign:PackIcon Kind="ChartLine" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر التحرير -->
                                        <Button Grid.Column="5" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.EditModelCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="تحرير">
                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر الحذف -->
                                        <Button Grid.Column="6" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.DeleteModelCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="حذف">
                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Border>
                        </materialDesign:Card>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Grid Grid.RowSpan="4" 
              Background="White" 
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري تحميل النماذج..." 
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
