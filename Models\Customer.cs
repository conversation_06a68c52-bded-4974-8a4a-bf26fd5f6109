using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    public class Customer : INotifyPropertyChanged
    {
        private int _id;
        private string _code = string.Empty;
        private string _name = string.Empty;
        private string _phone = string.Empty;
        private string _email = string.Empty;
        private string _address = string.Empty;
        private decimal _balance;
        private decimal _creditLimit;
        private string _paymentTerms = "نقدي";
        private string _customerType = "فرد";
        private string _taxNumber = string.Empty;
        private string _notes = string.Empty;
        private bool _isActive = true;
        private DateTime _createdAt;
        private DateTime? _updatedAt;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Phone
        {
            get => _phone;
            set
            {
                if (_phone != value)
                {
                    _phone = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Email
        {
            get => _email;
            set
            {
                if (_email != value)
                {
                    _email = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Address
        {
            get => _address;
            set
            {
                if (_address != value)
                {
                    _address = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Balance
        {
            get => _balance;
            set
            {
                if (_balance != value)
                {
                    _balance = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasOutstandingBalance));
                }
            }
        }

        public decimal CreditLimit
        {
            get => _creditLimit;
            set
            {
                if (_creditLimit != value)
                {
                    _creditLimit = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PaymentTerms
        {
            get => _paymentTerms;
            set
            {
                if (_paymentTerms != value)
                {
                    _paymentTerms = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CustomerType
        {
            get => _customerType;
            set
            {
                if (_customerType != value)
                {
                    _customerType = value;
                    OnPropertyChanged();
                }
            }
        }

        public string TaxNumber
        {
            get => _taxNumber;
            set
            {
                if (_taxNumber != value)
                {
                    _taxNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        // Calculated properties
        public bool HasOutstandingBalance => Balance > 0;

        // Clone method
        public Customer Clone()
        {
            return new Customer
            {
                Id = this.Id,
                Code = this.Code,
                Name = this.Name,
                Phone = this.Phone,
                Email = this.Email,
                Address = this.Address,
                Balance = this.Balance,
                CreditLimit = this.CreditLimit,
                PaymentTerms = this.PaymentTerms,
                CustomerType = this.CustomerType,
                TaxNumber = this.TaxNumber,
                Notes = this.Notes,
                IsActive = this.IsActive,
                CreatedAt = this.CreatedAt,
                UpdatedAt = this.UpdatedAt
            };
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // Ensure non-nullable reference type requirements
        public Customer()
        {
            // Initialize non-nullable fields
            Code = string.Empty;
            Name = string.Empty;
            Phone = string.Empty;
            Email = string.Empty;
            Address = string.Empty;
            PaymentTerms = "نقدي";
            CustomerType = "فرد";
            TaxNumber = string.Empty;
            Notes = string.Empty;
        }
    }

    public class CustomerValidator : AbstractValidator<Customer>
    {
        public CustomerValidator()
        {
            RuleFor(c => c.Name).NotEmpty().WithMessage("Customer name is required")
                .MaximumLength(100).WithMessage("Customer name cannot exceed 100 characters");

            RuleFor(c => c.Phone).MaximumLength(20).WithMessage("Phone number cannot exceed 20 characters");

            RuleFor(c => c.Email).EmailAddress().When(c => !string.IsNullOrEmpty(c.Email))
                .WithMessage("Invalid email address format")
                .MaximumLength(100).WithMessage("Email cannot exceed 100 characters");

            RuleFor(c => c.Address).MaximumLength(200).WithMessage("Address cannot exceed 200 characters");
        }
    }
}