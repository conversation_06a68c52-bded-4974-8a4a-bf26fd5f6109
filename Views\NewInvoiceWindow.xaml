<Window x:Class="SalesManagementSystem.Views.NewInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="فاتورة مبيعات جديدة" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <!-- Style for center-aligned text in DataGrid -->
        <Style x:Key="CenterAlignedTextBlock" TargetType="TextBlock">
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>

        <!-- Style for right-aligned text in DataGrid -->
        <Style x:Key="RightAlignedTextBlock" TargetType="TextBlock">
            <Setter Property="TextAlignment" Value="Right"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Receipt" Width="32" Height="32"
                                           Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="فاتورة مبيعات جديدة" FontSize="24" FontWeight="Bold"
                              Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock x:Name="HeaderTotalLabel" Text="0.00 دج" FontSize="28" FontWeight="Bold"
                              Foreground="Yellow" VerticalAlignment="Center" Margin="0,0,20,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Invoice Info -->
        <Border Grid.Row="1" Background="LightBlue" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="رقم الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="InvoiceNumberTextBox" Height="35" IsReadOnly="True"
                            Background="LightGray" Text="سيتم إنشاؤه تلقائياً"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Margin="10,0">
                    <TextBlock Text="تاريخ الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="InvoiceDatePicker" Height="35" SelectedDate="{x:Static sys:DateTime.Now}"
                               xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Margin="10,0">
                    <TextBlock Text="تاريخ الاستحقاق:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="DueDatePicker" Height="35"/>
                </StackPanel>

                <StackPanel Grid.Column="3" Margin="10,0,0,0">
                    <TextBlock Text="طريقة الدفع:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="PaymentMethodComboBox" Height="35" SelectedIndex="0">
                        <ComboBoxItem Content="نقدي"/>
                        <ComboBoxItem Content="بطاقة ائتمان"/>
                        <ComboBoxItem Content="تحويل بنكي"/>
                        <ComboBoxItem Content="شيك"/>
                        <ComboBoxItem Content="آجل"/>
                    </ComboBox>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Customer Selection -->
        <Border Grid.Row="2" Background="LightGreen" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="🧑‍💼 العميل:" FontSize="16" FontWeight="Bold"
                          VerticalAlignment="Center" Margin="0,0,10,0"/>

                <Grid Grid.Column="1" Margin="0,0,10,0">
                    <ComboBox x:Name="CustomerComboBox" Height="35" IsEditable="True"
                             DisplayMemberPath="Name" SelectedValuePath="Id"
                             SelectionChanged="CustomerComboBox_SelectionChanged"/>
                </Grid>

                <Button Grid.Column="2" Content="➕ عميل جديد" Width="100" Height="35" Margin="0,0,10,0"
                       Background="Blue" Foreground="White" FontWeight="Bold"
                       Click="AddNewCustomer_Click"/>

                <Button Grid.Column="3" Content="📋 تفاصيل" Width="80" Height="35"
                       Background="Purple" Foreground="White" FontWeight="Bold"
                       Click="CustomerDetails_Click"/>
            </Grid>
        </Border>

        <!-- Product Search and Add -->
        <Border Grid.Row="3" Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Search Section -->
                <Border Grid.Row="0" Background="LightYellow" Padding="15" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="🔍 إضافة منتج:" FontSize="16" FontWeight="Bold"
                                  VerticalAlignment="Center" Margin="0,0,10,0"/>

                        <Grid Grid.Column="1" Margin="0,0,10,0">
                            <TextBox x:Name="ProductSearchTextBox" FontSize="16" Height="35"
                                    Background="White" BorderBrush="DarkGreen" BorderThickness="2"
                                    TextChanged="ProductSearchTextBox_TextChanged"
                                    KeyDown="ProductSearchTextBox_KeyDown"
                                    ToolTip="ابحث عن المنتج بالاسم أو الكود أو امسح الكودبار..."/>

                            <!-- Suggestions Popup -->
                            <Popup x:Name="ProductSuggestionsPopup"
                                   PlacementTarget="{Binding ElementName=ProductSearchTextBox}"
                                   Placement="Bottom" IsOpen="False" StaysOpen="False"
                                   AllowsTransparency="True" PopupAnimation="Slide">
                                <Border Background="White" BorderBrush="DarkGreen" BorderThickness="2"
                                       CornerRadius="5" MaxHeight="200" MinWidth="400">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Gray" BlurRadius="10" ShadowDepth="3" Opacity="0.5"/>
                                    </Border.Effect>
                                    <ListBox x:Name="ProductSuggestionsListBox"
                                            Background="Transparent" BorderThickness="0"
                                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                                            MouseDoubleClick="ProductSuggestionsListBox_MouseDoubleClick">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Grid Margin="5">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Grid.Column="0" Text="📦" FontSize="14"
                                                              VerticalAlignment="Center" Margin="0,0,8,0"/>

                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                        <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                                        <TextBlock Text="{Binding Code}" FontSize="12" Foreground="Gray"/>
                                                    </StackPanel>

                                                    <TextBlock Grid.Column="2" Text="{Binding SalePrice, StringFormat='{}{0:F0} دج'}"
                                                              FontWeight="Bold" FontSize="12" Foreground="DarkGreen"
                                                              VerticalAlignment="Center" Margin="8,0,0,0"/>
                                                </Grid>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                    </ListBox>
                                </Border>
                            </Popup>
                        </Grid>

                        <TextBox Grid.Column="2" x:Name="QuantityTextBox" Width="80" Height="35" Margin="0,0,10,0"
                                Text="1" TextAlignment="Center" ToolTip="الكمية"/>

                        <Button Grid.Column="3" Content="➕ إضافة" Width="80" Height="35" Margin="0,0,10,0"
                               Background="Green" Foreground="White" FontWeight="Bold"
                               Click="AddProduct_Click"/>

                        <Button Grid.Column="4" Content="🗑️ مسح" Width="80" Height="35"
                               Background="Orange" Foreground="White" FontWeight="Bold"
                               Click="ClearSearch_Click"/>
                    </Grid>
                </Border>

                <!-- Products DataGrid -->
                <DataGrid Grid.Row="1" x:Name="InvoiceItemsDataGrid" AutoGenerateColumns="False"
                         CanUserAddRows="False" GridLinesVisibility="All" AlternatingRowBackground="LightGray"
                         SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="المنتج" Binding="{Binding ProductName}" Width="*" IsReadOnly="True"/>
                        <DataGridTextColumn Header="الكود" Binding="{Binding ProductCode}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"
                                           ElementStyle="{StaticResource CenterAlignedTextBlock}"/>
                        <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice, StringFormat='{}{0:F2}'}" Width="100"
                                           ElementStyle="{StaticResource CenterAlignedTextBlock}"/>
                        <DataGridTextColumn Header="الخصم %" Binding="{Binding DiscountPercentage}" Width="80"
                                           ElementStyle="{StaticResource CenterAlignedTextBlock}"/>
                        <DataGridTextColumn Header="المجموع" Binding="{Binding LineTotal, StringFormat='{}{0:F2}'}" Width="120"
                                           ElementStyle="{StaticResource CenterAlignedTextBlock}" IsReadOnly="True"/>
                        <DataGridTemplateColumn Header="العمليات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="✏️" Width="30" Height="25" Margin="2"
                                               ToolTip="تعديل" Click="EditItem_Click"/>
                                        <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                               ToolTip="حذف" Click="DeleteItem_Click"
                                               Background="Red" Foreground="White"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Summary -->
        <Border Grid.Row="4" Background="LightCyan" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                </Grid.ColumnDefinitions>

                <!-- Notes -->
                <StackPanel Grid.Column="0" Margin="0,0,20,0">
                    <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="NotesTextBox" Height="80" TextWrapping="Wrap"
                            AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                </StackPanel>

                <!-- Totals -->
                <StackPanel Grid.Column="1">
                    <Grid Margin="0,0,0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="المجموع الفرعي:" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" x:Name="SubTotalLabel" Text="0.00 دج" FontWeight="Bold"/>
                    </Grid>

                    <Grid Margin="0,0,0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="الخصم:" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" x:Name="DiscountLabel" Text="0.00 دج" FontWeight="Bold"/>
                    </Grid>

                    <Grid Margin="0,0,0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="الضريبة:" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" x:Name="TaxLabel" Text="0.00 دج" FontWeight="Bold"/>
                    </Grid>

                    <Separator Margin="0,5"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="الإجمالي:" FontWeight="Bold" FontSize="16"/>
                        <TextBlock Grid.Column="1" x:Name="TotalLabel" Text="0.00 دج" FontWeight="Bold"
                                  FontSize="16" Foreground="DarkGreen"/>
                    </Grid>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Row="5" Background="LightGray" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="💾 حفظ كمسودة" Width="120" Height="40" Margin="5"
                       Background="Blue" Foreground="White" FontWeight="Bold"
                       Click="SaveDraft_Click"/>

                <Button Content="✅ حفظ ودفع" Width="120" Height="40" Margin="5"
                       Background="Green" Foreground="White" FontWeight="Bold"
                       Click="SaveAndPay_Click"/>

                <Button Content="🖨️ طباعة" Width="120" Height="40" Margin="5"
                       Background="Purple" Foreground="White" FontWeight="Bold"
                       Click="Print_Click"/>

                <Button Content="❌ إلغاء" Width="120" Height="40" Margin="5"
                       Background="Red" Foreground="White" FontWeight="Bold"
                       Click="Cancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
