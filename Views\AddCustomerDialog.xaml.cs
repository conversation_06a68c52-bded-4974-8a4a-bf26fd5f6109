using System;
using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class AddCustomerDialog : Window
    {
        private readonly CustomerService _customerService;
        public Customer? NewCustomer { get; private set; }

        public AddCustomerDialog()
        {
            InitializeComponent();

            var databaseService = new DatabaseService();
            _customerService = new CustomerService(databaseService);
        }

        private async void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(NameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    NameTextBox.Focus();
                    return;
                }

                // إنشاء عميل جديد
                var customer = new Customer
                {
                    Name = NameTextBox.Text.Trim(),
                    Phone = PhoneTextBox.Text.Trim(),
                    Email = EmailTextBox.Text.Trim(),
                    Address = AddressTextBox.Text.Trim(),
                    Notes = NotesTextBox.Text.Trim(),
                    CreatedAt = DateTime.Now,
                    IsActive = true
                };

                // حفظ العميل
                var savedCustomer = await _customerService.AddCustomerAsync(customer);
                if (savedCustomer != null && savedCustomer.Id > 0)
                {
                    NewCustomer = savedCustomer;

                    MessageBox.Show("تم إضافة العميل بنجاح", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة العميل", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العميل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
