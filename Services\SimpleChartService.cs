using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;

namespace SalesManagementSystem.Services
{
    public class SimpleChartService
    {
        private readonly SimpleReportService _reportService;

        public SimpleChartService(SimpleReportService reportService)
        {
            _reportService = reportService;
        }

        public async Task<SeriesCollection> GetMonthlySalesChartAsync(int year)
        {
            try
            {
                var startDate = new DateTime(year, 1, 1);
                var endDate = new DateTime(year, 12, 31);

                // Generate sample data for demonstration
                var monthlyData = new List<decimal>();
                for (int i = 1; i <= 12; i++)
                {
                    var monthStart = new DateTime(year, i, 1);
                    var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                    var sales = await _reportService.GetTotalSalesAsync(monthStart, monthEnd);
                    monthlyData.Add(sales);
                }

                var series = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "المبيعات الشهرية",
                        Values = new ChartValues<decimal>(monthlyData),
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8,
                        Fill = Brushes.Transparent,
                        Stroke = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                        StrokeThickness = 3
                    }
                };

                return series;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء الرسم البياني للمبيعات الشهرية");
                return new SeriesCollection();
            }
        }

        public async Task<SeriesCollection> GetTopProductsPieChartAsync(DateTime startDate, DateTime endDate, int topCount = 5)
        {
            try
            {
                var productSales = await _reportService.GetProductSalesReportAsync(startDate, endDate);
                var topProducts = productSales.Take(topCount);

                var series = new SeriesCollection();
                var colors = GetChartColors();
                int colorIndex = 0;

                foreach (var product in topProducts)
                {
                    series.Add(new PieSeries
                    {
                        Title = product.ProductName,
                        Values = new ChartValues<decimal> { product.TotalRevenue },
                        DataLabels = true,
                        LabelPoint = chartPoint => $"{product.ProductName}: {chartPoint.Y:C}",
                        Fill = new SolidColorBrush(colors[colorIndex % colors.Count])
                    });
                    colorIndex++;
                }

                return series;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء الرسم الدائري للمنتجات");
                return new SeriesCollection();
            }
        }

        public async Task<SeriesCollection> GetProfitLossChartAsync(int year)
        {
            try
            {
                var startDate = new DateTime(year, 1, 1);
                var endDate = new DateTime(year, 12, 31);
                var profitLoss = await _reportService.GetProfitLossReportAsync(startDate, endDate);

                // Create dummy monthly data for demonstration
                var monthlyRevenue = new List<decimal>();
                var monthlyExpenses = new List<decimal>();

                for (int i = 1; i <= 12; i++)
                {
                    monthlyRevenue.Add(profitLoss.TotalRevenue / 12);
                    monthlyExpenses.Add(profitLoss.TotalExpenses / 12);
                }

                var series = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = "الإيرادات",
                        Values = new ChartValues<decimal>(monthlyRevenue),
                        Fill = new SolidColorBrush(Color.FromRgb(76, 175, 80))
                    },
                    new ColumnSeries
                    {
                        Title = "المصروفات",
                        Values = new ChartValues<decimal>(monthlyExpenses),
                        Fill = new SolidColorBrush(Color.FromRgb(244, 67, 54))
                    }
                };

                return series;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء رسم الأرباح والخسائر");
                return new SeriesCollection();
            }
        }

        public string[] GetMonthLabels()
        {
            return new[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                          "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر" };
        }

        public Func<double, string> GetCurrencyFormatter()
        {
            return value => $"{value:C}";
        }

        public Func<double, string> GetPercentageFormatter()
        {
            return value => $"{value:F1}%";
        }

        public SeriesCollection GetEmployeePerformanceChart(List<SimpleEmployeePerformanceReport> employeePerformance)
        {
            var series = new SeriesCollection
            {
                new ColumnSeries
                {
                    Title = "إجمالي الإيرادات",
                    Values = new ChartValues<decimal>(employeePerformance.Select(e => e.TotalRevenue)),
                    Fill = new SolidColorBrush(Color.FromRgb(33, 150, 243))
                }
            };

            return series;
        }

        public string[] GetEmployeeLabels(List<SimpleEmployeePerformanceReport> employeePerformance)
        {
            return employeePerformance.Select(e => e.EmployeeName).ToArray();
        }

        public SeriesCollection GetPeriodComparisonChart(SimplePeriodComparisonReport comparison)
        {
            var series = new SeriesCollection
            {
                new ColumnSeries
                {
                    Title = comparison.Period1.PeriodName,
                    Values = new ChartValues<decimal> { comparison.Period1.Sales, comparison.Period1.Purchases, comparison.Period1.Expenses, comparison.Period1.Profit },
                    Fill = new SolidColorBrush(Color.FromRgb(33, 150, 243))
                },
                new ColumnSeries
                {
                    Title = comparison.Period2.PeriodName,
                    Values = new ChartValues<decimal> { comparison.Period2.Sales, comparison.Period2.Purchases, comparison.Period2.Expenses, comparison.Period2.Profit },
                    Fill = new SolidColorBrush(Color.FromRgb(76, 175, 80))
                }
            };

            return series;
        }

        public string[] GetPeriodComparisonLabels()
        {
            return new[] { "المبيعات", "المشتريات", "المصروفات", "الأرباح" };
        }

        public SeriesCollection GetTrendAnalysisChart(SimpleTrendAnalysisReport trendAnalysis)
        {
            var series = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "اتجاه المبيعات",
                    Values = new ChartValues<decimal>(trendAnalysis.SalesTrend.Select(t => t.Value)),
                    PointGeometry = DefaultGeometries.Circle,
                    PointGeometrySize = 8,
                    Fill = Brushes.Transparent,
                    Stroke = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                    StrokeThickness = 3
                },
                new LineSeries
                {
                    Title = "اتجاه الأرباح",
                    Values = new ChartValues<decimal>(trendAnalysis.ProfitTrend.Select(t => t.Value)),
                    PointGeometry = DefaultGeometries.Circle,
                    PointGeometrySize = 8,
                    Fill = Brushes.Transparent,
                    Stroke = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                    StrokeThickness = 3
                }
            };

            return series;
        }

        private List<Color> GetChartColors()
        {
            return new List<Color>
            {
                Color.FromRgb(33, 150, 243),   // Blue
                Color.FromRgb(76, 175, 80),    // Green
                Color.FromRgb(255, 152, 0),    // Orange
                Color.FromRgb(156, 39, 176),   // Purple
                Color.FromRgb(244, 67, 54),    // Red
                Color.FromRgb(0, 188, 212),    // Cyan
                Color.FromRgb(255, 193, 7),    // Amber
                Color.FromRgb(96, 125, 139)    // Blue Grey
            };
        }
    }

    // Extension method for SimpleReportService
    public static class SimpleReportServiceExtensions
    {
        public static async Task<decimal> GetTotalSalesAsync(this SimpleReportService service, DateTime startDate, DateTime endDate)
        {
            try
            {
                var report = await service.GetSalesReportAsync(startDate, endDate);
                return report.TotalSales;
            }
            catch
            {
                return 0;
            }
        }
    }
}
