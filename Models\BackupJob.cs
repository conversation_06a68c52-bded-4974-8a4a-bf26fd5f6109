using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج مهمة النسخ الاحتياطي
    /// </summary>
    public class BackupJob : INotifyPropertyChanged
    {
        private int _id;
        private string _jobName = string.Empty;
        private string _jobCode = string.Empty;
        private BackupType _backupType = BackupType.Full;
        private BackupFrequency _frequency = BackupFrequency.Daily;
        private BackupStatus _status = BackupStatus.Scheduled;
        private string _sourcePath = string.Empty;
        private string _destinationPath = string.Empty;
        private string _cloudProvider = string.Empty;
        private string _cloudCredentials = string.Empty;
        private bool _isEncrypted = true;
        private string _encryptionKey = string.Empty;
        private bool _isCompressed = true;
        private CompressionLevel _compressionLevel = CompressionLevel.Medium;
        private int _retentionDays = 30;
        private int _maxBackupCopies = 10;
        private DateTime _nextRunTime = DateTime.Now.AddDays(1);
        private DateTime? _lastRunTime;
        private DateTime? _lastSuccessTime;
        private TimeSpan _estimatedDuration = TimeSpan.FromHours(1);
        private TimeSpan? _actualDuration;
        private long _estimatedSize;
        private long _actualSize;
        private string _includeFilters = string.Empty;
        private string _excludeFilters = string.Empty;
        private bool _verifyIntegrity = true;
        private bool _sendNotifications = true;
        private string _notificationEmails = string.Empty;
        private string _errorMessage = string.Empty;
        private int _successCount;
        private int _failureCount;
        private double _successRate;
        private string _createdBy = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private ObservableCollection<BackupHistory> _backupHistory = new();
        private ObservableCollection<BackupFile> _backupFiles = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string JobName
        {
            get => _jobName;
            set
            {
                if (_jobName != value)
                {
                    _jobName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string JobCode
        {
            get => _jobCode;
            set
            {
                if (_jobCode != value)
                {
                    _jobCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public BackupType BackupType
        {
            get => _backupType;
            set
            {
                if (_backupType != value)
                {
                    _backupType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(BackupTypeDisplay));
                    OnPropertyChanged(nameof(BackupTypeIcon));
                }
            }
        }

        public BackupFrequency Frequency
        {
            get => _frequency;
            set
            {
                if (_frequency != value)
                {
                    _frequency = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FrequencyDisplay));
                    UpdateNextRunTime();
                }
            }
        }

        public BackupStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsRunning));
                    OnPropertyChanged(nameof(CanStart));
                    OnPropertyChanged(nameof(CanStop));
                }
            }
        }

        public string SourcePath
        {
            get => _sourcePath;
            set
            {
                if (_sourcePath != value)
                {
                    _sourcePath = value;
                    OnPropertyChanged();
                }
            }
        }

        public string DestinationPath
        {
            get => _destinationPath;
            set
            {
                if (_destinationPath != value)
                {
                    _destinationPath = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CloudProvider
        {
            get => _cloudProvider;
            set
            {
                if (_cloudProvider != value)
                {
                    _cloudProvider = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsCloudBackup));
                }
            }
        }

        public string CloudCredentials
        {
            get => _cloudCredentials;
            set
            {
                if (_cloudCredentials != value)
                {
                    _cloudCredentials = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsEncrypted
        {
            get => _isEncrypted;
            set
            {
                if (_isEncrypted != value)
                {
                    _isEncrypted = value;
                    OnPropertyChanged();
                }
            }
        }

        public string EncryptionKey
        {
            get => _encryptionKey;
            set
            {
                if (_encryptionKey != value)
                {
                    _encryptionKey = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsCompressed
        {
            get => _isCompressed;
            set
            {
                if (_isCompressed != value)
                {
                    _isCompressed = value;
                    OnPropertyChanged();
                }
            }
        }

        public CompressionLevel CompressionLevel
        {
            get => _compressionLevel;
            set
            {
                if (_compressionLevel != value)
                {
                    _compressionLevel = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CompressionLevelDisplay));
                }
            }
        }

        public int RetentionDays
        {
            get => _retentionDays;
            set
            {
                if (_retentionDays != value)
                {
                    _retentionDays = value;
                    OnPropertyChanged();
                }
            }
        }

        public int MaxBackupCopies
        {
            get => _maxBackupCopies;
            set
            {
                if (_maxBackupCopies != value)
                {
                    _maxBackupCopies = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime NextRunTime
        {
            get => _nextRunTime;
            set
            {
                if (_nextRunTime != value)
                {
                    _nextRunTime = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedNextRunTime));
                    OnPropertyChanged(nameof(TimeUntilNextRun));
                    OnPropertyChanged(nameof(IsOverdue));
                }
            }
        }

        public DateTime? LastRunTime
        {
            get => _lastRunTime;
            set
            {
                if (_lastRunTime != value)
                {
                    _lastRunTime = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLastRunTime));
                }
            }
        }

        public DateTime? LastSuccessTime
        {
            get => _lastSuccessTime;
            set
            {
                if (_lastSuccessTime != value)
                {
                    _lastSuccessTime = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLastSuccessTime));
                    OnPropertyChanged(nameof(DaysSinceLastSuccess));
                }
            }
        }

        public TimeSpan EstimatedDuration
        {
            get => _estimatedDuration;
            set
            {
                if (_estimatedDuration != value)
                {
                    _estimatedDuration = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedEstimatedDuration));
                }
            }
        }

        public TimeSpan? ActualDuration
        {
            get => _actualDuration;
            set
            {
                if (_actualDuration != value)
                {
                    _actualDuration = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedActualDuration));
                }
            }
        }

        public long EstimatedSize
        {
            get => _estimatedSize;
            set
            {
                if (_estimatedSize != value)
                {
                    _estimatedSize = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedEstimatedSize));
                }
            }
        }

        public long ActualSize
        {
            get => _actualSize;
            set
            {
                if (_actualSize != value)
                {
                    _actualSize = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedActualSize));
                }
            }
        }

        public string IncludeFilters
        {
            get => _includeFilters;
            set
            {
                if (_includeFilters != value)
                {
                    _includeFilters = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ExcludeFilters
        {
            get => _excludeFilters;
            set
            {
                if (_excludeFilters != value)
                {
                    _excludeFilters = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool VerifyIntegrity
        {
            get => _verifyIntegrity;
            set
            {
                if (_verifyIntegrity != value)
                {
                    _verifyIntegrity = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool SendNotifications
        {
            get => _sendNotifications;
            set
            {
                if (_sendNotifications != value)
                {
                    _sendNotifications = value;
                    OnPropertyChanged();
                }
            }
        }

        public string NotificationEmails
        {
            get => _notificationEmails;
            set
            {
                if (_notificationEmails != value)
                {
                    _notificationEmails = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasError));
                }
            }
        }

        public int SuccessCount
        {
            get => _successCount;
            set
            {
                if (_successCount != value)
                {
                    _successCount = value;
                    OnPropertyChanged();
                    CalculateSuccessRate();
                }
            }
        }

        public int FailureCount
        {
            get => _failureCount;
            set
            {
                if (_failureCount != value)
                {
                    _failureCount = value;
                    OnPropertyChanged();
                    CalculateSuccessRate();
                }
            }
        }

        public double SuccessRate
        {
            get => _successRate;
            set
            {
                if (_successRate != value)
                {
                    _successRate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedSuccessRate));
                    OnPropertyChanged(nameof(SuccessRateColor));
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<BackupHistory> BackupHistory
        {
            get => _backupHistory;
            set
            {
                if (_backupHistory != value)
                {
                    _backupHistory = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<BackupFile> BackupFiles
        {
            get => _backupFiles;
            set
            {
                if (_backupFiles != value)
                {
                    _backupFiles = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TotalBackupFiles));
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsCloudBackup => !string.IsNullOrEmpty(CloudProvider);
        public bool IsRunning => Status == BackupStatus.Running;
        public bool CanStart => Status == BackupStatus.Scheduled || Status == BackupStatus.Failed || Status == BackupStatus.Completed;
        public bool CanStop => Status == BackupStatus.Running;
        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);
        public bool IsOverdue => NextRunTime < DateTime.Now && Status == BackupStatus.Scheduled;
        public int TotalBackupFiles => BackupFiles?.Count ?? 0;

        public int DaysSinceLastSuccess
        {
            get
            {
                if (!LastSuccessTime.HasValue) return int.MaxValue;
                return (int)(DateTime.Now - LastSuccessTime.Value).TotalDays;
            }
        }

        public string TimeUntilNextRun
        {
            get
            {
                if (Status != BackupStatus.Scheduled) return "غير مجدول";
                var timeSpan = NextRunTime - DateTime.Now;
                if (timeSpan.TotalDays >= 1)
                    return $"{(int)timeSpan.TotalDays} يوم";
                if (timeSpan.TotalHours >= 1)
                    return $"{(int)timeSpan.TotalHours} ساعة";
                if (timeSpan.TotalMinutes >= 1)
                    return $"{(int)timeSpan.TotalMinutes} دقيقة";
                return timeSpan.TotalSeconds > 0 ? "أقل من دقيقة" : "متأخر";
            }
        }

        // Display Properties
        public string BackupTypeDisplay
        {
            get
            {
                return BackupType switch
                {
                    BackupType.Full => "نسخة كاملة",
                    BackupType.Incremental => "نسخة تزايدية",
                    BackupType.Differential => "نسخة تفاضلية",
                    BackupType.Mirror => "نسخة مرآة",
                    _ => "غير محدد"
                };
            }
        }

        public string FrequencyDisplay
        {
            get
            {
                return Frequency switch
                {
                    BackupFrequency.Hourly => "كل ساعة",
                    BackupFrequency.Daily => "يومي",
                    BackupFrequency.Weekly => "أسبوعي",
                    BackupFrequency.Monthly => "شهري",
                    BackupFrequency.Manual => "يدوي",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    BackupStatus.Scheduled => "مجدول",
                    BackupStatus.Running => "قيد التشغيل",
                    BackupStatus.Completed => "مكتمل",
                    BackupStatus.Failed => "فشل",
                    BackupStatus.Cancelled => "ملغي",
                    BackupStatus.Paused => "متوقف مؤقتاً",
                    _ => "غير محدد"
                };
            }
        }

        public string CompressionLevelDisplay
        {
            get
            {
                return CompressionLevel switch
                {
                    CompressionLevel.None => "بدون ضغط",
                    CompressionLevel.Low => "ضغط منخفض",
                    CompressionLevel.Medium => "ضغط متوسط",
                    CompressionLevel.High => "ضغط عالي",
                    CompressionLevel.Maximum => "ضغط أقصى",
                    _ => "غير محدد"
                };
            }
        }

        public string BackupTypeIcon
        {
            get
            {
                return BackupType switch
                {
                    BackupType.Full => "DatabaseExport",
                    BackupType.Incremental => "DatabasePlus",
                    BackupType.Differential => "DatabaseSync",
                    BackupType.Mirror => "DatabaseRefresh",
                    _ => "Database"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    BackupStatus.Scheduled => "Blue",
                    BackupStatus.Running => "Orange",
                    BackupStatus.Completed => "Green",
                    BackupStatus.Failed => "Red",
                    BackupStatus.Cancelled => "Gray",
                    BackupStatus.Paused => "Purple",
                    _ => "Gray"
                };
            }
        }

        public string SuccessRateColor
        {
            get
            {
                return SuccessRate switch
                {
                    >= 95 => "Green",
                    >= 80 => "Orange",
                    _ => "Red"
                };
            }
        }

        // Formatted Properties
        public string FormattedNextRunTime => NextRunTime.ToString("dd/MM/yyyy HH:mm");
        public string FormattedLastRunTime => LastRunTime?.ToString("dd/MM/yyyy HH:mm") ?? "لم يتم التشغيل";
        public string FormattedLastSuccessTime => LastSuccessTime?.ToString("dd/MM/yyyy HH:mm") ?? "لا يوجد نجاح";
        public string FormattedEstimatedDuration => EstimatedDuration.ToString(@"hh\:mm\:ss");
        public string FormattedActualDuration => ActualDuration?.ToString(@"hh\:mm\:ss") ?? "غير محدد";
        public string FormattedEstimatedSize => FormatFileSize(EstimatedSize);
        public string FormattedActualSize => FormatFileSize(ActualSize);
        public string FormattedSuccessRate => $"{SuccessRate:F1}%";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        private void UpdateNextRunTime()
        {
            var baseTime = LastRunTime ?? DateTime.Now;
            NextRunTime = Frequency switch
            {
                BackupFrequency.Hourly => baseTime.AddHours(1),
                BackupFrequency.Daily => baseTime.AddDays(1),
                BackupFrequency.Weekly => baseTime.AddDays(7),
                BackupFrequency.Monthly => baseTime.AddMonths(1),
                BackupFrequency.Manual => DateTime.MaxValue,
                _ => baseTime.AddDays(1)
            };
        }

        private void CalculateSuccessRate()
        {
            var totalRuns = SuccessCount + FailureCount;
            if (totalRuns > 0)
                SuccessRate = (double)SuccessCount / totalRuns * 100;
            else
                SuccessRate = 0;
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";
            
            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:F2} {sizes[order]}";
        }

        public void Start()
        {
            Status = BackupStatus.Running;
            LastRunTime = DateTime.Now;
            ErrorMessage = string.Empty;
            UpdatedAt = DateTime.Now;
        }

        public void Complete(long actualSize, TimeSpan duration)
        {
            Status = BackupStatus.Completed;
            ActualSize = actualSize;
            ActualDuration = duration;
            LastSuccessTime = DateTime.Now;
            SuccessCount++;
            UpdateNextRunTime();
            UpdatedAt = DateTime.Now;
        }

        public void Fail(string errorMessage)
        {
            Status = BackupStatus.Failed;
            ErrorMessage = errorMessage;
            FailureCount++;
            UpdatedAt = DateTime.Now;
        }

        public void Cancel()
        {
            Status = BackupStatus.Cancelled;
            UpdatedAt = DateTime.Now;
        }

        public void Pause()
        {
            Status = BackupStatus.Paused;
            UpdatedAt = DateTime.Now;
        }

        public void Resume()
        {
            Status = BackupStatus.Running;
            UpdatedAt = DateTime.Now;
        }

        public void Schedule()
        {
            Status = BackupStatus.Scheduled;
            UpdateNextRunTime();
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// سجل النسخ الاحتياطية
    /// </summary>
    public class BackupHistory
    {
        public int Id { get; set; }
        public int BackupJobId { get; set; }
        public DateTime StartTime { get; set; } = DateTime.Now;
        public DateTime? EndTime { get; set; }
        public BackupStatus Status { get; set; } = BackupStatus.Running;
        public long BackupSize { get; set; }
        public int FilesCount { get; set; }
        public string BackupPath { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string ChecksumHash { get; set; } = string.Empty;
        public bool IsVerified { get; set; }

        public TimeSpan? Duration => EndTime.HasValue ? EndTime - StartTime : null;
        public string FormattedStartTime => StartTime.ToString("dd/MM/yyyy HH:mm");
        public string FormattedEndTime => EndTime?.ToString("dd/MM/yyyy HH:mm") ?? "قيد التشغيل";
        public string FormattedDuration => Duration?.ToString(@"hh\:mm\:ss") ?? "غير محدد";
        public string FormattedBackupSize => FormatFileSize(BackupSize);

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";
            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت" };
            int order = 0;
            double size = bytes;
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            return $"{size:F2} {sizes[order]}";
        }
    }

    /// <summary>
    /// ملف النسخة الاحتياطية
    /// </summary>
    public class BackupFile
    {
        public int Id { get; set; }
        public int BackupJobId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? ExpiryDate { get; set; }
        public string ChecksumHash { get; set; } = string.Empty;
        public bool IsEncrypted { get; set; }
        public bool IsCompressed { get; set; }
        public string CloudLocation { get; set; } = string.Empty;

        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate < DateTime.Now;
        public string FormattedFileSize => FormatFileSize(FileSize);
        public string FormattedCreatedDate => CreatedDate.ToString("dd/MM/yyyy HH:mm");
        public string FormattedExpiryDate => ExpiryDate?.ToString("dd/MM/yyyy") ?? "لا تنتهي";

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";
            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت" };
            int order = 0;
            double size = bytes;
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            return $"{size:F2} {sizes[order]}";
        }
    }

    #endregion

    #region Enums

    public enum BackupType
    {
        Full,           // نسخة كاملة
        Incremental,    // نسخة تزايدية
        Differential,   // نسخة تفاضلية
        Mirror          // نسخة مرآة
    }

    public enum BackupFrequency
    {
        Hourly,         // كل ساعة
        Daily,          // يومي
        Weekly,         // أسبوعي
        Monthly,        // شهري
        Manual          // يدوي
    }

    public enum BackupStatus
    {
        Scheduled,      // مجدول
        Running,        // قيد التشغيل
        Completed,      // مكتمل
        Failed,         // فشل
        Cancelled,      // ملغي
        Paused          // متوقف مؤقتاً
    }

    public enum CompressionLevel
    {
        None,           // بدون ضغط
        Low,            // ضغط منخفض
        Medium,         // ضغط متوسط
        High,           // ضغط عالي
        Maximum         // ضغط أقصى
    }

    #endregion

    #region Validation

    public class BackupJobValidator : AbstractValidator<BackupJob>
    {
        public BackupJobValidator()
        {
            RuleFor(b => b.JobName)
                .NotEmpty().WithMessage("اسم المهمة مطلوب")
                .MaximumLength(200).WithMessage("اسم المهمة لا يمكن أن يتجاوز 200 حرف");

            RuleFor(b => b.JobCode)
                .NotEmpty().WithMessage("كود المهمة مطلوب")
                .MaximumLength(50).WithMessage("كود المهمة لا يمكن أن يتجاوز 50 حرف");

            RuleFor(b => b.SourcePath)
                .NotEmpty().WithMessage("مسار المصدر مطلوب");

            RuleFor(b => b.DestinationPath)
                .NotEmpty().WithMessage("مسار الوجهة مطلوب");

            RuleFor(b => b.RetentionDays)
                .GreaterThan(0).WithMessage("فترة الاحتفاظ يجب أن تكون أكبر من صفر");

            RuleFor(b => b.MaxBackupCopies)
                .GreaterThan(0).WithMessage("عدد النسخ الأقصى يجب أن يكون أكبر من صفر");

            RuleFor(b => b.NextRunTime)
                .GreaterThan(DateTime.Now).When(b => b.Frequency != BackupFrequency.Manual)
                .WithMessage("وقت التشغيل التالي يجب أن يكون في المستقبل");
        }
    }

    #endregion
}
