<UserControl x:Class="SalesManagementSystem.Views.CRMDashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- تحويل حالة العميل إلى لون -->
        <Style x:Key="CustomerStatusIndicator" TargetType="Ellipse">
            <Setter Property="Fill" Value="Gray"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Active">
                    <Setter Property="Fill" Value="Green"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Inactive">
                    <Setter Property="Fill" Value="Orange"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Prospect">
                    <Setter Property="Fill" Value="Blue"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Lead">
                    <Setter Property="Fill" Value="Purple"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Blocked">
                    <Setter Property="Fill" Value="Red"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل مرحلة الفرصة إلى لون -->
        <Style x:Key="OpportunityStageCard" TargetType="Border">
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Stage}" Value="Prospecting">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Stage}" Value="Qualification">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Stage}" Value="NeedsAnalysis">
                    <Setter Property="Background" Value="#F3E5F5"/>
                    <Setter Property="BorderBrush" Value="#9C27B0"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Stage}" Value="Proposal">
                    <Setter Property="Background" Value="#E0F2F1"/>
                    <Setter Property="BorderBrush" Value="#009688"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Stage}" Value="Negotiation">
                    <Setter Property="Background" Value="#EFEBE9"/>
                    <Setter Property="BorderBrush" Value="#795548"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Stage}" Value="Closing">
                    <Setter Property="Background" Value="#E8F5E8"/>
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="AccountGroup" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="لوحة تحكم CRM المتكاملة" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding AddCustomerCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountPlus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة عميل"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding AddOpportunityCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="TrendingUp" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="فرصة جديدة"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding CreateCampaignCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Bullhorn" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="حملة تسويقية"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات سريعة -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي العملاء -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AccountGroup" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalCustomers}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي العملاء" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- العملاء النشطون -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AccountCheck" Width="32" Height="32" 
                                           Foreground="Green"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ActiveCustomers}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Green"/>
                    <TextBlock Text="عملاء نشطون" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- الفرص المفتوحة -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="TrendingUp" Width="32" Height="32" 
                                           Foreground="Orange"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding OpenOpportunities}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Orange"/>
                    <TextBlock Text="فرص مفتوحة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- قيمة الفرص -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CurrencyUsd" Width="32" Height="32" 
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalOpportunityValue, StringFormat='{}{0:C}'}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="قيمة الفرص" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- الحملات النشطة -->
                <StackPanel Grid.Column="4" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Bullhorn" Width="32" Height="32" 
                                           Foreground="Purple"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ActiveCampaigns}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Purple"/>
                    <TextBlock Text="حملات نشطة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- معدل التحويل -->
                <StackPanel Grid.Column="5" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ChartLine" Width="32" Height="32" 
                                           Foreground="Teal"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ConversionRate, StringFormat='{}{0:F1}%'}" 
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Teal"/>
                    <TextBlock Text="معدل التحويل" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط التنقل السريع -->
        <materialDesign:Card Grid.Row="2" Margin="16,8,16,8" Padding="12">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ViewCustomersCommand}"
                        Margin="8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AccountGroup" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إدارة العملاء"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ViewOpportunitiesCommand}"
                        Margin="8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="TrendingUp" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="الفرص التجارية"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ViewCampaignsCommand}"
                        Margin="8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Bullhorn" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="الحملات التسويقية"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ViewReportsCommand}"
                        Margin="8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ChartBox" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="التقارير"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ViewActivitiesCommand}"
                        Margin="8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ClockOutline" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="الأنشطة"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </materialDesign:Card>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="3" Margin="16,8,16,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- العملاء والفرص الحديثة -->
            <Grid Grid.Column="0" Margin="0,0,8,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- العملاء الجدد -->
                <materialDesign:Card Grid.Row="0" Margin="0,0,0,8" Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="AccountPlus" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="العملاء الجدد" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     VerticalAlignment="Center"/>
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                    Command="{Binding ViewAllCustomersCommand}"
                                    HorizontalAlignment="Right"
                                    Margin="8,0,0,0">
                                <materialDesign:PackIcon Kind="ArrowLeft" Width="16" Height="16"/>
                            </Button>
                        </StackPanel>

                        <!-- قائمة العملاء -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding RecentCustomers}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,0,0,8" 
                                              Padding="12"
                                              CornerRadius="4"
                                              Background="{DynamicResource MaterialDesignCardBackground}"
                                              BorderThickness="1"
                                              BorderBrush="{DynamicResource MaterialDesignDivider}">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- مؤشر الحالة -->
                                                <Ellipse Grid.Column="0" 
                                                       Width="12" Height="12"
                                                       Style="{StaticResource CustomerStatusIndicator}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>

                                                <!-- معلومات العميل -->
                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="{Binding DisplayName}" 
                                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                                             FontWeight="Bold"/>
                                                    <TextBlock Text="{Binding PrimaryEmail}" 
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             Opacity="0.7"/>
                                                    <TextBlock Text="{Binding FormattedCreatedAt}" 
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             Opacity="0.5"/>
                                                </StackPanel>

                                                <!-- إجراءات -->
                                                <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                            Command="{Binding DataContext.ViewCustomerCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                            CommandParameter="{Binding}"
                                                            ToolTip="عرض التفاصيل">
                                                        <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                                    </Button>
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                            Command="{Binding DataContext.ContactCustomerCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                            CommandParameter="{Binding}"
                                                            ToolTip="اتصال">
                                                        <materialDesign:PackIcon Kind="Phone" Width="16" Height="16"/>
                                                    </Button>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>

                <!-- الفرص الحديثة -->
                <materialDesign:Card Grid.Row="1" Margin="0,8,0,0" Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="TrendingUp" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="الفرص التجارية الحديثة" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     VerticalAlignment="Center"/>
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                    Command="{Binding ViewAllOpportunitiesCommand}"
                                    HorizontalAlignment="Right"
                                    Margin="8,0,0,0">
                                <materialDesign:PackIcon Kind="ArrowLeft" Width="16" Height="16"/>
                            </Button>
                        </StackPanel>

                        <!-- قائمة الفرص -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding RecentOpportunities}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,0,0,8" 
                                              Padding="12"
                                              CornerRadius="4"
                                              Style="{StaticResource OpportunityStageCard}">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- معلومات الفرصة -->
                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="{Binding Title}" 
                                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                                             FontWeight="Bold"/>
                                                    <TextBlock Text="{Binding CustomerName}" 
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             Opacity="0.7"/>
                                                    <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                                        <TextBlock Text="{Binding FormattedEstimatedValue}" 
                                                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                                 Foreground="{DynamicResource SecondaryHueMidBrush}"
                                                                 FontWeight="Bold"/>
                                                        <TextBlock Text=" • " 
                                                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                                 Margin="4,0"/>
                                                        <TextBlock Text="{Binding FormattedProbability}" 
                                                                 Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                                    </StackPanel>
                                                </StackPanel>

                                                <!-- مرحلة الفرصة -->
                                                <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                                    <materialDesign:Chip Content="{Binding StageDisplay}" 
                                                                       FontSize="10"
                                                                       Margin="0,0,0,4"/>
                                                    <TextBlock Text="{Binding FormattedExpectedCloseDate}" 
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             HorizontalAlignment="Right"
                                                             Opacity="0.7"/>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- الأنشطة والتذكيرات -->
            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                <!-- الأنشطة الحديثة -->
                <materialDesign:Card Padding="16" Margin="0,0,0,16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="ClockOutline" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="الأنشطة الحديثة" 
                                     Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- قائمة الأنشطة -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" MaxHeight="200">
                            <ItemsControl ItemsSource="{Binding RecentActivities}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,0,0,8" 
                                              Padding="8"
                                              CornerRadius="4"
                                              Background="{DynamicResource MaterialDesignCardBackground}"
                                              BorderThickness="1"
                                              BorderBrush="{DynamicResource MaterialDesignDivider}">
                                            <StackPanel>
                                                <TextBlock Text="{Binding ActivityType}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         FontWeight="Bold"/>
                                                <TextBlock Text="{Binding Description}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         TextWrapping="Wrap"
                                                         Margin="0,2,0,0"/>
                                                <TextBlock Text="{Binding FormattedActivityDate}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         Opacity="0.5"
                                                         Margin="0,2,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>

                <!-- التذكيرات والمتابعات -->
                <materialDesign:Card Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Bell" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="التذكيرات والمتابعات" 
                                     Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- قائمة التذكيرات -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" MaxHeight="200">
                            <ItemsControl ItemsSource="{Binding UpcomingFollowUps}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,0,0,8" 
                                              Padding="8"
                                              CornerRadius="4"
                                              Background="#FFF3E0"
                                              BorderThickness="1"
                                              BorderBrush="#FF9800">
                                            <StackPanel>
                                                <TextBlock Text="{Binding Title}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         FontWeight="Bold"/>
                                                <TextBlock Text="{Binding CustomerName}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         Margin="0,2,0,0"/>
                                                <TextBlock Text="{Binding FormattedNextFollowUpDate}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         Foreground="#FF9800"
                                                         FontWeight="Bold"
                                                         Margin="0,2,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>
            </StackPanel>
        </Grid>

        <!-- مؤشر التحميل -->
        <Grid Grid.RowSpan="4" 
              Background="White" 
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري التحميل..." 
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
