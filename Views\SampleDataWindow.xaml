<Window x:Class="SalesManagementSystem.Views.SampleDataWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إدارة البيانات الافتراضية" Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="إدارة البيانات الافتراضية" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- Description -->
        <TextBlock Grid.Row="1" TextWrapping="Wrap" Margin="0,0,0,20"
                   Text="يمكنك إنشاء 4 منتجات افتراضية لاختبار النظام. هذه المنتجات ستتضمن فئات ومخازن افتراضية أيضاً."/>

        <!-- Content -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Sample Products Info -->
                <GroupBox Header="المنتجات الافتراضية" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock Text="سيتم إنشاء المنتجات التالية:" FontWeight="Bold" Margin="0,0,0,10"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="1. " FontWeight="Bold"/>
                            <TextBlock Text="هاتف ذكي سامسونج - كود: ELEC001 - باركود: 1234567890123"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="2. " FontWeight="Bold"/>
                            <TextBlock Text="لابتوب ديل - كود: ELEC002 - باركود: 2345678901234"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="3. " FontWeight="Bold"/>
                            <TextBlock Text="قميص قطني رجالي - كود: CLOTH001 - باركود: 3456789012345"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="4. " FontWeight="Bold"/>
                            <TextBlock Text="أرز بسمتي - كود: FOOD001 - باركود: 4567890123456"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- Categories Info -->
                <GroupBox Header="الفئات الافتراضية" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock Text="• إلكترونيات"/>
                        <TextBlock Text="• ملابس"/>
                        <TextBlock Text="• أطعمة"/>
                        <TextBlock Text="• أدوات منزلية"/>
                    </StackPanel>
                </GroupBox>

                <!-- Warehouses Info -->
                <GroupBox Header="المخازن الافتراضية" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock Text="• المخزن الرئيسي"/>
                        <TextBlock Text="• مخزن الإلكترونيات"/>
                    </StackPanel>
                </GroupBox>

                <!-- Status -->
                <TextBlock Name="StatusTextBlock" 
                           Text="اضغط على 'إنشاء البيانات الافتراضية' للبدء"
                           FontStyle="Italic" 
                           HorizontalAlignment="Center"
                           Margin="0,20"/>

                <!-- Progress Bar -->
                <ProgressBar Name="ProgressBar" 
                             Height="10" 
                             Margin="0,10"
                             Visibility="Collapsed"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="CreateDataButton" 
                    Content="إنشاء البيانات الافتراضية" 
                    Click="CreateDataButton_Click"
                    Padding="20,10" 
                    Margin="10,0"
                    Background="#4CAF50"
                    Foreground="White"
                    FontWeight="Bold"/>
            
            <Button Name="ResetDataButton" 
                    Content="إعادة تعيين البيانات" 
                    Click="ResetDataButton_Click"
                    Padding="20,10" 
                    Margin="10,0"
                    Background="#FF9800"
                    Foreground="White"
                    FontWeight="Bold"/>
            
            <Button Name="TestProductsButton" 
                    Content="اختبار إضافة منتج" 
                    Click="TestProductsButton_Click"
                    Padding="20,10" 
                    Margin="10,0"
                    Background="#2196F3"
                    Foreground="White"
                    FontWeight="Bold"/>
            
            <Button Name="CloseButton" 
                    Content="إغلاق" 
                    Click="CloseButton_Click"
                    Padding="20,10" 
                    Margin="10,0"/>
        </StackPanel>
    </Grid>
</Window>
