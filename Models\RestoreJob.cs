using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج مهمة استعادة البيانات
    /// </summary>
    public class RestoreJob : INotifyPropertyChanged
    {
        private int _id;
        private string _restoreName = string.Empty;
        private string _restoreCode = string.Empty;
        private RestoreType _restoreType = RestoreType.Full;
        private RestoreStatus _status = RestoreStatus.Pending;
        private int _backupJobId;
        private int _backupHistoryId;
        private string _backupFilePath = string.Empty;
        private string _restoreDestination = string.Empty;
        private bool _overwriteExisting = true;
        private bool _verifyIntegrity = true;
        private bool _createRestorePoint = true;
        private DateTime _restorePointDate = DateTime.Now;
        private string _selectedFiles = string.Empty;
        private string _selectedTables = string.Empty;
        private DateTime _requestedAt = DateTime.Now;
        private DateTime? _startedAt;
        private DateTime? _completedAt;
        private TimeSpan _estimatedDuration = TimeSpan.FromMinutes(30);
        private TimeSpan? _actualDuration;
        private long _totalSize;
        private long _restoredSize;
        private int _totalFiles;
        private int _restoredFiles;
        private double _progressPercentage;
        private string _currentOperation = string.Empty;
        private string _errorMessage = string.Empty;
        private string _warningMessages = string.Empty;
        private string _requestedBy = string.Empty;
        private string _approvedBy = string.Empty;
        private DateTime? _approvedAt;
        private string _approvalNotes = string.Empty;
        private bool _requiresApproval = true;
        private string _businessJustification = string.Empty;
        private RestorePriority _priority = RestorePriority.Medium;
        private ObservableCollection<RestoreItem> _restoreItems = new();
        private ObservableCollection<RestoreLog> _restoreLogs = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string RestoreName
        {
            get => _restoreName;
            set
            {
                if (_restoreName != value)
                {
                    _restoreName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string RestoreCode
        {
            get => _restoreCode;
            set
            {
                if (_restoreCode != value)
                {
                    _restoreCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public RestoreType RestoreType
        {
            get => _restoreType;
            set
            {
                if (_restoreType != value)
                {
                    _restoreType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(RestoreTypeDisplay));
                    OnPropertyChanged(nameof(RestoreTypeIcon));
                }
            }
        }

        public RestoreStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsRunning));
                    OnPropertyChanged(nameof(CanStart));
                    OnPropertyChanged(nameof(CanCancel));
                    OnPropertyChanged(nameof(IsCompleted));
                }
            }
        }

        public int BackupJobId
        {
            get => _backupJobId;
            set
            {
                if (_backupJobId != value)
                {
                    _backupJobId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int BackupHistoryId
        {
            get => _backupHistoryId;
            set
            {
                if (_backupHistoryId != value)
                {
                    _backupHistoryId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string BackupFilePath
        {
            get => _backupFilePath;
            set
            {
                if (_backupFilePath != value)
                {
                    _backupFilePath = value;
                    OnPropertyChanged();
                }
            }
        }

        public string RestoreDestination
        {
            get => _restoreDestination;
            set
            {
                if (_restoreDestination != value)
                {
                    _restoreDestination = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool OverwriteExisting
        {
            get => _overwriteExisting;
            set
            {
                if (_overwriteExisting != value)
                {
                    _overwriteExisting = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool VerifyIntegrity
        {
            get => _verifyIntegrity;
            set
            {
                if (_verifyIntegrity != value)
                {
                    _verifyIntegrity = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool CreateRestorePoint
        {
            get => _createRestorePoint;
            set
            {
                if (_createRestorePoint != value)
                {
                    _createRestorePoint = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime RestorePointDate
        {
            get => _restorePointDate;
            set
            {
                if (_restorePointDate != value)
                {
                    _restorePointDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedRestorePointDate));
                }
            }
        }

        public string SelectedFiles
        {
            get => _selectedFiles;
            set
            {
                if (_selectedFiles != value)
                {
                    _selectedFiles = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SelectedTables
        {
            get => _selectedTables;
            set
            {
                if (_selectedTables != value)
                {
                    _selectedTables = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime RequestedAt
        {
            get => _requestedAt;
            set
            {
                if (_requestedAt != value)
                {
                    _requestedAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedRequestedAt));
                }
            }
        }

        public DateTime? StartedAt
        {
            get => _startedAt;
            set
            {
                if (_startedAt != value)
                {
                    _startedAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedStartedAt));
                }
            }
        }

        public DateTime? CompletedAt
        {
            get => _completedAt;
            set
            {
                if (_completedAt != value)
                {
                    _completedAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCompletedAt));
                }
            }
        }

        public TimeSpan EstimatedDuration
        {
            get => _estimatedDuration;
            set
            {
                if (_estimatedDuration != value)
                {
                    _estimatedDuration = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedEstimatedDuration));
                }
            }
        }

        public TimeSpan? ActualDuration
        {
            get => _actualDuration;
            set
            {
                if (_actualDuration != value)
                {
                    _actualDuration = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedActualDuration));
                }
            }
        }

        public long TotalSize
        {
            get => _totalSize;
            set
            {
                if (_totalSize != value)
                {
                    _totalSize = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalSize));
                }
            }
        }

        public long RestoredSize
        {
            get => _restoredSize;
            set
            {
                if (_restoredSize != value)
                {
                    _restoredSize = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedRestoredSize));
                    UpdateProgress();
                }
            }
        }

        public int TotalFiles
        {
            get => _totalFiles;
            set
            {
                if (_totalFiles != value)
                {
                    _totalFiles = value;
                    OnPropertyChanged();
                }
            }
        }

        public int RestoredFiles
        {
            get => _restoredFiles;
            set
            {
                if (_restoredFiles != value)
                {
                    _restoredFiles = value;
                    OnPropertyChanged();
                    UpdateProgress();
                }
            }
        }

        public double ProgressPercentage
        {
            get => _progressPercentage;
            set
            {
                if (_progressPercentage != value)
                {
                    _progressPercentage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedProgressPercentage));
                }
            }
        }

        public string CurrentOperation
        {
            get => _currentOperation;
            set
            {
                if (_currentOperation != value)
                {
                    _currentOperation = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasError));
                }
            }
        }

        public string WarningMessages
        {
            get => _warningMessages;
            set
            {
                if (_warningMessages != value)
                {
                    _warningMessages = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasWarnings));
                }
            }
        }

        public string RequestedBy
        {
            get => _requestedBy;
            set
            {
                if (_requestedBy != value)
                {
                    _requestedBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ApprovedBy
        {
            get => _approvedBy;
            set
            {
                if (_approvedBy != value)
                {
                    _approvedBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? ApprovedAt
        {
            get => _approvedAt;
            set
            {
                if (_approvedAt != value)
                {
                    _approvedAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedApprovedAt));
                    OnPropertyChanged(nameof(IsApproved));
                }
            }
        }

        public string ApprovalNotes
        {
            get => _approvalNotes;
            set
            {
                if (_approvalNotes != value)
                {
                    _approvalNotes = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool RequiresApproval
        {
            get => _requiresApproval;
            set
            {
                if (_requiresApproval != value)
                {
                    _requiresApproval = value;
                    OnPropertyChanged();
                }
            }
        }

        public string BusinessJustification
        {
            get => _businessJustification;
            set
            {
                if (_businessJustification != value)
                {
                    _businessJustification = value;
                    OnPropertyChanged();
                }
            }
        }

        public RestorePriority Priority
        {
            get => _priority;
            set
            {
                if (_priority != value)
                {
                    _priority = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PriorityDisplay));
                    OnPropertyChanged(nameof(PriorityColor));
                }
            }
        }

        public ObservableCollection<RestoreItem> RestoreItems
        {
            get => _restoreItems;
            set
            {
                if (_restoreItems != value)
                {
                    _restoreItems = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<RestoreLog> RestoreLogs
        {
            get => _restoreLogs;
            set
            {
                if (_restoreLogs != value)
                {
                    _restoreLogs = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsRunning => Status == RestoreStatus.Running;
        public bool CanStart => Status == RestoreStatus.Pending || Status == RestoreStatus.Approved;
        public bool CanCancel => Status == RestoreStatus.Running || Status == RestoreStatus.Pending;
        public bool IsCompleted => Status == RestoreStatus.Completed || Status == RestoreStatus.Failed;
        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);
        public bool HasWarnings => !string.IsNullOrEmpty(WarningMessages);
        public bool IsApproved => ApprovedAt.HasValue;

        // Display Properties
        public string RestoreTypeDisplay
        {
            get
            {
                return RestoreType switch
                {
                    RestoreType.Full => "استعادة كاملة",
                    RestoreType.Selective => "استعادة انتقائية",
                    RestoreType.Database => "استعادة قاعدة البيانات",
                    RestoreType.Files => "استعادة الملفات",
                    RestoreType.System => "استعادة النظام",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    RestoreStatus.Pending => "في الانتظار",
                    RestoreStatus.Approved => "معتمد",
                    RestoreStatus.Running => "قيد التشغيل",
                    RestoreStatus.Completed => "مكتمل",
                    RestoreStatus.Failed => "فشل",
                    RestoreStatus.Cancelled => "ملغي",
                    RestoreStatus.Rejected => "مرفوض",
                    _ => "غير محدد"
                };
            }
        }

        public string PriorityDisplay
        {
            get
            {
                return Priority switch
                {
                    RestorePriority.Low => "منخفض",
                    RestorePriority.Medium => "متوسط",
                    RestorePriority.High => "عالي",
                    RestorePriority.Critical => "حرج",
                    _ => "غير محدد"
                };
            }
        }

        public string RestoreTypeIcon
        {
            get
            {
                return RestoreType switch
                {
                    RestoreType.Full => "DatabaseImport",
                    RestoreType.Selective => "DatabaseSearch",
                    RestoreType.Database => "Database",
                    RestoreType.Files => "FileRestore",
                    RestoreType.System => "Restore",
                    _ => "DatabaseRestore"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    RestoreStatus.Pending => "Orange",
                    RestoreStatus.Approved => "Blue",
                    RestoreStatus.Running => "Purple",
                    RestoreStatus.Completed => "Green",
                    RestoreStatus.Failed => "Red",
                    RestoreStatus.Cancelled => "Gray",
                    RestoreStatus.Rejected => "Red",
                    _ => "Gray"
                };
            }
        }

        public string PriorityColor
        {
            get
            {
                return Priority switch
                {
                    RestorePriority.Low => "Green",
                    RestorePriority.Medium => "Orange",
                    RestorePriority.High => "Red",
                    RestorePriority.Critical => "Purple",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedRestorePointDate => RestorePointDate.ToString("dd/MM/yyyy HH:mm");
        public string FormattedRequestedAt => RequestedAt.ToString("dd/MM/yyyy HH:mm");
        public string FormattedStartedAt => StartedAt?.ToString("dd/MM/yyyy HH:mm") ?? "لم يبدأ";
        public string FormattedCompletedAt => CompletedAt?.ToString("dd/MM/yyyy HH:mm") ?? "لم يكتمل";
        public string FormattedApprovedAt => ApprovedAt?.ToString("dd/MM/yyyy HH:mm") ?? "غير معتمد";
        public string FormattedEstimatedDuration => EstimatedDuration.ToString(@"hh\:mm\:ss");
        public string FormattedActualDuration => ActualDuration?.ToString(@"hh\:mm\:ss") ?? "غير محدد";
        public string FormattedTotalSize => FormatFileSize(TotalSize);
        public string FormattedRestoredSize => FormatFileSize(RestoredSize);
        public string FormattedProgressPercentage => $"{ProgressPercentage:F1}%";

        #endregion

        #region Methods

        private void UpdateProgress()
        {
            if (TotalSize > 0)
                ProgressPercentage = (double)RestoredSize / TotalSize * 100;
            else if (TotalFiles > 0)
                ProgressPercentage = (double)RestoredFiles / TotalFiles * 100;
            else
                ProgressPercentage = 0;
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";
            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت" };
            int order = 0;
            double size = bytes;
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            return $"{size:F2} {sizes[order]}";
        }

        public void Approve(string approvedBy, string notes = "")
        {
            Status = RestoreStatus.Approved;
            ApprovedBy = approvedBy;
            ApprovedAt = DateTime.Now;
            ApprovalNotes = notes;
        }

        public void Reject(string rejectedBy, string reason)
        {
            Status = RestoreStatus.Rejected;
            ApprovedBy = rejectedBy;
            ApprovedAt = DateTime.Now;
            ApprovalNotes = $"مرفوض: {reason}";
        }

        public void Start()
        {
            Status = RestoreStatus.Running;
            StartedAt = DateTime.Now;
            CurrentOperation = "بدء عملية الاستعادة...";
        }

        public void Complete()
        {
            Status = RestoreStatus.Completed;
            CompletedAt = DateTime.Now;
            ActualDuration = CompletedAt - StartedAt;
            ProgressPercentage = 100;
            CurrentOperation = "تمت الاستعادة بنجاح";
        }

        public void Fail(string errorMessage)
        {
            Status = RestoreStatus.Failed;
            CompletedAt = DateTime.Now;
            ActualDuration = CompletedAt - StartedAt;
            ErrorMessage = errorMessage;
            CurrentOperation = "فشلت عملية الاستعادة";
        }

        public void Cancel()
        {
            Status = RestoreStatus.Cancelled;
            CompletedAt = DateTime.Now;
            ActualDuration = CompletedAt - StartedAt;
            CurrentOperation = "تم إلغاء عملية الاستعادة";
        }

        public void AddLog(string message, RestoreLogLevel level = RestoreLogLevel.Info)
        {
            RestoreLogs.Add(new RestoreLog
            {
                RestoreJobId = Id,
                Message = message,
                Level = level,
                Timestamp = DateTime.Now
            });
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// عنصر الاستعادة
    /// </summary>
    public class RestoreItem
    {
        public int Id { get; set; }
        public int RestoreJobId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemPath { get; set; } = string.Empty;
        public RestoreItemType ItemType { get; set; } = RestoreItemType.File;
        public long ItemSize { get; set; }
        public bool IsSelected { get; set; } = true;
        public RestoreItemStatus Status { get; set; } = RestoreItemStatus.Pending;
        public string ErrorMessage { get; set; } = string.Empty;

        public string ItemTypeDisplay
        {
            get
            {
                return ItemType switch
                {
                    RestoreItemType.File => "ملف",
                    RestoreItemType.Folder => "مجلد",
                    RestoreItemType.Database => "قاعدة بيانات",
                    RestoreItemType.Table => "جدول",
                    RestoreItemType.Registry => "سجل النظام",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    RestoreItemStatus.Pending => "في الانتظار",
                    RestoreItemStatus.Restoring => "قيد الاستعادة",
                    RestoreItemStatus.Completed => "مكتمل",
                    RestoreItemStatus.Failed => "فشل",
                    RestoreItemStatus.Skipped => "تم التخطي",
                    _ => "غير محدد"
                };
            }
        }

        public string FormattedItemSize => FormatFileSize(ItemSize);

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";
            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت" };
            int order = 0;
            double size = bytes;
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            return $"{size:F2} {sizes[order]}";
        }
    }

    /// <summary>
    /// سجل الاستعادة
    /// </summary>
    public class RestoreLog
    {
        public int Id { get; set; }
        public int RestoreJobId { get; set; }
        public string Message { get; set; } = string.Empty;
        public RestoreLogLevel Level { get; set; } = RestoreLogLevel.Info;
        public DateTime Timestamp { get; set; } = DateTime.Now;

        public string LevelDisplay
        {
            get
            {
                return Level switch
                {
                    RestoreLogLevel.Info => "معلومات",
                    RestoreLogLevel.Warning => "تحذير",
                    RestoreLogLevel.Error => "خطأ",
                    RestoreLogLevel.Success => "نجح",
                    _ => "غير محدد"
                };
            }
        }

        public string LevelColor
        {
            get
            {
                return Level switch
                {
                    RestoreLogLevel.Info => "Blue",
                    RestoreLogLevel.Warning => "Orange",
                    RestoreLogLevel.Error => "Red",
                    RestoreLogLevel.Success => "Green",
                    _ => "Gray"
                };
            }
        }

        public string FormattedTimestamp => Timestamp.ToString("HH:mm:ss");
    }

    #endregion

    #region Enums

    public enum RestoreType
    {
        Full,           // استعادة كاملة
        Selective,      // استعادة انتقائية
        Database,       // استعادة قاعدة البيانات
        Files,          // استعادة الملفات
        System          // استعادة النظام
    }

    public enum RestoreStatus
    {
        Pending,        // في الانتظار
        Approved,       // معتمد
        Running,        // قيد التشغيل
        Completed,      // مكتمل
        Failed,         // فشل
        Cancelled,      // ملغي
        Rejected        // مرفوض
    }

    public enum RestorePriority
    {
        Low,            // منخفض
        Medium,         // متوسط
        High,           // عالي
        Critical        // حرج
    }

    public enum RestoreItemType
    {
        File,           // ملف
        Folder,         // مجلد
        Database,       // قاعدة بيانات
        Table,          // جدول
        Registry        // سجل النظام
    }

    public enum RestoreItemStatus
    {
        Pending,        // في الانتظار
        Restoring,      // قيد الاستعادة
        Completed,      // مكتمل
        Failed,         // فشل
        Skipped         // تم التخطي
    }

    public enum RestoreLogLevel
    {
        Info,           // معلومات
        Warning,        // تحذير
        Error,          // خطأ
        Success         // نجح
    }

    #endregion

    #region Validation

    public class RestoreJobValidator : AbstractValidator<RestoreJob>
    {
        public RestoreJobValidator()
        {
            RuleFor(r => r.RestoreName)
                .NotEmpty().WithMessage("اسم الاستعادة مطلوب")
                .MaximumLength(200).WithMessage("اسم الاستعادة لا يمكن أن يتجاوز 200 حرف");

            RuleFor(r => r.RestoreCode)
                .NotEmpty().WithMessage("كود الاستعادة مطلوب")
                .MaximumLength(50).WithMessage("كود الاستعادة لا يمكن أن يتجاوز 50 حرف");

            RuleFor(r => r.BackupFilePath)
                .NotEmpty().WithMessage("مسار ملف النسخة الاحتياطية مطلوب");

            RuleFor(r => r.RestoreDestination)
                .NotEmpty().WithMessage("وجهة الاستعادة مطلوبة");

            RuleFor(r => r.BusinessJustification)
                .NotEmpty().When(r => r.RequiresApproval)
                .WithMessage("المبرر التجاري مطلوب للاستعادة التي تحتاج موافقة");

            RuleFor(r => r.RequestedBy)
                .NotEmpty().WithMessage("طالب الاستعادة مطلوب");
        }
    }

    #endregion
}
