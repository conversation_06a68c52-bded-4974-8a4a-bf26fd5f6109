<UserControl x:Class="SalesManagementSystem.Views.AIDashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- تحويل حالة النموذج إلى لون -->
        <Style x:Key="ModelStatusCard" TargetType="Border">
            <Setter Property="Background" Value="#FFF3E0"/>
            <Setter Property="BorderBrush" Value="#FF9800"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Training">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Trained">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Deployed">
                    <Setter Property="Background" Value="#E8F5E8"/>
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Failed">
                    <Setter Property="Background" Value="#FFEBEE"/>
                    <Setter Property="BorderBrush" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Deprecated">
                    <Setter Property="Background" Value="#FAFAFA"/>
                    <Setter Property="BorderBrush" Value="#9E9E9E"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل نوع النموذج إلى أيقونة -->
        <Style x:Key="ModelTypeIcon" TargetType="materialDesign:PackIcon">
            <Setter Property="Kind" Value="Brain"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Type}" Value="Prediction">
                    <Setter Property="Kind" Value="Crystal"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Classification">
                    <Setter Property="Kind" Value="SortVariant"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Regression">
                    <Setter Property="Kind" Value="TrendingUp"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Clustering">
                    <Setter Property="Kind" Value="Scatter"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Recommendation">
                    <Setter Property="Kind" Value="ThumbUp"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Anomaly">
                    <Setter Property="Kind" Value="AlertCircle"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="NLP">
                    <Setter Property="Kind" Value="MessageText"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="TimeSeries">
                    <Setter Property="Kind" Value="ChartTimeline"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="DeepLearning">
                    <Setter Property="Kind" Value="Brain"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Reinforcement">
                    <Setter Property="Kind" Value="Robot"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Brain" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="لوحة تحكم الذكاء الاصطناعي والتحليلات" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding CreateModelCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="نموذج جديد"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RunPredictionCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Crystal" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تشغيل تنبؤ"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding AnalyzeDataCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartBox" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحليل البيانات"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات الذكاء الاصطناعي -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي النماذج -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Brain" Width="28" Height="28" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalModels}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي النماذج" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- النماذج المنشورة -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CloudUpload" Width="28" Height="28" 
                                           Foreground="Green"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding DeployedModels}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Green"/>
                    <TextBlock Text="نماذج منشورة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- التنبؤات اليومية -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Crystal" Width="28" Height="28" 
                                           Foreground="Orange"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding DailyPredictions}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Orange"/>
                    <TextBlock Text="تنبؤات اليوم" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- متوسط الدقة -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Target" Width="28" Height="28" 
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding AverageAccuracy, StringFormat='{}{0:F1}%'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="متوسط الدقة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- التحليلات المكتملة -->
                <StackPanel Grid.Column="4" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ChartBox" Width="28" Height="28" 
                                           Foreground="Purple"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding CompletedAnalyses}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Purple"/>
                    <TextBlock Text="تحليلات مكتملة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- وقت المعالجة -->
                <StackPanel Grid.Column="5" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Speedometer" Width="28" Height="28" 
                                           Foreground="Teal"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding AverageProcessingTime}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Teal"/>
                    <TextBlock Text="متوسط المعالجة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط التنقل السريع -->
        <materialDesign:Card Grid.Row="2" Margin="16,8,16,8" Padding="12">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ViewModelsCommand}"
                        Margin="8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Brain" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إدارة النماذج"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ViewPredictionsCommand}"
                        Margin="8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Crystal" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="التنبؤات"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ViewAnalyticsCommand}"
                        Margin="8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ChartBox" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="التحليلات"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ViewInsightsCommand}"
                        Margin="8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Lightbulb" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="الرؤى الذكية"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ViewAlertsCommand}"
                        Margin="8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Bell" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="التنبيهات الذكية"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </materialDesign:Card>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="3" Margin="16,8,16,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- النماذج والتحليلات -->
            <Grid Grid.Column="0" Margin="0,0,8,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- النماذج النشطة -->
                <materialDesign:Card Grid.Row="0" Margin="0,0,0,8" Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Brain" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="النماذج النشطة" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     VerticalAlignment="Center"/>
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                    Command="{Binding ViewAllModelsCommand}"
                                    HorizontalAlignment="Right"
                                    Margin="8,0,0,0">
                                <materialDesign:PackIcon Kind="ArrowLeft" Width="16" Height="16"/>
                            </Button>
                        </StackPanel>

                        <!-- قائمة النماذج -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding ActiveModels}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,0,0,8" 
                                              Padding="12"
                                              CornerRadius="4"
                                              Style="{StaticResource ModelStatusCard}">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- أيقونة النموذج -->
                                                <materialDesign:PackIcon Grid.Column="0" 
                                                                       Style="{StaticResource ModelTypeIcon}"
                                                                       Width="20" Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,12,0"/>

                                                <!-- معلومات النموذج -->
                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="{Binding Name}" 
                                                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                                                             FontWeight="Bold"/>
                                                    <TextBlock Text="{Binding TypeDisplay}" 
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             Opacity="0.7"/>
                                                    <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                                        <TextBlock Text="الدقة: " 
                                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                                        <TextBlock Text="{Binding FormattedAccuracy}" 
                                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                                 Foreground="{Binding AccuracyColor}"
                                                                 FontWeight="Bold"/>
                                                    </StackPanel>
                                                </StackPanel>

                                                <!-- حالة النموذج -->
                                                <materialDesign:Chip Grid.Column="2" 
                                                                   Content="{Binding StatusDisplay}" 
                                                                   FontSize="10"
                                                                   Background="{Binding StatusColor}"
                                                                   VerticalAlignment="Center"
                                                                   Margin="8,0"/>

                                                <!-- إجراءات -->
                                                <StackPanel Grid.Column="3" Orientation="Horizontal">
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                            Command="{Binding DataContext.ViewModelCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                            CommandParameter="{Binding}"
                                                            ToolTip="عرض التفاصيل">
                                                        <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                                    </Button>
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                            Command="{Binding DataContext.RunModelCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                            CommandParameter="{Binding}"
                                                            ToolTip="تشغيل النموذج">
                                                        <materialDesign:PackIcon Kind="Play" Width="16" Height="16"/>
                                                    </Button>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>

                <!-- التحليلات الحديثة -->
                <materialDesign:Card Grid.Row="1" Margin="0,8,0,0" Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="ChartBox" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="التحليلات الحديثة" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     VerticalAlignment="Center"/>
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                    Command="{Binding ViewAllAnalyticsCommand}"
                                    HorizontalAlignment="Right"
                                    Margin="8,0,0,0">
                                <materialDesign:PackIcon Kind="ArrowLeft" Width="16" Height="16"/>
                            </Button>
                        </StackPanel>

                        <!-- رسم بياني للتحليلات -->
                        <lvc:CartesianChart Grid.Row="1" 
                                          Series="{Binding AnalyticsSeries}" 
                                          AxisX="{Binding AnalyticsAxisX}"
                                          AxisY="{Binding AnalyticsAxisY}"
                                          LegendLocation="Bottom"/>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- الرؤى والتنبيهات -->
            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                <!-- الرؤى الذكية -->
                <materialDesign:Card Padding="16" Margin="0,0,0,16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Lightbulb" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="الرؤى الذكية" 
                                     Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- قائمة الرؤى -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" MaxHeight="200">
                            <ItemsControl ItemsSource="{Binding SmartInsights}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,0,0,8" 
                                              Padding="8"
                                              CornerRadius="4"
                                              Background="#E3F2FD"
                                              BorderThickness="1"
                                              BorderBrush="#2196F3">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                    <materialDesign:PackIcon Kind="Lightbulb" Width="16" Height="16" 
                                                                           Foreground="#2196F3" VerticalAlignment="Center"/>
                                                    <TextBlock Text="{Binding Title}" 
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             FontWeight="Bold"
                                                             Margin="8,0,0,0"/>
                                                </StackPanel>
                                                <TextBlock Text="{Binding Description}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         TextWrapping="Wrap"/>
                                                <TextBlock Text="{Binding FormattedCreatedAt}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         Opacity="0.5"
                                                         Margin="0,4,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>

                <!-- التنبيهات الذكية -->
                <materialDesign:Card Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Bell" Width="20" Height="20" 
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="التنبيهات الذكية" 
                                     Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- قائمة التنبيهات -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" MaxHeight="200">
                            <ItemsControl ItemsSource="{Binding SmartAlerts}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,0,0,8" 
                                              Padding="8"
                                              CornerRadius="4"
                                              Background="{Binding SeverityBackground}"
                                              BorderThickness="1"
                                              BorderBrush="{Binding SeverityColor}">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                    <materialDesign:PackIcon Kind="{Binding SeverityIcon}" Width="16" Height="16" 
                                                                           Foreground="{Binding SeverityColor}" VerticalAlignment="Center"/>
                                                    <TextBlock Text="{Binding SeverityDisplay}" 
                                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                             FontWeight="Bold"
                                                             Margin="8,0,0,0"/>
                                                </StackPanel>
                                                <TextBlock Text="{Binding Message}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         TextWrapping="Wrap"/>
                                                <TextBlock Text="{Binding FormattedCreatedAt}" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         Opacity="0.5"
                                                         Margin="0,4,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>
            </StackPanel>
        </Grid>

        <!-- مؤشر التحميل -->
        <Grid Grid.RowSpan="4" 
              Background="White" 
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري معالجة البيانات..." 
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
