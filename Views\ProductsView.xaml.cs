using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.Views.Dialogs;

namespace SalesManagementSystem.Views
{
    public partial class ProductsView : UserControl
    {
        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private List<ProductViewModel> _allProducts = new();
        private List<Category> _categories = new();

        public ProductsView()
        {
            InitializeComponent();

            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);

            Loaded += ProductsView_Loaded;
        }

        private async void ProductsView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                // Load categories for filter
                await LoadCategoriesAsync();

                // Load products
                await LoadProductsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadCategoriesAsync()
        {
            _categories = (await _productService.GetAllCategoriesAsync()).ToList();

            // Add "All Categories" option
            var allCategoriesItem = new Category { Id = 0, Name = "جميع الفئات" };
            _categories.Insert(0, allCategoriesItem);

            CategoryComboBox.ItemsSource = _categories;
            CategoryComboBox.DisplayMemberPath = "Name";
            CategoryComboBox.SelectedValuePath = "Id";
            CategoryComboBox.SelectedIndex = 0;
        }

        private async Task LoadProductsAsync()
        {
            var products = await _productService.GetAllProductsAsync();

            _allProducts = products.Select(p => new ProductViewModel
            {
                Id = p.Id,
                Code = p.Code,
                Name = p.Name,
                Description = p.Description,
                CategoryId = p.CategoryId,
                CategoryName = p.CategoryName ?? "غير محدد",
                PurchasePrice = p.PurchasePrice,
                SellingPrice = p.SalePrice,
                SalePrice2 = p.SalePrice2,
                Quantity = p.Quantity,
                MinQuantity = p.MinQuantity,
                WarehouseId = p.WarehouseId,
                WarehouseName = p.WarehouseName ?? "غير محدد",
                IsLowStock = p.Quantity <= p.MinQuantity,
                CreatedAt = p.CreatedAt,
                UpdatedAt = p.UpdatedAt
            }).ToList();

            ApplyFilters();
        }

        private void ApplyFilters()
        {
            var filteredProducts = _allProducts.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filteredProducts = filteredProducts.Where(p =>
                    p.Name.ToLower().Contains(searchTerm) ||
                    p.Code.ToLower().Contains(searchTerm) ||
                    p.CategoryName.ToLower().Contains(searchTerm));
            }

            // Apply category filter
            if (CategoryComboBox.SelectedValue != null && (int)CategoryComboBox.SelectedValue != 0)
            {
                var selectedCategoryId = (int)CategoryComboBox.SelectedValue;
                filteredProducts = filteredProducts.Where(p => p.CategoryId == selectedCategoryId);
            }

            ProductsDataGrid.ItemsSource = filteredProducts.ToList();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void CategoryComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private async void AddProductButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new Views.Dialogs.ProductDialog();
                if (dialog.ShowDialog() == true)
                {
                    await LoadProductsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة المنتج: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void EditProduct_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProductViewModel product)
            {
                var productToEdit = new Product
                {
                    Id = product.Id,
                    Code = product.Code,
                    Name = product.Name,
                    Description = product.Description,
                    CategoryId = product.CategoryId,
                    PurchasePrice = product.PurchasePrice,
                    SalePrice = product.SellingPrice,
                    SalePrice2 = product.SalePrice2,
                    Quantity = product.Quantity,
                    MinQuantity = product.MinQuantity,
                    WarehouseId = product.WarehouseId,
                    WarehouseName = product.WarehouseName,
                    CreatedAt = product.CreatedAt,
                    UpdatedAt = product.UpdatedAt
                };

                var dialog = new Views.Dialogs.ProductDialog(productToEdit);
                if (dialog.ShowDialog() == true)
                {
                    await LoadProductsAsync();
                }
            }
        }

        private async void DeleteProduct_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProductViewModel product)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المنتج '{product.Name}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _productService.DeleteProductAsync(product.Id);
                        await LoadProductsAsync();

                        MessageBox.Show("تم حذف المنتج بنجاح", "نجح",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private async void UpdateDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد تحديث قاعدة البيانات لإضافة الأعمدة المفقودة؟\n\nسيتم إضافة:\n- عمود سعر البيع الثاني\n- عمود الوحدة\n- عمود الباركود\n- عمود حالة النشاط\n- عمود تتبع المخزون\n- عمود معرف المخزن",
                    "تحديث قاعدة البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // Force database schema update by creating a new DatabaseService instance
                    // This will trigger the UpdateDatabaseSchema method in the constructor
                    var dbService = new Services.DatabaseService();

                    MessageBox.Show(
                        "تم تحديث قاعدة البيانات بنجاح!\n\nيمكنك الآن استخدام جميع الميزات الجديدة.",
                        "نجح التحديث",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    // Refresh the products list
                    await LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تحديث قاعدة البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }
    }

    public class ProductViewModel
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int? CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public decimal PurchasePrice { get; set; }
        public decimal SellingPrice { get; set; }
        public decimal SalePrice2 { get; set; }
        public int Quantity { get; set; }
        public int MinQuantity { get; set; }
        public int? WarehouseId { get; set; }
        public string WarehouseName { get; set; } = string.Empty;
        public bool IsLowStock { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
