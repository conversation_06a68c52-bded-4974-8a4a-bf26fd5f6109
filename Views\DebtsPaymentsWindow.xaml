<Window x:Class="SalesManagementSystem.Views.DebtsPaymentsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الديون والمستحقات - نظام إدارة المبيعات"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                               Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#FFF5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#FFE91E63">
            <Border.Effect>
                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
            </Border.Effect>
            <Grid Margin="20,0">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="💳" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة الديون والمستحقات" FontSize="18"
                              FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center">
                    <TextBlock Text="إجمالي الديون: " FontSize="12" Foreground="LightPink"/>
                    <TextBlock x:Name="TotalDebtsLabel" Text="0.00 دج" FontSize="12" FontWeight="Bold" Foreground="Yellow"/>
                    <TextBlock Text=" | إجمالي المستحقات: " FontSize="12" Foreground="LightPink" Margin="20,0,0,0"/>
                    <TextBlock x:Name="TotalPayablesLabel" Text="0.00 دج" FontSize="12" FontWeight="Bold" Foreground="Yellow"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <TabControl Grid.Row="1" Margin="10">

            <!-- تبويب ديون العملاء -->
            <TabItem Header="💰 ديون العملاء">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- شريط البحث والفلترة -->
                    <Border Grid.Row="0" Background="White" CornerRadius="10" Margin="10" Padding="15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <TextBlock Text="🔍 البحث:" FontSize="12" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBox x:Name="CustomerSearchTextBox" Width="200" Height="30"
                                        VerticalAlignment="Center" Padding="5"
                                        TextChanged="CustomerSearch_TextChanged"/>

                                <TextBlock Text="الحالة:" FontSize="12" VerticalAlignment="Center" Margin="20,0,10,0"/>
                                <ComboBox x:Name="CustomerStatusComboBox" Width="120" Height="30"
                                         VerticalAlignment="Center" SelectionChanged="CustomerStatus_SelectionChanged"
                                         SelectedIndex="0">
                                    <ComboBoxItem Content="الكل"/>
                                    <ComboBoxItem Content="عليه ديون"/>
                                    <ComboBoxItem Content="مسدد"/>
                                </ComboBox>
                            </StackPanel>

                            <Button Grid.Column="1" Content="💰 دفعة جديدة"
                                   Style="{StaticResource ModernButtonStyle}"
                                   Background="#FF4CAF50" Click="NewCustomerPayment_Click" Width="120"/>

                            <Button Grid.Column="2" Content="📊 تقرير الديون"
                                   Style="{StaticResource ModernButtonStyle}"
                                   Background="#FF2196F3" Click="CustomerDebtsReport_Click" Width="120"/>
                        </Grid>
                    </Border>

                    <!-- جدول ديون العملاء -->
                    <Border Grid.Row="1" Background="White" CornerRadius="10" Margin="10">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.2"/>
                        </Border.Effect>
                        <DataGrid x:Name="CustomerDebtsDataGrid" AutoGenerateColumns="False"
                                 CanUserAddRows="False" CanUserDeleteRows="False"
                                 IsReadOnly="True" GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column" Margin="10"
                                 SelectionChanged="CustomerDebtsDataGrid_SelectionChanged">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم العميل" Binding="{Binding CustomerName}" Width="150"/>
                                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="120"/>
                                <DataGridTextColumn Header="إجمالي المشتريات" Binding="{Binding TotalPurchases, StringFormat='{}{0:F2} دج'}" Width="120"/>
                                <DataGridTextColumn Header="إجمالي المدفوع" Binding="{Binding TotalPaid, StringFormat='{}{0:F2} دج'}" Width="120"/>
                                <DataGridTextColumn Header="المبلغ المتبقي" Binding="{Binding RemainingDebt, StringFormat='{}{0:F2} دج'}" Width="120"/>
                                <DataGridTextColumn Header="آخر دفعة" Binding="{Binding LastPaymentDate, StringFormat='yyyy/MM/dd'}" Width="100"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                                <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <Button Content="💰" ToolTip="دفعة جديدة"
                                                       Style="{StaticResource ModernButtonStyle}"
                                                       Background="#FF4CAF50" Width="30" Height="25"
                                                       Click="PayCustomerDebt_Click" Margin="2"/>
                                                <Button Content="📋" ToolTip="تاريخ الدفعات"
                                                       Style="{StaticResource ModernButtonStyle}"
                                                       Background="#FF2196F3" Width="30" Height="25"
                                                       Click="ViewCustomerPayments_Click" Margin="2"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Border>

                    <!-- إحصائيات ديون العملاء -->
                    <Border Grid.Row="2" Background="White" CornerRadius="10" Margin="10" Padding="15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Text="عدد العملاء المدينين" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="DebtorCustomersCountLabel" Text="0" FontSize="20" FontWeight="Bold"
                                          Foreground="#FFE91E63" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Text="إجمالي الديون" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalCustomerDebtsLabel" Text="0.00 دج" FontSize="20" FontWeight="Bold"
                                          Foreground="#FFF44336" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Text="أكبر دين" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="LargestDebtLabel" Text="0.00 دج" FontSize="20" FontWeight="Bold"
                                          Foreground="#FFFF9800" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                <TextBlock Text="متوسط الدين" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="AverageDebtLabel" Text="0.00 دج" FontSize="20" FontWeight="Bold"
                                          Foreground="#FF9C27B0" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>

            <!-- تبويب مستحقات الموردين -->
            <TabItem Header="🏭 مستحقات الموردين">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- شريط البحث والفلترة للموردين -->
                    <Border Grid.Row="0" Background="White" CornerRadius="10" Margin="10" Padding="15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <TextBlock Text="🔍 البحث:" FontSize="12" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBox x:Name="SupplierSearchTextBox" Width="200" Height="30"
                                        VerticalAlignment="Center" Padding="5"
                                        TextChanged="SupplierSearch_TextChanged"/>

                                <TextBlock Text="الحالة:" FontSize="12" VerticalAlignment="Center" Margin="20,0,10,0"/>
                                <ComboBox x:Name="SupplierStatusComboBox" Width="120" Height="30"
                                         VerticalAlignment="Center" SelectionChanged="SupplierStatus_SelectionChanged"
                                         SelectedIndex="0">
                                    <ComboBoxItem Content="الكل"/>
                                    <ComboBoxItem Content="مستحقات معلقة"/>
                                    <ComboBoxItem Content="مسدد"/>
                                </ComboBox>
                            </StackPanel>

                            <Button Grid.Column="1" Content="💰 دفعة للمورد"
                                   Style="{StaticResource ModernButtonStyle}"
                                   Background="#FF607D8B" Click="NewSupplierPayment_Click" Width="120"/>

                            <Button Grid.Column="2" Content="📊 تقرير المستحقات"
                                   Style="{StaticResource ModernButtonStyle}"
                                   Background="#FF2196F3" Click="SupplierPayablesReport_Click" Width="130"/>
                        </Grid>
                    </Border>

                    <!-- جدول مستحقات الموردين -->
                    <Border Grid.Row="1" Background="White" CornerRadius="10" Margin="10">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.2"/>
                        </Border.Effect>
                        <DataGrid x:Name="SupplierPayablesDataGrid" AutoGenerateColumns="False"
                                 CanUserAddRows="False" CanUserDeleteRows="False"
                                 IsReadOnly="True" GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column" Margin="10"
                                 SelectionChanged="SupplierPayablesDataGrid_SelectionChanged">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم المورد" Binding="{Binding SupplierName}" Width="150"/>
                                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="120"/>
                                <DataGridTextColumn Header="إجمالي المشتريات" Binding="{Binding TotalPurchases, StringFormat='{}{0:F2} دج'}" Width="120"/>
                                <DataGridTextColumn Header="إجمالي المدفوع" Binding="{Binding TotalPaid, StringFormat='{}{0:F2} دج'}" Width="120"/>
                                <DataGridTextColumn Header="المبلغ المتبقي" Binding="{Binding RemainingPayable, StringFormat='{}{0:F2} دج'}" Width="120"/>
                                <DataGridTextColumn Header="آخر دفعة" Binding="{Binding LastPaymentDate, StringFormat='yyyy/MM/dd'}" Width="100"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                                <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <Button Content="💰" ToolTip="دفعة للمورد"
                                                       Style="{StaticResource ModernButtonStyle}"
                                                       Background="#FF607D8B" Width="30" Height="25"
                                                       Click="PaySupplierPayable_Click" Margin="2"/>
                                                <Button Content="📋" ToolTip="تاريخ الدفعات"
                                                       Style="{StaticResource ModernButtonStyle}"
                                                       Background="#FF2196F3" Width="30" Height="25"
                                                       Click="ViewSupplierPayments_Click" Margin="2"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Border>

                    <!-- إحصائيات مستحقات الموردين -->
                    <Border Grid.Row="2" Background="White" CornerRadius="10" Margin="10" Padding="15">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.2"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Text="عدد الموردين" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="SuppliersWithPayablesCountLabel" Text="0" FontSize="20" FontWeight="Bold"
                                          Foreground="#FF607D8B" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Text="إجمالي المستحقات" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalSupplierPayablesLabel" Text="0.00 دج" FontSize="20" FontWeight="Bold"
                                          Foreground="#FFF44336" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Text="أكبر مستحق" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="LargestPayableLabel" Text="0.00 دج" FontSize="20" FontWeight="Bold"
                                          Foreground="#FFFF9800" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                <TextBlock Text="متوسط المستحق" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="AveragePayableLabel" Text="0.00 دج" FontSize="20" FontWeight="Bold"
                                          Foreground="#FF9C27B0" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- شريط الأزرار -->
        <Border Grid.Row="2" Background="White">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button x:Name="RefreshButton" Content="🔄 تحديث البيانات"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF4CAF50" Click="Refresh_Click" Width="130"/>

                <Button x:Name="ExportButton" Content="📤 تصدير التقرير"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF2196F3" Click="Export_Click" Width="130"/>

                <Button x:Name="CloseButton" Content="🚪 إغلاق"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF607D8B" Click="Close_Click" Width="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
