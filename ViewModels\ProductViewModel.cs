using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class ProductViewModel : BaseViewModel
    {
        #region Services

        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private readonly SimpleExportService _exportService;
        private readonly SimpleImportService _importService;

        #endregion

        #region Properties

        private ObservableCollection<Product> _products = new();
        public ObservableCollection<Product> Products
        {
            get => _products;
            set => SetProperty(ref _products, value);
        }

        private ObservableCollection<Product> _filteredProducts = new();
        public ObservableCollection<Product> FilteredProducts
        {
            get => _filteredProducts;
            set => SetProperty(ref _filteredProducts, value);
        }

        private ObservableCollection<Category> _categories = new();
        public ObservableCollection<Category> Categories
        {
            get => _categories;
            set => SetProperty(ref _categories, value);
        }

        private Product? _selectedProduct;
        public Product? SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                if (SetProperty(ref _selectedProduct, value))
                {
                    EditProductCommand.RaiseCanExecuteChanged();
                    DeleteProductCommand.RaiseCanExecuteChanged();
                }
            }
        }

        private string _searchText = string.Empty;
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    ApplyFilters();
                }
            }
        }

        private Category? _selectedCategory;
        public Category? SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                if (SetProperty(ref _selectedCategory, value))
                {
                    ApplyFilters();
                }
            }
        }

        private bool _showLowStockOnly;
        public bool ShowLowStockOnly
        {
            get => _showLowStockOnly;
            set
            {
                if (SetProperty(ref _showLowStockOnly, value))
                {
                    ApplyFilters();
                }
            }
        }

        private int _totalProducts;
        public int TotalProducts
        {
            get => _totalProducts;
            set => SetProperty(ref _totalProducts, value);
        }

        private int _lowStockProducts;
        public int LowStockProducts
        {
            get => _lowStockProducts;
            set => SetProperty(ref _lowStockProducts, value);
        }

        private decimal _totalValue;
        public decimal TotalValue
        {
            get => _totalValue;
            set => SetProperty(ref _totalValue, value);
        }

        #endregion

        #region Commands

        private DelegateCommand? _addProductCommand;
        public DelegateCommand AddProductCommand => _addProductCommand ??= new DelegateCommand(AddProduct);

        private DelegateCommand? _editProductCommand;
        public DelegateCommand EditProductCommand => _editProductCommand ??= new DelegateCommand(EditProduct, CanEditProduct);

        private DelegateCommand? _deleteProductCommand;
        public DelegateCommand DeleteProductCommand => _deleteProductCommand ??= new DelegateCommand(DeleteProduct, CanDeleteProduct);

        private DelegateCommand? _clearFiltersCommand;
        public DelegateCommand ClearFiltersCommand => _clearFiltersCommand ??= new DelegateCommand(ClearFilters);

        private DelegateCommand? _exportProductsCommand;
        public DelegateCommand ExportProductsCommand => _exportProductsCommand ??= new DelegateCommand(ExportProducts);

        private DelegateCommand? _importProductsCommand;
        public DelegateCommand ImportProductsCommand => _importProductsCommand ??= new DelegateCommand(ImportProducts);

        #endregion

        #region Constructor

        public ProductViewModel()
        {
            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);
            _exportService = new SimpleExportService();
            _importService = new SimpleImportService();

            _ = LoadDataAsync();
        }

        #endregion

        #region Methods

        protected override async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                ClearError();

                await LoadProductsAsync();
                await LoadCategoriesAsync();
                CalculateStatistics();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل البيانات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تحميل بيانات المنتجات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadProductsAsync()
        {
            var products = await _productService.GetAllProductsAsync();
            Products.Clear();

            foreach (var product in products)
            {
                Products.Add(product);
            }
        }

        private async Task LoadCategoriesAsync()
        {
            var categories = await _productService.GetAllCategoriesAsync();
            Categories.Clear();
            Categories.Add(new Category { Id = 0, Name = "جميع الفئات" });

            foreach (var category in categories)
            {
                Categories.Add(category);
            }

            SelectedCategory = Categories.FirstOrDefault();
        }

        private void CalculateStatistics()
        {
            TotalProducts = Products.Count;
            LowStockProducts = Products.Count(p => p.Quantity <= p.MinQuantity);
            TotalValue = Products.Sum(p => p.Quantity * p.PurchasePrice);
        }

        private void ApplyFilters()
        {
            var filtered = Products.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                filtered = filtered.Where(p =>
                    p.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    p.Code.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    (p.Description?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false));
            }

            // Apply category filter
            if (SelectedCategory != null && SelectedCategory.Id > 0)
            {
                filtered = filtered.Where(p => p.CategoryId == SelectedCategory.Id);
            }

            // Apply low stock filter
            if (ShowLowStockOnly)
            {
                filtered = filtered.Where(p => p.Quantity <= p.MinQuantity);
            }

            FilteredProducts.Clear();
            foreach (var product in filtered.OrderBy(p => p.Name))
            {
                FilteredProducts.Add(product);
            }
        }

        #endregion

        #region Command Handlers

        private void AddProduct()
        {
            var dialog = new Views.Dialogs.ProductDialog();
            if (dialog.ShowDialog() == true && dialog.Result != null)
            {
                _ = LoadDataAsync();
            }
        }

        private void EditProduct()
        {
            if (SelectedProduct != null)
            {
                var dialog = new Views.Dialogs.ProductDialog(SelectedProduct);
                if (dialog.ShowDialog() == true && dialog.Result != null)
                {
                    _ = LoadDataAsync();
                }
            }
        }

        private bool CanEditProduct()
        {
            return SelectedProduct != null;
        }

        private async void DeleteProduct()
        {
            if (SelectedProduct == null) return;

            try
            {
                var result = System.Windows.MessageBox.Show(
                    $"هل أنت متأكد من حذف المنتج '{SelectedProduct.Name}'؟",
                    "تأكيد الحذف",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    IsLoading = true;
                    await _productService.DeleteProductAsync(SelectedProduct.Id);

                    LoggingService.LogProductUpdate(SelectedProduct.Name, "حذف", $"تم حذف المنتج بنجاح");

                    await LoadDataAsync();

                    System.Windows.MessageBox.Show("تم حذف المنتج بنجاح", "نجح",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حذف المنتج: {ex.Message}");
                LoggingService.LogError(ex, $"خطأ في حذف المنتج: {SelectedProduct.Name}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanDeleteProduct()
        {
            return SelectedProduct != null;
        }

        private void ClearFilters()
        {
            SearchText = string.Empty;
            SelectedCategory = Categories.FirstOrDefault();
            ShowLowStockOnly = false;
        }

        private async void ExportProducts()
        {
            try
            {
                IsLoading = true;

                // إنشاء مسار الملف للتصدير
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "ملفات إكسل (*.xlsx)|*.xlsx|ملفات CSV (*.csv)|*.csv",
                    Title = "تصدير المنتجات",
                    FileName = $"Products_Export_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (dialog.ShowDialog() == true)
                {
                    // الحصول على جميع المنتجات
                    var products = await _productService.GetAllProductsAsync();

                    if (products == null || !products.Any())
                    {
                        System.Windows.MessageBox.Show("لا توجد منتجات للتصدير", "تنبيه",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                        return;
                    }

                    string extension = System.IO.Path.GetExtension(dialog.FileName).ToLower();

                    if (extension == ".xlsx")
                    {
                        // تصدير إلى ملف إكسل
                        await _exportService.ExportToExcelAsync(products, dialog.FileName, "المنتجات");
                    }
                    else if (extension == ".csv")
                    {
                        // تصدير إلى ملف CSV
                        await _exportService.ExportToCsvAsync(products, dialog.FileName);
                    }

                    System.Windows.MessageBox.Show("تم تصدير المنتجات بنجاح", "تم",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تصدير المنتجات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تصدير المنتجات");
                System.Windows.MessageBox.Show($"حدث خطأ أثناء تصدير المنتجات: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void ImportProducts()
        {
            try
            {
                IsLoading = true;

                // إنشاء مسار الملف للاستيراد
                var dialog = new Microsoft.Win32.OpenFileDialog
                {
                    Filter = "ملفات إكسل (*.xlsx)|*.xlsx|ملفات CSV (*.csv)|*.csv",
                    Title = "استيراد المنتجات"
                };

                if (dialog.ShowDialog() == true)
                {
                    string extension = System.IO.Path.GetExtension(dialog.FileName).ToLower();
                    List<Product> importedProducts = new List<Product>();

                    if (extension == ".xlsx")
                    {
                        // استيراد من ملف إكسل
                        importedProducts = await _importService.ImportProductsFromExcelAsync(dialog.FileName);
                    }
                    else if (extension == ".csv")
                    {
                        // استيراد من ملف CSV
                        importedProducts = await _importService.ImportProductsFromCsvAsync(dialog.FileName);
                    }

                    if (importedProducts == null || !importedProducts.Any())
                    {
                        System.Windows.MessageBox.Show("لم يتم العثور على منتجات في الملف المحدد", "تنبيه",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                        return;
                    }

                    // تأكيد من المستخدم
                    var result = System.Windows.MessageBox.Show(
                        $"تم العثور على {importedProducts.Count} منتج في الملف. هل تريد استيرادها؟",
                        "تأكيد الاستيراد",
                        System.Windows.MessageBoxButton.YesNo,
                        System.Windows.MessageBoxImage.Question);

                    if (result == System.Windows.MessageBoxResult.Yes)
                    {
                        // حفظ المنتجات المستوردة
                        int successCount = 0;
                        foreach (var product in importedProducts)
                        {
                            try
                            {
                                // التحقق من وجود المنتج
                                var existingProduct = await _productService.GetProductByBarcodeAsync(product.Barcode);

                                if (existingProduct != null)
                                {
                                    // تحديث المنتج الموجود
                                    product.Id = existingProduct.Id;
                                    await _productService.UpdateProductAsync(product);
                                }
                                else
                                {
                                    // إضافة منتج جديد
                                    await _productService.AddProductAsync(product);
                                }

                                successCount++;
                            }
                            catch (Exception ex)
                            {
                                LoggingService.LogError(ex, $"خطأ في استيراد المنتج {product.Name}");
                            }
                        }

                        // إعادة تحميل المنتجات
                        await LoadProductsAsync();

                        System.Windows.MessageBox.Show($"تم استيراد {successCount} من {importedProducts.Count} منتج بنجاح", "تم",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في استيراد المنتجات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في استيراد المنتجات");
                System.Windows.MessageBox.Show($"حدث خطأ أثناء استيراد المنتجات: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Public Methods

        public async Task OnProductSaved()
        {
            await LoadDataAsync();
        }

        #endregion
    }
}
