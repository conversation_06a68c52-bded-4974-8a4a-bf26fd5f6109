<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة نظام التقارير المتقدم - نظام إدارة المبيعات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 28px;
            font-weight: 600;
        }

        .header .actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-outline {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .filters {
            padding: 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr auto;
            gap: 16px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #2196F3;
        }

        .content {
            padding: 24px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
        }

        .stat-card.sales { border-left-color: #2196F3; }
        .stat-card.profit { border-left-color: #4CAF50; }
        .stat-card.orders { border-left-color: #FF9800; }
        .stat-card.average { border-left-color: #9C27B0; }

        .stat-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            color: #6c757d;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #212529;
        }

        .chart-section {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 32px;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-header h3 {
            color: #212529;
            font-size: 20px;
        }

        .chart-container {
            position: relative;
            height: 400px;
        }

        .data-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .data-header {
            background: #f8f9fa;
            padding: 20px 24px;
            border-bottom: 1px solid #e9ecef;
        }

        .data-header h3 {
            color: #212529;
            font-size: 18px;
        }

        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 16px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .data-table tbody tr:hover {
            background: #f8f9fa;
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .loading.show {
            display: flex;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .material-icons {
            font-size: 24px;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
        }

        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            color: #6c757d;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #2196F3;
            border-bottom-color: #2196F3;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 768px) {
            .filters {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <span class="material-icons">assessment</span>
                التقارير المتقدمة
            </h1>
            <div class="actions">
                <button class="btn btn-outline" onclick="exportExcel()">
                    <span class="material-icons">file_download</span>
                    Excel
                </button>
                <button class="btn btn-outline" onclick="exportPDF()">
                    <span class="material-icons">picture_as_pdf</span>
                    PDF
                </button>
                <button class="btn btn-primary" onclick="printReport()">
                    <span class="material-icons">print</span>
                    طباعة
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters">
            <div class="form-group">
                <label>من تاريخ</label>
                <input type="date" class="form-control" id="startDate" value="2024-01-01">
            </div>
            <div class="form-group">
                <label>إلى تاريخ</label>
                <input type="date" class="form-control" id="endDate" value="2024-12-31">
            </div>
            <div class="form-group">
                <label>نوع التقرير</label>
                <select class="form-control" id="reportType" onchange="changeReportType()">
                    <option value="sales">المبيعات</option>
                    <option value="inventory">المخزون</option>
                    <option value="customers">العملاء</option>
                    <option value="products">المنتجات</option>
                    <option value="profit">الأرباح والخسائر</option>
                    <option value="employees">أداء الموظفين</option>
                    <option value="trends">تحليل الاتجاهات</option>
                </select>
            </div>
            <button class="btn btn-primary" onclick="refreshReports()">
                <span class="material-icons">refresh</span>
                تحديث
            </button>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card sales">
                    <div class="stat-header">
                        <span class="material-icons">attach_money</span>
                        <span>إجمالي المبيعات</span>
                    </div>
                    <div class="stat-value">2,450,000 ر.س</div>
                </div>
                <div class="stat-card profit">
                    <div class="stat-header">
                        <span class="material-icons">trending_up</span>
                        <span>إجمالي الأرباح</span>
                    </div>
                    <div class="stat-value">485,000 ر.س</div>
                </div>
                <div class="stat-card orders">
                    <div class="stat-header">
                        <span class="material-icons">receipt</span>
                        <span>عدد الطلبات</span>
                    </div>
                    <div class="stat-value">1,247</div>
                </div>
                <div class="stat-card average">
                    <div class="stat-header">
                        <span class="material-icons">calculate</span>
                        <span>متوسط قيمة الطلب</span>
                    </div>
                    <div class="stat-value">1,965 ر.س</div>
                </div>
            </div>

            <!-- Chart Section -->
            <div class="chart-section">
                <div class="chart-header">
                    <h3>الرسم البياني - المبيعات الشهرية</h3>
                    <div>
                        <button class="btn btn-outline" onclick="changeChartType('line')">خطي</button>
                        <button class="btn btn-outline" onclick="changeChartType('bar')">أعمدة</button>
                        <button class="btn btn-outline" onclick="changeChartType('pie')">دائري</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>

            <!-- Data Section -->
            <div class="data-section">
                <div class="data-header">
                    <h3>بيانات التقرير</h3>
                </div>
                
                <div class="tabs">
                    <button class="tab active" onclick="showTab('sales-data')">بيانات المبيعات</button>
                    <button class="tab" onclick="showTab('products-data')">مبيعات المنتجات</button>
                    <button class="tab" onclick="showTab('customers-data')">مبيعات العملاء</button>
                    <button class="tab" onclick="showTab('inventory-data')">بيانات المخزون</button>
                </div>

                <div id="sales-data" class="tab-content active">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>العميل</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>الخصم</th>
                                    <th>الضريبة</th>
                                    <th>صافي المبلغ</th>
                                    <th>البائع</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-15</td>
                                    <td>شركة الأمل التجارية</td>
                                    <td>15,500 ر.س</td>
                                    <td>500 ر.س</td>
                                    <td>2,250 ر.س</td>
                                    <td>17,250 ر.س</td>
                                    <td>أحمد محمد</td>
                                </tr>
                                <tr>
                                    <td>2024-01-14</td>
                                    <td>مؤسسة النور</td>
                                    <td>8,750 ر.س</td>
                                    <td>250 ر.س</td>
                                    <td>1,275 ر.س</td>
                                    <td>9,775 ر.س</td>
                                    <td>فاطمة علي</td>
                                </tr>
                                <tr>
                                    <td>2024-01-13</td>
                                    <td>شركة المستقبل</td>
                                    <td>22,000 ر.س</td>
                                    <td>1,000 ر.س</td>
                                    <td>3,150 ر.س</td>
                                    <td>24,150 ر.س</td>
                                    <td>محمد سالم</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="products-data" class="tab-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>اسم المنتج</th>
                                    <th>الكود</th>
                                    <th>الفئة</th>
                                    <th>الكمية المباعة</th>
                                    <th>إجمالي الإيرادات</th>
                                    <th>متوسط السعر</th>
                                    <th>عدد الطلبات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>لابتوب ديل XPS 13</td>
                                    <td>LAP001</td>
                                    <td>أجهزة كمبيوتر</td>
                                    <td>45</td>
                                    <td>180,000 ر.س</td>
                                    <td>4,000 ر.س</td>
                                    <td>38</td>
                                </tr>
                                <tr>
                                    <td>هاتف آيفون 15</td>
                                    <td>PHN002</td>
                                    <td>هواتف ذكية</td>
                                    <td>67</td>
                                    <td>268,000 ر.س</td>
                                    <td>4,000 ر.س</td>
                                    <td>52</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="customers-data" class="tab-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>اسم العميل</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>عدد الطلبات</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>متوسط قيمة الطلب</th>
                                    <th>آخر طلب</th>
                                    <th>نوع العميل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>شركة الأمل التجارية</td>
                                    <td>0501234567</td>
                                    <td><EMAIL></td>
                                    <td>15</td>
                                    <td>75,000 ر.س</td>
                                    <td>5,000 ر.س</td>
                                    <td>2024-01-15</td>
                                    <td>VIP</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="inventory-data" class="tab-content">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>اسم المنتج</th>
                                    <th>الكود</th>
                                    <th>الفئة</th>
                                    <th>المخزون الحالي</th>
                                    <th>الحد الأدنى</th>
                                    <th>سعر التكلفة</th>
                                    <th>سعر البيع</th>
                                    <th>هامش الربح %</th>
                                    <th>حالة المخزون</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>لابتوب ديل XPS 13</td>
                                    <td>LAP001</td>
                                    <td>أجهزة كمبيوتر</td>
                                    <td>25</td>
                                    <td>10</td>
                                    <td>3,200 ر.س</td>
                                    <td>4,000 ر.س</td>
                                    <td>20.00</td>
                                    <td>جيد</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading" id="loadingOverlay">
        <div style="text-align: center; color: white;">
            <div class="spinner"></div>
            <p style="margin-top: 20px; font-size: 18px;">جاري تحميل التقرير...</p>
        </div>
    </div>

    <script>
        // Initialize Chart
        let salesChart;
        
        function initChart() {
            const ctx = document.getElementById('salesChart').getContext('2d');
            salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                    datasets: [{
                        label: 'المبيعات الشهرية',
                        data: [180000, 220000, 195000, 240000, 280000, 320000, 290000, 350000, 310000, 380000, 420000, 450000],
                        borderColor: '#2196F3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                font: {
                                    family: 'Cairo'
                                },
                                callback: function(value) {
                                    return value.toLocaleString('ar-SA') + ' ر.س';
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    family: 'Cairo'
                                }
                            }
                        }
                    }
                }
            });
        }

        // Tab Functions
        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabId).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Chart Functions
        function changeChartType(type) {
            if (salesChart) {
                salesChart.destroy();
            }
            
            const ctx = document.getElementById('salesChart').getContext('2d');
            const data = {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'المبيعات',
                    data: [180000, 220000, 195000, 240000, 280000, 320000],
                    backgroundColor: type === 'pie' ? 
                        ['#2196F3', '#4CAF50', '#FF9800', '#9C27B0', '#F44336', '#00BCD4'] :
                        'rgba(33, 150, 243, 0.8)',
                    borderColor: '#2196F3',
                    borderWidth: 2
                }]
            };
            
            salesChart = new Chart(ctx, {
                type: type,
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                font: { family: 'Cairo' }
                            }
                        }
                    }
                }
            });
        }

        // Action Functions
        function refreshReports() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                alert('تم تحديث التقارير بنجاح!');
            }, 2000);
        }

        function exportExcel() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                alert('تم تصدير التقرير إلى Excel بنجاح!');
            }, 1500);
        }

        function exportPDF() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                alert('تم تصدير التقرير إلى PDF بنجاح!');
            }, 1500);
        }

        function printReport() {
            window.print();
        }

        function changeReportType() {
            const reportType = document.getElementById('reportType').value;
            showLoading();
            setTimeout(() => {
                hideLoading();
                alert(`تم تغيير نوع التقرير إلى: ${document.getElementById('reportType').selectedOptions[0].text}`);
            }, 1000);
        }

        function showLoading() {
            document.getElementById('loadingOverlay').classList.add('show');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.remove('show');
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            
            // Set current date
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('endDate').value = today;
        });
    </script>
</body>
</html>
