<Window x:Class="SalesManagementSystem.Views.BarcodeManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:controls="clr-namespace:SalesManagementSystem.Controls"
        Title="إدارة الباركود"
        Height="700"
        Width="1000"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="Barcode"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="إدارة الباركود"
                      Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                      VerticalAlignment="Center"
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Main Content -->
        <TabControl Grid.Row="1"
                   Style="{DynamicResource MaterialDesignTabControl}">
            
            <!-- Barcode Scanner Tab -->
            <TabItem Header="ماسح الباركود">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="BarcodeScanner"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                
                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="16"/>
                        <ColumnDefinition Width="300"/>
                    </Grid.ColumnDefinitions>

                    <!-- Scanner -->
                    <controls:BarcodeScanner Grid.Column="0"
                                           ProductFound="BarcodeScanner_ProductFound"
                                           ProductSelected="BarcodeScanner_ProductSelected"/>

                    <!-- Product Details -->
                    <GroupBox Grid.Column="2"
                             Header="تفاصيل المنتج"
                             Style="{DynamicResource MaterialDesignCardGroupBox}"
                             Visibility="{Binding HasSelectedProduct, Converter={StaticResource BoolToVisConverter}}">
                        <StackPanel Margin="16">
                            <TextBlock Text="{Binding SelectedProduct.Name}"
                                      Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                                      Margin="0,0,0,8"/>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,4">
                                <TextBlock Text="الكود: " FontWeight="Medium"/>
                                <TextBlock Text="{Binding SelectedProduct.Code}"/>
                            </StackPanel>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,4">
                                <TextBlock Text="الباركود: " FontWeight="Medium"/>
                                <TextBlock Text="{Binding SelectedProduct.Barcode}"/>
                            </StackPanel>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,4">
                                <TextBlock Text="السعر: " FontWeight="Medium"/>
                                <TextBlock Text="{Binding SelectedProduct.SalePrice, StringFormat=F2}"/>
                            </StackPanel>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,4">
                                <TextBlock Text="المخزون: " FontWeight="Medium"/>
                                <TextBlock Text="{Binding SelectedProduct.Quantity}"/>
                            </StackPanel>

                            <Button Command="{Binding EditProductCommand}"
                                   Style="{DynamicResource MaterialDesignRaisedButton}"
                                   Margin="0,16,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Edit"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,4,0"/>
                                    <TextBlock Text="تعديل المنتج" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- Barcode Generator Tab -->
            <TabItem Header="مولد الباركود">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Creation"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                
                <controls:BarcodeDisplay Margin="16"/>
            </TabItem>

            <!-- Product Barcodes Tab -->
            <TabItem Header="باركود المنتجات">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Package"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Search -->
                    <TextBox Grid.Row="0"
                            Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                            materialDesign:HintAssist.Hint="البحث في المنتجات..."
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            Margin="0,0,0,16"/>

                    <!-- Products List -->
                    <DataGrid Grid.Row="1"
                             ItemsSource="{Binding Products}"
                             SelectedItem="{Binding SelectedProduct}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             Style="{DynamicResource MaterialDesignDataGrid}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم"
                                              Binding="{Binding Name}"
                                              Width="2*"/>
                            <DataGridTextColumn Header="الكود"
                                              Binding="{Binding Code}"
                                              Width="*"/>
                            <DataGridTextColumn Header="الباركود"
                                              Binding="{Binding Barcode}"
                                              Width="*"/>
                            <DataGridTextColumn Header="السعر"
                                              Binding="{Binding SalePrice, StringFormat=F2}"
                                              Width="*"/>
                            <DataGridTemplateColumn Header="إجراءات" Width="Auto">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Command="{Binding DataContext.GenerateProductBarcodeCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                   CommandParameter="{Binding}"
                                                   Style="{DynamicResource MaterialDesignIconButton}"
                                                   ToolTip="توليد باركود">
                                                <materialDesign:PackIcon Kind="Barcode"/>
                                            </Button>
                                            <Button Command="{Binding DataContext.PrintProductBarcodeCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                   CommandParameter="{Binding}"
                                                   Style="{DynamicResource MaterialDesignIconButton}"
                                                   ToolTip="طباعة باركود">
                                                <materialDesign:PackIcon Kind="Printer"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Actions -->
                    <StackPanel Grid.Row="2"
                               Orientation="Horizontal"
                               HorizontalAlignment="Left"
                               Margin="0,16,0,0">
                        
                        <Button Command="{Binding GenerateAllBarcodesCommand}"
                               Style="{DynamicResource MaterialDesignRaisedButton}"
                               Margin="0,0,8,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Creation"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="توليد باركود لجميع المنتجات" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Command="{Binding PrintAllBarcodesCommand}"
                               Style="{DynamicResource MaterialDesignOutlinedButton}"
                               Margin="0,0,8,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Printer"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="طباعة جميع الباركودات" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Command="{Binding RefreshCommand}"
                               Style="{DynamicResource MaterialDesignOutlinedButton}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</Window>
