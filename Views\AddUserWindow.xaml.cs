using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// نافذة إضافة مستخدم جديد
    /// </summary>
    public partial class AddUserWindow : Window
    {
        private readonly LoginAuthService _authService;
        public LoginAuthService.SystemUser? NewUser { get; private set; }

        public AddUserWindow()
        {
            InitializeComponent();
            _authService = LoginAuthService.Instance;
            
            // تحديد الدور الافتراضي
            RoleComboBox.SelectedIndex = 2; // موظف مبيعات
        }

        // حفظ المستخدم الجديد
        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                var username = UsernameTextBox.Text.Trim();
                var fullName = FullNameTextBox.Text.Trim();
                var role = (RoleComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "";

                // إنشاء المستخدم الجديد
                var newUser = new LoginAuthService.SystemUser
                {
                    Username = username,
                    PasswordHash = "1234", // سيتم تشفيرها في AddUser
                    FullName = fullName,
                    Role = role,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    Permissions = GetPermissionsForRole(role)
                };

                // إضافة المستخدم
                if (_authService.AddUser(newUser))
                {
                    NewUser = newUser;
                    DialogResult = true;
                    this.Close();
                }
                else
                {
                    ShowError("اسم المستخدم موجود بالفعل. يرجى اختيار اسم مختلف.");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إضافة المستخدم: {ex.Message}");
            }
        }

        // التحقق من صحة البيانات
        private bool ValidateInput()
        {
            var username = UsernameTextBox.Text.Trim();
            var fullName = FullNameTextBox.Text.Trim();

            if (string.IsNullOrEmpty(username))
            {
                ShowError("يرجى إدخال اسم المستخدم");
                UsernameTextBox.Focus();
                return false;
            }

            if (username.Length < 3)
            {
                ShowError("اسم المستخدم يجب أن يكون 3 أحرف على الأقل");
                UsernameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrEmpty(fullName))
            {
                ShowError("يرجى إدخال الاسم الكامل");
                FullNameTextBox.Focus();
                return false;
            }

            if (RoleComboBox.SelectedItem == null)
            {
                ShowError("يرجى اختيار الدور");
                RoleComboBox.Focus();
                return false;
            }

            // إخفاء رسالة الخطأ إذا كانت البيانات صحيحة
            ErrorMessage.Visibility = Visibility.Collapsed;
            return true;
        }

        // تحديد الصلاحيات حسب الدور
        private List<string> GetPermissionsForRole(string role)
        {
            return role switch
            {
                "مدير النظام" => new List<string>
                {
                    "sales", "purchases", "products", "customers", "reports", "settings",
                    "inventory", "suppliers", "expenses", "backup", "user_management",
                    "system_settings", "quick_sale", "crm", "analytics", "view_products",
                    "view_reports", "manage_users", "delete_users", "create_users"
                },
                "محاسبة" => new List<string>
                {
                    "sales", "reports", "customers", "view_products", "expenses"
                },
                "موظف مبيعات" => new List<string>
                {
                    "sales", "customers", "products", "quick_sale"
                },
                "مدير المخزون" => new List<string>
                {
                    "products", "inventory", "purchases", "suppliers"
                },
                "موظف استقبال" => new List<string>
                {
                    "customers", "sales", "quick_sale"
                },
                "مدير فرع" => new List<string>
                {
                    "sales", "customers", "products", "reports", "expenses"
                },
                "محاسبة مساعدة" => new List<string>
                {
                    "sales", "customers", "view_reports"
                },
                "مديرة العملاء" => new List<string>
                {
                    "customers", "sales", "reports", "crm"
                },
                _ => new List<string> { "sales", "customers" }
            };
        }

        // عرض رسالة خطأ
        private void ShowError(string message)
        {
            ErrorMessage.Text = message;
            ErrorMessage.Visibility = Visibility.Visible;
        }

        // إلغاء العملية
        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            this.Close();
        }
    }
}
