<Window x:Class="SalesManagementSystem.Views.AddUserWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة مستخدم جديد" 
        Height="500" Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                               Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#FFF5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#FF1976D2">
            <Border.Effect>
                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
            </Border.Effect>
            <Grid Margin="20,0">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="➕" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="إضافة مستخدم جديد" FontSize="18"
                              FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- نموذج الإدخال -->
        <Border Grid.Row="1" Background="White" Margin="30" CornerRadius="10">
            <Border.Effect>
                <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
            </Border.Effect>
            <Grid Margin="30">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- اسم المستخدم -->
                <TextBlock Grid.Row="0" Text="اسم المستخدم:" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="UsernameTextBox" Grid.Row="1" Height="35" FontSize="14"
                        Padding="10,8" Margin="0,0,0,15" BorderThickness="1" BorderBrush="#FFCCCCCC"/>

                <!-- الاسم الكامل -->
                <TextBlock Grid.Row="2" Text="الاسم الكامل:" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="FullNameTextBox" Grid.Row="3" Height="35" FontSize="14"
                        Padding="10,8" Margin="0,0,0,15" BorderThickness="1" BorderBrush="#FFCCCCCC"/>

                <!-- الدور -->
                <TextBlock Grid.Row="4" Text="الدور:" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="RoleComboBox" Grid.Row="5" Height="35" FontSize="14"
                         Padding="10,8" Margin="0,0,0,15" BorderThickness="1" BorderBrush="#FFCCCCCC">
                    <ComboBoxItem Content="مدير النظام"/>
                    <ComboBoxItem Content="محاسبة"/>
                    <ComboBoxItem Content="موظف مبيعات"/>
                    <ComboBoxItem Content="مدير المخزون"/>
                    <ComboBoxItem Content="موظف استقبال"/>
                    <ComboBoxItem Content="مدير فرع"/>
                    <ComboBoxItem Content="محاسبة مساعدة"/>
                    <ComboBoxItem Content="مديرة العملاء"/>
                </ComboBox>

                <!-- كلمة المرور -->
                <TextBlock Grid.Row="6" Text="كلمة المرور الافتراضية: 1234" FontSize="12" 
                          Foreground="Gray" Margin="0,0,0,20"/>

                <!-- رسالة الخطأ -->
                <TextBlock x:Name="ErrorMessage" Grid.Row="7" Text="" FontSize="12" 
                          Foreground="Red" HorizontalAlignment="Center" VerticalAlignment="Top"
                          Visibility="Collapsed"/>
            </Grid>
        </Border>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button x:Name="SaveButton" Content="💾 حفظ" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF4CAF50" Click="Save_Click" Width="100"/>
                
                <Button x:Name="CancelButton" Content="❌ إلغاء" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FFF44336" Click="Cancel_Click" Width="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
