using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class ProductDialogViewModel : BaseViewModel
    {
        #region Services

        private readonly DatabaseService? _dbService;
        private readonly ProductService? _productService;
        private readonly CategoryService? _categoryService;
        private readonly WarehouseService? _warehouseService;

        #endregion

        #region Properties

        private Product _product = new();
        public Product Product
        {
            get => _product;
            set
            {
                if (SetProperty(ref _product, value))
                {
                    OnProductChanged();
                    // تحديث الباركودات عند تغيير المنتج
                    UpdateBarcodesFromProduct();
                }
            }
        }

        // خاصية الباركودات المتعددة
        private string _barcodes = string.Empty;
        public string Barcodes
        {
            get => _barcodes;
            set
            {
                if (SetProperty(ref _barcodes, value))
                {
                    // تحديث الباركود الأساسي (أول باركود في القائمة)
                    var firstBarcode = GetFirstBarcode();
                    if (Product.Barcode != firstBarcode)
                    {
                        Product.Barcode = firstBarcode;
                        OnPropertyChanged(nameof(Product));
                        OnPropertyChanged(nameof(HasBarcode));
                        GenerateBarcodeImage();
                    }
                }
            }
        }

        private ObservableCollection<Category> _categories = new();
        public ObservableCollection<Category> Categories
        {
            get => _categories;
            set => SetProperty(ref _categories, value);
        }

        private Category? _selectedCategory;
        public Category? SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                if (SetProperty(ref _selectedCategory, value))
                {
                    if (value != null)
                        Product.CategoryId = value.Id;
                }
            }
        }

        private ObservableCollection<string> _units = new();
        public ObservableCollection<string> Units
        {
            get => _units;
            set => SetProperty(ref _units, value);
        }

        private ObservableCollection<Warehouse> _warehouses = new();
        public ObservableCollection<Warehouse> Warehouses
        {
            get => _warehouses;
            set => SetProperty(ref _warehouses, value);
        }

        private Warehouse? _selectedWarehouse;
        public Warehouse? SelectedWarehouse
        {
            get => _selectedWarehouse;
            set
            {
                if (SetProperty(ref _selectedWarehouse, value))
                {
                    if (value != null)
                    {
                        Product.WarehouseId = value.Id;
                        Product.WarehouseName = value.Name;
                    }
                    SaveCommand.RaiseCanExecuteChanged();
                }
            }
        }

        private string _windowTitle = "إضافة منتج جديد";
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }

        private string _headerIcon = "Package";
        public string HeaderIcon
        {
            get => _headerIcon;
            set => SetProperty(ref _headerIcon, value);
        }

        private bool _isCodeEditable = true;
        public bool IsCodeEditable
        {
            get => _isCodeEditable;
            set => SetProperty(ref _isCodeEditable, value);
        }

        private bool _isEditMode;
        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                if (SetProperty(ref _isEditMode, value))
                {
                    UpdateWindowTitle();
                    IsCodeEditable = !value;
                }
            }
        }

        public decimal ProfitMargin
        {
            get
            {
                if (Product.PurchasePrice > 0)
                {
                    return ((Product.SalePrice - Product.PurchasePrice) / Product.PurchasePrice) * 100;
                }
                return 0;
            }
        }

        public decimal ProfitMargin2
        {
            get
            {
                if (Product.PurchasePrice > 0)
                {
                    return ((Product.SalePrice2 - Product.PurchasePrice) / Product.PurchasePrice) * 100;
                }
                return 0;
            }
        }

        public bool IsLowStock => Product.Quantity <= Product.MinQuantity;

        // خصائص الباركود
        public bool HasBarcode => !string.IsNullOrWhiteSpace(Product.Barcode);

        private BitmapImage? _barcodeImage;
        public BitmapImage? BarcodeImage
        {
            get => _barcodeImage;
            set => SetProperty(ref _barcodeImage, value);
        }

        #endregion

        #region Commands

        private DelegateCommand? _saveCommand;
        public DelegateCommand SaveCommand => _saveCommand ??= new DelegateCommand(SaveProduct, CanSaveProduct);

        private DelegateCommand? _cancelCommand;
        public DelegateCommand CancelCommand => _cancelCommand ??= new DelegateCommand(Cancel);

        private DelegateCommand? _generateBarcodeCommand;
        public DelegateCommand GenerateBarcodeCommand => _generateBarcodeCommand ??= new DelegateCommand(GenerateBarcode);

        #endregion

        #region Constructor

        public ProductDialogViewModel()
        {
            try
            {
                // تهيئة الخدمات مع معالجة الأخطاء
                _dbService = new DatabaseService();
                _productService = new ProductService(_dbService);
                _categoryService = new CategoryService(_dbService);

                var settingsService = new SettingsService(_dbService);
                var notificationService = new NotificationService(_dbService, settingsService);
                _warehouseService = new WarehouseService(_dbService, notificationService);

                // تهيئة البيانات الأساسية
                InitializeUnits();
                LoadSampleWarehouses();

                // توليد كود المنتج التلقائي للمنتجات الجديدة
                if (!IsEditMode)
                {
                    _ = GenerateProductCodeAsync().ContinueWith(task =>
                    {
                        if (task.IsCompletedSuccessfully)
                        {
                            Product.Code = task.Result;
                            OnPropertyChanged(nameof(Product));
                        }
                    }, TaskScheduler.FromCurrentSynchronizationContext());
                }

                // مراقبة تغييرات الأسعار لحساب هامش الربح فوراً
                Product.PropertyChanged += Product_PropertyChanged;

                // تحميل البيانات بشكل غير متزامن
                _ = LoadDataAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء ProductDialogViewModel: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"StackTrace: {ex.StackTrace}");

                // تهيئة الخدمات الأساسية كـ fallback
                try
                {
                    _dbService ??= new DatabaseService();
                    _productService ??= new ProductService(_dbService);
                    _categoryService ??= new CategoryService(_dbService);

                    var settingsService = new SettingsService(_dbService);
                    var notificationService = new NotificationService(_dbService, settingsService);
                    _warehouseService ??= new WarehouseService(_dbService, notificationService);

                    InitializeUnits();
                    LoadSampleWarehouses();
                }
                catch (Exception innerEx)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات الأساسية: {innerEx.Message}");

                    // تهيئة أساسية جداً
                    Units = new ObservableCollection<string>();
                    Warehouses = new ObservableCollection<Warehouse>();
                    Categories = new ObservableCollection<Category>();
                }
            }
        }

        public ProductDialogViewModel(Product product) : this()
        {
            Product = product.Clone();
            IsEditMode = true;
        }

        #endregion

        #region Methods

        private void InitializeUnits()
        {
            Units.Clear();
            Units.Add("قطعة");
            Units.Add("كيلو");
            Units.Add("جرام");
            Units.Add("لتر");
            Units.Add("متر");
            Units.Add("علبة");
            Units.Add("كرتون");
            Units.Add("دزينة");
        }

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تحميل البيانات...";
                ClearError();

                System.Diagnostics.Debug.WriteLine("بدء تحميل بيانات حوار المنتج");

                // تحقق من وجود الخدمات قبل الاستخدام
                if (_categoryService != null)
                {
                    await LoadCategoriesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم تحميل {Categories.Count} فئات");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("خدمة الفئات غير متاحة");
                }

                if (_warehouseService != null)
                {
                    await LoadWarehousesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم تحميل {Warehouses.Count} مخازن");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("خدمة المخازن غير متاحة، استخدام البيانات التجريبية");
                    LoadSampleWarehouses();
                }

                if (IsEditMode && _categoryService != null)
                {
                    await LoadProductCategoryAsync();
                    await LoadProductWarehouseAsync();
                    System.Diagnostics.Debug.WriteLine("تم تحميل بيانات المنتج للتعديل");
                }

                System.Diagnostics.Debug.WriteLine("انتهاء تحميل بيانات حوار المنتج");
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"StackTrace: {ex.StackTrace}");

                // تحميل البيانات التجريبية كـ fallback
                try
                {
                    LoadSampleWarehouses();
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات التجريبية: {fallbackEx.Message}");
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                if (_categoryService == null)
                {
                    System.Diagnostics.Debug.WriteLine("خدمة الفئات غير متاحة");
                    return;
                }

                var categories = await _categoryService.GetAllCategoriesAsync();
                Categories.Clear();

                foreach (var category in categories)
                {
                    Categories.Add(category);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الفئات: {ex.Message}");
                // إضافة فئة افتراضية
                Categories.Clear();
                Categories.Add(new Category { Id = 1, Name = "عام" });
            }
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                var warehouses = await _warehouseService!.GetAllWarehousesAsync();
                Warehouses.Clear();

                foreach (var warehouse in warehouses.Where(w => w.IsActive))
                {
                    Warehouses.Add(warehouse);
                }

                // إذا لم يتم العثور على مخازن، استخدم البيانات التجريبية
                if (!Warehouses.Any())
                {
                    LoadSampleWarehouses();
                    return;
                }

                // تعيين المخزن الافتراضي إذا لم يكن هناك مخزن محدد
                if (!IsEditMode && Warehouses.Any())
                {
                    SelectedWarehouse = Warehouses.FirstOrDefault(w => w.IsDefault) ?? Warehouses.First();
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل تحميل المخازن، استخدم بيانات تجريبية
                LoadSampleWarehouses();
                LoggingService.LogError(ex, "فشل في تحميل المخازن، استخدام البيانات التجريبية");
            }
        }

        private void LoadSampleWarehouses()
        {
            try
            {
                Warehouses.Clear();
                Warehouses.Add(new Warehouse
                {
                    Id = 1,
                    Code = "WH001",
                    Name = "المخزن الرئيسي",
                    City = "الرياض",
                    Type = WarehouseType.Main,
                    Status = WarehouseStatus.Active,
                    IsDefault = true
                });
                Warehouses.Add(new Warehouse
                {
                    Id = 2,
                    Code = "WH002",
                    Name = "مخزن الفرع الشرقي",
                    City = "الدمام",
                    Type = WarehouseType.Branch,
                    Status = WarehouseStatus.Active,
                    IsDefault = false
                });
                Warehouses.Add(new Warehouse
                {
                    Id = 3,
                    Code = "WH003",
                    Name = "مخزن العبور",
                    City = "جدة",
                    Type = WarehouseType.Transit,
                    Status = WarehouseStatus.Active,
                    IsDefault = false
                });

                // تعيين المخزن الافتراضي للمنتجات الجديدة
                if (!IsEditMode && Warehouses.Any())
                {
                    SelectedWarehouse = Warehouses.First();
                }

                System.Diagnostics.Debug.WriteLine($"تم تحميل {Warehouses.Count} مخازن تجريبية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المخازن التجريبية: {ex.Message}");
            }
        }

        private async Task LoadProductCategoryAsync()
        {
            if (Product.CategoryId.HasValue && Product.CategoryId.Value > 0)
            {
                var category = await _categoryService!.GetCategoryByIdAsync(Product.CategoryId.Value);
                if (category != null)
                {
                    SelectedCategory = Categories.FirstOrDefault(c => c.Id == category.Id);
                }
            }
        }

        private Task LoadProductWarehouseAsync()
        {
            if (Product.WarehouseId.HasValue && Product.WarehouseId.Value > 0)
            {
                SelectedWarehouse = Warehouses.FirstOrDefault(w => w.Id == Product.WarehouseId.Value);
            }
            return Task.CompletedTask;
        }

        private void OnProductChanged()
        {
            // تحديث هوامش الربح عند تغيير الأسعار
            OnPropertyChanged(nameof(ProfitMargin));
            OnPropertyChanged(nameof(ProfitMargin2));
            OnPropertyChanged(nameof(IsLowStock));

            // تحديث الأرباح المحسوبة
            OnPropertyChanged(nameof(Profit));
            OnPropertyChanged(nameof(Profit2));

            SaveCommand.RaiseCanExecuteChanged();
        }

        // خصائص الربح المحسوبة
        public decimal Profit => Product.SalePrice - Product.PurchasePrice;
        public decimal Profit2 => Product.SalePrice2 - Product.PurchasePrice;

        // مراقبة تغييرات خصائص المنتج لحساب هامش الربح فوراً
        private void Product_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(Product.PurchasePrice) ||
                e.PropertyName == nameof(Product.SalePrice) ||
                e.PropertyName == nameof(Product.SalePrice2))
            {
                // تحديث هوامش الربح فوراً عند تغيير الأسعار
                OnPropertyChanged(nameof(ProfitMargin));
                OnPropertyChanged(nameof(ProfitMargin2));
                OnPropertyChanged(nameof(Profit));
                OnPropertyChanged(nameof(Profit2));

                // تحديث حالة زر الحفظ
                SaveCommand.RaiseCanExecuteChanged();
            }
        }

        private void UpdateWindowTitle()
        {
            WindowTitle = IsEditMode ? "تعديل المنتج" : "إضافة منتج جديد";
            HeaderIcon = IsEditMode ? "PackageVariant" : "Package";
        }

        // دوال الباركود
        private string GetFirstBarcode()
        {
            if (string.IsNullOrWhiteSpace(Barcodes))
                return string.Empty;

            var lines = Barcodes.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            return lines.FirstOrDefault()?.Trim() ?? string.Empty;
        }

        private void UpdateBarcodesFromProduct()
        {
            if (!string.IsNullOrWhiteSpace(Product.Barcode) && string.IsNullOrWhiteSpace(Barcodes))
            {
                Barcodes = Product.Barcode;
            }
        }

        private void GenerateBarcodeImage()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(Product.Barcode))
                {
                    BarcodeImage = null;
                    return;
                }

                // هنا يمكن إضافة مكتبة توليد الباركود
                // مؤقتاً سنترك هذا فارغ
                BarcodeImage = null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في توليد صورة الباركود: {ex.Message}");
                BarcodeImage = null;
            }
        }

        // توليد كود المنتج التلقائي
        private async Task<string> GenerateProductCodeAsync()
        {
            try
            {
                if (_productService == null)
                    return GenerateFallbackProductCode();

                // الحصول على آخر كود منتج من قاعدة البيانات
                var lastProduct = await _productService.GetLastProductAsync();
                if (lastProduct != null && !string.IsNullOrWhiteSpace(lastProduct.Code))
                {
                    // استخراج الرقم من الكود الأخير
                    var match = System.Text.RegularExpressions.Regex.Match(lastProduct.Code, @"(\d+)$");
                    if (match.Success && int.TryParse(match.Value, out int lastNumber))
                    {
                        return $"PRD{(lastNumber + 1):D6}";
                    }
                }

                // إذا لم يوجد منتج سابق، ابدأ من 1
                return "PRD000001";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في توليد كود المنتج: {ex.Message}");
                return GenerateFallbackProductCode();
            }
        }

        private string GenerateFallbackProductCode()
        {
            // توليد كود بناءً على الوقت الحالي كـ fallback
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            return $"PRD{timestamp.Substring(timestamp.Length - 6)}";
        }

        #endregion

        #region Command Handlers

        private async void SaveProduct()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = IsEditMode ? "جاري تحديث المنتج..." : "جاري حفظ المنتج...";
                ClearError();

                System.Diagnostics.Debug.WriteLine($"بدء حفظ المنتج: {Product.Name}");

                // Validate input
                if (!ValidateProduct())
                {
                    System.Diagnostics.Debug.WriteLine("فشل في التحقق من صحة البيانات");
                    return;
                }

                // Generate code if new product
                if (!IsEditMode && string.IsNullOrWhiteSpace(Product.Code))
                {
                    Product.Code = await GenerateProductCodeAsync();
                }

                // Generate barcode if product doesn't have one
                if (string.IsNullOrWhiteSpace(Product.Barcode))
                {
                    GenerateBarcode();
                }

                // Save multiple barcodes if provided
                await SaveMultipleBarcodesAsync();

                // تعيين معرف الفئة والمخزن
                if (SelectedCategory != null)
                {
                    Product.CategoryId = SelectedCategory.Id;
                    System.Diagnostics.Debug.WriteLine($"تم تعيين الفئة: {SelectedCategory.Name} (ID: {SelectedCategory.Id})");
                }

                if (SelectedWarehouse != null)
                {
                    Product.WarehouseId = SelectedWarehouse.Id;
                    Product.WarehouseName = SelectedWarehouse.Name;
                    System.Diagnostics.Debug.WriteLine($"تم تعيين المخزن: {SelectedWarehouse.Name} (ID: {SelectedWarehouse.Id})");
                }

                // Save product
                if (IsEditMode)
                {
                    var success = await _productService!.UpdateProductAsync(Product);
                    if (success)
                    {
                        LoggingService.LogProductUpdate(Product.Name, "تحديث", "تم تحديث المنتج بنجاح");

                        // عرض رسالة نجاح
                        System.Windows.MessageBox.Show(
                            $"تم تحديث المنتج '{Product.Name}' بنجاح!",
                            "نجح التحديث",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Information);

                        ProductSaved?.Invoke(Product);
                        RequestClose?.Invoke(true);
                    }
                    else
                    {
                        SetError("فشل في تحديث المنتج");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"محاولة إضافة المنتج: {Product.Name}");
                    System.Diagnostics.Debug.WriteLine($"تفاصيل المنتج - الفئة: {Product.CategoryId}, المخزن: {Product.WarehouseId}");

                    var result = await _productService!.AddProductAsync(Product);
                    System.Diagnostics.Debug.WriteLine($"نتيجة الإضافة: {(result != null ? "نجح" : "فشل")}");

                    if (result != null)
                    {
                        Product = result; // Update with the saved product (including ID)
                        LoggingService.LogProductUpdate(Product.Name, "إضافة", "تم إضافة المنتج بنجاح");

                        System.Diagnostics.Debug.WriteLine($"تم حفظ المنتج بنجاح - ID: {result.Id}");

                        // عرض رسالة نجاح
                        System.Windows.MessageBox.Show(
                            $"تم إضافة المنتج '{Product.Name}' بنجاح!\n\nكود المنتج: {Product.Code}\nالباركود: {Product.Barcode}",
                            "نجح الحفظ",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Information);

                        ProductSaved?.Invoke(Product);
                        RequestClose?.Invoke(true);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("فشل في حفظ المنتج - النتيجة null");
                        SetError("فشل في حفظ المنتج");
                    }
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ المنتج: {ex.Message}");
                LoggingService.LogError(ex, $"خطأ في حفظ المنتج: {Product.Name}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanSaveProduct()
        {
            return !string.IsNullOrWhiteSpace(Product.Name) &&
                   Product.SalePrice > 0 &&
                   Product.PurchasePrice > 0 &&
                   Product.SalePrice2 >= 0 && // السعر الثاني اختياري (يمكن أن يكون صفر)
                   SelectedCategory != null &&
                   SelectedWarehouse != null &&
                   !IsLoading;
        }

        private bool ValidateProduct()
        {
            ClearAllValidationErrors();

            System.Diagnostics.Debug.WriteLine($"التحقق من صحة المنتج:");
            System.Diagnostics.Debug.WriteLine($"- الاسم: '{Product.Name}'");
            System.Diagnostics.Debug.WriteLine($"- سعر الشراء: {Product.PurchasePrice}");
            System.Diagnostics.Debug.WriteLine($"- سعر البيع الأول: {Product.SalePrice}");
            System.Diagnostics.Debug.WriteLine($"- سعر البيع الثاني: {Product.SalePrice2}");
            System.Diagnostics.Debug.WriteLine($"- الفئة المختارة: {SelectedCategory?.Name}");
            System.Diagnostics.Debug.WriteLine($"- المخزن المختار: {SelectedWarehouse?.Name}");

            if (string.IsNullOrWhiteSpace(Product.Name))
            {
                AddValidationError(nameof(Product.Name), "اسم المنتج مطلوب");
                System.Diagnostics.Debug.WriteLine("خطأ: اسم المنتج مطلوب");
            }

            if (Product.SalePrice <= 0)
            {
                AddValidationError(nameof(Product.SalePrice), "سعر البيع يجب أن يكون أكبر من صفر");
            }

            if (Product.PurchasePrice <= 0)
            {
                AddValidationError(nameof(Product.PurchasePrice), "سعر الشراء يجب أن يكون أكبر من صفر");
            }

            if (Product.SalePrice <= Product.PurchasePrice)
            {
                AddValidationError(nameof(Product.SalePrice), "سعر البيع الأول يجب أن يكون أكبر من سعر الشراء");
            }

            if (Product.SalePrice2 < 0)
            {
                AddValidationError(nameof(Product.SalePrice2), "سعر البيع الثاني لا يمكن أن يكون سالب");
            }

            if (Product.SalePrice2 > 0 && Product.SalePrice2 <= Product.PurchasePrice)
            {
                AddValidationError(nameof(Product.SalePrice2), "سعر البيع الثاني يجب أن يكون أكبر من سعر الشراء");
            }

            if (SelectedCategory == null)
            {
                AddValidationError(nameof(SelectedCategory), "يجب اختيار فئة للمنتج");
                System.Diagnostics.Debug.WriteLine("خطأ: يجب اختيار فئة للمنتج");
            }

            if (SelectedWarehouse == null)
            {
                AddValidationError(nameof(SelectedWarehouse), "يجب اختيار مخزن للمنتج");
                System.Diagnostics.Debug.WriteLine("خطأ: يجب اختيار مخزن للمنتج");
            }

            if (Product.Quantity < 0)
            {
                AddValidationError(nameof(Product.Quantity), "الكمية لا يمكن أن تكون سالبة");
            }

            if (Product.MinQuantity < 0)
            {
                AddValidationError(nameof(Product.MinQuantity), "الحد الأدنى للكمية لا يمكن أن يكون سالب");
            }

            if (HasValidationErrors)
            {
                var firstError = GetValidationErrors(nameof(Product.Name)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Product.SalePrice)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Product.SalePrice2)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Product.PurchasePrice)).FirstOrDefault() ??
                               GetValidationErrors(nameof(SelectedCategory)).FirstOrDefault() ??
                               GetValidationErrors(nameof(SelectedWarehouse)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Product.Quantity)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Product.MinQuantity)).FirstOrDefault();

                SetError(firstError ?? "يرجى تصحيح الأخطاء المدخلة");
                System.Diagnostics.Debug.WriteLine($"فشل التحقق من الصحة: {firstError}");
                return false;
            }

            System.Diagnostics.Debug.WriteLine("نجح التحقق من صحة البيانات");
            return true;
        }

        private void GenerateBarcode()
        {
            try
            {
                // توليد باركود تلقائي إذا لم يكن موجود
                if (string.IsNullOrWhiteSpace(Product.Barcode))
                {
                    // توليد باركود بناءً على كود المنتج أو timestamp
                    var baseCode = !string.IsNullOrWhiteSpace(Product.Code) ? Product.Code : DateTime.Now.ToString("yyyyMMddHHmmss");
                    var numericCode = System.Text.RegularExpressions.Regex.Replace(baseCode, @"[^\d]", "");

                    if (numericCode.Length < 12)
                    {
                        numericCode = numericCode.PadLeft(12, '0');
                    }
                    else if (numericCode.Length > 12)
                    {
                        numericCode = numericCode.Substring(0, 12);
                    }

                    Product.Barcode = numericCode;

                    // إضافة الباركود الجديد إلى قائمة الباركودات
                    if (string.IsNullOrWhiteSpace(Barcodes))
                    {
                        Barcodes = Product.Barcode;
                    }
                    else if (!Barcodes.Contains(Product.Barcode))
                    {
                        Barcodes += Environment.NewLine + Product.Barcode;
                    }

                    OnPropertyChanged(nameof(Product));
                    OnPropertyChanged(nameof(HasBarcode));
                    GenerateBarcodeImage();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في توليد الباركود: {ex.Message}");
            }
        }

        private Task SaveMultipleBarcodesAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(Barcodes) || _productService == null)
                    return Task.CompletedTask;

                // الحصول على جميع الباركودات من النص
                var barcodeLines = Barcodes.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries)
                                          .Select(line => line.Trim())
                                          .Where(line => !string.IsNullOrWhiteSpace(line))
                                          .Distinct()
                                          .ToList();

                // حفظ الباركودات المتعددة (يمكن إضافة جدول منفصل للباركودات المتعددة لاحقاً)
                // مؤقتاً سنحفظ الباركود الأول فقط في جدول المنتجات
                if (barcodeLines.Any())
                {
                    Product.Barcode = barcodeLines.First();
                }

                System.Diagnostics.Debug.WriteLine($"تم حفظ {barcodeLines.Count} باركود للمنتج {Product.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الباركودات المتعددة: {ex.Message}");
            }
            return Task.CompletedTask;
        }

        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }

        #endregion

        #region Events

        public event Action<Product>? ProductSaved;
        public event Action<bool>? RequestClose;

        #endregion
    }
}
