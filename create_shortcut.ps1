# إنشاء اختصار لسطح المكتب لنظام إدارة المبيعات
$WshShell = New-Object -comObject WScript.Shell
$DesktopPath = [System.Environment]::GetFolderPath('Desktop')
$ShortcutPath = Join-Path $DesktopPath "نظام إدارة المبيعات.lnk"
$Shortcut = $WshShell.CreateShortcut($ShortcutPath)

# إعداد الاختصار
$CurrentPath = Get-Location
$Shortcut.TargetPath = "dotnet.exe"
$Shortcut.Arguments = "run"
$Shortcut.WorkingDirectory = $CurrentPath.Path
$Shortcut.IconLocation = "shell32.dll,21"
$Shortcut.Description = "نظام إدارة المبيعات - Sales Management System"

# حفظ الاختصار
$Shortcut.Save()

Write-Host "تم إنشاء الاختصار بنجاح على سطح المكتب!" -ForegroundColor Green
Write-Host "مسار الاختصار: $ShortcutPath" -ForegroundColor Yellow
