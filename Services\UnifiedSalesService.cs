using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class UnifiedSalesService
    {
        private readonly DatabaseService _dbService;
        private readonly CustomerService _customerService;

        public UnifiedSalesService(DatabaseService dbService)
        {
            _dbService = dbService;
            _customerService = new CustomerService(dbService);
        }

        /// <summary>
        /// الحصول على جميع المبيعات (فواتير + بيع سريع) موحدة
        /// </summary>
        public async Task<List<UnifiedSale>> GetAllUnifiedSalesAsync()
        {
            var unifiedSales = new List<UnifiedSale>();

            try
            {
                // الحصول على فواتير البيع
                var invoiceSales = await GetInvoiceSalesAsync();
                unifiedSales.AddRange(invoiceSales);

                // الحصول على عمليات البيع السريع
                var quickSales = await GetQuickSalesAsync();
                unifiedSales.AddRange(quickSales);

                // ترتيب حسب التاريخ (الأحدث أولاً)
                unifiedSales = unifiedSales.OrderByDescending(s => s.SaleDate).ToList();

                LoggingService.LogInfo($"تم الحصول على {unifiedSales.Count} عملية بيع موحدة");
                return unifiedSales;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على المبيعات الموحدة");
                return new List<UnifiedSale>();
            }
        }

        /// <summary>
        /// الحصول على فواتير البيع كمبيعات موحدة
        /// </summary>
        private async Task<List<UnifiedSale>> GetInvoiceSalesAsync()
        {
            const string sql = @"
                SELECT 
                    i.Id,
                    i.InvoiceNumber as SaleNumber,
                    i.CustomerId,
                    COALESCE(c.Name, 'عميل غير محدد') as CustomerName,
                    i.InvoiceDate as SaleDate,
                    i.Subtotal,
                    i.DiscountAmount as Discount,
                    i.TaxAmount as Tax,
                    i.TotalAmount,
                    i.PaidAmount,
                    i.RemainingAmount,
                    i.PaymentMethod,
                    CASE 
                        WHEN i.RemainingAmount = 0 THEN 'مدفوع'
                        WHEN i.PaidAmount > 0 THEN 'جزئي'
                        ELSE 'غير مدفوع'
                    END as PaymentStatus,
                    i.Notes,
                    i.CreatedAt
                FROM Invoices i
                LEFT JOIN Customers c ON i.CustomerId = c.Id
                WHERE i.InvoiceType = 'Sales'
                ORDER BY i.InvoiceDate DESC";

            var invoices = await _dbService.QueryAsync<dynamic>(sql);
            var unifiedSales = new List<UnifiedSale>();

            foreach (var invoice in invoices)
            {
                // الحصول على الديون السابقة للعميل
                var previousDebt = await GetCustomerPreviousDebtAsync((int)invoice.CustomerId, (DateTime)invoice.SaleDate);

                var unifiedSale = new UnifiedSale
                {
                    Id = (int)invoice.Id,
                    SaleNumber = invoice.SaleNumber?.ToString() ?? "",
                    SaleType = "فاتورة",
                    CustomerId = (int)invoice.CustomerId,
                    CustomerName = invoice.CustomerName?.ToString() ?? "عميل غير محدد",
                    SaleDate = (DateTime)invoice.SaleDate,
                    Subtotal = Convert.ToDecimal(invoice.Subtotal),
                    Discount = Convert.ToDecimal(invoice.Discount),
                    Tax = Convert.ToDecimal(invoice.Tax),
                    PreviousDebt = previousDebt,
                    TotalAmount = Convert.ToDecimal(invoice.TotalAmount),
                    PaidAmount = Convert.ToDecimal(invoice.PaidAmount),
                    RemainingAmount = Convert.ToDecimal(invoice.RemainingAmount),
                    PaymentMethod = invoice.PaymentMethod?.ToString() ?? "",
                    PaymentStatus = invoice.PaymentStatus?.ToString() ?? "",
                    Notes = invoice.Notes?.ToString() ?? "",
                    CreatedAt = (DateTime)invoice.CreatedAt
                };

                unifiedSales.Add(unifiedSale);
            }

            return unifiedSales;
        }

        /// <summary>
        /// الحصول على عمليات البيع السريع كمبيعات موحدة
        /// </summary>
        private async Task<List<UnifiedSale>> GetQuickSalesAsync()
        {
            const string sql = @"
                SELECT 
                    s.Id,
                    s.InvoiceNumber as SaleNumber,
                    s.CustomerId,
                    COALESCE(c.Name, 'عميل افتراضي') as CustomerName,
                    s.Date as SaleDate,
                    s.Subtotal,
                    s.Discount,
                    s.Tax,
                    s.Total as TotalAmount,
                    s.Total as PaidAmount,
                    0 as RemainingAmount,
                    s.PaymentMethod,
                    s.PaymentStatus,
                    s.Notes,
                    s.CreatedAt
                FROM Sales s
                LEFT JOIN Customers c ON s.CustomerId = c.Id
                ORDER BY s.Date DESC";

            var sales = await _dbService.QueryAsync<dynamic>(sql);
            var unifiedSales = new List<UnifiedSale>();

            foreach (var sale in sales)
            {
                // الحصول على الديون السابقة للعميل
                var previousDebt = await GetCustomerPreviousDebtAsync((int)sale.CustomerId, (DateTime)sale.SaleDate);

                var unifiedSale = new UnifiedSale
                {
                    Id = (int)sale.Id,
                    SaleNumber = sale.SaleNumber?.ToString() ?? "",
                    SaleType = "بيع سريع",
                    CustomerId = (int)sale.CustomerId,
                    CustomerName = sale.CustomerName?.ToString() ?? "عميل افتراضي",
                    SaleDate = (DateTime)sale.SaleDate,
                    Subtotal = Convert.ToDecimal(sale.Subtotal),
                    Discount = Convert.ToDecimal(sale.Discount),
                    Tax = Convert.ToDecimal(sale.Tax),
                    PreviousDebt = previousDebt,
                    TotalAmount = Convert.ToDecimal(sale.TotalAmount),
                    PaidAmount = Convert.ToDecimal(sale.PaidAmount),
                    RemainingAmount = Convert.ToDecimal(sale.RemainingAmount),
                    PaymentMethod = sale.PaymentMethod?.ToString() ?? "",
                    PaymentStatus = sale.PaymentStatus?.ToString() ?? "",
                    Notes = sale.Notes?.ToString() ?? "",
                    CreatedAt = (DateTime)sale.CreatedAt
                };

                unifiedSales.Add(unifiedSale);
            }

            return unifiedSales;
        }

        /// <summary>
        /// الحصول على الديون السابقة للعميل قبل تاريخ معين
        /// </summary>
        public async Task<decimal> GetCustomerPreviousDebtAsync(int customerId, DateTime beforeDate)
        {
            try
            {
                const string sql = @"
                    SELECT COALESCE(SUM(Amount), 0) as TotalDebt
                    FROM CustomerDebts 
                    WHERE CustomerId = @CustomerId 
                    AND CreatedAt < @BeforeDate 
                    AND Status != 'مدفوع'";

                var result = await _dbService.QuerySingleOrDefaultAsync<decimal?>(sql, new { CustomerId = customerId, BeforeDate = beforeDate });
                return result ?? 0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على الديون السابقة للعميل {customerId}");
                return 0;
            }
        }

        /// <summary>
        /// الحصول على إجمالي الديون الحالية للعميل
        /// </summary>
        public async Task<decimal> GetCustomerCurrentDebtAsync(int customerId)
        {
            try
            {
                const string sql = @"
                    SELECT COALESCE(SUM(Amount), 0) as TotalDebt
                    FROM CustomerDebts 
                    WHERE CustomerId = @CustomerId 
                    AND Status != 'مدفوع'";

                var result = await _dbService.QuerySingleOrDefaultAsync<decimal?>(sql, new { CustomerId = customerId });
                return result ?? 0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على الديون الحالية للعميل {customerId}");
                return 0;
            }
        }

        /// <summary>
        /// البحث في المبيعات الموحدة
        /// </summary>
        public async Task<List<UnifiedSale>> SearchUnifiedSalesAsync(string searchTerm, DateTime? fromDate = null, DateTime? toDate = null, string? saleType = null)
        {
            var allSales = await GetAllUnifiedSalesAsync();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                allSales = allSales.Where(s => 
                    s.SaleNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    s.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    s.Notes.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                ).ToList();
            }

            if (fromDate.HasValue)
            {
                allSales = allSales.Where(s => s.SaleDate.Date >= fromDate.Value.Date).ToList();
            }

            if (toDate.HasValue)
            {
                allSales = allSales.Where(s => s.SaleDate.Date <= toDate.Value.Date).ToList();
            }

            if (!string.IsNullOrEmpty(saleType))
            {
                allSales = allSales.Where(s => s.SaleType == saleType).ToList();
            }

            return allSales;
        }
    }
}
