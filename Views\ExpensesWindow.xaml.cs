using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Views
{
    public partial class ExpensesWindow : Window
    {
        private ObservableCollection<Expense> _expenses;

        public ExpensesWindow()
        {
            InitializeComponent();
            _expenses = new ObservableCollection<Expense>();
            ExpensesDataGrid.ItemsSource = _expenses;

            LoadSampleData();
            UpdateSummary();
        }

        private void LoadSampleData()
        {
            // بيانات تجريبية للمصاريف
            var sampleExpenses = new[]
            {
                new Expense
                {
                    Id = 1,
                    Title = "راتب أحمد محمد",
                    CategoryId = 1,
                    CategoryName = "راتب موظف",
                    Amount = 45000,
                    Date = DateTime.Now.AddDays(-5),
                    EmployeeName = "أحمد محمد",
                    Description = "راتب شهر ديسمبر",
                    Notes = "تم الدفع نقداً",
                    CreatedAt = DateTime.Now.AddDays(-5)
                },
                new Expense
                {
                    Id = 2,
                    Title = "منتجات منتهية الصلاحية",
                    CategoryId = 2,
                    CategoryName = "منتج منتهي الصلاحية",
                    Amount = 8500,
                    Date = DateTime.Now.AddDays(-3),
                    ProductName = "حليب مجفف - 10 علب",
                    Description = "منتجات انتهت صلاحيتها",
                    Notes = "تم التخلص منها",
                    CreatedAt = DateTime.Now.AddDays(-3)
                },
                new Expense
                {
                    Id = 3,
                    Title = "فاتورة الكهرباء",
                    CategoryId = 4,
                    CategoryName = "فواتير ومرافق",
                    Amount = 12000,
                    Date = DateTime.Now.AddDays(-7),
                    Description = "فاتورة كهرباء المحل",
                    Notes = "شهر نوفمبر",
                    CreatedAt = DateTime.Now.AddDays(-7)
                },
                new Expense
                {
                    Id = 4,
                    Title = "منتجات تالفة",
                    CategoryId = 3,
                    CategoryName = "منتج تالف",
                    Amount = 15000,
                    Date = DateTime.Now.AddDays(-2),
                    ProductName = "شاشات كمبيوتر - 3 قطع",
                    Description = "شاشات تعرضت للكسر أثناء النقل",
                    Notes = "مطالبة تأمين مقدمة",
                    CreatedAt = DateTime.Now.AddDays(-2)
                },
                new Expense
                {
                    Id = 5,
                    Title = "إيجار المحل",
                    CategoryId = 5,
                    CategoryName = "إيجار",
                    Amount = 35000,
                    Date = DateTime.Now.AddDays(-10),
                    Description = "إيجار شهر ديسمبر",
                    Notes = "تم الدفع للمالك",
                    CreatedAt = DateTime.Now.AddDays(-10)
                }
            };

            foreach (var expense in sampleExpenses)
            {
                _expenses.Add(expense);
            }
        }

        private void UpdateSummary()
        {
            if (_expenses.Count == 0)
            {
                ExpensesCountLabel.Text = "0";
                TotalExpensesLabel.Text = "0.00 دج";
                MonthlyExpensesLabel.Text = "0.00 دج";
                MaxExpenseLabel.Text = "0.00 دج";
                AverageExpenseLabel.Text = "0.00 دج";
                return;
            }

            var totalAmount = _expenses.Sum(e => e.Amount);
            var monthlyAmount = _expenses.Where(e => e.IsCurrentMonth).Sum(e => e.Amount);
            var maxAmount = _expenses.Max(e => e.Amount);
            var averageAmount = _expenses.Average(e => e.Amount);

            ExpensesCountLabel.Text = _expenses.Count.ToString();
            TotalExpensesLabel.Text = $"{totalAmount:F2} دج";
            MonthlyExpensesLabel.Text = $"{monthlyAmount:F2} دج";
            MaxExpenseLabel.Text = $"{maxAmount:F2} دج";
            AverageExpenseLabel.Text = $"{averageAmount:F2} دج";
        }

        private void AddExpense_Click(object sender, RoutedEventArgs e)
        {
            var addExpenseWindow = new AddExpenseWindow();
            if (addExpenseWindow.ShowDialog() == true && addExpenseWindow.NewExpense != null)
            {
                addExpenseWindow.NewExpense.Id = _expenses.Count > 0 ? _expenses.Max(e => e.Id) + 1 : 1;
                addExpenseWindow.NewExpense.CreatedAt = DateTime.Now;
                _expenses.Add(addExpenseWindow.NewExpense);
                UpdateSummary();

                MessageBox.Show("تم إضافة المصروف بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void EditExpense_Click(object sender, RoutedEventArgs e)
        {
            if (ExpensesDataGrid.SelectedItem is Expense selectedExpense)
            {
                var editExpenseWindow = new AddExpenseWindow(selectedExpense);
                if (editExpenseWindow.ShowDialog() == true && editExpenseWindow.NewExpense != null)
                {
                    var index = _expenses.IndexOf(selectedExpense);
                    if (index >= 0)
                    {
                        editExpenseWindow.NewExpense.Id = selectedExpense.Id;
                        editExpenseWindow.NewExpense.CreatedAt = selectedExpense.CreatedAt;
                        editExpenseWindow.NewExpense.UpdatedAt = DateTime.Now;
                        _expenses[index] = editExpenseWindow.NewExpense;
                        UpdateSummary();

                        MessageBox.Show("تم تعديل المصروف بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مصروف للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void DeleteExpense_Click(object sender, RoutedEventArgs e)
        {
            if (ExpensesDataGrid.SelectedItem is Expense selectedExpense)
            {
                var result = MessageBox.Show($"هل تريد حذف المصروف '{selectedExpense.Title}'؟",
                                           "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    _expenses.Remove(selectedExpense);
                    UpdateSummary();
                    MessageBox.Show("تم حذف المصروف بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مصروف للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void ExpenseReport_Click(object sender, RoutedEventArgs e)
        {
            var reportWindow = new ExpenseReportWindow(_expenses.ToList());
            reportWindow.ShowDialog();
        }

        private void Search_Click(object sender, RoutedEventArgs e)
        {
            var searchWindow = new ExpenseSearchWindow();
            if (searchWindow.ShowDialog() == true)
            {
                // تطبيق فلاتر البحث
                MessageBox.Show("ميزة البحث قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    // نوافذ مؤقتة للوظائف
    public class ExpenseReportWindow : Window
    {
        public ExpenseReportWindow(System.Collections.Generic.List<Expense> expenses)
        {
            Title = "تقرير المصاريف";
            Width = 600;
            Height = 400;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            var content = new StackPanel();
            content.Children.Add(new TextBlock
            {
                Text = $"تقرير المصاريف - {expenses.Count} مصروف",
                HorizontalAlignment = HorizontalAlignment.Center,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(10)
            });

            content.Children.Add(new TextBlock
            {
                Text = $"إجمالي المصاريف: {expenses.Sum(e => e.Amount):F2} دج",
                HorizontalAlignment = HorizontalAlignment.Center,
                FontSize = 14,
                Margin = new Thickness(10)
            });

            Content = content;
        }
    }

    public class ExpenseSearchWindow : Window
    {
        public ExpenseSearchWindow()
        {
            Title = "البحث في المصاريف";
            Width = 400;
            Height = 300;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            Content = new TextBlock
            {
                Text = "نافذة البحث - قيد التطوير",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
        }
    }
}
