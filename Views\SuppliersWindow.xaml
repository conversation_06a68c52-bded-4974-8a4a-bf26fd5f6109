<Window x:Class="SalesManagementSystem.Views.SuppliersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الموردين"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Purple">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="🏭" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="إدارة الموردين" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Add/Edit Supplier -->
            <GroupBox Grid.Column="0" Header="إضافة/تعديل مورد" Margin="0,0,10,0">
                <StackPanel Margin="10">
                    <TextBlock Text="اسم المورد:" Margin="0,0,0,5"/>
                    <TextBox Margin="0,0,0,10"/>

                    <TextBlock Text="رقم الهاتف:" Margin="0,0,0,5"/>
                    <TextBox Margin="0,0,0,10"/>

                    <TextBlock Text="البريد الإلكتروني:" Margin="0,0,0,5"/>
                    <TextBox Margin="0,0,0,10"/>

                    <TextBlock Text="العنوان:" Margin="0,0,0,5"/>
                    <TextBox Margin="0,0,0,10" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>

                    <TextBlock Text="رقم السجل التجاري:" Margin="0,0,0,5"/>
                    <TextBox Margin="0,0,0,10"/>

                    <TextBlock Text="الرقم الضريبي:" Margin="0,0,0,5"/>
                    <TextBox Margin="0,0,0,10"/>

                    <CheckBox Content="مورد نشط" IsChecked="True" Margin="0,10,0,10"/>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                        <Button Content="💾 حفظ" Width="80" Height="30" Margin="5"
                               Background="Green" Foreground="White"/>
                        <Button Content="🔄 جديد" Width="80" Height="30" Margin="5"
                               Background="Blue" Foreground="White"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>

            <!-- Suppliers List -->
            <GroupBox Grid.Column="1" Header="قائمة الموردين">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Search -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBox Width="200" Margin="0,0,10,0" Text="البحث في الموردين..."/>
                        <Button Content="🔍 بحث" Width="80" Background="Orange" Foreground="White"/>
                    </StackPanel>

                    <!-- Suppliers DataGrid -->
                    <DataGrid Grid.Row="1" AutoGenerateColumns="False" CanUserAddRows="False">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم" Width="*"/>
                            <DataGridTextColumn Header="الهاتف" Width="120"/>
                            <DataGridTextColumn Header="البريد الإلكتروني" Width="150"/>
                            <DataGridTextColumn Header="المدينة" Width="100"/>
                            <DataGridCheckBoxColumn Header="نشط" Width="60"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="✏️" Width="30" Height="25" Margin="2"
                                                   Background="Blue" Foreground="White" ToolTip="تعديل"/>
                                            <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                                   Background="Red" Foreground="White" ToolTip="حذف"/>
                                            <Button Content="📋" Width="30" Height="25" Margin="2"
                                                   Background="Green" Foreground="White" ToolTip="تفاصيل"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>

                        <!-- البيانات ستُحمل من قاعدة البيانات -->
                    </DataGrid>
                </Grid>
            </GroupBox>
        </Grid>

        <!-- Footer -->
        <Border Grid.Row="2" Background="LightGray">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="📊 تقرير الموردين" Width="130" Height="35" Margin="10"
                       Background="Purple" Foreground="White" FontWeight="Bold"/>
                <Button Content="📤 تصدير" Width="100" Height="35" Margin="10"
                       Background="Orange" Foreground="White" FontWeight="Bold"/>
                <Button Content="❌ إغلاق" Width="100" Height="35" Margin="10"
                       Background="Gray" Foreground="White" FontWeight="Bold" Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
