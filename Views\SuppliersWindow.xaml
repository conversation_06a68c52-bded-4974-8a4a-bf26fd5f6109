﻿<Window x:Class="SalesManagementSystem.Views.SuppliersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الموردين"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Purple">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="🏭" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="إدارة الموردين" FontSize="18"
                          FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Add/Edit Supplier -->
            <GroupBox Grid.Column="0" Header="إضافة/تعديل مورد" Margin="0,0,10,0">
                <StackPanel Margin="10">
                    <TextBlock Text="اسم المورد:" Margin="0,0,0,5"/>
                    <TextBox x:Name="SupplierNameTextBox" Margin="0,0,0,10"/>

                    <TextBlock Text="رقم الهاتف:" Margin="0,0,0,5"/>
                    <TextBox x:Name="PhoneTextBox" Margin="0,0,0,10"/>

                    <TextBlock Text="البريد الإلكتروني:" Margin="0,0,0,5"/>
                    <TextBox x:Name="EmailTextBox" Margin="0,0,0,10"/>

                    <TextBlock Text="العنوان:" Margin="0,0,0,5"/>
                    <TextBox x:Name="AddressTextBox" Margin="0,0,0,10" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                        <Button x:Name="SaveButton" Content=" حفظ" Width="80" Height="30" Margin="5"
                               Background="Green" Foreground="White" Click="SaveButton_Click"/>
                        <Button x:Name="NewButton" Content=" جديد" Width="80" Height="30" Margin="5"
                               Background="Blue" Foreground="White" Click="NewButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>

            <!-- Suppliers List -->
            <GroupBox Grid.Column="1" Header="قائمة الموردين">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Search -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBox x:Name="SearchTextBox" Width="200" Margin="0,0,10,0" Text="البحث في الموردين..."/>
                        <Button x:Name="SearchButton" Content=" بحث" Width="80" Background="Orange" Foreground="White" Click="SearchButton_Click"/>
                    </StackPanel>

                    <!-- Suppliers DataGrid -->
                    <DataGrid x:Name="SuppliersDataGrid" Grid.Row="1" AutoGenerateColumns="False" CanUserAddRows="False" SelectionChanged="SuppliersDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="*"/>
                            <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                            <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="150"/>
                            <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="200"/>
                            <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat=F2}" Width="100"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="" Width="30" Height="25" Margin="2"
                                                   Background="Blue" Foreground="White" ToolTip="تعديل" Click="EditButton_Click"/>
                                            <Button Content="" Width="30" Height="25" Margin="2"
                                                   Background="Red" Foreground="White" ToolTip="حذف" Click="DeleteButton_Click"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </GroupBox>
        </Grid>
    </Grid>
</Window>
