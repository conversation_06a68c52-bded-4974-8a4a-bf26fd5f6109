using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views.Dialogs
{
    public partial class ProductDialog : Window
    {
        private readonly ProductDialogViewModel _viewModel;

        public Product? Result { get; private set; }

        public ProductDialog(Product? existingProduct = null)
        {
            InitializeComponent();

            _viewModel = existingProduct != null
                ? new ProductDialogViewModel(existingProduct)
                : new ProductDialogViewModel();

            DataContext = _viewModel;

            // Subscribe to events
            _viewModel.ProductSaved += OnProductSaved;
            _viewModel.RequestClose += OnRequestClose;
        }

        #region Event Handlers

        private void OnProductSaved(Product product)
        {
            Result = product;
            DialogResult = true;
        }

        private void OnRequestClose(bool result)
        {
            DialogResult = result;
            Close();
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // Unsubscribe from events
            _viewModel.ProductSaved -= OnProductSaved;
            _viewModel.RequestClose -= OnRequestClose;

            base.OnClosed(e);
        }

        #endregion
    }
}
