using System;
using System.IO;
using System.Data.SQLite;
using SalesManagementSystem.Services;

namespace SalesManagementSystem
{
    public static class DatabaseInitializer
    {
        private static readonly string DatabasePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "SalesManagementSystem",
            "SalesDB.sqlite");

        public static void Initialize()
        {
            try
            {
                // إنشاء مجلد التطبيق إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(DatabasePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                if (!File.Exists(DatabasePath))
                {
                    CreateDatabase();
                }

                // التحقق من صحة قاعدة البيانات
                ValidateDatabase();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تهيئة قاعدة البيانات");
                
                // محاولة إنشاء قاعدة بيانات جديدة
                try
                {
                    if (File.Exists(DatabasePath))
                    {
                        File.Delete(DatabasePath);
                    }
                    CreateDatabase();
                }
                catch (Exception createEx)
                {
                    LoggingService.LogError(createEx, "فشل في إنشاء قاعدة بيانات جديدة");
                    throw;
                }
            }
        }

        private static void CreateDatabase()
        {
            SQLiteConnection.CreateFile(DatabasePath);

            using var connection = new SQLiteConnection($"Data Source={DatabasePath};Version=3;");
            connection.Open();

            // إنشاء الجداول الأساسية
            CreateBasicTables(connection);
        }

        private static void CreateBasicTables(SQLiteConnection connection)
        {
            var commands = new[]
            {
                // جدول المستخدمين
                @"CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT NOT NULL UNIQUE,
                    PasswordHash TEXT NOT NULL,
                    Email TEXT,
                    FullName TEXT,
                    Role TEXT DEFAULT 'User',
                    IsActive INTEGER DEFAULT 1,
                    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt TEXT
                );",

                // جدول المنتجات
                @"CREATE TABLE IF NOT EXISTS Products (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Code TEXT UNIQUE,
                    Barcode TEXT,
                    Description TEXT,
                    CategoryId INTEGER,
                    SalePrice REAL DEFAULT 0,
                    PurchasePrice REAL DEFAULT 0,
                    Quantity INTEGER DEFAULT 0,
                    MinimumStock INTEGER DEFAULT 0,
                    IsActive INTEGER DEFAULT 1,
                    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt TEXT
                );",

                // جدول العملاء
                @"CREATE TABLE IF NOT EXISTS Customers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Email TEXT,
                    Phone TEXT,
                    Address TEXT,
                    IsActive INTEGER DEFAULT 1,
                    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt TEXT
                );",

                // جدول المبيعات
                @"CREATE TABLE IF NOT EXISTS Sales (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT NOT NULL UNIQUE,
                    CustomerId INTEGER,
                    Date TEXT DEFAULT CURRENT_TIMESTAMP,
                    Subtotal REAL DEFAULT 0,
                    Tax REAL DEFAULT 0,
                    Discount REAL DEFAULT 0,
                    Total REAL DEFAULT 0,
                    Status TEXT DEFAULT 'Completed',
                    CreatedBy TEXT,
                    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                );",

                // إدراج مستخدم افتراضي
                @"INSERT OR IGNORE INTO Users (Username, PasswordHash, FullName, Role) 
                  VALUES ('admin', 'admin123', 'مدير النظام', 'Admin');",

                // إدراج منتجات تجريبية
                @"INSERT OR IGNORE INTO Products (Name, Code, SalePrice, Quantity) 
                  VALUES 
                  ('منتج تجريبي 1', 'P001', 100.0, 50),
                  ('منتج تجريبي 2', 'P002', 200.0, 30),
                  ('منتج تجريبي 3', 'P003', 150.0, 25);",

                // إدراج عملاء تجريبيين
                @"INSERT OR IGNORE INTO Customers (Name, Phone) 
                  VALUES 
                  ('عميل تجريبي 1', '0501234567'),
                  ('عميل تجريبي 2', '0507654321');"
            };

            foreach (var commandText in commands)
            {
                using var command = new SQLiteCommand(commandText, connection);
                command.ExecuteNonQuery();
            }
        }

        private static void ValidateDatabase()
        {
            using var connection = new SQLiteConnection($"Data Source={DatabasePath};Version=3;");
            connection.Open();

            // التحقق من وجود الجداول الأساسية
            var tables = new[] { "Users", "Products", "Customers", "Sales" };
            
            foreach (var table in tables)
            {
                using var command = new SQLiteCommand(
                    $"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}';", 
                    connection);
                
                var result = command.ExecuteScalar();
                if (result == null)
                {
                    throw new Exception($"الجدول {table} غير موجود في قاعدة البيانات");
                }
            }
        }

        public static string GetConnectionString()
        {
            return $"Data Source={DatabasePath};Version=3;";
        }
    }
}
