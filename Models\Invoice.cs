using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج الفاتورة المتقدم
    /// </summary>
    public class Invoice : INotifyPropertyChanged
    {
        private int _id;
        private string _invoiceNumber = string.Empty;
        private InvoiceType _invoiceType = InvoiceType.Sales;
        private InvoiceStatus _status = InvoiceStatus.Draft;
        private int? _customerId;
        private int? _supplierId;
        private DateTime _invoiceDate = DateTime.Now;
        private DateTime? _dueDate;
        private decimal _subtotal;
        private decimal _taxAmount;
        private decimal _discountAmount;
        private decimal _totalAmount;
        private decimal _paidAmount;
        private decimal _remainingAmount;
        private string _notes = string.Empty;
        private string _terms = string.Empty;
        private string _createdBy = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private string _customer = string.Empty;
        private ObservableCollection<InvoiceItem> _items = new();
        private ObservableCollection<Payment> _payments = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set
            {
                if (_invoiceNumber != value)
                {
                    _invoiceNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public InvoiceType InvoiceType
        {
            get => _invoiceType;
            set
            {
                if (_invoiceType != value)
                {
                    _invoiceType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(InvoiceTypeDisplay));
                    OnPropertyChanged(nameof(InvoiceTypeIcon));
                }
            }
        }

        public InvoiceStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(CanEdit));
                    OnPropertyChanged(nameof(CanDelete));
                }
            }
        }

        public int? CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? SupplierId
        {
            get => _supplierId;
            set
            {
                if (_supplierId != value)
                {
                    _supplierId = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime InvoiceDate
        {
            get => _invoiceDate;
            set
            {
                if (_invoiceDate != value)
                {
                    _invoiceDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedInvoiceDate));
                }
            }
        }

        public DateTime? DueDate
        {
            get => _dueDate;
            set
            {
                if (_dueDate != value)
                {
                    _dueDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDueDate));
                    OnPropertyChanged(nameof(IsOverdue));
                    OnPropertyChanged(nameof(DaysUntilDue));
                }
            }
        }

        public decimal Subtotal
        {
            get => _subtotal;
            set
            {
                if (_subtotal != value)
                {
                    _subtotal = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedSubtotal));
                    CalculateTotal();
                }
            }
        }

        public decimal TaxAmount
        {
            get => _taxAmount;
            set
            {
                if (_taxAmount != value)
                {
                    _taxAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTaxAmount));
                    CalculateTotal();
                }
            }
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set
            {
                if (_discountAmount != value)
                {
                    _discountAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDiscountAmount));
                    CalculateTotal();
                }
            }
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set
            {
                if (_totalAmount != value)
                {
                    _totalAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalAmount));
                    UpdateRemainingAmount();
                }
            }
        }

        public decimal PaidAmount
        {
            get => _paidAmount;
            set
            {
                if (_paidAmount != value)
                {
                    _paidAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedPaidAmount));
                    UpdateRemainingAmount();
                }
            }
        }

        public decimal RemainingAmount
        {
            get => _remainingAmount;
            set
            {
                if (_remainingAmount != value)
                {
                    _remainingAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedRemainingAmount));
                    OnPropertyChanged(nameof(IsFullyPaid));
                    OnPropertyChanged(nameof(PaymentProgress));
                    UpdateStatus();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Terms
        {
            get => _terms;
            set
            {
                if (_terms != value)
                {
                    _terms = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<InvoiceItem> Items
        {
            get => _items;
            set
            {
                if (_items != value)
                {
                    _items = value;
                    OnPropertyChanged();
                    CalculateSubtotal();
                }
            }
        }

        public ObservableCollection<Payment> Payments
        {
            get => _payments;
            set
            {
                if (_payments != value)
                {
                    _payments = value;
                    OnPropertyChanged();
                    CalculatePaidAmount();
                }
            }
        }

        public string Customer
        {
            get => _customer;
            set
            {
                if (_customer != value)
                {
                    _customer = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public string FormattedSubtotal => Subtotal.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTaxAmount => TaxAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedDiscountAmount => DiscountAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalAmount => TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedPaidAmount => PaidAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedRemainingAmount => RemainingAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedInvoiceDate => InvoiceDate.ToString("dd/MM/yyyy");
        public string FormattedDueDate => DueDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");

        public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.Now && Status != InvoiceStatus.Paid;
        public bool IsFullyPaid => RemainingAmount <= 0;
        public bool CanEdit => Status == InvoiceStatus.Draft || Status == InvoiceStatus.Sent;
        public bool CanDelete => Status == InvoiceStatus.Draft;

        public int DaysUntilDue
        {
            get
            {
                if (!DueDate.HasValue) return 0;
                return (int)(DueDate.Value - DateTime.Now).TotalDays;
            }
        }

        public double PaymentProgress
        {
            get
            {
                if (TotalAmount == 0) return 0;
                return (double)(PaidAmount / TotalAmount) * 100;
            }
        }

        public string InvoiceTypeDisplay
        {
            get
            {
                return InvoiceType switch
                {
                    InvoiceType.Sales => "فاتورة مبيعات",
                    InvoiceType.Purchase => "فاتورة مشتريات",
                    InvoiceType.Return => "فاتورة مرتجع",
                    InvoiceType.Credit => "إشعار دائن",
                    InvoiceType.Debit => "إشعار مدين",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    InvoiceStatus.Draft => "مسودة",
                    InvoiceStatus.Sent => "مرسلة",
                    InvoiceStatus.Paid => "مدفوعة",
                    InvoiceStatus.PartiallyPaid => "مدفوعة جزئياً",
                    InvoiceStatus.Overdue => "متأخرة",
                    InvoiceStatus.Cancelled => "ملغية",
                    _ => "غير محدد"
                };
            }
        }

        public string InvoiceTypeIcon
        {
            get
            {
                return InvoiceType switch
                {
                    InvoiceType.Sales => "Receipt",
                    InvoiceType.Purchase => "ShoppingCart",
                    InvoiceType.Return => "Undo",
                    InvoiceType.Credit => "Plus",
                    InvoiceType.Debit => "Minus",
                    _ => "FileDocument"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    InvoiceStatus.Draft => "Gray",
                    InvoiceStatus.Sent => "Blue",
                    InvoiceStatus.Paid => "Green",
                    InvoiceStatus.PartiallyPaid => "Orange",
                    InvoiceStatus.Overdue => "Red",
                    InvoiceStatus.Cancelled => "Gray",
                    _ => "Gray"
                };
            }
        }

        // خصائص إضافية للعرض
        public int ItemsCount => Items?.Count ?? 0;
        public decimal TotalQuantity
        {
            get
            {
                try
                {
                    return Items?.Sum(i => i.Quantity) ?? 0;
                }
                catch
                {
                    return 0;
                }
            }
        }

        #endregion

        #region Methods

        public void AddItem(InvoiceItem item)
        {
            Items.Add(item);
            CalculateSubtotal();
        }

        public void RemoveItem(InvoiceItem item)
        {
            Items.Remove(item);
            CalculateSubtotal();
        }

        public void CalculateSubtotal()
        {
            try
            {
                Subtotal = Items?.Sum(item => item.Total) ?? 0;
            }
            catch
            {
                Subtotal = 0;
            }
        }

        public void CalculateTotal()
        {
            TotalAmount = Subtotal + TaxAmount - DiscountAmount;
        }

        public void CalculatePaidAmount()
        {
            try
            {
                PaidAmount = Payments?.Where(p => p.Status == PaymentStatus.Paid).Sum(p => p.Amount) ?? 0;
            }
            catch
            {
                PaidAmount = 0;
            }
        }

        private void UpdateRemainingAmount()
        {
            RemainingAmount = TotalAmount - PaidAmount;
        }

        private void UpdateStatus()
        {
            if (RemainingAmount <= 0 && TotalAmount > 0)
            {
                Status = InvoiceStatus.Paid;
            }
            else if (PaidAmount > 0 && RemainingAmount > 0)
            {
                Status = InvoiceStatus.PartiallyPaid;
            }
            else if (IsOverdue)
            {
                Status = InvoiceStatus.Overdue;
            }
        }

        public void MarkAsSent()
        {
            Status = InvoiceStatus.Sent;
            UpdatedAt = DateTime.Now;
        }

        public void MarkAsPaid()
        {
            PaidAmount = TotalAmount;
            Status = InvoiceStatus.Paid;
            UpdatedAt = DateTime.Now;
        }

        public void Cancel()
        {
            Status = InvoiceStatus.Cancelled;
            UpdatedAt = DateTime.Now;
        }

        public void AddPayment(Payment payment)
        {
            Payments.Add(payment);
            CalculatePaidAmount();
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// عنصر الفاتورة
    /// </summary>
    public class InvoiceItem : INotifyPropertyChanged
    {
        private int _id;
        private int _invoiceId;
        private int _productId;
        private string _productName = string.Empty;
        private string _description = string.Empty;
        private decimal _quantity;
        private decimal _unitPrice;
        private decimal _discount;
        private decimal _total;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int InvoiceId
        {
            get => _invoiceId;
            set
            {
                if (_invoiceId != value)
                {
                    _invoiceId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ProductId
        {
            get => _productId;
            set
            {
                if (_productId != value)
                {
                    _productId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductName
        {
            get => _productName;
            set
            {
                if (_productName != value)
                {
                    _productName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                    CalculateTotal();
                }
            }
        }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set
            {
                if (_unitPrice != value)
                {
                    _unitPrice = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedUnitPrice));
                    CalculateTotal();
                }
            }
        }

        public decimal Discount
        {
            get => _discount;
            set
            {
                if (_discount != value)
                {
                    _discount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDiscount));
                    CalculateTotal();
                }
            }
        }

        public decimal Total
        {
            get => _total;
            set
            {
                if (_total != value)
                {
                    _total = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotal));
                }
            }
        }

        public string FormattedUnitPrice => UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedDiscount => Discount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotal => Total.ToString("C", new System.Globalization.CultureInfo("ar-SA"));

        private void CalculateTotal()
        {
            Total = (Quantity * UnitPrice) - Discount;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    #region Enums

    public enum InvoiceType
    {
        Sales,      // مبيعات
        Purchase,   // مشتريات
        Return,     // مرتجع
        Credit,     // إشعار دائن
        Debit       // إشعار مدين
    }

    public enum InvoiceStatus
    {
        Draft,          // مسودة
        Sent,           // مرسلة
        Paid,           // مدفوعة
        PartiallyPaid,  // مدفوعة جزئياً
        Overdue,        // متأخرة
        Cancelled       // ملغية
    }

    #endregion

    #region Validation

    public class InvoiceValidator : AbstractValidator<Invoice>
    {
        public InvoiceValidator()
        {
            RuleFor(i => i.InvoiceNumber)
                .NotEmpty().WithMessage("رقم الفاتورة مطلوب")
                .MaximumLength(50).WithMessage("رقم الفاتورة لا يمكن أن يتجاوز 50 حرف");

            RuleFor(i => i.InvoiceDate)
                .NotEmpty().WithMessage("تاريخ الفاتورة مطلوب");

            RuleFor(i => i.TotalAmount)
                .GreaterThan(0).WithMessage("إجمالي الفاتورة يجب أن يكون أكبر من صفر");

            RuleFor(i => i.Items)
                .NotEmpty().WithMessage("يجب إضافة عنصر واحد على الأقل للفاتورة");
        }
    }

    public class InvoiceItemValidator : AbstractValidator<InvoiceItem>
    {
        public InvoiceItemValidator()
        {
            RuleFor(i => i.ProductName)
                .NotEmpty().WithMessage("اسم المنتج مطلوب");

            RuleFor(i => i.Quantity)
                .GreaterThan(0).WithMessage("الكمية يجب أن تكون أكبر من صفر");

            RuleFor(i => i.UnitPrice)
                .GreaterThan(0).WithMessage("سعر الوحدة يجب أن يكون أكبر من صفر");
        }
    }

    #endregion
}
