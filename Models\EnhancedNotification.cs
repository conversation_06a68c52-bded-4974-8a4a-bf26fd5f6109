using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;

namespace SalesManagementSystem.Models
{
    public enum EnhancedNotificationType
    {
        Info,
        Warning,
        Error,
        Success,
        Sale,
        Expense,
        LowStock,
        SystemUpdate,
        NewCustomer,
        Payment
    }

    public enum EnhancedNotificationPriority
    {
        Low,
        Normal,
        High,
        Critical
    }

    public class EnhancedNotification : INotifyPropertyChanged
    {
        private int _id;
        private string _title = string.Empty;
        private string _message = string.Empty;
        private EnhancedNotificationType _type;
        private EnhancedNotificationPriority _priority;
        private DateTime _createdAt;
        private bool _isRead;
        private string _actionUrl = string.Empty;
        private string _category = string.Empty;
        private bool _isSticky;

        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged();
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                OnPropertyChanged();
            }
        }

        public string Message
        {
            get => _message;
            set
            {
                _message = value;
                OnPropertyChanged();
            }
        }

        public EnhancedNotificationType Type
        {
            get => _type;
            set
            {
                _type = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TypeIcon));
                OnPropertyChanged(nameof(TypeColor));
                OnPropertyChanged(nameof(TypeBackground));
            }
        }

        public EnhancedNotificationPriority Priority
        {
            get => _priority;
            set
            {
                _priority = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PriorityIcon));
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                _createdAt = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TimeAgo));
                OnPropertyChanged(nameof(FormattedTime));
            }
        }

        public bool IsRead
        {
            get => _isRead;
            set
            {
                _isRead = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ReadStatusIcon));
                OnPropertyChanged(nameof(FontWeight));
            }
        }

        public string ActionUrl
        {
            get => _actionUrl;
            set
            {
                _actionUrl = value;
                OnPropertyChanged();
            }
        }

        public string Category
        {
            get => _category;
            set
            {
                _category = value;
                OnPropertyChanged();
            }
        }

        public bool IsSticky
        {
            get => _isSticky;
            set
            {
                _isSticky = value;
                OnPropertyChanged();
            }
        }

        // Computed properties
        public string TypeIcon => Type switch
        {
            EnhancedNotificationType.Info => "ℹ️",
            EnhancedNotificationType.Warning => "⚠️",
            EnhancedNotificationType.Error => "❌",
            EnhancedNotificationType.Success => "✅",
            EnhancedNotificationType.Sale => "💰",
            EnhancedNotificationType.Expense => "💸",
            EnhancedNotificationType.LowStock => "📦",
            EnhancedNotificationType.SystemUpdate => "🔄",
            EnhancedNotificationType.NewCustomer => "👤",
            EnhancedNotificationType.Payment => "💳",
            _ => "ℹ️"
        };

        public string TypeColor => Type switch
        {
            EnhancedNotificationType.Info => "#2196F3",
            EnhancedNotificationType.Warning => "#FF9800",
            EnhancedNotificationType.Error => "#F44336",
            EnhancedNotificationType.Success => "#4CAF50",
            EnhancedNotificationType.Sale => "#4CAF50",
            EnhancedNotificationType.Expense => "#FF5722",
            EnhancedNotificationType.LowStock => "#FF9800",
            EnhancedNotificationType.SystemUpdate => "#9C27B0",
            EnhancedNotificationType.NewCustomer => "#2196F3",
            EnhancedNotificationType.Payment => "#607D8B",
            _ => "#2196F3"
        };

        public string TypeBackground => Type switch
        {
            EnhancedNotificationType.Info => "#E3F2FD",
            EnhancedNotificationType.Warning => "#FFF3E0",
            EnhancedNotificationType.Error => "#FFEBEE",
            EnhancedNotificationType.Success => "#E8F5E8",
            EnhancedNotificationType.Sale => "#E8F5E8",
            EnhancedNotificationType.Expense => "#FFEBEE",
            EnhancedNotificationType.LowStock => "#FFF3E0",
            EnhancedNotificationType.SystemUpdate => "#F3E5F5",
            EnhancedNotificationType.NewCustomer => "#E3F2FD",
            EnhancedNotificationType.Payment => "#ECEFF1",
            _ => "#E3F2FD"
        };

        public string PriorityIcon => Priority switch
        {
            EnhancedNotificationPriority.Low => "",
            EnhancedNotificationPriority.Normal => "",
            EnhancedNotificationPriority.High => "🔥",
            EnhancedNotificationPriority.Critical => "🚨",
            _ => ""
        };

        public string ReadStatusIcon => IsRead ? "" : "🔴";
        public string FontWeight => IsRead ? "Normal" : "Bold";

        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.Now - CreatedAt;
                if (timeSpan.TotalMinutes < 1)
                    return "الآن";
                if (timeSpan.TotalMinutes < 60)
                    return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
                if (timeSpan.TotalHours < 24)
                    return $"منذ {(int)timeSpan.TotalHours} ساعة";
                return $"منذ {(int)timeSpan.TotalDays} يوم";
            }
        }

        public string FormattedTime => CreatedAt.ToString("HH:mm");

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
