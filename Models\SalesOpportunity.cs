using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج الفرصة التجارية
    /// </summary>
    public class SalesOpportunity : INotifyPropertyChanged
    {
        private int _id;
        private string _opportunityCode = string.Empty;
        private string _title = string.Empty;
        private string _description = string.Empty;
        private int _customerId;
        private string _customerName = string.Empty;
        private int _assignedSalesRepId;
        private string _assignedSalesRep = string.Empty;
        private OpportunityStage _stage = OpportunityStage.Prospecting;
        private OpportunityStatus _status = OpportunityStatus.Open;
        private OpportunityType _type = OpportunityType.NewBusiness;
        private OpportunityPriority _priority = OpportunityPriority.Medium;
        private OpportunitySource _source = OpportunitySource.Website;
        private decimal _estimatedValue;
        private decimal _actualValue;
        private int _probabilityPercentage = 50;
        private DateTime _expectedCloseDate = DateTime.Now.AddDays(30);
        private DateTime? _actualCloseDate;
        private string _competitorInfo = string.Empty;
        private string _keyDecisionMakers = string.Empty;
        private string _painPoints = string.Empty;
        private string _proposedSolution = string.Empty;
        private string _nextSteps = string.Empty;
        private DateTime? _lastActivityDate;
        private DateTime? _nextFollowUpDate;
        private string _lossReason = string.Empty;
        private string _winReason = string.Empty;
        private string _tags = string.Empty;
        private string _notes = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private string _createdBy = string.Empty;
        private ObservableCollection<OpportunityActivity> _activities = new();
        private ObservableCollection<OpportunityProduct> _products = new();
        private ObservableCollection<OpportunityDocument> _documents = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string OpportunityCode
        {
            get => _opportunityCode;
            set
            {
                if (_opportunityCode != value)
                {
                    _opportunityCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CustomerName
        {
            get => _customerName;
            set
            {
                if (_customerName != value)
                {
                    _customerName = value;
                    OnPropertyChanged();
                }
            }
        }

        public int AssignedSalesRepId
        {
            get => _assignedSalesRepId;
            set
            {
                if (_assignedSalesRepId != value)
                {
                    _assignedSalesRepId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string AssignedSalesRep
        {
            get => _assignedSalesRep;
            set
            {
                if (_assignedSalesRep != value)
                {
                    _assignedSalesRep = value;
                    OnPropertyChanged();
                }
            }
        }

        public OpportunityStage Stage
        {
            get => _stage;
            set
            {
                if (_stage != value)
                {
                    _stage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StageDisplay));
                    OnPropertyChanged(nameof(StageColor));
                    OnPropertyChanged(nameof(StageProgress));
                }
            }
        }

        public OpportunityStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsOpen));
                    OnPropertyChanged(nameof(IsClosed));
                }
            }
        }

        public OpportunityType Type
        {
            get => _type;
            set
            {
                if (_type != value)
                {
                    _type = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TypeDisplay));
                }
            }
        }

        public OpportunityPriority Priority
        {
            get => _priority;
            set
            {
                if (_priority != value)
                {
                    _priority = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PriorityDisplay));
                    OnPropertyChanged(nameof(PriorityColor));
                }
            }
        }

        public OpportunitySource Source
        {
            get => _source;
            set
            {
                if (_source != value)
                {
                    _source = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(SourceDisplay));
                }
            }
        }

        public decimal EstimatedValue
        {
            get => _estimatedValue;
            set
            {
                if (_estimatedValue != value)
                {
                    _estimatedValue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedEstimatedValue));
                    OnPropertyChanged(nameof(WeightedValue));
                }
            }
        }

        public decimal ActualValue
        {
            get => _actualValue;
            set
            {
                if (_actualValue != value)
                {
                    _actualValue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedActualValue));
                }
            }
        }

        public int ProbabilityPercentage
        {
            get => _probabilityPercentage;
            set
            {
                if (_probabilityPercentage != value)
                {
                    _probabilityPercentage = Math.Max(0, Math.Min(100, value));
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedProbability));
                    OnPropertyChanged(nameof(WeightedValue));
                }
            }
        }

        public DateTime ExpectedCloseDate
        {
            get => _expectedCloseDate;
            set
            {
                if (_expectedCloseDate != value)
                {
                    _expectedCloseDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedExpectedCloseDate));
                    OnPropertyChanged(nameof(DaysToClose));
                    OnPropertyChanged(nameof(IsOverdue));
                }
            }
        }

        public DateTime? ActualCloseDate
        {
            get => _actualCloseDate;
            set
            {
                if (_actualCloseDate != value)
                {
                    _actualCloseDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedActualCloseDate));
                }
            }
        }

        public string CompetitorInfo
        {
            get => _competitorInfo;
            set
            {
                if (_competitorInfo != value)
                {
                    _competitorInfo = value;
                    OnPropertyChanged();
                }
            }
        }

        public string KeyDecisionMakers
        {
            get => _keyDecisionMakers;
            set
            {
                if (_keyDecisionMakers != value)
                {
                    _keyDecisionMakers = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PainPoints
        {
            get => _painPoints;
            set
            {
                if (_painPoints != value)
                {
                    _painPoints = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProposedSolution
        {
            get => _proposedSolution;
            set
            {
                if (_proposedSolution != value)
                {
                    _proposedSolution = value;
                    OnPropertyChanged();
                }
            }
        }

        public string NextSteps
        {
            get => _nextSteps;
            set
            {
                if (_nextSteps != value)
                {
                    _nextSteps = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? LastActivityDate
        {
            get => _lastActivityDate;
            set
            {
                if (_lastActivityDate != value)
                {
                    _lastActivityDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLastActivityDate));
                    OnPropertyChanged(nameof(DaysSinceLastActivity));
                }
            }
        }

        public DateTime? NextFollowUpDate
        {
            get => _nextFollowUpDate;
            set
            {
                if (_nextFollowUpDate != value)
                {
                    _nextFollowUpDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedNextFollowUpDate));
                    OnPropertyChanged(nameof(IsFollowUpDue));
                }
            }
        }

        public string LossReason
        {
            get => _lossReason;
            set
            {
                if (_lossReason != value)
                {
                    _lossReason = value;
                    OnPropertyChanged();
                }
            }
        }

        public string WinReason
        {
            get => _winReason;
            set
            {
                if (_winReason != value)
                {
                    _winReason = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Tags
        {
            get => _tags;
            set
            {
                if (_tags != value)
                {
                    _tags = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<OpportunityActivity> Activities
        {
            get => _activities;
            set
            {
                if (_activities != value)
                {
                    _activities = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<OpportunityProduct> Products
        {
            get => _products;
            set
            {
                if (_products != value)
                {
                    _products = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<OpportunityDocument> Documents
        {
            get => _documents;
            set
            {
                if (_documents != value)
                {
                    _documents = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public decimal WeightedValue => EstimatedValue * (ProbabilityPercentage / 100m);

        public int DaysToClose => (ExpectedCloseDate - DateTime.Now).Days;

        public int DaysSinceLastActivity => LastActivityDate.HasValue 
            ? (DateTime.Now - LastActivityDate.Value).Days 
            : -1;

        public bool IsOverdue => DateTime.Now > ExpectedCloseDate && Status == OpportunityStatus.Open;

        public bool IsFollowUpDue => NextFollowUpDate.HasValue && DateTime.Now >= NextFollowUpDate.Value;

        public bool IsOpen => Status == OpportunityStatus.Open;

        public bool IsClosed => Status == OpportunityStatus.Won || Status == OpportunityStatus.Lost;

        public int StageProgress
        {
            get
            {
                return Stage switch
                {
                    OpportunityStage.Prospecting => 10,
                    OpportunityStage.Qualification => 25,
                    OpportunityStage.NeedsAnalysis => 40,
                    OpportunityStage.Proposal => 60,
                    OpportunityStage.Negotiation => 80,
                    OpportunityStage.Closing => 95,
                    _ => 0
                };
            }
        }

        // Display Properties
        public string StageDisplay
        {
            get
            {
                return Stage switch
                {
                    OpportunityStage.Prospecting => "البحث عن العملاء",
                    OpportunityStage.Qualification => "التأهيل",
                    OpportunityStage.NeedsAnalysis => "تحليل الاحتياجات",
                    OpportunityStage.Proposal => "تقديم العرض",
                    OpportunityStage.Negotiation => "التفاوض",
                    OpportunityStage.Closing => "الإغلاق",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    OpportunityStatus.Open => "مفتوحة",
                    OpportunityStatus.Won => "مكسوبة",
                    OpportunityStatus.Lost => "مفقودة",
                    OpportunityStatus.Cancelled => "ملغية",
                    _ => "غير محدد"
                };
            }
        }

        public string TypeDisplay
        {
            get
            {
                return Type switch
                {
                    OpportunityType.NewBusiness => "عمل جديد",
                    OpportunityType.ExistingBusiness => "عمل موجود",
                    OpportunityType.Renewal => "تجديد",
                    OpportunityType.Upgrade => "ترقية",
                    _ => "غير محدد"
                };
            }
        }

        public string PriorityDisplay
        {
            get
            {
                return Priority switch
                {
                    OpportunityPriority.Low => "منخفضة",
                    OpportunityPriority.Medium => "متوسطة",
                    OpportunityPriority.High => "عالية",
                    OpportunityPriority.Critical => "حرجة",
                    _ => "غير محدد"
                };
            }
        }

        public string SourceDisplay
        {
            get
            {
                return Source switch
                {
                    OpportunitySource.Website => "الموقع الإلكتروني",
                    OpportunitySource.SocialMedia => "وسائل التواصل",
                    OpportunitySource.Referral => "إحالة",
                    OpportunitySource.Advertisement => "إعلان",
                    OpportunitySource.Event => "فعالية",
                    OpportunitySource.ColdCall => "اتصال مباشر",
                    OpportunitySource.Email => "بريد إلكتروني",
                    OpportunitySource.Partner => "شريك",
                    OpportunitySource.Other => "أخرى",
                    _ => "غير محدد"
                };
            }
        }

        // Color Properties
        public string StageColor
        {
            get
            {
                return Stage switch
                {
                    OpportunityStage.Prospecting => "Blue",
                    OpportunityStage.Qualification => "Orange",
                    OpportunityStage.NeedsAnalysis => "Purple",
                    OpportunityStage.Proposal => "Teal",
                    OpportunityStage.Negotiation => "Brown",
                    OpportunityStage.Closing => "Green",
                    _ => "Gray"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    OpportunityStatus.Open => "Blue",
                    OpportunityStatus.Won => "Green",
                    OpportunityStatus.Lost => "Red",
                    OpportunityStatus.Cancelled => "Gray",
                    _ => "Gray"
                };
            }
        }

        public string PriorityColor
        {
            get
            {
                return Priority switch
                {
                    OpportunityPriority.Low => "Green",
                    OpportunityPriority.Medium => "Orange",
                    OpportunityPriority.High => "Red",
                    OpportunityPriority.Critical => "Purple",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedEstimatedValue => $"{EstimatedValue:C}";
        public string FormattedActualValue => $"{ActualValue:C}";
        public string FormattedWeightedValue => $"{WeightedValue:C}";
        public string FormattedProbability => $"{ProbabilityPercentage}%";
        public string FormattedExpectedCloseDate => ExpectedCloseDate.ToString("dd/MM/yyyy");
        public string FormattedActualCloseDate => ActualCloseDate?.ToString("dd/MM/yyyy") ?? "لم يتم الإغلاق";
        public string FormattedLastActivityDate => LastActivityDate?.ToString("dd/MM/yyyy") ?? "لا يوجد";
        public string FormattedNextFollowUpDate => NextFollowUpDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        public void MoveToNextStage()
        {
            if (Stage < OpportunityStage.Closing)
            {
                Stage = (OpportunityStage)((int)Stage + 1);
                UpdatedAt = DateTime.Now;
            }
        }

        public void MoveToPreviousStage()
        {
            if (Stage > OpportunityStage.Prospecting)
            {
                Stage = (OpportunityStage)((int)Stage - 1);
                UpdatedAt = DateTime.Now;
            }
        }

        public void MarkAsWon(decimal actualValue, string winReason = "")
        {
            Status = OpportunityStatus.Won;
            ActualValue = actualValue;
            WinReason = winReason;
            ActualCloseDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        public void MarkAsLost(string lossReason = "")
        {
            Status = OpportunityStatus.Lost;
            LossReason = lossReason;
            ActualCloseDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        public void AddActivity(string activityType, string description, string performedBy = "")
        {
            Activities.Add(new OpportunityActivity
            {
                OpportunityId = Id,
                ActivityType = activityType,
                Description = description,
                ActivityDate = DateTime.Now,
                PerformedBy = performedBy
            });

            LastActivityDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// نشاط الفرصة التجارية
    /// </summary>
    public class OpportunityActivity
    {
        public int Id { get; set; }
        public int OpportunityId { get; set; }
        public string ActivityType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime ActivityDate { get; set; } = DateTime.Now;
        public string PerformedBy { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;

        public string FormattedActivityDate => ActivityDate.ToString("dd/MM/yyyy HH:mm");
    }

    /// <summary>
    /// منتج الفرصة التجارية
    /// </summary>
    public class OpportunityProduct
    {
        public int Id { get; set; }
        public int OpportunityId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public string Notes { get; set; } = string.Empty;

        public string FormattedUnitPrice => $"{UnitPrice:C}";
        public string FormattedTotalPrice => $"{TotalPrice:C}";
    }

    /// <summary>
    /// مستند الفرصة التجارية
    /// </summary>
    public class OpportunityDocument
    {
        public int Id { get; set; }
        public int OpportunityId { get; set; }
        public string DocumentName { get; set; } = string.Empty;
        public string DocumentType { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime UploadedAt { get; set; } = DateTime.Now;
        public string UploadedBy { get; set; } = string.Empty;

        public string FormattedFileSize => FormatFileSize(FileSize);
        public string FormattedUploadedAt => UploadedAt.ToString("dd/MM/yyyy HH:mm");

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";
            
            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:F2} {sizes[order]}";
        }
    }

    #endregion

    #region Enums

    public enum OpportunityStage
    {
        Prospecting,    // البحث عن العملاء
        Qualification,  // التأهيل
        NeedsAnalysis,  // تحليل الاحتياجات
        Proposal,       // تقديم العرض
        Negotiation,    // التفاوض
        Closing         // الإغلاق
    }

    public enum OpportunityStatus
    {
        Open,           // مفتوحة
        Won,            // مكسوبة
        Lost,           // مفقودة
        Cancelled       // ملغية
    }

    public enum OpportunityType
    {
        NewBusiness,    // عمل جديد
        ExistingBusiness, // عمل موجود
        Renewal,        // تجديد
        Upgrade         // ترقية
    }

    public enum OpportunityPriority
    {
        Low,            // منخفضة
        Medium,         // متوسطة
        High,           // عالية
        Critical        // حرجة
    }

    public enum OpportunitySource
    {
        Website,        // الموقع الإلكتروني
        SocialMedia,    // وسائل التواصل
        Referral,       // إحالة
        Advertisement,  // إعلان
        Event,          // فعالية
        ColdCall,       // اتصال مباشر
        Email,          // بريد إلكتروني
        Partner,        // شريك
        Other           // أخرى
    }

    #endregion

    #region Validation

    public class SalesOpportunityValidator : AbstractValidator<SalesOpportunity>
    {
        public SalesOpportunityValidator()
        {
            RuleFor(o => o.Title)
                .NotEmpty().WithMessage("عنوان الفرصة مطلوب")
                .MaximumLength(200).WithMessage("عنوان الفرصة لا يمكن أن يتجاوز 200 حرف");

            RuleFor(o => o.CustomerId)
                .GreaterThan(0).WithMessage("العميل مطلوب");

            RuleFor(o => o.EstimatedValue)
                .GreaterThan(0).WithMessage("القيمة المقدرة يجب أن تكون أكبر من صفر");

            RuleFor(o => o.ProbabilityPercentage)
                .InclusiveBetween(0, 100).WithMessage("نسبة الاحتمالية يجب أن تكون بين 0 و 100");

            RuleFor(o => o.ExpectedCloseDate)
                .GreaterThanOrEqualTo(DateTime.Today).WithMessage("تاريخ الإغلاق المتوقع يجب أن يكون في المستقبل");
        }
    }

    #endregion
}
