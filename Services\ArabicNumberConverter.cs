using System;
using System.Globalization;
using System.Text;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة تحويل الأرقام من الهندية إلى العربية والعكس
    /// </summary>
    public static class ArabicNumberConverter
    {
        // الأرقام الهندية (الإنجليزية)
        private static readonly char[] HindiNumbers = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' };

        // الأرقام العربية
        private static readonly char[] ArabicNumbers = { '٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩' };

        /// <summary>
        /// إبقاء النص بالأرقام الإنجليزية (بدون تحويل)
        /// </summary>
        /// <param name="text">النص المراد إرجاعه كما هو</param>
        /// <returns>النص بالأرقام الإنجليزية</returns>
        public static string ConvertToArabicNumbers(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            // إرجاع النص كما هو بالأرقام الإنجليزية
            return text;
        }

        /// <summary>
        /// تحويل النص من الأرقام العربية إلى الهندية
        /// </summary>
        /// <param name="text">النص المراد تحويله</param>
        /// <returns>النص بالأرقام الهندية</returns>
        public static string ConvertToHindiNumbers(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            var result = new StringBuilder(text);

            for (int i = 0; i < ArabicNumbers.Length; i++)
            {
                result.Replace(ArabicNumbers[i], HindiNumbers[i]);
            }

            return result.ToString();
        }

        /// <summary>
        /// تحويل رقم إلى نص بالأرقام العربية
        /// </summary>
        /// <param name="number">الرقم المراد تحويله</param>
        /// <returns>النص بالأرقام العربية</returns>
        public static string ConvertNumberToArabic(int number)
        {
            return ConvertToArabicNumbers(number.ToString());
        }

        /// <summary>
        /// تحويل رقم عشري إلى نص بالأرقام العربية
        /// </summary>
        /// <param name="number">الرقم المراد تحويله</param>
        /// <param name="decimalPlaces">عدد الخانات العشرية</param>
        /// <returns>النص بالأرقام العربية</returns>
        public static string ConvertDecimalToArabic(decimal number, int decimalPlaces = 2)
        {
            string format = decimalPlaces > 0 ? $"F{decimalPlaces}" : "F0";
            return ConvertToArabicNumbers(number.ToString(format));
        }

        /// <summary>
        /// تحويل رقم مع تنسيق العملة الجزائرية
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="currency">العملة (افتراضي: دج)</param>
        /// <returns>النص بالأرقام الإنجليزية مع العملة الجزائرية</returns>
        public static string ConvertCurrencyToArabic(decimal amount, string currency = "دج")
        {
            var formattedAmount = ConvertDecimalToArabic(amount, 2);
            return $"{formattedAmount} {currency}";
        }

        /// <summary>
        /// تحويل التاريخ إلى نص بالأرقام العربية
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <param name="format">تنسيق التاريخ</param>
        /// <returns>التاريخ بالأرقام العربية</returns>
        public static string ConvertDateToArabic(DateTime date, string format = "yyyy/MM/dd")
        {
            return ConvertToArabicNumbers(date.ToString(format));
        }

        /// <summary>
        /// تحويل الوقت إلى نص بالأرقام العربية
        /// </summary>
        /// <param name="time">الوقت</param>
        /// <param name="format">تنسيق الوقت</param>
        /// <returns>الوقت بالأرقام العربية</returns>
        public static string ConvertTimeToArabic(DateTime time, string format = "HH:mm")
        {
            return ConvertToArabicNumbers(time.ToString(format));
        }

        /// <summary>
        /// تحويل النسبة المئوية إلى نص بالأرقام العربية
        /// </summary>
        /// <param name="percentage">النسبة المئوية</param>
        /// <returns>النسبة بالأرقام العربية</returns>
        public static string ConvertPercentageToArabic(decimal percentage)
        {
            return ConvertToArabicNumbers($"{percentage:F1}%");
        }

        /// <summary>
        /// تحويل رقم الهاتف إلى أرقام عربية
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <returns>رقم الهاتف بالأرقام العربية</returns>
        public static string ConvertPhoneToArabic(string phoneNumber)
        {
            return ConvertToArabicNumbers(phoneNumber);
        }

        /// <summary>
        /// تحويل الباركود إلى أرقام عربية
        /// </summary>
        /// <param name="barcode">الباركود</param>
        /// <returns>الباركود بالأرقام العربية</returns>
        public static string ConvertBarcodeToArabic(string barcode)
        {
            return ConvertToArabicNumbers(barcode);
        }

        /// <summary>
        /// تحويل نص مختلط (يحتوي على أرقام ونصوص) إلى أرقام عربية
        /// </summary>
        /// <param name="mixedText">النص المختلط</param>
        /// <returns>النص بالأرقام العربية</returns>
        public static string ConvertMixedTextToArabic(string mixedText)
        {
            return ConvertToArabicNumbers(mixedText);
        }

        /// <summary>
        /// فحص ما إذا كان النص يحتوي على أرقام عربية
        /// </summary>
        /// <param name="text">النص المراد فحصه</param>
        /// <returns>true إذا كان يحتوي على أرقام عربية</returns>
        public static bool ContainsArabicNumbers(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            foreach (char arabicNumber in ArabicNumbers)
            {
                if (text.Contains(arabicNumber))
                    return true;
            }
            return false;
        }

        /// <summary>
        /// فحص ما إذا كان النص يحتوي على أرقام هندية
        /// </summary>
        /// <param name="text">النص المراد فحصه</param>
        /// <returns>true إذا كان يحتوي على أرقام هندية</returns>
        public static bool ContainsHindiNumbers(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            foreach (char hindiNumber in HindiNumbers)
            {
                if (text.Contains(hindiNumber))
                    return true;
            }
            return false;
        }
    }
}
