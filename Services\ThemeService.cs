using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Animation;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الثيمات والألوان المتقدمة
    /// </summary>
    public class ThemeService
    {
        #region Singleton

        private static ThemeService? _instance;
        public static ThemeService Instance => _instance ??= new ThemeService();

        #endregion

        #region Enums

        public enum ThemeType
        {
            Light,
            Dark,
            Modern,
            Auto
        }

        public enum AccentColor
        {
            Blue,
            Purple,
            Orange,
            Green,
            Red,
            Teal,
            Pink,
            Indigo
        }

        #endregion

        #region Properties

        public ThemeType CurrentTheme { get; private set; } = ThemeType.Modern;
        public AccentColor CurrentAccent { get; private set; } = AccentColor.Blue;
        public bool IsAnimatedTransitions { get; set; } = true;

        #endregion

        #region Events

        public event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

        #endregion

        #region Theme Management

        /// <summary>
        /// تطبيق ثيم جديد
        /// </summary>
        public async Task ApplyThemeAsync(ThemeType theme, AccentColor? accent = null)
        {
            try
            {
                var oldTheme = CurrentTheme;
                CurrentTheme = theme;

                if (accent.HasValue)
                {
                    CurrentAccent = accent.Value;
                }

                // تحديد مسار ملف الثيم
                var themeUri = GetThemeUri(theme);
                var accentUri = GetAccentUri(CurrentAccent);

                if (IsAnimatedTransitions)
                {
                    await AnimateThemeTransitionAsync();
                }

                // تطبيق الثيم الجديد
                ApplyResourceDictionary(themeUri);
                ApplyResourceDictionary(accentUri);

                // حفظ الإعدادات
                await SaveThemeSettingsAsync();

                // إثارة حدث تغيير الثيم
                ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(oldTheme, CurrentTheme, CurrentAccent));

                LoggingService.LogInfo($"تم تطبيق الثيم: {theme} مع اللون: {CurrentAccent}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تطبيق الثيم");
                throw;
            }
        }

        /// <summary>
        /// تبديل بين الثيم الفاتح والمظلم
        /// </summary>
        public async Task ToggleThemeAsync()
        {
            var newTheme = CurrentTheme switch
            {
                ThemeType.Light => ThemeType.Dark,
                ThemeType.Dark => ThemeType.Light,
                ThemeType.Modern => ThemeType.Dark,
                _ => ThemeType.Light
            };

            await ApplyThemeAsync(newTheme);
        }

        /// <summary>
        /// تطبيق لون التمييز
        /// </summary>
        public async Task ApplyAccentColorAsync(AccentColor accent)
        {
            await ApplyThemeAsync(CurrentTheme, accent);
        }

        /// <summary>
        /// تحميل إعدادات الثيم المحفوظة
        /// </summary>
        public async Task LoadSavedThemeAsync()
        {
            try
            {
                // استخدام DatabaseService مؤقتاً حتى يتم إصلاح SettingsService
                var themeStr = "Modern"; // افتراضي
                var accentStr = "Blue"; // افتراضي

                if (Enum.TryParse<ThemeType>(themeStr, out var theme) &&
                    Enum.TryParse<AccentColor>(accentStr, out var accent))
                {
                    await ApplyThemeAsync(theme, accent);
                }
                else
                {
                    // تطبيق الثيم الافتراضي
                    await ApplyThemeAsync(ThemeType.Modern, AccentColor.Blue);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل إعدادات الثيم");
                // تطبيق الثيم الافتراضي في حالة الخطأ
                await ApplyThemeAsync(ThemeType.Modern, AccentColor.Blue);
            }
        }

        #endregion

        #region Color Management

        /// <summary>
        /// الحصول على لون من الثيم الحالي
        /// </summary>
        public Color GetThemeColor(string colorKey)
        {
            try
            {
                if (Application.Current.Resources[colorKey] is Color color)
                {
                    return color;
                }

                if (Application.Current.Resources[colorKey] is SolidColorBrush brush)
                {
                    return brush.Color;
                }

                return Colors.Transparent;
            }
            catch
            {
                return Colors.Transparent;
            }
        }

        /// <summary>
        /// الحصول على فرشاة من الثيم الحالي
        /// </summary>
        public SolidColorBrush GetThemeBrush(string brushKey)
        {
            try
            {
                if (Application.Current.Resources[brushKey] is SolidColorBrush brush)
                {
                    return brush;
                }

                if (Application.Current.Resources[brushKey] is Color color)
                {
                    return new SolidColorBrush(color);
                }

                return new SolidColorBrush(Colors.Transparent);
            }
            catch
            {
                return new SolidColorBrush(Colors.Transparent);
            }
        }

        /// <summary>
        /// إنشاء لون متدرج
        /// </summary>
        public Color BlendColors(Color color1, Color color2, double ratio)
        {
            var r = (byte)(color1.R * (1 - ratio) + color2.R * ratio);
            var g = (byte)(color1.G * (1 - ratio) + color2.G * ratio);
            var b = (byte)(color1.B * (1 - ratio) + color2.B * ratio);
            var a = (byte)(color1.A * (1 - ratio) + color2.A * ratio);

            return Color.FromArgb(a, r, g, b);
        }

        /// <summary>
        /// تفتيح لون
        /// </summary>
        public Color LightenColor(Color color, double factor)
        {
            return BlendColors(color, Colors.White, factor);
        }

        /// <summary>
        /// تغميق لون
        /// </summary>
        public Color DarkenColor(Color color, double factor)
        {
            return BlendColors(color, Colors.Black, factor);
        }

        #endregion

        #region Private Methods

        private Uri GetThemeUri(ThemeType theme)
        {
            return theme switch
            {
                ThemeType.Light => new Uri("/Resources/Themes/LightTheme.xaml", UriKind.Relative),
                ThemeType.Dark => new Uri("/Resources/Themes/DarkTheme.xaml", UriKind.Relative),
                ThemeType.Modern => new Uri("/Resources/Themes/ModernTheme.xaml", UriKind.Relative),
                ThemeType.Auto => GetAutoThemeUri(),
                _ => new Uri("/Resources/Themes/ModernTheme.xaml", UriKind.Relative)
            };
        }

        private Uri GetAutoThemeUri()
        {
            // تحديد الثيم بناءً على إعدادات النظام
            var isLightTheme = IsSystemLightTheme();
            return isLightTheme
                ? new Uri("/Resources/Themes/LightTheme.xaml", UriKind.Relative)
                : new Uri("/Resources/Themes/DarkTheme.xaml", UriKind.Relative);
        }

        private Uri GetAccentUri(AccentColor accent)
        {
            // يمكن إضافة ملفات ألوان منفصلة لاحقاً
            return accent switch
            {
                AccentColor.Blue => new Uri("pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Blue.xaml"),
                AccentColor.Purple => new Uri("pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Purple.xaml"),
                AccentColor.Orange => new Uri("pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Orange.xaml"),
                AccentColor.Green => new Uri("pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Green.xaml"),
                AccentColor.Red => new Uri("pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Red.xaml"),
                AccentColor.Teal => new Uri("pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Teal.xaml"),
                AccentColor.Pink => new Uri("pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Pink.xaml"),
                AccentColor.Indigo => new Uri("pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Indigo.xaml"),
                _ => new Uri("pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Blue.xaml")
            };
        }

        private void ApplyResourceDictionary(Uri uri)
        {
            try
            {
                var resourceDict = new ResourceDictionary { Source = uri };

                // إزالة القاموس القديم إذا كان موجوداً
                var existingDict = Application.Current.Resources.MergedDictionaries
                    .FirstOrDefault(d => d.Source?.ToString().Contains(Path.GetFileNameWithoutExtension(uri.ToString())) == true);

                if (existingDict != null)
                {
                    Application.Current.Resources.MergedDictionaries.Remove(existingDict);
                }

                // إضافة القاموس الجديد
                Application.Current.Resources.MergedDictionaries.Add(resourceDict);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تطبيق قاموس الموارد: {uri}");
            }
        }

        private async Task AnimateThemeTransitionAsync()
        {
            if (!IsAnimatedTransitions) return;

            try
            {
                // تأثير انتقال سلس للثيم
                var mainWindow = Application.Current.MainWindow;
                if (mainWindow != null)
                {
                    var fadeOut = new DoubleAnimation
                    {
                        From = 1.0,
                        To = 0.95,
                        Duration = TimeSpan.FromMilliseconds(150),
                        EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                    };

                    var fadeIn = new DoubleAnimation
                    {
                        From = 0.95,
                        To = 1.0,
                        Duration = TimeSpan.FromMilliseconds(150),
                        EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
                    };

                    var tcs = new TaskCompletionSource<bool>();
                    fadeOut.Completed += (s, e) =>
                    {
                        mainWindow.BeginAnimation(UIElement.OpacityProperty, fadeIn);
                    };
                    fadeIn.Completed += (s, e) => tcs.SetResult(true);

                    mainWindow.BeginAnimation(UIElement.OpacityProperty, fadeOut);
                    await tcs.Task;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تأثير انتقال الثيم");
            }
        }

        private async Task SaveThemeSettingsAsync()
        {
            try
            {
                // حفظ مؤقت - يمكن تحسينه لاحقاً
                LoggingService.LogInfo($"تم حفظ إعدادات الثيم: {CurrentTheme}, {CurrentAccent}");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في حفظ إعدادات الثيم");
            }
        }

        private bool IsSystemLightTheme()
        {
            try
            {
                // فحص إعدادات النظام لتحديد الثيم
                using var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
                var value = key?.GetValue("AppsUseLightTheme");
                return value is int intValue && intValue == 1;
            }
            catch
            {
                return true; // افتراضي: ثيم فاتح
            }
        }

        #endregion
    }

    #region Helper Classes

    public class ThemeSettings
    {
        public ThemeService.ThemeType Theme { get; set; }
        public ThemeService.AccentColor Accent { get; set; }
        public bool IsAnimatedTransitions { get; set; }
    }

    public class ThemeChangedEventArgs : EventArgs
    {
        public ThemeService.ThemeType OldTheme { get; }
        public ThemeService.ThemeType NewTheme { get; }
        public ThemeService.AccentColor AccentColor { get; }

        public ThemeChangedEventArgs(ThemeService.ThemeType oldTheme, ThemeService.ThemeType newTheme, ThemeService.AccentColor accentColor)
        {
            OldTheme = oldTheme;
            NewTheme = newTheme;
            AccentColor = accentColor;
        }
    }

    #endregion
}
