using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج موقع المخزن
    /// </summary>
    public class WarehouseLocation : INotifyPropertyChanged
    {
        private int _id;
        private int _warehouseId;
        private string _code = string.Empty;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private LocationType _type = LocationType.Shelf;
        private LocationStatus _status = LocationStatus.Available;
        private string _aisle = string.Empty;
        private string _rack = string.Empty;
        private string _shelf = string.Empty;
        private string _bin = string.Empty;
        private decimal _capacity;
        private decimal _usedCapacity;
        private decimal _availableCapacity;
        private string _barcode = string.Empty;
        private string _notes = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private ObservableCollection<InventoryItem> _inventory = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int WarehouseId
        {
            get => _warehouseId;
            set
            {
                if (_warehouseId != value)
                {
                    _warehouseId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public LocationType Type
        {
            get => _type;
            set
            {
                if (_type != value)
                {
                    _type = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TypeDisplay));
                    OnPropertyChanged(nameof(TypeIcon));
                }
            }
        }

        public LocationStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsAvailable));
                }
            }
        }

        public string Aisle
        {
            get => _aisle;
            set
            {
                if (_aisle != value)
                {
                    _aisle = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string Rack
        {
            get => _rack;
            set
            {
                if (_rack != value)
                {
                    _rack = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string Shelf
        {
            get => _shelf;
            set
            {
                if (_shelf != value)
                {
                    _shelf = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string Bin
        {
            get => _bin;
            set
            {
                if (_bin != value)
                {
                    _bin = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public decimal Capacity
        {
            get => _capacity;
            set
            {
                if (_capacity != value)
                {
                    _capacity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCapacity));
                    UpdateAvailableCapacity();
                }
            }
        }

        public decimal UsedCapacity
        {
            get => _usedCapacity;
            set
            {
                if (_usedCapacity != value)
                {
                    _usedCapacity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedUsedCapacity));
                    OnPropertyChanged(nameof(CapacityUsagePercentage));
                    UpdateAvailableCapacity();
                }
            }
        }

        public decimal AvailableCapacity
        {
            get => _availableCapacity;
            set
            {
                if (_availableCapacity != value)
                {
                    _availableCapacity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAvailableCapacity));
                }
            }
        }

        public string Barcode
        {
            get => _barcode;
            set
            {
                if (_barcode != value)
                {
                    _barcode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<InventoryItem> Inventory
        {
            get => _inventory;
            set
            {
                if (_inventory != value)
                {
                    _inventory = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TotalItems));
                    OnPropertyChanged(nameof(TotalValue));
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsAvailable => Status == LocationStatus.Available;
        public int TotalItems => Inventory?.Sum(i => (int)i.Quantity) ?? 0;
        public decimal TotalValue => Inventory?.Sum(i => i.TotalValue) ?? 0;

        public double CapacityUsagePercentage
        {
            get
            {
                if (Capacity == 0) return 0;
                return (double)(UsedCapacity / Capacity) * 100;
            }
        }

        public string FullAddress
        {
            get
            {
                var parts = new[] { Aisle, Rack, Shelf, Bin }.Where(p => !string.IsNullOrEmpty(p));
                return string.Join("-", parts);
            }
        }

        public string FormattedCapacity => $"{Capacity:N2} م³";
        public string FormattedUsedCapacity => $"{UsedCapacity:N2} م³";
        public string FormattedAvailableCapacity => $"{AvailableCapacity:N2} م³";
        public string FormattedTotalValue => TotalValue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        public string TypeDisplay
        {
            get
            {
                return Type switch
                {
                    LocationType.Shelf => "رف",
                    LocationType.Bin => "صندوق",
                    LocationType.Pallet => "منصة",
                    LocationType.Floor => "أرضية",
                    LocationType.Rack => "حامل",
                    LocationType.Zone => "منطقة",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    LocationStatus.Available => "متاح",
                    LocationStatus.Occupied => "مشغول",
                    LocationStatus.Reserved => "محجوز",
                    LocationStatus.Maintenance => "صيانة",
                    LocationStatus.Blocked => "محظور",
                    _ => "غير محدد"
                };
            }
        }

        public string TypeIcon
        {
            get
            {
                return Type switch
                {
                    LocationType.Shelf => "Bookshelf",
                    LocationType.Bin => "Package",
                    LocationType.Pallet => "ViewGrid",
                    LocationType.Floor => "FloorPlan",
                    LocationType.Rack => "Rack",
                    LocationType.Zone => "MapMarker",
                    _ => "MapMarker"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    LocationStatus.Available => "Green",
                    LocationStatus.Occupied => "Blue",
                    LocationStatus.Reserved => "Orange",
                    LocationStatus.Maintenance => "Purple",
                    LocationStatus.Blocked => "Red",
                    _ => "Gray"
                };
            }
        }

        #endregion

        #region Methods

        private void UpdateAvailableCapacity()
        {
            AvailableCapacity = Capacity - UsedCapacity;
        }

        public void UpdateInventory()
        {
            OnPropertyChanged(nameof(TotalItems));
            OnPropertyChanged(nameof(TotalValue));
            
            // حساب السعة المستخدمة بناءً على المخزون
            UsedCapacity = Inventory.Sum(i => i.Volume);
        }

        public void SetAvailable()
        {
            Status = LocationStatus.Available;
            UpdatedAt = DateTime.Now;
        }

        public void SetOccupied()
        {
            Status = LocationStatus.Occupied;
            UpdatedAt = DateTime.Now;
        }

        public void SetReserved()
        {
            Status = LocationStatus.Reserved;
            UpdatedAt = DateTime.Now;
        }

        public void SetMaintenance()
        {
            Status = LocationStatus.Maintenance;
            UpdatedAt = DateTime.Now;
        }

        public void Block()
        {
            Status = LocationStatus.Blocked;
            UpdatedAt = DateTime.Now;
        }

        public bool CanAcceptItem(decimal requiredCapacity)
        {
            return IsAvailable && AvailableCapacity >= requiredCapacity;
        }

        public void GenerateBarcode()
        {
            if (string.IsNullOrEmpty(Barcode))
            {
                Barcode = $"LOC-{WarehouseId:D3}-{Id:D6}";
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Enums

    public enum LocationType
    {
        Shelf,          // رف
        Bin,            // صندوق
        Pallet,         // منصة
        Floor,          // أرضية
        Rack,           // حامل
        Zone            // منطقة
    }

    public enum LocationStatus
    {
        Available,      // متاح
        Occupied,       // مشغول
        Reserved,       // محجوز
        Maintenance,    // صيانة
        Blocked         // محظور
    }

    #endregion

    #region Validation

    public class WarehouseLocationValidator : AbstractValidator<WarehouseLocation>
    {
        public WarehouseLocationValidator()
        {
            RuleFor(l => l.Code)
                .NotEmpty().WithMessage("كود الموقع مطلوب")
                .MaximumLength(20).WithMessage("كود الموقع لا يمكن أن يتجاوز 20 حرف");

            RuleFor(l => l.Name)
                .NotEmpty().WithMessage("اسم الموقع مطلوب")
                .MaximumLength(100).WithMessage("اسم الموقع لا يمكن أن يتجاوز 100 حرف");

            RuleFor(l => l.WarehouseId)
                .GreaterThan(0).WithMessage("معرف المخزن مطلوب");

            RuleFor(l => l.Capacity)
                .GreaterThan(0).WithMessage("سعة الموقع يجب أن تكون أكبر من صفر");

            RuleFor(l => l.UsedCapacity)
                .GreaterThanOrEqualTo(0).WithMessage("السعة المستخدمة لا يمكن أن تكون سالبة")
                .LessThanOrEqualTo(l => l.Capacity).WithMessage("السعة المستخدمة لا يمكن أن تتجاوز سعة الموقع");
        }
    }

    #endregion
}
