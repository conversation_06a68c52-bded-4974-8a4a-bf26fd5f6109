using System;
using System.Threading.Tasks;
using System.Windows;
using SalesManagementSystem.Services;
using SalesManagementSystem.Views.Dialogs;

namespace SalesManagementSystem.Views
{
    public partial class SampleDataWindow : Window
    {
        private readonly SampleDataService _sampleDataService;
        private bool _isProcessing = false;

        public SampleDataWindow()
        {
            InitializeComponent();
            
            var dbService = new DatabaseService();
            _sampleDataService = new SampleDataService(dbService);
        }

        private async void CreateDataButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing) return;

            try
            {
                _isProcessing = true;
                SetUIState(false);
                
                StatusTextBlock.Text = "جاري إنشاء البيانات الافتراضية...";
                ProgressBar.Visibility = Visibility.Visible;
                ProgressBar.IsIndeterminate = true;

                var success = await _sampleDataService.CreateSampleProductsAsync();

                if (success)
                {
                    StatusTextBlock.Text = "✅ تم إنشاء البيانات الافتراضية بنجاح!";
                    MessageBox.Show(
                        "تم إنشاء 4 منتجات افتراضية مع الفئات والمخازن بنجاح!\n\n" +
                        "يمكنك الآن:\n" +
                        "• البحث عن المنتجات بالاسم أو الكود\n" +
                        "• تعديل أو حذف المنتجات\n" +
                        "• إضافة منتجات جديدة\n" +
                        "• استخدام الباركود للبحث السريع",
                        "نجح الإنشاء",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                else
                {
                    StatusTextBlock.Text = "❌ فشل في إنشاء البيانات الافتراضية";
                    MessageBox.Show(
                        "حدث خطأ أثناء إنشاء البيانات الافتراضية.\nيرجى المحاولة مرة أخرى.",
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "❌ حدث خطأ غير متوقع";
                MessageBox.Show(
                    $"حدث خطأ غير متوقع:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                _isProcessing = false;
                SetUIState(true);
                ProgressBar.Visibility = Visibility.Collapsed;
                ProgressBar.IsIndeterminate = false;
            }
        }

        private async void ResetDataButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing) return;

            var result = MessageBox.Show(
                "هل أنت متأكد من إعادة تعيين جميع البيانات؟\n\n" +
                "سيتم حذف جميع المنتجات والفئات والمخازن الموجودة وإنشاء بيانات افتراضية جديدة.\n\n" +
                "هذا الإجراء لا يمكن التراجع عنه!",
                "تأكيد إعادة التعيين",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result != MessageBoxResult.Yes) return;

            try
            {
                _isProcessing = true;
                SetUIState(false);
                
                StatusTextBlock.Text = "جاري إعادة تعيين البيانات...";
                ProgressBar.Visibility = Visibility.Visible;
                ProgressBar.IsIndeterminate = true;

                var success = await _sampleDataService.ResetSampleDataAsync();

                if (success)
                {
                    StatusTextBlock.Text = "✅ تم إعادة تعيين البيانات بنجاح!";
                    MessageBox.Show(
                        "تم إعادة تعيين جميع البيانات وإنشاء بيانات افتراضية جديدة بنجاح!",
                        "نجح إعادة التعيين",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                else
                {
                    StatusTextBlock.Text = "❌ فشل في إعادة تعيين البيانات";
                    MessageBox.Show(
                        "حدث خطأ أثناء إعادة تعيين البيانات.\nيرجى المحاولة مرة أخرى.",
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "❌ حدث خطأ غير متوقع";
                MessageBox.Show(
                    $"حدث خطأ غير متوقع:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                _isProcessing = false;
                SetUIState(true);
                ProgressBar.Visibility = Visibility.Collapsed;
                ProgressBar.IsIndeterminate = false;
            }
        }

        private void TestProductsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var productDialog = new ProductDialog();
                var result = productDialog.ShowDialog();
                
                if (result == true && productDialog.Result != null)
                {
                    MessageBox.Show(
                        $"تم حفظ المنتج بنجاح!\n\n" +
                        $"الاسم: {productDialog.Result.Name}\n" +
                        $"الكود: {productDialog.Result.Code}\n" +
                        $"الباركود: {productDialog.Result.Barcode}\n" +
                        $"السعر: {productDialog.Result.SalePrice:C}",
                        "نجح الحفظ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                    
                    StatusTextBlock.Text = $"✅ تم إضافة المنتج: {productDialog.Result.Name}";
                }
                else
                {
                    StatusTextBlock.Text = "تم إلغاء إضافة المنتج";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء فتح نافذة إضافة المنتج:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void SetUIState(bool enabled)
        {
            CreateDataButton.IsEnabled = enabled;
            ResetDataButton.IsEnabled = enabled;
            TestProductsButton.IsEnabled = enabled;
            CloseButton.IsEnabled = enabled;
        }
    }
}
