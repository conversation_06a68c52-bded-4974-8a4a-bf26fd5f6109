using System;
using System.ComponentModel;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// عنصر في البيع السريع
    /// </summary>
    public class QuickSaleItem : INotifyPropertyChanged
    {
        private int _quantity;
        private decimal _unitPrice;

        public string Barcode { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public int ProductId { get; set; }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set
            {
                _unitPrice = value;
                OnPropertyChanged(nameof(UnitPrice));
                OnPropertyChanged(nameof(Total));
                OnPropertyChanged(nameof(FormattedUnitPrice));
                OnPropertyChanged(nameof(FormattedTotal));
            }
        }

        public int Quantity
        {
            get => _quantity;
            set
            {
                _quantity = value;
                OnPropertyChanged(nameof(Quantity));
                OnPropertyChanged(nameof(Total));
                OnPropertyChanged(nameof(FormattedTotal));
            }
        }

        public decimal Total => UnitPrice * Quantity;

        // خصائص للعرض المنسق
        public string FormattedUnitPrice => $"{UnitPrice:F2} دج";
        public string FormattedTotal => $"{Total:F2} دج";

        public DateTime AddedTime { get; set; } = DateTime.Now;
        public string Notes { get; set; } = string.Empty;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
