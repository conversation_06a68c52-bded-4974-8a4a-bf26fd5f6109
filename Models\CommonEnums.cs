namespace SalesManagementSystem.Models
{
    /// <summary>
    /// مستوى شدة التنبيه
    /// </summary>
    public enum AlertSeverity
    {
        Info = 0,           // معلومات
        Low = 1,            // منخفض
        Warning = 2,        // تحذير
        Medium = 3,         // متوسط
        High = 4,           // عالي
        Error = 5,          // خطأ
        Critical = 6        // حرج
    }

    /// <summary>
    /// حالة العنصر العامة
    /// </summary>
    public enum ItemStatus
    {
        Active = 0,         // نشط
        Inactive = 1,       // غير نشط
        Pending = 2,        // معلق
        Suspended = 3,      // معلق مؤقتاً
        Deleted = 4         // محذوف
    }

    /// <summary>
    /// أولوية العنصر
    /// </summary>
    public enum Priority
    {
        Low = 0,            // منخفض
        Normal = 1,         // عادي
        Medium = 2,         // متوسط
        High = 3,           // عالي
        Critical = 4        // حرج
    }

    /// <summary>
    /// نوع العملية
    /// </summary>
    public enum OperationType
    {
        Create = 0,         // إنشاء
        Read = 1,           // قراءة
        Update = 2,         // تحديث
        Delete = 3,         // حذف
        Import = 4,         // استيراد
        Export = 5,         // تصدير
        Backup = 6,         // نسخ احتياطي
        Restore = 7         // استعادة
    }

    /// <summary>
    /// حالة المعالجة
    /// </summary>
    public enum ProcessingStatus
    {
        NotStarted = 0,     // لم تبدأ
        InProgress = 1,     // قيد المعالجة
        Completed = 2,      // مكتملة
        Failed = 3,         // فشلت
        Cancelled = 4,      // ملغية
        Paused = 5          // متوقفة مؤقتاً
    }

    /// <summary>
    /// نوع البيانات
    /// </summary>
    public enum DataType
    {
        Text = 0,           // نص
        Number = 1,         // رقم
        Date = 2,           // تاريخ
        Boolean = 3,        // منطقي
        Currency = 4,       // عملة
        Percentage = 5,     // نسبة مئوية
        Email = 6,          // بريد إلكتروني
        Phone = 7,          // هاتف
        Url = 8,            // رابط
        Image = 9,          // صورة
        File = 10           // ملف
    }

    /// <summary>
    /// نوع التقرير
    /// </summary>
    public enum ReportType
    {
        Sales = 0,          // مبيعات
        Inventory = 1,      // مخزون
        Financial = 2,      // مالي
        Customer = 3,       // عملاء
        Supplier = 4,       // موردين
        Product = 5,        // منتجات
        Analytics = 6,      // تحليلات
        System = 7          // نظام
    }

    /// <summary>
    /// فترة التقرير
    /// </summary>
    public enum ReportPeriod
    {
        Today = 0,          // اليوم
        Yesterday = 1,      // أمس
        ThisWeek = 2,       // هذا الأسبوع
        LastWeek = 3,       // الأسبوع الماضي
        ThisMonth = 4,      // هذا الشهر
        LastMonth = 5,      // الشهر الماضي
        ThisQuarter = 6,    // هذا الربع
        LastQuarter = 7,    // الربع الماضي
        ThisYear = 8,       // هذا العام
        LastYear = 9,       // العام الماضي
        Custom = 10         // مخصص
    }

    /// <summary>
    /// تنسيق التصدير
    /// </summary>
    public enum ExportFormat
    {
        Excel = 0,          // Excel
        Pdf = 1,            // PDF
        Csv = 2,            // CSV
        Json = 3,           // JSON
        Xml = 4,            // XML
        Word = 5,           // Word
        Html = 6            // HTML
    }

    /// <summary>
    /// نوع الاتصال
    /// </summary>
    public enum ConnectionType
    {
        Local = 0,          // محلي
        Network = 1,        // شبكة
        Cloud = 2,          // سحابي
        Remote = 3,         // بعيد
        Api = 4,            // API
        Database = 5,       // قاعدة بيانات
        File = 6,           // ملف
        Service = 7         // خدمة
    }

    /// <summary>
    /// نوع الأمان
    /// </summary>
    public enum SecurityLevel
    {
        Public = 0,         // عام
        Internal = 1,       // داخلي
        Confidential = 2,   // سري
        Restricted = 3,     // مقيد
        TopSecret = 4       // سري للغاية
    }

    /// <summary>
    /// نوع الإجراء
    /// </summary>
    public enum ActionType
    {
        View = 0,           // عرض
        Add = 1,            // إضافة
        Edit = 2,           // تعديل
        Delete = 3,         // حذف
        Print = 4,          // طباعة
        Export = 5,         // تصدير
        Import = 6,         // استيراد
        Send = 7,           // إرسال
        Approve = 8,        // موافقة
        Reject = 9,         // رفض
        Cancel = 10,        // إلغاء
        Archive = 11        // أرشفة
    }

    /// <summary>
    /// نوع الإشعار
    /// </summary>
    public enum NotificationCategory
    {
        System = 0,         // نظام
        Sales = 1,          // مبيعات
        Inventory = 2,      // مخزون
        Financial = 3,      // مالي
        Customer = 4,       // عملاء
        Security = 5,       // أمان
        Backup = 6,         // نسخ احتياطي
        Update = 7,         // تحديث
        Maintenance = 8,    // صيانة
        Alert = 9           // تنبيه
    }

    /// <summary>
    /// طريقة الإشعار
    /// </summary>
    public enum NotificationMethod
    {
        InApp = 0,          // داخل التطبيق
        Email = 1,          // بريد إلكتروني
        Sms = 2,            // رسالة نصية
        Push = 3,           // إشعار فوري
        Desktop = 4,        // سطح المكتب
        Sound = 5,          // صوت
        Popup = 6,          // نافذة منبثقة
        Toast = 7           // إشعار مؤقت
    }

    /// <summary>
    /// حالة الاتصال
    /// </summary>
    public enum ConnectionStatus
    {
        Disconnected = 0,   // منقطع
        Connecting = 1,     // يتصل
        Connected = 2,      // متصل
        Error = 3,          // خطأ
        Timeout = 4,        // انتهت المهلة
        Retry = 5           // إعادة المحاولة
    }

    /// <summary>
    /// نوع المزامنة
    /// </summary>
    public enum SyncType
    {
        Manual = 0,         // يدوي
        Automatic = 1,      // تلقائي
        Scheduled = 2,      // مجدول
        RealTime = 3,       // فوري
        OnDemand = 4        // عند الطلب
    }

    /// <summary>
    /// حالة المزامنة
    /// </summary>
    public enum SyncStatus
    {
        NotSynced = 0,      // غير مزامن
        Syncing = 1,        // يزامن
        Synced = 2,         // مزامن
        Conflict = 3,       // تعارض
        Error = 4           // خطأ
    }
}
