# Create desktop shortcut for Sales Management System
$WshShell = New-Object -comObject WScript.Shell
$DesktopPath = [System.Environment]::GetFolderPath('Desktop')
$ShortcutPath = Join-Path $DesktopPath "Sales Management System.lnk"
$Shortcut = $WshShell.CreateShortcut($ShortcutPath)

# Setup shortcut properties
$CurrentPath = Get-Location
$Shortcut.TargetPath = "dotnet.exe"
$Shortcut.Arguments = "run"
$Shortcut.WorkingDirectory = $CurrentPath.Path
$Shortcut.IconLocation = "shell32.dll,21"
$Shortcut.Description = "Sales Management System"

# Save the shortcut
$Shortcut.Save()

Write-Host "Desktop shortcut created successfully!" -ForegroundColor Green
Write-Host "Shortcut path: $ShortcutPath" -ForegroundColor Yellow
