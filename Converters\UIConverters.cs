using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;

namespace SalesManagementSystem.Converters
{
    /// <summary>
    /// محول للتحقق من القيم الفارغة وإرجاع Visibility
    /// </summary>
    public class NullToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return Visibility.Collapsed;

            if (value is string str && string.IsNullOrWhiteSpace(str))
                return Visibility.Collapsed;

            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول Boolean إلى Visibility
    /// </summary>
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }
            return false;
        }
    }

    /// <summary>
    /// محول Boolean إلى Visibility معكوس
    /// </summary>
    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Collapsed;
            }
            return true;
        }
    }

    /// <summary>
    /// محول Boolean إلى Cursor
    /// </summary>
    public class BooleanToCursorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && boolValue)
            {
                return Cursors.Hand;
            }
            return Cursors.Arrow;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول Boolean إلى Boolean معكوس
    /// </summary>
    public class InverseBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }
    }

    /// <summary>
    /// محول القيم المتعددة لـ Boolean
    /// </summary>
    public class MultiBooleanConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length == 0)
                return false;

            var operation = parameter?.ToString()?.ToLower() ?? "and";

            switch (operation)
            {
                case "and":
                    foreach (var value in values)
                    {
                        if (value is bool boolValue && !boolValue)
                            return false;
                    }
                    return true;

                case "or":
                    foreach (var value in values)
                    {
                        if (value is bool boolValue && boolValue)
                            return true;
                    }
                    return false;

                default:
                    return false;
            }
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول النسبة المئوية إلى عرض
    /// </summary>
    public class PercentageToWidthConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length >= 2 &&
                values[0] is double percentage &&
                values[1] is double totalWidth)
            {
                return (percentage / 100.0) * totalWidth;
            }
            return 0.0;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول الرقم إلى نص مع تنسيق
    /// </summary>
    public class NumberToFormattedStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return "0";

            var format = parameter?.ToString() ?? "N0";

            if (value is decimal decimalValue)
                return decimalValue.ToString(format, culture);

            if (value is double doubleValue)
                return doubleValue.ToString(format, culture);

            if (value is float floatValue)
                return floatValue.ToString(format, culture);

            if (value is int intValue)
                return intValue.ToString(format, culture);

            if (value is long longValue)
                return longValue.ToString(format, culture);

            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && !string.IsNullOrWhiteSpace(stringValue))
            {
                if (targetType == typeof(decimal) || targetType == typeof(decimal?))
                {
                    if (decimal.TryParse(stringValue, NumberStyles.Any, culture, out decimal result))
                        return result;
                }
                else if (targetType == typeof(double) || targetType == typeof(double?))
                {
                    if (double.TryParse(stringValue, NumberStyles.Any, culture, out double result))
                        return result;
                }
                else if (targetType == typeof(int) || targetType == typeof(int?))
                {
                    if (int.TryParse(stringValue, NumberStyles.Any, culture, out int result))
                        return result;
                }
            }

            return Activator.CreateInstance(targetType) ?? new object();
        }
    }

    /// <summary>
    /// محول التاريخ إلى نص نسبي
    /// </summary>
    public class DateToRelativeStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DateTime dateTime)
            {
                var timeSpan = DateTime.Now - dateTime;

                if (timeSpan.TotalMinutes < 1)
                    return "الآن";
                else if (timeSpan.TotalMinutes < 60)
                    return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
                else if (timeSpan.TotalHours < 24)
                    return $"منذ {(int)timeSpan.TotalHours} ساعة";
                else if (timeSpan.TotalDays < 7)
                    return $"منذ {(int)timeSpan.TotalDays} يوم";
                else if (timeSpan.TotalDays < 30)
                    return $"منذ {(int)(timeSpan.TotalDays / 7)} أسبوع";
                else if (timeSpan.TotalDays < 365)
                    return $"منذ {(int)(timeSpan.TotalDays / 30)} شهر";
                else
                    return $"منذ {(int)(timeSpan.TotalDays / 365)} سنة";
            }

            return value?.ToString() ?? "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول الحجم إلى نص قابل للقراءة
    /// </summary>
    public class FileSizeToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is long bytes)
            {
                string[] sizes = { "B", "KB", "MB", "GB", "TB" };
                double len = bytes;
                int order = 0;

                while (len >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    len = len / 1024;
                }

                return $"{len:0.##} {sizes[order]}";
            }

            return "0 B";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
