using System;
using System.Threading.Tasks;
using SalesManagementSystem.Services;

namespace SalesManagementSystem
{
    public static class TestRunner
    {
        public static async Task RunBasicTests()
        {
            Console.WriteLine("🚀 بدء اختبار نظام إدارة المبيعات...");

            try
            {
                // اختبار خدمة قاعدة البيانات
                Console.WriteLine("📊 اختبار خدمة قاعدة البيانات...");
                var dbService = new DatabaseService();
                Console.WriteLine("✅ تم إنشاء قاعدة البيانات بنجاح");

                // اختبار الحصول على إصدار قاعدة البيانات
                var version = await dbService.GetDatabaseVersionAsync();
                Console.WriteLine($"📋 إصدار قاعدة البيانات: {version}");

                // اختبار خدمة الإعدادات
                Console.WriteLine("⚙️ اختبار خدمة الإعدادات...");
                var settingsService = new SettingsService(dbService);
                await settingsService.InitializeAsync();

                var companyName = await settingsService.GetCompanyNameAsync();
                Console.WriteLine($"🏢 اسم الشركة: {companyName}");

                // اختبار خدمة المنتجات
                Console.WriteLine("📦 اختبار خدمة المنتجات...");
                var productService = new ProductService(dbService);
                var productCount = await productService.GetProductCountAsync();
                Console.WriteLine($"📊 عدد المنتجات: {productCount}");

                // اختبار خدمة العملاء
                Console.WriteLine("👥 اختبار خدمة العملاء...");
                var customerService = new CustomerService(dbService);
                var customerCount = await customerService.GetCustomerCountAsync();
                Console.WriteLine($"📊 عدد العملاء: {customerCount}");

                Console.WriteLine("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح.");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"📋 تفاصيل الخطأ: {ex.StackTrace}");
                throw;
            }

            Console.WriteLine("✅ انتهى الاختبار بنجاح!");
        }
    }
}
