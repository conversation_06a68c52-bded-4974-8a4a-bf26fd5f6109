# نظام إدارة المخازن المتعددة - Sales Management System

## نظرة عامة

تم تطوير نظام إدارة مخازن متعدد ومتقدم يوفر حلولاً شاملة لإدارة المخازن، المواقع، نقل المخزون، والتتبع الدقيق مع واجهات حديثة وميزات ذكية.

## الميزات الرئيسية

### 🏢 إدارة المخازن الشاملة
- **أنواع متعددة**: رئيسي، فرع، عبور، مرتجعات، تالف، افتراضي
- **حالات متقدمة**: نشط، غير نشط، صيانة، مغلق
- **معلومات تفصيلية**: العنوان، المدير، السعة، الاتصال
- **إدارة السعة**: تتبع السعة الإجمالية والمستخدمة والمتاحة
- **مخزن افتراضي**: تعيين مخزن افتراضي للعمليات

### 📍 نظام المواقع المتطور
- **أنواع المواقع**: رف، صندوق، منصة، أرضية، حامل، منطقة
- **عنونة هرمية**: ممر - حامل - رف - صندوق
- **حالات ديناميكية**: متاح، مشغول، محجوز، صيانة، محظور
- **باركود تلقائي**: توليد باركود فريد لكل موقع
- **إدارة السعة**: تتبع سعة كل موقع على حدة

### 📦 إدارة المخزون المتقدمة
- **تتبع متعدد المخازن**: مراقبة المخزون عبر جميع المخازن
- **معلومات تفصيلية**: الكمية، التكلفة، الحجم، الوزن
- **إدارة الدفعات**: تتبع أرقام الدفعات والمسلسلات
- **تواريخ الانتهاء**: مراقبة المنتجات منتهية الصلاحية
- **مستويات المخزون**: حد أدنى، حد أقصى، نقطة إعادة الطلب
- **حجز المخزون**: حجز كميات للطلبات المعلقة

### 🚚 نظام نقل المخزون
- **أنواع النقل**: مخزن إلى مخزن، موقع إلى موقع، تسوية، إرجاع، تالف
- **مراحل النقل**: طلب، موافقة، شحن، استلام
- **تتبع الشحنات**: رقم تتبع، شركة النقل، التواريخ
- **موافقات متدرجة**: نظام موافقات للنقل
- **تسجيل شامل**: تسجيل جميع مراحل النقل

### 📊 تقارير وإحصائيات متقدمة
- **إحصائيات المخازن**: السعة، المخزون، القيمة
- **تقارير المخزون**: منخفض، منتهي الصلاحية، محجوز
- **تقارير النقل**: حالة النقل، الأداء، التأخير
- **تحليل الاستخدام**: كفاءة المخازن والمواقع

## البنية التقنية

### النماذج (Models)
```
Models/
├── Warehouse.cs                # نموذج المخزن المتقدم
├── WarehouseLocation.cs        # نموذج موقع المخزن
├── InventoryItem.cs            # نموذج عنصر المخزون المحدث
└── InventoryTransfer.cs        # نموذج نقل المخزون
```

### الخدمات (Services)
```
Services/
├── WarehouseService.cs         # خدمة إدارة المخازن
├── InventoryService.cs         # خدمة إدارة المخزون المحدثة
├── InventoryTransferService.cs # خدمة نقل المخزون
└── DatabaseService.cs          # خدمة قاعدة البيانات المحدثة
```

### الواجهات (Views)
```
Views/
├── WarehouseManagementView.xaml    # واجهة إدارة المخازن
├── InventoryTransferView.xaml      # واجهة نقل المخزون
├── WarehouseDetailsView.xaml       # تفاصيل المخزن
└── LocationManagementView.xaml     # إدارة المواقع
```

## قاعدة البيانات

### جدول المخازن (Warehouses)
```sql
CREATE TABLE Warehouses (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Code TEXT NOT NULL UNIQUE,
    Name TEXT NOT NULL,
    Description TEXT,
    Type TEXT NOT NULL DEFAULT 'Main',
    Status TEXT NOT NULL DEFAULT 'Active',
    Address TEXT,
    City TEXT,
    Phone TEXT,
    Email TEXT,
    ManagerName TEXT,
    TotalCapacity DECIMAL(10,2) DEFAULT 0,
    UsedCapacity DECIMAL(10,2) DEFAULT 0,
    AvailableCapacity DECIMAL(10,2) DEFAULT 0,
    IsDefault BOOLEAN DEFAULT 0,
    Notes TEXT,
    CreatedBy TEXT,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT
);
```

### جدول مواقع المخازن (WarehouseLocations)
```sql
CREATE TABLE WarehouseLocations (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    WarehouseId INTEGER NOT NULL,
    Code TEXT NOT NULL,
    Name TEXT NOT NULL,
    Description TEXT,
    Type TEXT NOT NULL DEFAULT 'Shelf',
    Status TEXT NOT NULL DEFAULT 'Available',
    Aisle TEXT,
    Rack TEXT,
    Shelf TEXT,
    Bin TEXT,
    Capacity DECIMAL(10,2) DEFAULT 0,
    UsedCapacity DECIMAL(10,2) DEFAULT 0,
    AvailableCapacity DECIMAL(10,2) DEFAULT 0,
    Barcode TEXT,
    Notes TEXT,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT
);
```

### جدول عناصر المخزون (InventoryItems)
```sql
CREATE TABLE InventoryItems (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductId INTEGER NOT NULL,
    WarehouseId INTEGER NOT NULL,
    LocationId INTEGER,
    Quantity DECIMAL(10,3) NOT NULL DEFAULT 0,
    UnitCost DECIMAL(10,2) NOT NULL DEFAULT 0,
    TotalValue DECIMAL(10,2) NOT NULL DEFAULT 0,
    ReservedQuantity DECIMAL(10,3) DEFAULT 0,
    AvailableQuantity DECIMAL(10,3) DEFAULT 0,
    MinStockLevel DECIMAL(10,3) DEFAULT 0,
    MaxStockLevel DECIMAL(10,3) DEFAULT 0,
    ReorderPoint DECIMAL(10,3) DEFAULT 0,
    Volume DECIMAL(10,3) DEFAULT 0,
    Weight DECIMAL(10,3) DEFAULT 0,
    BatchNumber TEXT,
    SerialNumber TEXT,
    ExpiryDate TEXT,
    LastUpdated TEXT NOT NULL,
    Notes TEXT
);
```

### جدول نقل المخزون (InventoryTransfers)
```sql
CREATE TABLE InventoryTransfers (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TransferNumber TEXT NOT NULL UNIQUE,
    TransferType TEXT NOT NULL DEFAULT 'WarehouseToWarehouse',
    Status TEXT NOT NULL DEFAULT 'Pending',
    FromWarehouseId INTEGER NOT NULL,
    ToWarehouseId INTEGER NOT NULL,
    FromLocationId INTEGER,
    ToLocationId INTEGER,
    RequestDate TEXT NOT NULL,
    ScheduledDate TEXT,
    ShippedDate TEXT,
    ReceivedDate TEXT,
    Reason TEXT NOT NULL,
    Notes TEXT,
    RequestedBy TEXT,
    ApprovedBy TEXT,
    ShippedBy TEXT,
    ReceivedBy TEXT,
    TotalValue DECIMAL(10,2) DEFAULT 0,
    TrackingNumber TEXT,
    CarrierName TEXT,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT
);
```

## الاستخدام

### إضافة مخزن جديد
```csharp
var warehouse = new Warehouse
{
    Code = "WH001",
    Name = "المخزن الرئيسي",
    Type = WarehouseType.Main,
    Status = WarehouseStatus.Active,
    Address = "شارع الملك فهد",
    City = "الرياض",
    ManagerName = "أحمد محمد",
    TotalCapacity = 10000.00m,
    IsDefault = true
};

var warehouseId = await warehouseService.AddWarehouseAsync(warehouse);
```

### إضافة موقع في المخزن
```csharp
var location = new WarehouseLocation
{
    WarehouseId = warehouseId,
    Code = "LOC001",
    Name = "الرف الأول",
    Type = LocationType.Shelf,
    Aisle = "A",
    Rack = "01",
    Shelf = "01",
    Bin = "01",
    Capacity = 100.00m
};

var locationId = await warehouseService.AddWarehouseLocationAsync(location);
```

### إضافة مخزون
```csharp
await inventoryService.AddInventoryAsync(
    productId: 1,
    warehouseId: warehouseId,
    locationId: locationId,
    quantity: 50,
    unitCost: 25.00m,
    batchNumber: "BATCH001",
    expiryDate: DateTime.Now.AddMonths(6)
);
```

### إنشاء طلب نقل
```csharp
var transfer = new InventoryTransfer
{
    TransferType = TransferType.WarehouseToWarehouse,
    FromWarehouseId = 1,
    ToWarehouseId = 2,
    Reason = "إعادة توزيع المخزون",
    RequestedBy = "مدير المخزون"
};

// إضافة عناصر النقل
transfer.AddItem(new InventoryTransferItem
{
    ProductId = 1,
    ProductName = "منتج أ",
    Quantity = 10,
    UnitCost = 25.00m
});

var transferId = await transferService.CreateTransferAsync(transfer);
```

### معالجة النقل
```csharp
// الموافقة على النقل
await transferService.ApproveTransferAsync(transferId, "مدير المخزون");

// شحن النقل
await transferService.ShipTransferAsync(transferId, "موظف الشحن", "TRK123", "شركة النقل");

// استلام النقل
await transferService.ReceiveTransferAsync(transferId, "مدير المخزن الوجهة");
```

### البحث والاستعلامات
```csharp
// البحث في المخازن
var warehouses = await warehouseService.SearchWarehousesAsync(
    searchTerm: "الرياض", 
    status: WarehouseStatus.Active, 
    type: WarehouseType.Main
);

// الحصول على المنتجات منخفضة المخزون
var lowStockItems = await inventoryService.GetLowStockItemsAsync();

// الحصول على المنتجات منتهية الصلاحية
var expiredItems = await inventoryService.GetExpiredItemsAsync();
```

## الميزات المتقدمة

### إدارة السعة الذكية
- حساب تلقائي للسعة المستخدمة والمتاحة
- تنبيهات عند اقتراب امتلاء المخزن
- توزيع ذكي للمنتجات على المواقع

### نظام التنبيهات
- تنبيهات المخزون المنخفض
- تنبيهات انتهاء الصلاحية
- تنبيهات طلبات النقل المعلقة
- تنبيهات تجاوز السعة

### التتبع والمراجعة
- سجل شامل لجميع حركات المخزون
- تتبع المسؤولين عن كل عملية
- إمكانية المراجعة والتدقيق

### التكامل مع الأنظمة الأخرى
- ربط مع نظام المبيعات للخصم التلقائي
- ربط مع نظام المشتريات للإضافة التلقائية
- ربط مع نظام الإشعارات للتنبيهات

## التقارير المتاحة

### تقارير المخازن
- تقرير حالة المخازن
- تقرير استخدام السعة
- تقرير أداء المخازن
- تقرير المخزون حسب المخزن

### تقارير المخزون
- تقرير المخزون الحالي
- تقرير المنتجات منخفضة المخزون
- تقرير المنتجات منتهية الصلاحية
- تقرير حركة المخزون

### تقارير النقل
- تقرير طلبات النقل
- تقرير أداء النقل
- تقرير التأخير في النقل
- تقرير تكلفة النقل

## الأمان والحماية

### التحقق من الصلاحيات
- صلاحيات مختلفة لكل نوع مستخدم
- تحكم في الوصول للمخازن
- موافقات متدرجة للنقل

### حماية البيانات
- تشفير البيانات الحساسة
- نسخ احتياطية تلقائية
- سجلات مراجعة شاملة

### منع الأخطاء
- التحقق من توفر المخزون قبل النقل
- منع النقل للمخازن المغلقة
- التحقق من السعة المتاحة

## استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في إنشاء المخزن**: تحقق من عدم تكرار الكود
2. **فشل النقل**: تأكد من توفر المخزون والسعة
3. **خطأ في الموقع**: تحقق من صحة عنوان الموقع

### حلول سريعة
- إعادة تشغيل الخدمة عند مشاكل الاتصال
- التحقق من صحة البيانات المدخلة
- مراجعة سجلات الأخطاء للتشخيص

## التطوير المستقبلي

### ميزات مخططة
- [ ] تكامل مع أنظمة WMS خارجية
- [ ] تطبيق موبايل لإدارة المخازن
- [ ] نظام RFID للتتبع التلقائي
- [ ] ذكاء اصطناعي لتحسين التوزيع
- [ ] تقارير تحليلية متقدمة

### تحسينات مقترحة
- [ ] تحسين أداء البحث والاستعلامات
- [ ] إضافة المزيد من أنواع المواقع
- [ ] تحسين واجهة المستخدم
- [ ] دعم الطباعة للباركود

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من سجلات الأخطاء
3. تواصل مع فريق التطوير

---

**تم تطوير هذا النظام بعناية لتوفير حل شامل ومتقدم لإدارة المخازن المتعددة في البيئة العربية.**
