using System;
using System.Globalization;
using System.Windows.Data;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Converters
{
    /// <summary>
    /// كلاس مساعد للقيم الافتراضية
    /// </summary>
    public static class DefaultValueHelper
    {
        public static object GetDefaultValue(Type type)
        {
            if (type == typeof(DateTime))
                return DateTime.Now;
            if (type == typeof(decimal))
                return 0m;
            if (type == typeof(double))
                return 0.0;
            if (type == typeof(float))
                return 0f;
            if (type == typeof(int))
                return 0;
            if (type == typeof(long))
                return 0L;
            if (type == typeof(bool))
                return false;

            return 0;
        }
    }

    /// <summary>
    /// محول لتحويل الأرقام إلى أرقام عربية في XAML
    /// </summary>
    public class ArabicNumberValueConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return string.Empty;

            // تحويل الأرقام حسب النوع
            switch (value)
            {
                case int intValue:
                    return ArabicNumberConverter.ConvertNumberToArabic(intValue);

                case decimal decimalValue:
                    return ArabicNumberConverter.ConvertDecimalToArabic(decimalValue);

                case double doubleValue:
                    return ArabicNumberConverter.ConvertDecimalToArabic((decimal)doubleValue);

                case float floatValue:
                    return ArabicNumberConverter.ConvertDecimalToArabic((decimal)floatValue);

                case DateTime dateValue:
                    string format = parameter?.ToString() ?? "yyyy/MM/dd";
                    return ArabicNumberConverter.ConvertDateToArabic(dateValue, format);

                case string stringValue:
                    return ArabicNumberConverter.ConvertToArabicNumbers(stringValue);

                default:
                    return ArabicNumberConverter.ConvertToArabicNumbers(value?.ToString() ?? "0");
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return targetType.IsValueType ? (Activator.CreateInstance(targetType) ?? DefaultValueHelper.GetDefaultValue(targetType)) : null;

            string stringValue = value?.ToString() ?? "";
            string hindiValue = ArabicNumberConverter.ConvertToHindiNumbers(stringValue);

            // تحويل النص المُدخل إلى النوع المطلوب
            try
            {
                if (targetType == typeof(int) || targetType == typeof(int?))
                {
                    return int.Parse(hindiValue);
                }
                else if (targetType == typeof(decimal) || targetType == typeof(decimal?))
                {
                    return decimal.Parse(hindiValue);
                }
                else if (targetType == typeof(double) || targetType == typeof(double?))
                {
                    return double.Parse(hindiValue);
                }
                else if (targetType == typeof(float) || targetType == typeof(float?))
                {
                    return float.Parse(hindiValue);
                }
                else if (targetType == typeof(DateTime) || targetType == typeof(DateTime?))
                {
                    return DateTime.Parse(hindiValue);
                }
                else
                {
                    return hindiValue;
                }
            }
            catch
            {
                return targetType.IsValueType ? (Activator.CreateInstance(targetType) ?? DefaultValueHelper.GetDefaultValue(targetType)) : value;
            }
        }
    }

    /// <summary>
    /// محول خاص للعملة
    /// </summary>
    public class ArabicCurrencyConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return "0.00 دج";

            decimal amount = 0;
            if (value is decimal decimalValue)
                amount = decimalValue;
            else if (decimal.TryParse(value.ToString(), out decimal parsedValue))
                amount = parsedValue;

            string currency = parameter?.ToString() ?? "دج";
            return ArabicNumberConverter.ConvertCurrencyToArabic(amount, currency);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return 0m;

            string stringValue = value?.ToString() ?? "";
            // إزالة العملة الجزائرية والمسافات
            stringValue = stringValue.Replace("دج", "").Replace("دينار", "").Replace("ريال", "").Replace("درهم", "").Trim();

            // تحويل إلى أرقام هندية (الأرقام الآن إنجليزية أصلاً)
            stringValue = ArabicNumberConverter.ConvertToHindiNumbers(stringValue);

            if (decimal.TryParse(stringValue, out decimal result))
                return result;

            return 0m;
        }
    }

    /// <summary>
    /// محول خاص للنسب المئوية
    /// </summary>
    public class ArabicPercentageConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return "٠%";

            decimal percentage = 0;
            if (value is decimal decimalValue)
                percentage = decimalValue;
            else if (decimal.TryParse(value.ToString(), out decimal parsedValue))
                percentage = parsedValue;

            return ArabicNumberConverter.ConvertPercentageToArabic(percentage);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return 0m;

            string stringValue = (value?.ToString() ?? "").Replace("%", "").Trim();
            stringValue = ArabicNumberConverter.ConvertToHindiNumbers(stringValue);

            if (decimal.TryParse(stringValue, out decimal result))
                return result;

            return 0m;
        }
    }

    /// <summary>
    /// محول خاص للتواريخ
    /// </summary>
    public class ArabicDateConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return string.Empty;

            if (value is DateTime dateValue)
            {
                string format = parameter?.ToString() ?? "yyyy/MM/dd";
                return ArabicNumberConverter.ConvertDateToArabic(dateValue, format);
            }

            return ArabicNumberConverter.ConvertToArabicNumbers(value?.ToString() ?? "");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return targetType.IsValueType ? (Activator.CreateInstance(targetType) ?? DefaultValueHelper.GetDefaultValue(targetType)) : null;

            string stringValue = ArabicNumberConverter.ConvertToHindiNumbers(value?.ToString() ?? "");

            if (DateTime.TryParse(stringValue, out DateTime result))
                return result;

            return targetType.IsValueType ? (Activator.CreateInstance(targetType) ?? DefaultValueHelper.GetDefaultValue(targetType)) : value;
        }
    }
}
