<Window x:Class="SalesManagementSystem.Views.PaymentInputWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="💳 إدخال المبلغ المدفوع" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        ResizeMode="NoResize"
        Background="LightBlue">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="DarkBlue"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
        
        <Style x:Key="InfoTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>
        
        <Style x:Key="AmountTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="20,5"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="DarkBlue" CornerRadius="5" Padding="15" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="💳" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="إدخال المبلغ المدفوع" FontSize="20" FontWeight="Bold" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- معلومات العميل -->
        <Border Grid.Row="1" Background="White" CornerRadius="5" Padding="15" Margin="0,0,0,15">
            <StackPanel>
                <TextBlock Text="👤 معلومات العميل" Style="{StaticResource HeaderTextStyle}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم العميل:" Style="{StaticResource InfoTextStyle}" FontWeight="Bold"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="CustomerNameText" Text="" Style="{StaticResource InfoTextStyle}" Foreground="DarkBlue"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="الديون السابقة:" Style="{StaticResource InfoTextStyle}" FontWeight="Bold"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="PreviousDebtText" Text="0.00 دج" Style="{StaticResource AmountTextStyle}" Foreground="Red"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- تفاصيل الفاتورة -->
        <Border Grid.Row="2" Background="White" CornerRadius="5" Padding="15" Margin="0,0,0,15">
            <StackPanel>
                <TextBlock Text="📄 تفاصيل الفاتورة" Style="{StaticResource HeaderTextStyle}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="المبلغ الفرعي:" Style="{StaticResource InfoTextStyle}" FontWeight="Bold"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="SubtotalText" Text="0.00 دج" Style="{StaticResource InfoTextStyle}"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="الخصم:" Style="{StaticResource InfoTextStyle}" FontWeight="Bold"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="DiscountText" Text="0.00 دج" Style="{StaticResource InfoTextStyle}"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="الضريبة:" Style="{StaticResource InfoTextStyle}" FontWeight="Bold"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="TaxText" Text="0.00 دج" Style="{StaticResource InfoTextStyle}"/>
                    
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="مبلغ الفاتورة:" Style="{StaticResource InfoTextStyle}" FontWeight="Bold"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" x:Name="InvoiceAmountText" Text="0.00 دج" Style="{StaticResource AmountTextStyle}" Foreground="Blue"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- المبلغ الإجمالي المطلوب -->
        <Border Grid.Row="3" Background="Orange" CornerRadius="5" Padding="15" Margin="0,0,0,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="💰 المبلغ الإجمالي المطلوب (شامل الديون السابقة):" 
                           FontSize="16" FontWeight="Bold" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock x:Name="TotalRequiredAmountText" Text="0.00 دج" 
                           FontSize="20" FontWeight="Bold" Foreground="Yellow"/>
            </StackPanel>
        </Border>

        <!-- إدخال المبلغ المدفوع -->
        <Border Grid.Row="4" Background="White" CornerRadius="5" Padding="15" Margin="0,0,0,15">
            <StackPanel>
                <TextBlock Text="💵 إدخال المبلغ المدفوع" Style="{StaticResource HeaderTextStyle}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="المبلغ المدفوع:" 
                               Style="{StaticResource InfoTextStyle}" FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="PaidAmountTextBox" 
                             Height="35" FontSize="16" FontWeight="Bold" TextAlignment="Center"
                             Margin="10,0" TextChanged="PaidAmountTextBox_TextChanged"/>
                    <TextBlock Grid.Row="0" Grid.Column="2" Text="دج" 
                               Style="{StaticResource InfoTextStyle}" FontWeight="Bold" VerticalAlignment="Center"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="طريقة الدفع:" 
                               Style="{StaticResource InfoTextStyle}" FontWeight="Bold" VerticalAlignment="Center"/>
                    <ComboBox Grid.Row="1" Grid.Column="1" x:Name="PaymentMethodComboBox" 
                              Height="35" FontSize="14" Margin="10,5">
                        <ComboBoxItem Content="نقدي" IsSelected="True"/>
                        <ComboBoxItem Content="بطاقة ائتمان"/>
                        <ComboBoxItem Content="تحويل بنكي"/>
                        <ComboBoxItem Content="شيك"/>
                        <ComboBoxItem Content="أخرى"/>
                    </ComboBox>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="المبلغ المتبقي:" 
                               Style="{StaticResource InfoTextStyle}" FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="RemainingAmountText" Text="0.00 دج" 
                               Style="{StaticResource AmountTextStyle}" Foreground="Red" Margin="10,5"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- أزرار سريعة -->
        <Border Grid.Row="5" Background="LightGray" CornerRadius="5" Padding="15">
            <StackPanel>
                <TextBlock Text="⚡ أزرار سريعة" Style="{StaticResource HeaderTextStyle}"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="💰 دفع كامل" Click="PayFullAmountButton_Click" 
                            Style="{StaticResource ButtonStyle}" Background="Green" Foreground="White"/>
                    <Button Content="📄 دفع الفاتورة فقط" Click="PayInvoiceOnlyButton_Click" 
                            Style="{StaticResource ButtonStyle}" Background="Blue" Foreground="White"/>
                    <Button Content="🔄 مسح" Click="ClearButton_Click" 
                            Style="{StaticResource ButtonStyle}" Background="Gray" Foreground="White"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="6" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Content="✅ تأكيد الدفع" Click="ConfirmButton_Click" 
                    Style="{StaticResource ButtonStyle}" Background="Green" Foreground="White"/>
            <Button Content="❌ إلغاء" Click="CancelButton_Click" 
                    Style="{StaticResource ButtonStyle}" Background="Red" Foreground="White"/>
        </StackPanel>
    </Grid>
</Window>
