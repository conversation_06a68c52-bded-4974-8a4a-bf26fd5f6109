using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة التقسيط المتقدمة
    /// </summary>
    public class InstallmentService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;
        private readonly PaymentService _paymentService;

        public InstallmentService(DatabaseService dbService, NotificationService notificationService, PaymentService paymentService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _paymentService = paymentService ?? throw new ArgumentNullException(nameof(paymentService));
        }

        #region Installment Plan Management

        /// <summary>
        /// إنشاء خطة تقسيط جديدة
        /// </summary>
        public async Task<int> CreateInstallmentPlanAsync(InstallmentPlan plan)
        {
            try
            {
                // توليد رقم خطة التقسيط
                if (string.IsNullOrEmpty(plan.PlanNumber))
                {
                    plan.PlanNumber = await GeneratePlanNumberAsync();
                }

                // حساب مبلغ القسط وتاريخ الانتهاء - سيتم حسابهما تلقائياً عند تعيين القيم

                const string sql = @"
                    INSERT INTO InstallmentPlans (
                        PlanNumber, CustomerId, InvoiceId, SaleId, TotalAmount,
                        DownPayment, InstallmentAmount, NumberOfInstallments, Frequency,
                        InterestRate, StartDate, EndDate, Status, Notes, CreatedBy, CreatedAt
                    ) VALUES (
                        @PlanNumber, @CustomerId, @InvoiceId, @SaleId, @TotalAmount,
                        @DownPayment, @InstallmentAmount, @NumberOfInstallments, @Frequency,
                        @InterestRate, @StartDate, @EndDate, @Status, @Notes, @CreatedBy, @CreatedAt
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    plan.PlanNumber,
                    plan.CustomerId,
                    plan.InvoiceId,
                    plan.SaleId,
                    plan.TotalAmount,
                    plan.DownPayment,
                    plan.InstallmentAmount,
                    plan.NumberOfInstallments,
                    Frequency = plan.Frequency.ToString(),
                    plan.InterestRate,
                    StartDate = plan.StartDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    EndDate = plan.EndDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    Status = plan.Status.ToString(),
                    plan.Notes,
                    plan.CreatedBy,
                    CreatedAt = plan.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var planId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                plan.Id = planId;

                // إنشاء جدول الدفعات
                plan.GeneratePaymentSchedule();
                foreach (var payment in plan.Payments)
                {
                    payment.InstallmentPlanId = planId;
                    await AddInstallmentPaymentAsync(payment);
                }

                // إرسال إشعار
                await SendInstallmentNotificationAsync(plan, "تم إنشاء خطة تقسيط جديدة");

                LoggingService.LogInfo($"تم إنشاء خطة تقسيط جديدة: {plan.PlanNumber}");
                return planId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء خطة التقسيط: {plan?.PlanNumber}");
                throw;
            }
        }

        /// <summary>
        /// تحديث خطة التقسيط
        /// </summary>
        public async Task<bool> UpdateInstallmentPlanAsync(InstallmentPlan plan)
        {
            try
            {
                plan.UpdatedAt = DateTime.Now;

                const string sql = @"
                    UPDATE InstallmentPlans SET
                        TotalAmount = @TotalAmount, DownPayment = @DownPayment,
                        InstallmentAmount = @InstallmentAmount, NumberOfInstallments = @NumberOfInstallments,
                        Frequency = @Frequency, InterestRate = @InterestRate, StartDate = @StartDate,
                        EndDate = @EndDate, Status = @Status, Notes = @Notes, UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                var parameters = new
                {
                    plan.Id,
                    plan.TotalAmount,
                    plan.DownPayment,
                    plan.InstallmentAmount,
                    plan.NumberOfInstallments,
                    Frequency = plan.Frequency.ToString(),
                    plan.InterestRate,
                    StartDate = plan.StartDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    EndDate = plan.EndDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    Status = plan.Status.ToString(),
                    plan.Notes,
                    UpdatedAt = plan.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var rowsAffected = await _dbService.ExecuteAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم تحديث خطة التقسيط: {plan.PlanNumber}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث خطة التقسيط: {plan?.Id}");
                throw;
            }
        }

        /// <summary>
        /// حذف خطة التقسيط
        /// </summary>
        public async Task<bool> DeleteInstallmentPlanAsync(int planId)
        {
            try
            {
                // حذف دفعات التقسيط أولاً
                await _dbService.ExecuteAsync("DELETE FROM InstallmentPayments WHERE InstallmentPlanId = @PlanId", new { PlanId = planId });

                // حذف خطة التقسيط
                const string sql = "DELETE FROM InstallmentPlans WHERE Id = @Id";
                var rowsAffected = await _dbService.ExecuteAsync(sql, new { Id = planId });

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم حذف خطة التقسيط: {planId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في حذف خطة التقسيط: {planId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على خطة التقسيط بالمعرف
        /// </summary>
        public async Task<InstallmentPlan?> GetInstallmentPlanByIdAsync(int planId)
        {
            try
            {
                const string sql = "SELECT * FROM InstallmentPlans WHERE Id = @Id";
                var planData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Id = planId });

                if (planData != null)
                {
                    var plan = MapToInstallmentPlan(planData);

                    // تحميل دفعات التقسيط
                    plan.Payments = new System.Collections.ObjectModel.ObservableCollection<InstallmentPayment>(
                        await GetInstallmentPaymentsAsync(planId));

                    return plan;
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على خطة التقسيط: {planId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع خطط التقسيط
        /// </summary>
        public async Task<IEnumerable<InstallmentPlan>> GetAllInstallmentPlansAsync(int limit = 100, int offset = 0)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM InstallmentPlans
                    ORDER BY CreatedAt DESC
                    LIMIT @Limit OFFSET @Offset";

                var plansData = await _dbService.QueryAsync<dynamic>(sql, new { Limit = limit, Offset = offset });
                return plansData.Select(MapToInstallmentPlan);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على خطط التقسيط");
                throw;
            }
        }

        /// <summary>
        /// الحصول على خطط التقسيط للعميل
        /// </summary>
        public async Task<IEnumerable<InstallmentPlan>> GetCustomerInstallmentPlansAsync(int customerId)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM InstallmentPlans
                    WHERE CustomerId = @CustomerId
                    ORDER BY CreatedAt DESC";

                var plansData = await _dbService.QueryAsync<dynamic>(sql, new { CustomerId = customerId });
                return plansData.Select(MapToInstallmentPlan);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على خطط التقسيط للعميل: {customerId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على خطط التقسيط المتعثرة
        /// </summary>
        public async Task<IEnumerable<InstallmentPlan>> GetDefaultedInstallmentPlansAsync()
        {
            try
            {
                const string sql = @"
                    SELECT DISTINCT ip.* FROM InstallmentPlans ip
                    INNER JOIN InstallmentPayments ipm ON ip.Id = ipm.InstallmentPlanId
                    WHERE ipm.DueDate < @CurrentDate
                    AND ipm.Status = 'Pending'
                    AND ip.Status = 'Active'
                    ORDER BY ip.StartDate ASC";

                var currentDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                var plansData = await _dbService.QueryAsync<dynamic>(sql, new { CurrentDate = currentDate });
                return plansData.Select(MapToInstallmentPlan);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على خطط التقسيط المتعثرة");
                throw;
            }
        }

        #endregion

        #region Installment Payments

        /// <summary>
        /// إضافة دفعة تقسيط
        /// </summary>
        public async Task<int> AddInstallmentPaymentAsync(InstallmentPayment payment)
        {
            try
            {
                const string sql = @"
                    INSERT INTO InstallmentPayments (
                        InstallmentPlanId, PaymentNumber, Amount, DueDate, PaidDate,
                        Status, LateFee, Notes
                    ) VALUES (
                        @InstallmentPlanId, @PaymentNumber, @Amount, @DueDate, @PaidDate,
                        @Status, @LateFee, @Notes
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    payment.InstallmentPlanId,
                    payment.PaymentNumber,
                    payment.Amount,
                    DueDate = payment.DueDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    PaidDate = payment.PaidDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    Status = payment.Status.ToString(),
                    payment.LateFee,
                    payment.Notes
                };

                var paymentId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                payment.Id = paymentId;

                return paymentId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إضافة دفعة التقسيط");
                throw;
            }
        }

        /// <summary>
        /// الحصول على دفعات التقسيط
        /// </summary>
        public async Task<IEnumerable<InstallmentPayment>> GetInstallmentPaymentsAsync(int planId)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM InstallmentPayments
                    WHERE InstallmentPlanId = @PlanId
                    ORDER BY PaymentNumber ASC";

                var paymentsData = await _dbService.QueryAsync<dynamic>(sql, new { PlanId = planId });
                return paymentsData.Select(MapToInstallmentPayment);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على دفعات التقسيط: {planId}");
                throw;
            }
        }

        /// <summary>
        /// دفع قسط
        /// </summary>
        public async Task<bool> PayInstallmentAsync(int paymentId, decimal? lateFee = null)
        {
            try
            {
                var payment = await GetInstallmentPaymentByIdAsync(paymentId);
                if (payment == null) return false;

                // إضافة رسوم التأخير إذا كانت موجودة
                if (lateFee.HasValue && lateFee.Value > 0)
                {
                    payment.LateFee = lateFee.Value;
                }

                payment.MarkAsPaid();

                const string sql = @"
                    UPDATE InstallmentPayments SET
                        PaidDate = @PaidDate, Status = @Status, LateFee = @LateFee
                    WHERE Id = @Id";

                var parameters = new
                {
                    payment.Id,
                    PaidDate = payment.PaidDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    Status = payment.Status.ToString(),
                    payment.LateFee
                };

                var rowsAffected = await _dbService.ExecuteAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    // التحقق من اكتمال خطة التقسيط
                    await CheckPlanCompletionAsync(payment.InstallmentPlanId);

                    // إرسال إشعار
                    await SendPaymentNotificationAsync(payment, "تم دفع القسط");

                    LoggingService.LogInfo($"تم دفع القسط: {paymentId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في دفع القسط: {paymentId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على دفعة التقسيط بالمعرف
        /// </summary>
        public async Task<InstallmentPayment?> GetInstallmentPaymentByIdAsync(int paymentId)
        {
            try
            {
                const string sql = "SELECT * FROM InstallmentPayments WHERE Id = @Id";
                var paymentData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Id = paymentId });

                if (paymentData != null)
                {
                    return MapToInstallmentPayment(paymentData);
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على دفعة التقسيط: {paymentId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على الدفعات المستحقة
        /// </summary>
        public async Task<IEnumerable<InstallmentPayment>> GetOverduePaymentsAsync()
        {
            try
            {
                const string sql = @"
                    SELECT * FROM InstallmentPayments
                    WHERE DueDate < @CurrentDate
                    AND Status = 'Pending'
                    ORDER BY DueDate ASC";

                var currentDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                var paymentsData = await _dbService.QueryAsync<dynamic>(sql, new { CurrentDate = currentDate });
                return paymentsData.Select(MapToInstallmentPayment);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على الدفعات المستحقة");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// توليد رقم خطة تقسيط جديد
        /// </summary>
        private async Task<string> GeneratePlanNumberAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM InstallmentPlans WHERE DATE(CreatedAt) = DATE('now')";
                var todayCount = await _dbService.QuerySingleAsync<int>(sql);

                var today = DateTime.Now;
                return $"INST-{today:yyyyMMdd}-{(todayCount + 1):D4}";
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في توليد رقم خطة التقسيط");
                return $"INST-{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// التحقق من اكتمال خطة التقسيط
        /// </summary>
        private async Task CheckPlanCompletionAsync(int planId)
        {
            try
            {
                const string sql = @"
                    SELECT COUNT(*) FROM InstallmentPayments
                    WHERE InstallmentPlanId = @PlanId AND Status = 'Pending'";

                var pendingCount = await _dbService.QuerySingleAsync<int>(sql, new { PlanId = planId });

                if (pendingCount == 0)
                {
                    // تحديث حالة الخطة إلى مكتملة
                    const string updateSql = @"
                        UPDATE InstallmentPlans SET
                            Status = 'Completed', UpdatedAt = @UpdatedAt
                        WHERE Id = @Id";

                    await _dbService.ExecuteAsync(updateSql, new
                    {
                        Id = planId,
                        UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    });

                    // إرسال إشعار الاكتمال
                    var plan = await GetInstallmentPlanByIdAsync(planId);
                    if (plan != null)
                    {
                        await SendInstallmentNotificationAsync(plan, "تم اكتمال خطة التقسيط");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في التحقق من اكتمال خطة التقسيط: {planId}");
            }
        }

        /// <summary>
        /// تحويل البيانات إلى نموذج خطة التقسيط
        /// </summary>
        private InstallmentPlan MapToInstallmentPlan(dynamic data)
        {
            return new InstallmentPlan
            {
                Id = data.Id,
                PlanNumber = data.PlanNumber ?? "",
                CustomerId = data.CustomerId,
                InvoiceId = data.InvoiceId,
                SaleId = data.SaleId,
                TotalAmount = data.TotalAmount ?? 0,
                DownPayment = data.DownPayment ?? 0,
                InstallmentAmount = data.InstallmentAmount ?? 0,
                NumberOfInstallments = data.NumberOfInstallments ?? 0,
                Frequency = Enum.TryParse<InstallmentFrequency>(data.Frequency, out InstallmentFrequency frequency) ? frequency : InstallmentFrequency.Monthly,
                InterestRate = data.InterestRate ?? 0,
                StartDate = DateTime.TryParse(data.StartDate, out DateTime startDate) ? startDate : DateTime.Now,
                EndDate = DateTime.TryParse(data.EndDate, out DateTime endDate) ? endDate : DateTime.Now,
                Status = Enum.TryParse<InstallmentStatus>(data.Status, out InstallmentStatus status) ? status : InstallmentStatus.Pending,
                Notes = data.Notes ?? "",
                CreatedBy = data.CreatedBy ?? "",
                CreatedAt = DateTime.TryParse(data.CreatedAt, out DateTime createdAt) ? createdAt : DateTime.Now,
                UpdatedAt = DateTime.TryParse(data.UpdatedAt, out DateTime updatedAt) ? updatedAt : null
            };
        }

        /// <summary>
        /// تحويل البيانات إلى نموذج دفعة التقسيط
        /// </summary>
        private InstallmentPayment MapToInstallmentPayment(dynamic data)
        {
            return new InstallmentPayment
            {
                Id = data.Id,
                InstallmentPlanId = data.InstallmentPlanId,
                PaymentNumber = data.PaymentNumber ?? 0,
                Amount = data.Amount ?? 0,
                DueDate = DateTime.TryParse(data.DueDate, out DateTime dueDate) ? dueDate : DateTime.Now,
                PaidDate = DateTime.TryParse(data.PaidDate, out DateTime paidDate) ? paidDate : null,
                Status = Enum.TryParse<PaymentStatus>(data.Status, out PaymentStatus status) ? status : PaymentStatus.Pending,
                LateFee = data.LateFee ?? 0,
                Notes = data.Notes ?? ""
            };
        }

        /// <summary>
        /// إرسال إشعار التقسيط
        /// </summary>
        private async Task SendInstallmentNotificationAsync(InstallmentPlan plan, string message)
        {
            try
            {
                var notification = new Notification
                {
                    Title = "إشعار تقسيط",
                    Message = $"{message}: {plan.PlanNumber} - {plan.FormattedTotalAmount}",
                    Type = "Installment",
                    Priority = "Normal",
                    ActionUrl = $"/installments/{plan.Id}",
                    CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await _notificationService.AddNotificationAsync(notification);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إرسال إشعار التقسيط");
            }
        }

        /// <summary>
        /// إرسال إشعار دفعة التقسيط
        /// </summary>
        private async Task SendPaymentNotificationAsync(InstallmentPayment payment, string message)
        {
            try
            {
                var notification = new Notification
                {
                    Title = "إشعار دفعة تقسيط",
                    Message = $"{message}: القسط رقم {payment.PaymentNumber} - {payment.FormattedAmount}",
                    Type = "InstallmentPayment",
                    Priority = payment.IsOverdue ? "High" : "Normal",
                    ActionUrl = $"/installments/payments/{payment.Id}",
                    CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await _notificationService.AddNotificationAsync(notification);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إرسال إشعار دفعة التقسيط");
            }
        }

        #endregion
    }
}
