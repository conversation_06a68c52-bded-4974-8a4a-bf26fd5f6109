<Window x:Class="SalesManagementSystem.Views.ResetPasswordWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعادة تعيين كلمة المرور"
        Height="400" Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                               Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF66BB6A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF388E3C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#FFF5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#FF9C27B0">
            <Border.Effect>
                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
            </Border.Effect>
            <Grid Margin="20,0">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🔑" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="إعادة تعيين كلمة المرور" FontSize="18"
                              FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- محتوى النافذة -->
        <Border Grid.Row="1" Background="White" Margin="30" CornerRadius="10">
            <Border.Effect>
                <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.2"/>
            </Border.Effect>
            <Grid Margin="30">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- معلومات المستخدم -->
                <Border Grid.Row="0" Background="#FFF3E5F5" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="👤 المستخدم المحدد" FontSize="12" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock x:Name="UserNameLabel" Text="فاطمة علي" FontSize="16"
                                  FontWeight="SemiBold" Foreground="#FF9C27B0"/>
                        <TextBlock x:Name="UserRoleLabel" Text="الدور: محاسبة" FontSize="12"
                                  Foreground="Gray" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- كلمة المرور الجديدة -->
                <TextBlock Grid.Row="1" Text="كلمة المرور الجديدة:" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                <Border Grid.Row="2" Background="White" CornerRadius="5" BorderThickness="2" BorderBrush="#FFCCCCCC" Margin="0,0,0,15">
                    <PasswordBox x:Name="NewPasswordBox" Height="35" FontSize="14"
                                Padding="10,8" Background="Transparent" BorderThickness="0"
                                PasswordChanged="NewPasswordBox_PasswordChanged">
                        <PasswordBox.Style>
                            <Style TargetType="PasswordBox">
                                <Style.Triggers>
                                    <Trigger Property="IsFocused" Value="True">
                                        <Setter Property="BorderBrush" Value="#FF9C27B0"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </PasswordBox.Style>
                    </PasswordBox>
                </Border>

                <!-- تأكيد كلمة المرور -->
                <TextBlock Grid.Row="3" Text="تأكيد كلمة المرور:" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                <Border Grid.Row="4" Background="White" CornerRadius="5" BorderThickness="2" BorderBrush="#FFCCCCCC" Margin="0,0,0,15">
                    <PasswordBox x:Name="ConfirmPasswordBox" Height="35" FontSize="14"
                                Padding="10,8" Background="Transparent" BorderThickness="0"
                                PasswordChanged="ConfirmPasswordBox_PasswordChanged">
                        <PasswordBox.Style>
                            <Style TargetType="PasswordBox">
                                <Style.Triggers>
                                    <Trigger Property="IsFocused" Value="True">
                                        <Setter Property="BorderBrush" Value="#FF9C27B0"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </PasswordBox.Style>
                    </PasswordBox>
                </Border>

                <!-- مؤشر قوة كلمة المرور -->
                <StackPanel Grid.Row="5" Margin="0,0,0,15">
                    <TextBlock Text="قوة كلمة المرور:" FontSize="11" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ProgressBar x:Name="PasswordStrengthBar" Height="8" Background="#FFEEEEEE"
                                BorderThickness="0"/>
                    <TextBlock x:Name="PasswordStrengthLabel" Text="ضعيفة" FontSize="10"
                              Foreground="Red" Margin="0,2,0,0"/>
                </StackPanel>

                <!-- إرشادات كلمة المرور -->
                <Border Grid.Row="6" Background="#FFE3F2FD" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="💡 إرشادات كلمة المرور" FontSize="11" FontWeight="Bold" Margin="0,0,0,8"/>
                        <TextBlock Text="• يجب أن تكون 4 أحرف على الأقل" FontSize="10" Margin="0,2"/>
                        <TextBlock Text="• يُفضل استخدام أرقام وحروف" FontSize="10" Margin="0,2"/>
                        <TextBlock Text="• تجنب كلمات المرور البسيطة" FontSize="10" Margin="0,2"/>
                    </StackPanel>
                </Border>

                <!-- رسالة الخطأ -->
                <TextBlock x:Name="ErrorMessage" Grid.Row="7" Text="" FontSize="12"
                          Foreground="Red" HorizontalAlignment="Center" VerticalAlignment="Top"
                          Visibility="Collapsed"/>
            </Grid>
        </Border>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button x:Name="ResetButton" Content="🔑 تعيين كلمة المرور"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FF9C27B0" Click="Reset_Click" Width="150" IsEnabled="False"/>

                <Button x:Name="GenerateButton" Content="🎲 توليد كلمة مرور"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FFFF9800" Click="Generate_Click" Width="130"/>

                <Button x:Name="CancelButton" Content="❌ إلغاء"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FFF44336" Click="Cancel_Click" Width="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
