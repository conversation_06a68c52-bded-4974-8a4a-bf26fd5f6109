using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// نافذة إدارة الديون والمستحقات
    /// </summary>
    public partial class DebtsPaymentsWindow : Window
    {
        private ObservableCollection<CustomerDebt> _customerDebts = new ObservableCollection<CustomerDebt>();
        private ObservableCollection<SupplierPayable> _supplierPayables = new ObservableCollection<SupplierPayable>();
        private List<CustomerDebt> _allCustomerDebts = new List<CustomerDebt>();
        private List<SupplierPayable> _allSupplierPayables = new List<SupplierPayable>();

        public DebtsPaymentsWindow()
        {
            try
            {
                InitializeComponent();
                InitializeData();
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة الديون والمستحقات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeData()
        {
            try
            {
                // تهيئة المجموعات إذا لم تكن مهيأة بالفعل
                if (_customerDebts == null)
                    _customerDebts = new ObservableCollection<CustomerDebt>();
                if (_supplierPayables == null)
                    _supplierPayables = new ObservableCollection<SupplierPayable>();
                if (_allCustomerDebts == null)
                    _allCustomerDebts = new List<CustomerDebt>();
                if (_allSupplierPayables == null)
                    _allSupplierPayables = new List<SupplierPayable>();

                // ربط البيانات بالجداول
                if (CustomerDebtsDataGrid != null)
                    CustomerDebtsDataGrid.ItemsSource = _customerDebts;
                if (SupplierPayablesDataGrid != null)
                    SupplierPayablesDataGrid.ItemsSource = _supplierPayables;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadData()
        {
            try
            {
                LoadCustomerDebts();
                LoadSupplierPayables();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadCustomerDebts()
        {
            // بيانات تجريبية لديون العملاء
            _allCustomerDebts = new List<CustomerDebt>
            {
                new CustomerDebt
                {
                    CustomerId = 1,
                    CustomerName = "أحمد محمد علي",
                    Phone = "0555123456",
                    TotalPurchases = 50000,
                    TotalPaid = 35000,
                    RemainingDebt = 15000,
                    LastPaymentDate = DateTime.Now.AddDays(-5),
                    Status = "عليه ديون"
                },
                new CustomerDebt
                {
                    CustomerId = 2,
                    CustomerName = "فاطمة حسن",
                    Phone = "0555234567",
                    TotalPurchases = 25000,
                    TotalPaid = 20000,
                    RemainingDebt = 5000,
                    LastPaymentDate = DateTime.Now.AddDays(-10),
                    Status = "عليه ديون"
                },
                new CustomerDebt
                {
                    CustomerId = 3,
                    CustomerName = "محمد عبدالله",
                    Phone = "0555345678",
                    TotalPurchases = 30000,
                    TotalPaid = 30000,
                    RemainingDebt = 0,
                    LastPaymentDate = DateTime.Now.AddDays(-2),
                    Status = "مسدد"
                },
                new CustomerDebt
                {
                    CustomerId = 4,
                    CustomerName = "سارة أحمد",
                    Phone = "0555456789",
                    TotalPurchases = 75000,
                    TotalPaid = 45000,
                    RemainingDebt = 30000,
                    LastPaymentDate = DateTime.Now.AddDays(-15),
                    Status = "عليه ديون"
                },
                new CustomerDebt
                {
                    CustomerId = 5,
                    CustomerName = "عبدالرحمن محمد",
                    Phone = "0555567890",
                    TotalPurchases = 40000,
                    TotalPaid = 25000,
                    RemainingDebt = 15000,
                    LastPaymentDate = DateTime.Now.AddDays(-7),
                    Status = "عليه ديون"
                }
            };

            RefreshCustomerDebts();
        }

        private void LoadSupplierPayables()
        {
            // بيانات تجريبية لمستحقات الموردين
            _allSupplierPayables = new List<SupplierPayable>
            {
                new SupplierPayable
                {
                    SupplierId = 1,
                    SupplierName = "شركة التقنية المتقدمة",
                    Phone = "0555111222",
                    TotalPurchases = 100000,
                    TotalPaid = 70000,
                    RemainingPayable = 30000,
                    LastPaymentDate = DateTime.Now.AddDays(-8),
                    Status = "مستحقات معلقة"
                },
                new SupplierPayable
                {
                    SupplierId = 2,
                    SupplierName = "مؤسسة الإلكترونيات الحديثة",
                    Phone = "0555222333",
                    TotalPurchases = 80000,
                    TotalPaid = 80000,
                    RemainingPayable = 0,
                    LastPaymentDate = DateTime.Now.AddDays(-3),
                    Status = "مسدد"
                },
                new SupplierPayable
                {
                    SupplierId = 3,
                    SupplierName = "شركة الأجهزة الذكية",
                    Phone = "0555333444",
                    TotalPurchases = 120000,
                    TotalPaid = 90000,
                    RemainingPayable = 30000,
                    LastPaymentDate = DateTime.Now.AddDays(-12),
                    Status = "مستحقات معلقة"
                }
            };

            RefreshSupplierPayables();
        }

        private void RefreshCustomerDebts()
        {
            _customerDebts.Clear();
            foreach (var debt in _allCustomerDebts)
            {
                _customerDebts.Add(debt);
            }
        }

        private void RefreshSupplierPayables()
        {
            _supplierPayables.Clear();
            foreach (var payable in _allSupplierPayables)
            {
                _supplierPayables.Add(payable);
            }
        }

        private void UpdateStatistics()
        {
            // إحصائيات ديون العملاء
            var debtorCustomers = _allCustomerDebts.Where(c => c.RemainingDebt > 0).ToList();
            var totalCustomerDebts = debtorCustomers.Sum(c => c.RemainingDebt);
            var largestDebt = debtorCustomers.Any() ? debtorCustomers.Max(c => c.RemainingDebt) : 0;
            var averageDebt = debtorCustomers.Any() ? debtorCustomers.Average(c => c.RemainingDebt) : 0;

            DebtorCustomersCountLabel.Text = debtorCustomers.Count.ToString();
            TotalCustomerDebtsLabel.Text = $"{totalCustomerDebts:F2} دج";
            LargestDebtLabel.Text = $"{largestDebt:F2} دج";
            AverageDebtLabel.Text = $"{averageDebt:F2} دج";

            // إحصائيات مستحقات الموردين
            var suppliersWithPayables = _allSupplierPayables.Where(s => s.RemainingPayable > 0).ToList();
            var totalSupplierPayables = suppliersWithPayables.Sum(s => s.RemainingPayable);
            var largestPayable = suppliersWithPayables.Any() ? suppliersWithPayables.Max(s => s.RemainingPayable) : 0;
            var averagePayable = suppliersWithPayables.Any() ? suppliersWithPayables.Average(s => s.RemainingPayable) : 0;

            SuppliersWithPayablesCountLabel.Text = suppliersWithPayables.Count.ToString();
            TotalSupplierPayablesLabel.Text = $"{totalSupplierPayables:F2} دج";
            LargestPayableLabel.Text = $"{largestPayable:F2} دج";
            AveragePayableLabel.Text = $"{averagePayable:F2} دج";

            // إجمالي في الشريط العلوي
            TotalDebtsLabel.Text = $"{totalCustomerDebts:F2} دج";
            TotalPayablesLabel.Text = $"{totalSupplierPayables:F2} دج";
        }

        // أحداث البحث والفلترة للعملاء
        private void CustomerSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterCustomerDebts();
        }

        private void CustomerStatus_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterCustomerDebts();
        }

        private void FilterCustomerDebts()
        {
            var searchText = CustomerSearchTextBox.Text?.ToLower() ?? "";
            var selectedStatus = (CustomerStatusComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "الكل";

            var filteredDebts = _allCustomerDebts.Where(debt =>
                (string.IsNullOrEmpty(searchText) ||
                 debt.CustomerName.ToLower().Contains(searchText) ||
                 debt.Phone.Contains(searchText)) &&
                (selectedStatus == "الكل" || debt.Status == selectedStatus)
            ).ToList();

            _customerDebts.Clear();
            foreach (var debt in filteredDebts)
            {
                _customerDebts.Add(debt);
            }
        }

        // أحداث البحث والفلترة للموردين
        private void SupplierSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterSupplierPayables();
        }

        private void SupplierStatus_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterSupplierPayables();
        }

        private void FilterSupplierPayables()
        {
            var searchText = SupplierSearchTextBox.Text?.ToLower() ?? "";
            var selectedStatus = (SupplierStatusComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "الكل";

            var filteredPayables = _allSupplierPayables.Where(payable =>
                (string.IsNullOrEmpty(searchText) ||
                 payable.SupplierName.ToLower().Contains(searchText) ||
                 payable.Phone.Contains(searchText)) &&
                (selectedStatus == "الكل" || payable.Status == selectedStatus)
            ).ToList();

            _supplierPayables.Clear();
            foreach (var payable in filteredPayables)
            {
                _supplierPayables.Add(payable);
            }
        }

        // أحداث الأزرار والإجراءات
        private void NewCustomerPayment_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("دفعة جديدة للعميل - قيد التطوير", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CustomerDebtsReport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير ديون العملاء - قيد التطوير", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void NewSupplierPayment_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("دفعة جديدة للمورد - قيد التطوير", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SupplierPayablesReport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تقرير مستحقات الموردين - قيد التطوير", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PayCustomerDebt_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var debt = button?.DataContext as CustomerDebt;
            if (debt != null)
            {
                MessageBox.Show($"دفعة جديدة للعميل: {debt.CustomerName}\nالمبلغ المتبقي: {debt.RemainingDebt:F2} دج",
                    "دفعة جديدة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ViewCustomerPayments_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var debt = button?.DataContext as CustomerDebt;
            if (debt != null)
            {
                MessageBox.Show($"تاريخ دفعات العميل: {debt.CustomerName}",
                    "تاريخ الدفعات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PaySupplierPayable_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var payable = button?.DataContext as SupplierPayable;
            if (payable != null)
            {
                MessageBox.Show($"دفعة جديدة للمورد: {payable.SupplierName}\nالمبلغ المتبقي: {payable.RemainingPayable:F2} دج",
                    "دفعة للمورد", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ViewSupplierPayments_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var payable = button?.DataContext as SupplierPayable;
            if (payable != null)
            {
                MessageBox.Show($"تاريخ دفعات المورد: {payable.SupplierName}",
                    "تاريخ الدفعات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void CustomerDebtsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي عند تغيير التحديد
        }

        private void SupplierPayablesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي عند تغيير التحديد
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
            MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Export_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تصدير التقرير - قيد التطوير", "تصدير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
