using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class QuickSaleWindow : Window
    {
        private ObservableCollection<QuickSaleItem> _saleItems;
        private readonly ProductService _productService;
        private List<Product> _allProducts;
        private List<Product> _filteredProducts;
        private bool _isSelectingFromSuggestions = false;

        public QuickSaleWindow()
        {
            InitializeComponent();
            _saleItems = new ObservableCollection<QuickSaleItem>();
            ProductsDataGrid.ItemsSource = _saleItems;
            _productService = new ProductService(new DatabaseService());
            _allProducts = new List<Product>();
            _filteredProducts = new List<Product>();

            // تحميل المنتجات من قاعدة البيانات
            LoadProductsAsync();

            // التركيز على مربع البحث
            SearchTextBox.Focus();

            // تحديث العنوان والملخص
            UpdateSummary();
            UpdateWindowTitle();
        }

        private async void LoadProductsAsync()
        {
            try
            {
                _allProducts = (await _productService.GetAllProductsAsync()).ToList();

                // إضافة منتجات تجريبية إذا كانت القائمة فارغة
                if (_allProducts.Count == 0)
                {
                    await AddSampleProductsAsync();
                    _allProducts = (await _productService.GetAllProductsAsync()).ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task AddSampleProductsAsync()
        {
            try
            {
                var sampleProducts = new[]
                {
                    new Product
                    {
                        Code = "LAP001",
                        Name = "لابتوب ديل انسبايرون 15",
                        Description = "لابتوب ديل انسبايرون 15 بوصة، معالج Intel Core i5، ذاكرة 8GB، قرص صلب 512GB SSD",
                        Barcode = "1234567890123",
                        PurchasePrice = 75000,
                        SalePrice = 95000,
                        SalePrice2 = 90000,
                        Quantity = 5,
                        MinQuantity = 2,
                        Unit = "قطعة",
                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    },
                    new Product
                    {
                        Code = "MOU001",
                        Name = "ماوس لوجيتك اللاسلكي",
                        Description = "ماوس لوجيتك لاسلكي بتقنية البلوتوث، دقة عالية، بطارية طويلة المدى",
                        Barcode = "9876543210987",
                        PurchasePrice = 1800,
                        SalePrice = 2500,
                        SalePrice2 = 2300,
                        Quantity = 25,
                        MinQuantity = 5,
                        Unit = "قطعة",
                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    },
                    new Product
                    {
                        Code = "KEY001",
                        Name = "كيبورد ميكانيكي RGB",
                        Description = "كيبورد ميكانيكي بإضاءة RGB، مفاتيح زرقاء، مقاوم للماء",
                        Barcode = "5555666677778",
                        PurchasePrice = 3200,
                        SalePrice = 4500,
                        SalePrice2 = 4200,
                        Quantity = 15,
                        MinQuantity = 3,
                        Unit = "قطعة",
                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    },
                    new Product
                    {
                        Code = "MON001",
                        Name = "شاشة سامسونج 24 بوصة",
                        Description = "شاشة سامسونج LED 24 بوصة، دقة Full HD، منافذ HDMI و VGA",
                        Barcode = "1111222233334",
                        PurchasePrice = 28000,
                        SalePrice = 35000,
                        SalePrice2 = 33000,
                        Quantity = 8,
                        MinQuantity = 2,
                        Unit = "قطعة",
                        IsActive = true,
                        TrackStock = true,
                        CreatedAt = DateTime.Now
                    }
                };

                foreach (var product in sampleProducts)
                {
                    await _productService.AddProductAsync(product);
                }

                MessageBox.Show("تم إضافة 4 منتجات تجريبية بنجاح!", "منتجات تجريبية",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتجات التجريبية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateWindowTitle()
        {
            var totalAmount = _saleItems.Sum(item => item.Total);
            var itemsCount = _saleItems.Count;

            if (totalAmount > 0)
            {
                Title = $"🛒 بيع سريع - {itemsCount} منتج - {totalAmount:F2} دج";
            }
            else
            {
                Title = "🛒 بيع سريع - مسح الكودبار";
            }
        }

        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            // التركيز على مربع البحث عند الضغط على أي مفتاح
            if (!SearchTextBox.IsFocused)
            {
                SearchTextBox.Focus();
            }
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (SuggestionsPopup.IsOpen && _filteredProducts.Count > 0)
                {
                    // إذا كانت الاقتراحات مفتوحة، اختر الأول
                    SuggestionsListBox.SelectedIndex = 0;
                    SelectSuggestion();
                }
                else
                {
                    // وإلا قم بالبحث العادي
                    HideSuggestions();
                    ProcessSearch();
                }
                e.Handled = true;
            }
            else if (e.Key == Key.Down && SuggestionsPopup.IsOpen)
            {
                // التنقل لأسفل في الاقتراحات
                SuggestionsListBox.Focus();
                if (SuggestionsListBox.Items.Count > 0)
                {
                    SuggestionsListBox.SelectedIndex = 0;
                }
                e.Handled = true;
            }
            else if (e.Key == Key.Escape)
            {
                // إخفاء الاقتراحات
                HideSuggestions();
                e.Handled = true;
            }
        }

        private System.Windows.Threading.DispatcherTimer? _searchTimer;

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isSelectingFromSuggestions)
                return;

            string searchText = SearchTextBox.Text.Trim();

            // إلغاء المؤقت السابق إن وجد
            _searchTimer?.Stop();

            if (string.IsNullOrEmpty(searchText))
            {
                HideSuggestions();
                return;
            }

            // تحديد ما إذا كان النص يبدو كباركود
            bool looksLikeBarcode = IsLikelyBarcode(searchText);

            if (looksLikeBarcode)
            {
                // معالجة فورية للكودبار
                HideSuggestions();
                ProcessSearch();
            }
            else if (searchText.Length >= 2)
            {
                // إظهار الاقتراحات للبحث بالاسم
                ShowSuggestions(searchText);

                // تأخير قصير للبحث التلقائي
                _searchTimer = new System.Windows.Threading.DispatcherTimer();
                _searchTimer.Interval = TimeSpan.FromMilliseconds(1000);
                _searchTimer.Tick += (s, args) =>
                {
                    _searchTimer.Stop();
                    if (SearchTextBox.Text.Trim() == searchText) // التأكد أن النص لم يتغير
                    {
                        ProcessSearch();
                    }
                };
                _searchTimer.Start();
            }
            else
            {
                HideSuggestions();
            }
        }

        private bool IsLikelyBarcode(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            // الكودبار عادة ما يكون:
            // 1. طوله بين 4-30 حرف (توسيع النطاق لدعم أنواع أكثر)
            // 2. يحتوي على أرقام بشكل أساسي (مع إمكانية وجود أحرف قليلة)
            // 3. لا يحتوي على مسافات
            // 4. يتم إدخاله بسرعة (ماسح الكودبار)

            if (text.Length < 4 || text.Length > 30)
                return false;

            if (text.Contains(' '))
                return false;

            // إذا كان 70% من النص أرقام أو أكثر، فهو على الأرجح كودبار
            // (تقليل النسبة لدعم كودبار مختلط)
            int digitCount = text.Count(char.IsDigit);
            double digitPercentage = (double)digitCount / text.Length;

            // أو إذا كان كله أرقام وطوله مناسب
            bool allDigits = text.All(char.IsDigit) && text.Length >= 4;

            // أو إذا كان يحتوي على أحرف إنجليزية وأرقام فقط (كودبار مختلط)
            bool alphanumeric = text.All(c => char.IsLetterOrDigit(c)) && digitPercentage >= 0.3;

            return digitPercentage >= 0.7 || allDigits || alphanumeric;
        }

        private void ShowSuggestions(string searchText)
        {
            if (_allProducts == null || _allProducts.Count == 0)
                return;

            // البحث في المنتجات
            _filteredProducts = _allProducts.Where(p =>
                p.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (!string.IsNullOrEmpty(p.Code) && p.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase)) ||
                (!string.IsNullOrEmpty(p.Description) && p.Description.Contains(searchText, StringComparison.OrdinalIgnoreCase))
            ).Take(10).ToList(); // أخذ أول 10 نتائج فقط

            if (_filteredProducts.Count > 0)
            {
                SuggestionsListBox.ItemsSource = _filteredProducts;
                SuggestionsPopup.IsOpen = true;
            }
            else
            {
                HideSuggestions();
            }
        }

        private void HideSuggestions()
        {
            SuggestionsPopup.IsOpen = false;
            SuggestionsListBox.ItemsSource = null;
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            // إظهار الاقتراحات عند التركيز إذا كان هناك نص
            string searchText = SearchTextBox.Text.Trim();
            if (!string.IsNullOrEmpty(searchText) && searchText.Length >= 2 && !IsLikelyBarcode(searchText))
            {
                ShowSuggestions(searchText);
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            // إخفاء الاقتراحات عند فقدان التركيز (مع تأخير قصير للسماح بالنقر)
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromMilliseconds(200);
            timer.Tick += (s, args) =>
            {
                timer.Stop();
                if (!SuggestionsListBox.IsMouseOver && !SearchTextBox.IsFocused)
                {
                    HideSuggestions();
                }
            };
            timer.Start();
        }

        private void SuggestionsListBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            SelectSuggestion();
        }

        private void SuggestionsListBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                SelectSuggestion();
                e.Handled = true;
            }
            else if (e.Key == Key.Escape)
            {
                HideSuggestions();
                SearchTextBox.Focus();
                e.Handled = true;
            }
        }

        private void SelectSuggestion()
        {
            if (SuggestionsListBox.SelectedItem is Product selectedProduct)
            {
                _isSelectingFromSuggestions = true;
                SearchTextBox.Text = selectedProduct.Name;
                _isSelectingFromSuggestions = false;

                HideSuggestions();
                ProcessSearchForProduct(selectedProduct);
                SearchTextBox.Focus();
            }
        }

        private void ProcessSearch()
        {
            string searchText = SearchTextBox.Text.Trim();
            if (string.IsNullOrEmpty(searchText))
                return;

            // البحث في المنتجات المحملة
            Product? foundProduct = null;
            bool isBarcodeLookup = IsLikelyBarcode(searchText);

            if (isBarcodeLookup)
            {
                // البحث بالباركود (مطابقة تامة أو جزئية)
                foundProduct = _allProducts.FirstOrDefault(p =>
                    !string.IsNullOrEmpty(p.Barcode) &&
                    (p.Barcode == searchText || p.Barcode.Contains(searchText)));

                // إذا لم يوجد، جرب البحث بالكود
                if (foundProduct == null)
                {
                    foundProduct = _allProducts.FirstOrDefault(p =>
                        !string.IsNullOrEmpty(p.Code) &&
                        (p.Code == searchText || p.Code.Contains(searchText)));
                }
            }
            else
            {
                // البحث بالاسم
                foundProduct = _allProducts.FirstOrDefault(p =>
                    p.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase));

                // إذا لم يوجد، جرب البحث في الوصف
                if (foundProduct == null)
                {
                    foundProduct = _allProducts.FirstOrDefault(p =>
                        !string.IsNullOrEmpty(p.Description) &&
                        p.Description.Contains(searchText, StringComparison.OrdinalIgnoreCase));
                }
            }

            if (foundProduct != null)
            {
                // البحث عن المنتج في القائمة الموجودة
                var existingItem = _saleItems.FirstOrDefault(item =>
                    item.ProductId == foundProduct.Id);

                if (existingItem != null)
                {
                    // إضافة وحدة واحدة للمنتج الموجود
                    existingItem.Quantity++;
                    string searchType = isBarcodeLookup ? "📱 كودبار" : "🔍 اسم";
                    LastActionLabel.Text = $"✅ {searchType}: +1 لـ {existingItem.ProductName}";
                    LastActionLabel.Foreground = System.Windows.Media.Brushes.Green;
                }
                else
                {
                    // إضافة منتج جديد
                    var newItem = new QuickSaleItem
                    {
                        ProductId = foundProduct.Id,
                        Barcode = foundProduct.Barcode ?? "",
                        ProductName = foundProduct.Name,
                        UnitPrice = foundProduct.SalePrice,
                        Quantity = 1
                    };

                    _saleItems.Add(newItem);
                    string searchType = isBarcodeLookup ? "📱 كودبار" : "🔍 اسم";
                    LastActionLabel.Text = $"🆕 {searchType}: {foundProduct.Name}";
                    LastActionLabel.Foreground = System.Windows.Media.Brushes.Blue;
                }
            }
            else
            {
                // منتج غير موجود
                string searchType = isBarcodeLookup ? "📱 كودبار" : "🔍 اسم";
                LastActionLabel.Text = $"❌ {searchType}: غير موجود - {searchText}";
                LastActionLabel.Foreground = System.Windows.Media.Brushes.Red;

                // عدم إظهار رسالة منبثقة للبحث بالاسم لتجنب الإزعاج
                if (isBarcodeLookup)
                {
                    MessageBox.Show($"لم يتم العثور على منتج بالكودبار: {searchText}", "كودبار غير موجود",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }

            // مسح مربع النص والتركيز عليه
            SearchTextBox.Clear();
            SearchTextBox.Focus();

            UpdateSummary();
        }

        private void ProcessSearchForProduct(Product foundProduct)
        {
            if (foundProduct == null) return;

            // البحث عن المنتج في القائمة الموجودة
            var existingItem = _saleItems.FirstOrDefault(item =>
                item.ProductId == foundProduct.Id);

            if (existingItem != null)
            {
                // إضافة وحدة واحدة للمنتج الموجود
                existingItem.Quantity++;
                LastActionLabel.Text = $"✅ 🔍 اقتراح: +1 لـ {existingItem.ProductName}";
                LastActionLabel.Foreground = System.Windows.Media.Brushes.Green;
            }
            else
            {
                // إضافة منتج جديد
                var newItem = new QuickSaleItem
                {
                    ProductId = foundProduct.Id,
                    Barcode = foundProduct.Barcode ?? "",
                    ProductName = foundProduct.Name,
                    UnitPrice = foundProduct.SalePrice,
                    Quantity = 1
                };

                _saleItems.Add(newItem);
                LastActionLabel.Text = $"🆕 🔍 اقتراح: {foundProduct.Name}";
                LastActionLabel.Foreground = System.Windows.Media.Brushes.Blue;
            }

            // مسح مربع النص والتركيز عليه
            SearchTextBox.Clear();
            SearchTextBox.Focus();

            UpdateSummary();
        }



        private void UpdateSummary()
        {
            var totalAmount = _saleItems.Sum(item => item.Total);

            // تحديث الملخص في الأسفل
            ItemsCountLabel.Text = _saleItems.Count.ToString();
            TotalQuantityLabel.Text = _saleItems.Sum(item => item.Quantity).ToString();
            TotalAmountLabel.Text = $"{totalAmount:F2} دج";

            // تحديث المبلغ في رأس النافذة بتأثير رقمي
            HeaderTotalLabel.Text = $"{totalAmount:F2} دج";

            // تحديث عنوان النافذة
            UpdateWindowTitle();

            // تأثير تحديث المبلغ مع تغيير اللون مؤقتاً
            AnimateHeaderTotal(totalAmount);
        }

        private void AnimateHeaderTotal(decimal amount)
        {
            // تأثير بصري عند تحديث المبلغ
            var originalBrush = HeaderTotalLabel.Foreground;

            if (amount > 0)
            {
                // تغيير اللون مؤقتاً للإشارة للتحديث
                HeaderTotalLabel.Foreground = System.Windows.Media.Brushes.Yellow;

                // إنشاء تأثير الرجوع للون الأصلي
                var timer = new System.Windows.Threading.DispatcherTimer();
                timer.Interval = TimeSpan.FromMilliseconds(300);
                timer.Tick += (s, e) =>
                {
                    HeaderTotalLabel.Foreground = originalBrush;
                    timer.Stop();
                };
                timer.Start();
            }
        }

        private void ManualSearch_Click(object sender, RoutedEventArgs e)
        {
            var searchWindow = new ProductSearchWindow();
            if (searchWindow.ShowDialog() == true && searchWindow.SelectedProduct != null)
            {
                var product = searchWindow.SelectedProduct;
                var existingItem = _saleItems.FirstOrDefault(item => item.Barcode == product.Barcode);

                if (existingItem != null)
                {
                    existingItem.Quantity++;
                }
                else
                {
                    _saleItems.Add(new QuickSaleItem
                    {
                        Barcode = product.Barcode,
                        ProductName = product.ProductName,
                        UnitPrice = product.UnitPrice,
                        Quantity = 1,
                        ProductId = product.ProductId
                    });
                }

                UpdateSummary();
                SearchTextBox.Focus();
            }
        }

        private void ClearBarcode_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Clear();
            SearchTextBox.Focus();
        }

        private void IncreaseQuantity_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is QuickSaleItem item)
            {
                item.Quantity++;
                UpdateSummary();
            }
        }

        private void DecreaseQuantity_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is QuickSaleItem item && item.Quantity > 1)
            {
                item.Quantity--;
                UpdateSummary();
            }
        }

        private void EditQuantity_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is QuickSaleItem item)
            {
                string input = Microsoft.VisualBasic.Interaction.InputBox(
                    "أدخل الكمية الجديدة:", "تعديل الكمية", item.Quantity.ToString());

                if (int.TryParse(input, out int newQuantity) && newQuantity > 0)
                {
                    item.Quantity = newQuantity;
                    UpdateSummary();
                }
            }
        }

        private void RemoveProduct_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is QuickSaleItem item)
            {
                var result = MessageBox.Show($"هل تريد حذف {item.ProductName}؟", "تأكيد الحذف",
                                           MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    _saleItems.Remove(item);
                    UpdateSummary();
                    LastActionLabel.Text = $"تم حذف {item.ProductName}";
                    LastActionLabel.Foreground = System.Windows.Media.Brushes.Orange;
                }
            }
        }

        private void CashPayment_Click(object sender, RoutedEventArgs e)
        {
            if (_saleItems.Count == 0)
            {
                MessageBox.Show("لا توجد منتجات للبيع!", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var paymentWindow = new QuickPaymentWindow(_saleItems.ToList(), "نقدي");
            if (paymentWindow.ShowDialog() == true)
            {
                MessageBox.Show("تم إتمام عملية البيع بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                _saleItems.Clear();
                UpdateSummary();
                LastActionLabel.Text = "تم إتمام البيع - جاهز للمسح";
                LastActionLabel.Foreground = System.Windows.Media.Brushes.Green;
                SearchTextBox.Focus();
            }
        }

        private void CardPayment_Click(object sender, RoutedEventArgs e)
        {
            if (_saleItems.Count == 0)
            {
                MessageBox.Show("لا توجد منتجات للبيع!", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var paymentWindow = new QuickPaymentWindow(_saleItems.ToList(), "بطاقة");
            if (paymentWindow.ShowDialog() == true)
            {
                MessageBox.Show("تم إتمام عملية البيع بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                _saleItems.Clear();
                UpdateSummary();
                LastActionLabel.Text = "تم إتمام البيع - جاهز للمسح";
                LastActionLabel.Foreground = System.Windows.Media.Brushes.Green;
                SearchTextBox.Focus();
            }
        }

        private void SaveDraft_Click(object sender, RoutedEventArgs e)
        {
            if (_saleItems.Count == 0)
            {
                MessageBox.Show("لا توجد منتجات للحفظ!", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBox.Show("تم حفظ الفاتورة كمسودة!", "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
            LastActionLabel.Text = "تم حفظ المسودة";
            LastActionLabel.Foreground = System.Windows.Media.Brushes.Blue;
        }

        private void ClearAll_Click(object sender, RoutedEventArgs e)
        {
            if (_saleItems.Count == 0)
                return;

            var result = MessageBox.Show("هل تريد مسح جميع المنتجات؟", "تأكيد المسح",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                _saleItems.Clear();
                UpdateSummary();
                LastActionLabel.Text = "تم مسح جميع المنتجات";
                LastActionLabel.Foreground = System.Windows.Media.Brushes.Red;
                SearchTextBox.Focus();
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            if (_saleItems.Count > 0)
            {
                var result = MessageBox.Show("هناك منتجات في القائمة. هل تريد الإغلاق؟", "تأكيد الإغلاق",
                                           MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                    return;
            }

            Close();
        }
    }

    // كلاس مؤقت لنافذة البحث اليدوي
    public class ProductSearchWindow : Window
    {
        public QuickSaleItem? SelectedProduct { get; set; }

        public ProductSearchWindow()
        {
            Title = "البحث عن منتج";
            Width = 400;
            Height = 300;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // محتوى مؤقت
            Content = new TextBlock
            {
                Text = "نافذة البحث اليدوي - قيد التطوير",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
        }
    }

    // كلاس مؤقت لنافذة الدفع
    public class QuickPaymentWindow : Window
    {
        public QuickPaymentWindow(List<QuickSaleItem> items, string paymentMethod)
        {
            Title = $"الدفع - {paymentMethod}";
            Width = 500;
            Height = 400;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // محتوى مؤقت
            var content = new StackPanel();
            content.Children.Add(new TextBlock
            {
                Text = $"نافذة الدفع {paymentMethod}",
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(10)
            });

            var okButton = new Button
            {
                Content = "تأكيد الدفع",
                Width = 100,
                Height = 30,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(10)
            };
            okButton.Click += (s, e) => { DialogResult = true; Close(); };
            content.Children.Add(okButton);

            Content = content;
        }
    }
}
