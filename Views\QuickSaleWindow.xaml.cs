using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Views
{
    public partial class QuickSaleWindow : Window
    {
        private ObservableCollection<QuickSaleItem> _saleItems;
        private readonly Random _random = new Random();

        public QuickSaleWindow()
        {
            InitializeComponent();
            _saleItems = new ObservableCollection<QuickSaleItem>();
            ProductsDataGrid.ItemsSource = _saleItems;

            // التركيز على مربع الكودبار
            BarcodeTextBox.Focus();

            // تحديث العنوان والملخص
            UpdateSummary();
            UpdateWindowTitle();
        }

        private void UpdateWindowTitle()
        {
            var totalAmount = _saleItems.Sum(item => item.Total);
            var itemsCount = _saleItems.Count;

            if (totalAmount > 0)
            {
                Title = $"🛒 بيع سريع - {itemsCount} منتج - {totalAmount:F2} دج";
            }
            else
            {
                Title = "🛒 بيع سريع - مسح الكودبار";
            }
        }

        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            // التركيز على مربع الكودبار عند الضغط على أي مفتاح
            if (!BarcodeTextBox.IsFocused)
            {
                BarcodeTextBox.Focus();
            }
        }

        private void BarcodeTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                ProcessBarcode();
            }
        }

        private void BarcodeTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // معالجة تلقائية للكودبار عند الوصول لطول معين
            string barcode = BarcodeTextBox.Text.Trim();
            if (barcode.Length >= 8) // افتراض أن الكودبار 8 أرقام أو أكثر
            {
                ProcessBarcode();
            }
        }

        private void ProcessBarcode()
        {
            string barcode = BarcodeTextBox.Text.Trim();
            if (string.IsNullOrEmpty(barcode))
                return;

            // البحث عن المنتج في القائمة الموجودة
            var existingItem = _saleItems.FirstOrDefault(item => item.Barcode == barcode);

            if (existingItem != null)
            {
                // إضافة وحدة واحدة للمنتج الموجود
                existingItem.Quantity++;
                LastActionLabel.Text = $"تم إضافة وحدة لـ {existingItem.ProductName}";
                LastActionLabel.Foreground = System.Windows.Media.Brushes.Green;
            }
            else
            {
                // البحث عن المنتج في قاعدة البيانات (محاكاة)
                var product = FindProductByBarcode(barcode);
                if (product != null)
                {
                    _saleItems.Add(product);
                    LastActionLabel.Text = $"تم إضافة منتج جديد: {product.ProductName}";
                    LastActionLabel.Foreground = System.Windows.Media.Brushes.Blue;
                }
                else
                {
                    // منتج غير موجود
                    LastActionLabel.Text = $"منتج غير موجود: {barcode}";
                    LastActionLabel.Foreground = System.Windows.Media.Brushes.Red;
                    MessageBox.Show($"لم يتم العثور على منتج بالكودبار: {barcode}", "منتج غير موجود",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }

            // مسح مربع النص والتركيز عليه
            BarcodeTextBox.Clear();
            BarcodeTextBox.Focus();

            UpdateSummary();
        }

        private QuickSaleItem? FindProductByBarcode(string barcode)
        {
            // محاكاة البحث في قاعدة البيانات
            // في التطبيق الحقيقي، ستبحث في قاعدة البيانات
            var sampleProducts = new[]
            {
                new { Barcode = "12345678", Name = "لابتوب ديل", Price = 85000m },
                new { Barcode = "87654321", Name = "ماوس لوجيتك", Price = 2500m },
                new { Barcode = "11111111", Name = "كيبورد ميكانيكي", Price = 4500m },
                new { Barcode = "22222222", Name = "شاشة سامسونج 24 بوصة", Price = 32000m },
                new { Barcode = "33333333", Name = "سماعات بلوتوث", Price = 8500m },
                new { Barcode = "44444444", Name = "كاميرا ويب HD", Price = 6500m },
                new { Barcode = "55555555", Name = "هارد ديسك خارجي 1TB", Price = 12000m },
                new { Barcode = "66666666", Name = "ذاكرة USB 32GB", Price = 1500m },
                new { Barcode = "77777777", Name = "شاحن لابتوب عام", Price = 3500m },
                new { Barcode = "88888888", Name = "حقيبة لابتوب", Price = 4000m }
            };

            var product = sampleProducts.FirstOrDefault(p => p.Barcode == barcode);
            if (product != null)
            {
                return new QuickSaleItem
                {
                    Barcode = product.Barcode,
                    ProductName = product.Name,
                    UnitPrice = product.Price,
                    Quantity = 1,
                    ProductId = _random.Next(1, 1000)
                };
            }

            return null;
        }

        private void UpdateSummary()
        {
            var totalAmount = _saleItems.Sum(item => item.Total);

            // تحديث الملخص في الأسفل
            ItemsCountLabel.Text = _saleItems.Count.ToString();
            TotalQuantityLabel.Text = _saleItems.Sum(item => item.Quantity).ToString();
            TotalAmountLabel.Text = $"{totalAmount:F2} دج";

            // تحديث المبلغ في رأس النافذة بتأثير رقمي
            HeaderTotalLabel.Text = $"{totalAmount:F2} دج";

            // تحديث عنوان النافذة
            UpdateWindowTitle();

            // تأثير تحديث المبلغ مع تغيير اللون مؤقتاً
            AnimateHeaderTotal(totalAmount);
        }

        private void AnimateHeaderTotal(decimal amount)
        {
            // تأثير بصري عند تحديث المبلغ
            var originalBrush = HeaderTotalLabel.Foreground;

            if (amount > 0)
            {
                // تغيير اللون مؤقتاً للإشارة للتحديث
                HeaderTotalLabel.Foreground = System.Windows.Media.Brushes.Yellow;

                // إنشاء تأثير الرجوع للون الأصلي
                var timer = new System.Windows.Threading.DispatcherTimer();
                timer.Interval = TimeSpan.FromMilliseconds(300);
                timer.Tick += (s, e) =>
                {
                    HeaderTotalLabel.Foreground = originalBrush;
                    timer.Stop();
                };
                timer.Start();
            }
        }

        private void ManualSearch_Click(object sender, RoutedEventArgs e)
        {
            var searchWindow = new ProductSearchWindow();
            if (searchWindow.ShowDialog() == true && searchWindow.SelectedProduct != null)
            {
                var product = searchWindow.SelectedProduct;
                var existingItem = _saleItems.FirstOrDefault(item => item.Barcode == product.Barcode);

                if (existingItem != null)
                {
                    existingItem.Quantity++;
                }
                else
                {
                    _saleItems.Add(new QuickSaleItem
                    {
                        Barcode = product.Barcode,
                        ProductName = product.ProductName,
                        UnitPrice = product.UnitPrice,
                        Quantity = 1,
                        ProductId = product.ProductId
                    });
                }

                UpdateSummary();
                BarcodeTextBox.Focus();
            }
        }

        private void ClearBarcode_Click(object sender, RoutedEventArgs e)
        {
            BarcodeTextBox.Clear();
            BarcodeTextBox.Focus();
        }

        private void IncreaseQuantity_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is QuickSaleItem item)
            {
                item.Quantity++;
                UpdateSummary();
            }
        }

        private void DecreaseQuantity_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is QuickSaleItem item && item.Quantity > 1)
            {
                item.Quantity--;
                UpdateSummary();
            }
        }

        private void EditQuantity_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is QuickSaleItem item)
            {
                string input = Microsoft.VisualBasic.Interaction.InputBox(
                    "أدخل الكمية الجديدة:", "تعديل الكمية", item.Quantity.ToString());

                if (int.TryParse(input, out int newQuantity) && newQuantity > 0)
                {
                    item.Quantity = newQuantity;
                    UpdateSummary();
                }
            }
        }

        private void RemoveProduct_Click(object sender, RoutedEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is QuickSaleItem item)
            {
                var result = MessageBox.Show($"هل تريد حذف {item.ProductName}؟", "تأكيد الحذف",
                                           MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    _saleItems.Remove(item);
                    UpdateSummary();
                    LastActionLabel.Text = $"تم حذف {item.ProductName}";
                    LastActionLabel.Foreground = System.Windows.Media.Brushes.Orange;
                }
            }
        }

        private void CashPayment_Click(object sender, RoutedEventArgs e)
        {
            if (_saleItems.Count == 0)
            {
                MessageBox.Show("لا توجد منتجات للبيع!", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var paymentWindow = new QuickPaymentWindow(_saleItems.ToList(), "نقدي");
            if (paymentWindow.ShowDialog() == true)
            {
                MessageBox.Show("تم إتمام عملية البيع بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                _saleItems.Clear();
                UpdateSummary();
                LastActionLabel.Text = "تم إتمام البيع - جاهز للمسح";
                LastActionLabel.Foreground = System.Windows.Media.Brushes.Green;
                BarcodeTextBox.Focus();
            }
        }

        private void CardPayment_Click(object sender, RoutedEventArgs e)
        {
            if (_saleItems.Count == 0)
            {
                MessageBox.Show("لا توجد منتجات للبيع!", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var paymentWindow = new QuickPaymentWindow(_saleItems.ToList(), "بطاقة");
            if (paymentWindow.ShowDialog() == true)
            {
                MessageBox.Show("تم إتمام عملية البيع بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                _saleItems.Clear();
                UpdateSummary();
                LastActionLabel.Text = "تم إتمام البيع - جاهز للمسح";
                LastActionLabel.Foreground = System.Windows.Media.Brushes.Green;
                BarcodeTextBox.Focus();
            }
        }

        private void SaveDraft_Click(object sender, RoutedEventArgs e)
        {
            if (_saleItems.Count == 0)
            {
                MessageBox.Show("لا توجد منتجات للحفظ!", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBox.Show("تم حفظ الفاتورة كمسودة!", "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
            LastActionLabel.Text = "تم حفظ المسودة";
            LastActionLabel.Foreground = System.Windows.Media.Brushes.Blue;
        }

        private void ClearAll_Click(object sender, RoutedEventArgs e)
        {
            if (_saleItems.Count == 0)
                return;

            var result = MessageBox.Show("هل تريد مسح جميع المنتجات؟", "تأكيد المسح",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                _saleItems.Clear();
                UpdateSummary();
                LastActionLabel.Text = "تم مسح جميع المنتجات";
                LastActionLabel.Foreground = System.Windows.Media.Brushes.Red;
                BarcodeTextBox.Focus();
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            if (_saleItems.Count > 0)
            {
                var result = MessageBox.Show("هناك منتجات في القائمة. هل تريد الإغلاق؟", "تأكيد الإغلاق",
                                           MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                    return;
            }

            Close();
        }
    }

    // كلاس مؤقت لنافذة البحث اليدوي
    public class ProductSearchWindow : Window
    {
        public QuickSaleItem? SelectedProduct { get; set; }

        public ProductSearchWindow()
        {
            Title = "البحث عن منتج";
            Width = 400;
            Height = 300;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // محتوى مؤقت
            Content = new TextBlock
            {
                Text = "نافذة البحث اليدوي - قيد التطوير",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
        }
    }

    // كلاس مؤقت لنافذة الدفع
    public class QuickPaymentWindow : Window
    {
        public QuickPaymentWindow(List<QuickSaleItem> items, string paymentMethod)
        {
            Title = $"الدفع - {paymentMethod}";
            Width = 500;
            Height = 400;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // محتوى مؤقت
            var content = new StackPanel();
            content.Children.Add(new TextBlock
            {
                Text = $"نافذة الدفع {paymentMethod}",
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(10)
            });

            var okButton = new Button
            {
                Content = "تأكيد الدفع",
                Width = 100,
                Height = 30,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(10)
            };
            okButton.Click += (s, e) => { DialogResult = true; Close(); };
            content.Children.Add(okButton);

            Content = content;
        }
    }
}
