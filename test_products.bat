@echo off
echo ========================================
echo    نظام إدارة المنتجات الافتراضية
echo ========================================
echo.
echo سيتم تشغيل النظام لاختبار:
echo 1. إنشاء 4 منتجات افتراضية
echo 2. اختبار زر الحفظ في نافذة إضافة المنتج
echo 3. البحث بالاسم والكود والباركود
echo 4. تعديل وحذف المنتجات
echo.
echo ========================================

cd /d "%~dp0"

echo بناء المشروع...
dotnet build --configuration Release --verbosity minimal

if %ERRORLEVEL% NEQ 0 (
    echo خطأ في بناء المشروع!
    pause
    exit /b 1
)

echo.
echo تشغيل النظام...
echo.
echo تعليمات الاستخدام:
echo - اذهب إلى قسم المنتجات
echo - اضغط على "إضافة منتج جديد"
echo - جرب البحث بالكود: ELEC001
echo - جرب البحث بالباركود: 1234567890123
echo.

dotnet run --configuration Release

pause
