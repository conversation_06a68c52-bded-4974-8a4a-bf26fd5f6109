<UserControl x:Class="SalesManagementSystem.Views.OpportunityManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- تحويل مرحلة الفرصة إلى لون -->
        <Style x:Key="OpportunityStageCard" TargetType="Border">
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Stage}" Value="Prospecting">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Stage}" Value="Qualification">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Stage}" Value="NeedsAnalysis">
                    <Setter Property="Background" Value="#F3E5F5"/>
                    <Setter Property="BorderBrush" Value="#9C27B0"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Stage}" Value="Proposal">
                    <Setter Property="Background" Value="#E0F2F1"/>
                    <Setter Property="BorderBrush" Value="#009688"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Stage}" Value="Negotiation">
                    <Setter Property="Background" Value="#EFEBE9"/>
                    <Setter Property="BorderBrush" Value="#795548"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Stage}" Value="Closing">
                    <Setter Property="Background" Value="#E8F5E8"/>
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل أولوية الفرصة إلى لون -->
        <Style x:Key="PriorityIndicator" TargetType="materialDesign:Chip">
            <Setter Property="Background" Value="Gray"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Priority}" Value="Low">
                    <Setter Property="Background" Value="Green"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Priority}" Value="Medium">
                    <Setter Property="Background" Value="Orange"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Priority}" Value="High">
                    <Setter Property="Background" Value="Red"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Priority}" Value="Critical">
                    <Setter Property="Background" Value="Purple"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="TrendingUp" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                           VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="إدارة الفرص التجارية" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding AddOpportunityCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="فرصة جديدة"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding ImportOpportunitiesCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Import" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="استيراد"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding ExportOpportunitiesCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Export" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding RefreshCommand}"
                            Margin="8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات الفرص -->
        <materialDesign:Card Grid.Row="1" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي الفرص -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="TrendingUp" Width="28" Height="28" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalOpportunities}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي الفرص" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- الفرص المفتوحة -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="FolderOpen" Width="28" Height="28" 
                                           Foreground="Orange"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding OpenOpportunities}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Orange"/>
                    <TextBlock Text="فرص مفتوحة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- الفرص المكسوبة -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CheckCircle" Width="28" Height="28" 
                                           Foreground="Green"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding WonOpportunities}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Green"/>
                    <TextBlock Text="فرص مكسوبة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- إجمالي القيمة -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CurrencyUsd" Width="28" Height="28" 
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalValue, StringFormat='{}{0:C}'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="إجمالي القيمة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- القيمة المرجحة -->
                <StackPanel Grid.Column="4" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ScaleBalance" Width="28" Height="28" 
                                           Foreground="Purple"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding WeightedValue, StringFormat='{}{0:C}'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Purple"/>
                    <TextBlock Text="القيمة المرجحة" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- معدل النجاح -->
                <StackPanel Grid.Column="5" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ChartLine" Width="28" Height="28" 
                                           Foreground="Teal"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding WinRate, StringFormat='{}{0:F1}%'}" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             HorizontalAlignment="Center"
                             Foreground="Teal"/>
                    <TextBlock Text="معدل النجاح" 
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط البحث والفلترة -->
        <materialDesign:Card Grid.Row="2" Margin="16,8,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- مربع البحث -->
                <TextBox Grid.Column="0" 
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="البحث في الفرص..."
                         materialDesign:TextFieldAssist.HasLeadingIcon="True"
                         materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,8,0"/>

                <!-- فلتر المرحلة -->
                <ComboBox Grid.Column="1" 
                          SelectedItem="{Binding SelectedStage}"
                          ItemsSource="{Binding Stages}"
                          materialDesign:HintAssist.Hint="المرحلة"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Display}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- فلتر الحالة -->
                <ComboBox Grid.Column="2" 
                          SelectedItem="{Binding SelectedStatus}"
                          ItemsSource="{Binding Statuses}"
                          materialDesign:HintAssist.Hint="الحالة"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Display}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- فلتر الأولوية -->
                <ComboBox Grid.Column="3" 
                          SelectedItem="{Binding SelectedPriority}"
                          ItemsSource="{Binding Priorities}"
                          materialDesign:HintAssist.Hint="الأولوية"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Display}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- أزرار الفلترة -->
                <StackPanel Grid.Column="4" Orientation="Horizontal" Margin="8,0,0,0">
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            Command="{Binding ApplyFiltersCommand}"
                            ToolTip="تطبيق الفلاتر">
                        <materialDesign:PackIcon Kind="FilterVariant" Width="20" Height="20"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            Command="{Binding ClearFiltersCommand}"
                            ToolTip="مسح الفلاتر">
                        <materialDesign:PackIcon Kind="FilterVariantRemove" Width="20" Height="20"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- قائمة الفرص -->
        <ScrollViewer Grid.Row="3" Margin="16,8,16,16" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding FilteredOpportunities}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="2" Margin="0,0,0,16"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <materialDesign:Card Margin="8" 
                                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
                            <Border BorderThickness="2" CornerRadius="4" Style="{StaticResource OpportunityStageCard}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- رأس البطاقة -->
                                    <Grid Grid.Row="0" Margin="16,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- عنوان الفرصة -->
                                        <StackPanel Grid.Column="0" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding Title}" 
                                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                     FontWeight="Bold"/>
                                            <TextBlock Text="{Binding OpportunityCode}" 
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     Opacity="0.7"/>
                                        </StackPanel>

                                        <!-- مؤشر الأولوية -->
                                        <materialDesign:Chip Grid.Column="1"
                                                           Content="{Binding PriorityDisplay}"
                                                           VerticalAlignment="Center"
                                                           FontSize="10"
                                                           Style="{StaticResource PriorityIndicator}"
                                                           Margin="8,0"/>

                                        <!-- مؤشر الحالة -->
                                        <materialDesign:Chip Grid.Column="2"
                                                           Content="{Binding StatusDisplay}"
                                                           VerticalAlignment="Center"
                                                           FontSize="10"
                                                           Background="{Binding StatusColor}"/>
                                    </Grid>

                                    <!-- محتوى البطاقة -->
                                    <Grid Grid.Row="1" Margin="16">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- معلومات العميل -->
                                        <StackPanel Grid.Row="0" Margin="0,0,0,8">
                                            <TextBlock Text="العميل" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                            <TextBlock Text="{Binding CustomerName}" Style="{StaticResource MaterialDesignBody1TextBlock}" FontWeight="Bold"/>
                                        </StackPanel>

                                        <!-- القيمة والاحتمالية -->
                                        <Grid Grid.Row="1" Margin="0,0,0,8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                <TextBlock Text="القيمة المقدرة" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding FormattedEstimatedValue}" 
                                                         Style="{StaticResource MaterialDesignBody1TextBlock}" 
                                                         Foreground="{DynamicResource SecondaryHueMidBrush}"
                                                         FontWeight="Bold"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                <TextBlock Text="الاحتمالية" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding FormattedProbability}" 
                                                             Style="{StaticResource MaterialDesignBody1TextBlock}" 
                                                             FontWeight="Bold"/>
                                                    <ProgressBar Value="{Binding ProbabilityPercentage}" 
                                                               Maximum="100" 
                                                               Width="50" 
                                                               Height="4" 
                                                               VerticalAlignment="Center"
                                                               Margin="8,0,0,0"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Grid>

                                        <!-- التواريخ -->
                                        <Grid Grid.Row="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                <TextBlock Text="تاريخ الإغلاق المتوقع" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding FormattedExpectedCloseDate}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                <TextBlock Text="آخر نشاط" Style="{StaticResource MaterialDesignCaptionTextBlock}" Opacity="0.7"/>
                                                <TextBlock Text="{Binding FormattedLastActivityDate}" Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            </StackPanel>
                                        </Grid>
                                    </Grid>

                                    <!-- شريط التقدم -->
                                    <ProgressBar Grid.Row="2" 
                                               Value="{Binding StageProgress}" 
                                               Maximum="100" 
                                               Height="6" 
                                               Margin="16,0,16,8"
                                               Foreground="{Binding StageColor}"/>

                                    <!-- أزرار الإجراءات -->
                                    <Grid Grid.Row="3" Margin="16,8,16,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- مرحلة الفرصة -->
                                        <materialDesign:Chip Grid.Column="0" 
                                                           Content="{Binding StageDisplay}" 
                                                           FontSize="10"
                                                           VerticalAlignment="Center"
                                                           Background="{Binding StageColor}"/>

                                        <!-- زر المرحلة التالية -->
                                        <Button Grid.Column="1" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.MoveToNextStageCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="المرحلة التالية">
                                            <materialDesign:PackIcon Kind="ArrowRight" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر إضافة نشاط -->
                                        <Button Grid.Column="2" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.AddActivityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="إضافة نشاط">
                                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر التحرير -->
                                        <Button Grid.Column="3" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.EditOpportunityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="تحرير">
                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر عرض التفاصيل -->
                                        <Button Grid.Column="4" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.ViewOpportunityDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="عرض التفاصيل">
                                            <materialDesign:PackIcon Kind="Eye" Width="18" Height="18"/>
                                        </Button>

                                        <!-- زر الحذف -->
                                        <Button Grid.Column="5" 
                                              Style="{StaticResource MaterialDesignIconButton}"
                                              Command="{Binding DataContext.DeleteOpportunityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}"
                                              ToolTip="حذف">
                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Border>
                        </materialDesign:Card>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Grid Grid.RowSpan="4" 
              Background="White" 
              Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"/>
                <TextBlock Text="جاري التحميل..." 
                         Style="{StaticResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
