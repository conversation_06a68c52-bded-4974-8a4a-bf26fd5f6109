using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج السلة الإلكترونية
    /// </summary>
    public class ShoppingCart : INotifyPropertyChanged
    {
        private int _id;
        private string _sessionId = string.Empty;
        private int? _customerId;
        private int _storeId;
        private string _currency = "SAR";
        private decimal _subtotal;
        private decimal _taxAmount;
        private decimal _shippingAmount;
        private decimal _discountAmount;
        private decimal _totalAmount;
        private string _couponCode = string.Empty;
        private decimal _couponDiscount;
        private string _shippingMethod = string.Empty;
        private decimal _shippingCost;
        private string _notes = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime _updatedAt = DateTime.Now;
        private DateTime? _expiresAt;
        private ObservableCollection<CartItem> _items = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SessionId
        {
            get => _sessionId;
            set
            {
                if (_sessionId != value)
                {
                    _sessionId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsGuestCart));
                }
            }
        }

        public int StoreId
        {
            get => _storeId;
            set
            {
                if (_storeId != value)
                {
                    _storeId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Currency
        {
            get => _currency;
            set
            {
                if (_currency != value)
                {
                    _currency = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Subtotal
        {
            get => _subtotal;
            set
            {
                if (_subtotal != value)
                {
                    _subtotal = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedSubtotal));
                }
            }
        }

        public decimal TaxAmount
        {
            get => _taxAmount;
            set
            {
                if (_taxAmount != value)
                {
                    _taxAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTaxAmount));
                }
            }
        }

        public decimal ShippingAmount
        {
            get => _shippingAmount;
            set
            {
                if (_shippingAmount != value)
                {
                    _shippingAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedShippingAmount));
                }
            }
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set
            {
                if (_discountAmount != value)
                {
                    _discountAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDiscountAmount));
                    OnPropertyChanged(nameof(HasDiscount));
                }
            }
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set
            {
                if (_totalAmount != value)
                {
                    _totalAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalAmount));
                }
            }
        }

        public string CouponCode
        {
            get => _couponCode;
            set
            {
                if (_couponCode != value)
                {
                    _couponCode = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasCoupon));
                }
            }
        }

        public decimal CouponDiscount
        {
            get => _couponDiscount;
            set
            {
                if (_couponDiscount != value)
                {
                    _couponDiscount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCouponDiscount));
                }
            }
        }

        public string ShippingMethod
        {
            get => _shippingMethod;
            set
            {
                if (_shippingMethod != value)
                {
                    _shippingMethod = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal ShippingCost
        {
            get => _shippingCost;
            set
            {
                if (_shippingCost != value)
                {
                    _shippingCost = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedShippingCost));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedUpdatedAt));
                }
            }
        }

        public DateTime? ExpiresAt
        {
            get => _expiresAt;
            set
            {
                if (_expiresAt != value)
                {
                    _expiresAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsExpired));
                    OnPropertyChanged(nameof(TimeToExpiry));
                }
            }
        }

        public ObservableCollection<CartItem> Items
        {
            get => _items;
            set
            {
                if (_items != value)
                {
                    _items = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ItemsCount));
                    OnPropertyChanged(nameof(TotalQuantity));
                    OnPropertyChanged(nameof(IsEmpty));
                    CalculateTotals();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsGuestCart => !CustomerId.HasValue;
        public bool IsEmpty => Items?.Count == 0;
        public bool HasDiscount => DiscountAmount > 0;
        public bool HasCoupon => !string.IsNullOrEmpty(CouponCode);
        public bool IsExpired => ExpiresAt.HasValue && ExpiresAt < DateTime.Now;
        public int ItemsCount => Items?.Count ?? 0;
        public int TotalQuantity => Items?.Sum(i => i.Quantity) ?? 0;

        public string TimeToExpiry
        {
            get
            {
                if (!ExpiresAt.HasValue || IsExpired) return "منتهية الصلاحية";
                var timeLeft = ExpiresAt.Value - DateTime.Now;
                if (timeLeft.TotalDays >= 1)
                    return $"{(int)timeLeft.TotalDays} يوم";
                if (timeLeft.TotalHours >= 1)
                    return $"{(int)timeLeft.TotalHours} ساعة";
                return $"{(int)timeLeft.TotalMinutes} دقيقة";
            }
        }

        // Formatted Properties
        public string FormattedSubtotal => Subtotal.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTaxAmount => TaxAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedShippingAmount => ShippingAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedDiscountAmount => DiscountAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalAmount => TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedCouponDiscount => CouponDiscount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedShippingCost => ShippingCost.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");
        public string FormattedUpdatedAt => UpdatedAt.ToString("dd/MM/yyyy HH:mm");

        #endregion

        #region Methods

        public void AddItem(int productId, string productName, string productSku, decimal unitPrice, int quantity = 1, string variantInfo = "", string productImage = "")
        {
            var existingItem = Items.FirstOrDefault(i => i.ProductId == productId && i.VariantInfo == variantInfo);
            
            if (existingItem != null)
            {
                existingItem.Quantity += quantity;
                existingItem.UpdateTotal();
            }
            else
            {
                var newItem = new CartItem
                {
                    CartId = Id,
                    ProductId = productId,
                    ProductName = productName,
                    ProductSku = productSku,
                    ProductImage = productImage,
                    UnitPrice = unitPrice,
                    Quantity = quantity,
                    VariantInfo = variantInfo
                };
                newItem.UpdateTotal();
                Items.Add(newItem);
            }

            UpdateTimestamp();
            CalculateTotals();
        }

        public void RemoveItem(int productId, string variantInfo = "")
        {
            var item = Items.FirstOrDefault(i => i.ProductId == productId && i.VariantInfo == variantInfo);
            if (item != null)
            {
                Items.Remove(item);
                UpdateTimestamp();
                CalculateTotals();
            }
        }

        public void UpdateItemQuantity(int productId, int newQuantity, string variantInfo = "")
        {
            var item = Items.FirstOrDefault(i => i.ProductId == productId && i.VariantInfo == variantInfo);
            if (item != null)
            {
                if (newQuantity <= 0)
                {
                    RemoveItem(productId, variantInfo);
                }
                else
                {
                    item.Quantity = newQuantity;
                    item.UpdateTotal();
                    UpdateTimestamp();
                    CalculateTotals();
                }
            }
        }

        public void Clear()
        {
            Items.Clear();
            CouponCode = string.Empty;
            CouponDiscount = 0;
            UpdateTimestamp();
            CalculateTotals();
        }

        public void ApplyCoupon(string couponCode, decimal discountAmount)
        {
            CouponCode = couponCode;
            CouponDiscount = discountAmount;
            UpdateTimestamp();
            CalculateTotals();
        }

        public void RemoveCoupon()
        {
            CouponCode = string.Empty;
            CouponDiscount = 0;
            UpdateTimestamp();
            CalculateTotals();
        }

        public void SetShipping(string method, decimal cost)
        {
            ShippingMethod = method;
            ShippingCost = cost;
            ShippingAmount = cost;
            UpdateTimestamp();
            CalculateTotals();
        }

        public void CalculateTotals()
        {
            Subtotal = Items.Sum(i => i.Total);
            
            // Calculate tax (assuming 15% VAT for Saudi Arabia)
            TaxAmount = Subtotal * 0.15m;
            
            // Calculate total
            TotalAmount = Subtotal + TaxAmount + ShippingAmount - DiscountAmount - CouponDiscount;
            
            // Ensure total is not negative
            if (TotalAmount < 0) TotalAmount = 0;
        }

        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.Now;
        }

        public void SetExpiry(TimeSpan duration)
        {
            ExpiresAt = DateTime.Now.Add(duration);
        }

        public void ExtendExpiry(TimeSpan additionalTime)
        {
            if (ExpiresAt.HasValue)
                ExpiresAt = ExpiresAt.Value.Add(additionalTime);
            else
                SetExpiry(additionalTime);
        }

        public bool HasProduct(int productId, string variantInfo = "")
        {
            return Items.Any(i => i.ProductId == productId && i.VariantInfo == variantInfo);
        }

        public int GetProductQuantity(int productId, string variantInfo = "")
        {
            var item = Items.FirstOrDefault(i => i.ProductId == productId && i.VariantInfo == variantInfo);
            return item?.Quantity ?? 0;
        }

        public OnlineOrder ConvertToOrder(int? customerId = null, string customerEmail = "", string customerPhone = "")
        {
            var order = new OnlineOrder
            {
                StoreId = StoreId,
                CustomerId = customerId ?? CustomerId,
                CustomerEmail = customerEmail,
                CustomerPhone = customerPhone,
                Currency = Currency,
                Subtotal = Subtotal,
                TaxAmount = TaxAmount,
                ShippingAmount = ShippingAmount,
                DiscountAmount = DiscountAmount,
                TotalAmount = TotalAmount,
                CouponCode = CouponCode,
                CouponDiscount = CouponDiscount,
                ShippingMethod = ShippingMethod,
                ShippingCost = ShippingCost,
                Notes = Notes
            };

            // Convert cart items to order items
            foreach (var cartItem in Items)
            {
                order.AddItem(new OrderItem
                {
                    ProductId = cartItem.ProductId,
                    ProductName = cartItem.ProductName,
                    ProductSku = cartItem.ProductSku,
                    ProductImage = cartItem.ProductImage,
                    Quantity = cartItem.Quantity,
                    UnitPrice = cartItem.UnitPrice,
                    Total = cartItem.Total,
                    VariantInfo = cartItem.VariantInfo
                });
            }

            return order;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// عنصر السلة
    /// </summary>
    public class CartItem : INotifyPropertyChanged
    {
        private int _id;
        private int _cartId;
        private int _productId;
        private string _productName = string.Empty;
        private string _productSku = string.Empty;
        private string _productImage = string.Empty;
        private decimal _unitPrice;
        private int _quantity = 1;
        private decimal _total;
        private string _variantInfo = string.Empty;
        private DateTime _addedAt = DateTime.Now;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CartId
        {
            get => _cartId;
            set
            {
                if (_cartId != value)
                {
                    _cartId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ProductId
        {
            get => _productId;
            set
            {
                if (_productId != value)
                {
                    _productId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductName
        {
            get => _productName;
            set
            {
                if (_productName != value)
                {
                    _productName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductSku
        {
            get => _productSku;
            set
            {
                if (_productSku != value)
                {
                    _productSku = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductImage
        {
            get => _productImage;
            set
            {
                if (_productImage != value)
                {
                    _productImage = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set
            {
                if (_unitPrice != value)
                {
                    _unitPrice = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedUnitPrice));
                    UpdateTotal();
                }
            }
        }

        public int Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                    UpdateTotal();
                }
            }
        }

        public decimal Total
        {
            get => _total;
            set
            {
                if (_total != value)
                {
                    _total = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotal));
                }
            }
        }

        public string VariantInfo
        {
            get => _variantInfo;
            set
            {
                if (_variantInfo != value)
                {
                    _variantInfo = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime AddedAt
        {
            get => _addedAt;
            set
            {
                if (_addedAt != value)
                {
                    _addedAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAddedAt));
                }
            }
        }

        // Formatted Properties
        public string FormattedUnitPrice => UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotal => Total.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedAddedAt => AddedAt.ToString("dd/MM/yyyy HH:mm");

        public void UpdateTotal()
        {
            Total = UnitPrice * Quantity;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    #region Validation

    public class ShoppingCartValidator : AbstractValidator<ShoppingCart>
    {
        public ShoppingCartValidator()
        {
            RuleFor(c => c.SessionId)
                .NotEmpty().WithMessage("معرف الجلسة مطلوب");

            RuleFor(c => c.StoreId)
                .GreaterThan(0).WithMessage("معرف المتجر مطلوب");

            RuleFor(c => c.Items)
                .NotEmpty().WithMessage("السلة فارغة");
        }
    }

    public class CartItemValidator : AbstractValidator<CartItem>
    {
        public CartItemValidator()
        {
            RuleFor(i => i.ProductId)
                .GreaterThan(0).WithMessage("معرف المنتج مطلوب");

            RuleFor(i => i.ProductName)
                .NotEmpty().WithMessage("اسم المنتج مطلوب");

            RuleFor(i => i.UnitPrice)
                .GreaterThan(0).WithMessage("سعر الوحدة يجب أن يكون أكبر من صفر");

            RuleFor(i => i.Quantity)
                .GreaterThan(0).WithMessage("الكمية يجب أن تكون أكبر من صفر");
        }
    }

    #endregion
}
