using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج المنتج الإلكتروني
    /// </summary>
    public class OnlineProduct : INotifyPropertyChanged
    {
        private int _id;
        private int _storeId;
        private int _productId;
        private string _sku = string.Empty;
        private string _name = string.Empty;
        private string _slug = string.Empty;
        private string _shortDescription = string.Empty;
        private string _fullDescription = string.Empty;
        private decimal _price;
        private decimal _comparePrice;
        private decimal _costPrice;
        private bool _trackQuantity = true;
        private int _stockQuantity;
        private int _lowStockThreshold = 5;
        private bool _allowBackorders;
        private ProductStatus _status = ProductStatus.Active;
        private ProductVisibility _visibility = ProductVisibility.Visible;
        private bool _isFeatured;
        private bool _isDigital;
        private decimal _weight;
        private string _dimensions = string.Empty;
        private string _shippingClass = string.Empty;
        private string _metaTitle = string.Empty;
        private string _metaDescription = string.Empty;
        private string _metaKeywords = string.Empty;
        private string _tags = string.Empty;
        private int _sortOrder;
        private DateTime? _publishDate;
        private DateTime? _saleStartDate;
        private DateTime? _saleEndDate;
        private decimal _salePrice;
        private int _viewCount;
        private decimal _averageRating;
        private int _reviewCount;
        private int _salesCount;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private ObservableCollection<ProductImage> _images = new();
        private ObservableCollection<ProductVariant> _variants = new();
        private ObservableCollection<ProductAttribute> _attributes = new();
        private ObservableCollection<ProductReview> _reviews = new();
        private ObservableCollection<int> _categoryIds = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int StoreId
        {
            get => _storeId;
            set
            {
                if (_storeId != value)
                {
                    _storeId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ProductId
        {
            get => _productId;
            set
            {
                if (_productId != value)
                {
                    _productId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Sku
        {
            get => _sku;
            set
            {
                if (_sku != value)
                {
                    _sku = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                    if (string.IsNullOrEmpty(Slug))
                        GenerateSlug();
                }
            }
        }

        public string Slug
        {
            get => _slug;
            set
            {
                if (_slug != value)
                {
                    _slug = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ShortDescription
        {
            get => _shortDescription;
            set
            {
                if (_shortDescription != value)
                {
                    _shortDescription = value;
                    OnPropertyChanged();
                }
            }
        }

        public string FullDescription
        {
            get => _fullDescription;
            set
            {
                if (_fullDescription != value)
                {
                    _fullDescription = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Price
        {
            get => _price;
            set
            {
                if (_price != value)
                {
                    _price = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedPrice));
                    OnPropertyChanged(nameof(DiscountPercentage));
                    OnPropertyChanged(nameof(HasDiscount));
                }
            }
        }

        public decimal ComparePrice
        {
            get => _comparePrice;
            set
            {
                if (_comparePrice != value)
                {
                    _comparePrice = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedComparePrice));
                    OnPropertyChanged(nameof(DiscountPercentage));
                    OnPropertyChanged(nameof(HasDiscount));
                }
            }
        }

        public decimal CostPrice
        {
            get => _costPrice;
            set
            {
                if (_costPrice != value)
                {
                    _costPrice = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCostPrice));
                    OnPropertyChanged(nameof(ProfitMargin));
                }
            }
        }

        public bool TrackQuantity
        {
            get => _trackQuantity;
            set
            {
                if (_trackQuantity != value)
                {
                    _trackQuantity = value;
                    OnPropertyChanged();
                }
            }
        }

        public int StockQuantity
        {
            get => _stockQuantity;
            set
            {
                if (_stockQuantity != value)
                {
                    _stockQuantity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsInStock));
                    OnPropertyChanged(nameof(IsLowStock));
                    OnPropertyChanged(nameof(StockStatus));
                }
            }
        }

        public int LowStockThreshold
        {
            get => _lowStockThreshold;
            set
            {
                if (_lowStockThreshold != value)
                {
                    _lowStockThreshold = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsLowStock));
                }
            }
        }

        public bool AllowBackorders
        {
            get => _allowBackorders;
            set
            {
                if (_allowBackorders != value)
                {
                    _allowBackorders = value;
                    OnPropertyChanged();
                }
            }
        }

        public ProductStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        public ProductVisibility Visibility
        {
            get => _visibility;
            set
            {
                if (_visibility != value)
                {
                    _visibility = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(VisibilityDisplay));
                    OnPropertyChanged(nameof(IsVisible));
                }
            }
        }

        public bool IsFeatured
        {
            get => _isFeatured;
            set
            {
                if (_isFeatured != value)
                {
                    _isFeatured = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsDigital
        {
            get => _isDigital;
            set
            {
                if (_isDigital != value)
                {
                    _isDigital = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Weight
        {
            get => _weight;
            set
            {
                if (_weight != value)
                {
                    _weight = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedWeight));
                }
            }
        }

        public string Dimensions
        {
            get => _dimensions;
            set
            {
                if (_dimensions != value)
                {
                    _dimensions = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ShippingClass
        {
            get => _shippingClass;
            set
            {
                if (_shippingClass != value)
                {
                    _shippingClass = value;
                    OnPropertyChanged();
                }
            }
        }

        public string MetaTitle
        {
            get => _metaTitle;
            set
            {
                if (_metaTitle != value)
                {
                    _metaTitle = value;
                    OnPropertyChanged();
                }
            }
        }

        public string MetaDescription
        {
            get => _metaDescription;
            set
            {
                if (_metaDescription != value)
                {
                    _metaDescription = value;
                    OnPropertyChanged();
                }
            }
        }

        public string MetaKeywords
        {
            get => _metaKeywords;
            set
            {
                if (_metaKeywords != value)
                {
                    _metaKeywords = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Tags
        {
            get => _tags;
            set
            {
                if (_tags != value)
                {
                    _tags = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TagsList));
                }
            }
        }

        public int SortOrder
        {
            get => _sortOrder;
            set
            {
                if (_sortOrder != value)
                {
                    _sortOrder = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? PublishDate
        {
            get => _publishDate;
            set
            {
                if (_publishDate != value)
                {
                    _publishDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedPublishDate));
                    OnPropertyChanged(nameof(IsPublished));
                }
            }
        }

        public DateTime? SaleStartDate
        {
            get => _saleStartDate;
            set
            {
                if (_saleStartDate != value)
                {
                    _saleStartDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsOnSale));
                }
            }
        }

        public DateTime? SaleEndDate
        {
            get => _saleEndDate;
            set
            {
                if (_saleEndDate != value)
                {
                    _saleEndDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsOnSale));
                }
            }
        }

        public decimal SalePrice
        {
            get => _salePrice;
            set
            {
                if (_salePrice != value)
                {
                    _salePrice = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedSalePrice));
                    OnPropertyChanged(nameof(CurrentPrice));
                    OnPropertyChanged(nameof(IsOnSale));
                }
            }
        }

        public int ViewCount
        {
            get => _viewCount;
            set
            {
                if (_viewCount != value)
                {
                    _viewCount = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal AverageRating
        {
            get => _averageRating;
            set
            {
                if (_averageRating != value)
                {
                    _averageRating = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedRating));
                }
            }
        }

        public int ReviewCount
        {
            get => _reviewCount;
            set
            {
                if (_reviewCount != value)
                {
                    _reviewCount = value;
                    OnPropertyChanged();
                }
            }
        }

        public int SalesCount
        {
            get => _salesCount;
            set
            {
                if (_salesCount != value)
                {
                    _salesCount = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<ProductImage> Images
        {
            get => _images;
            set
            {
                if (_images != value)
                {
                    _images = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(MainImage));
                    OnPropertyChanged(nameof(HasImages));
                }
            }
        }

        public ObservableCollection<ProductVariant> Variants
        {
            get => _variants;
            set
            {
                if (_variants != value)
                {
                    _variants = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasVariants));
                }
            }
        }

        public ObservableCollection<ProductAttribute> Attributes
        {
            get => _attributes;
            set
            {
                if (_attributes != value)
                {
                    _attributes = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<ProductReview> Reviews
        {
            get => _reviews;
            set
            {
                if (_reviews != value)
                {
                    _reviews = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<int> CategoryIds
        {
            get => _categoryIds;
            set
            {
                if (_categoryIds != value)
                {
                    _categoryIds = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsActive => Status == ProductStatus.Active;
        public bool IsVisible => Visibility == ProductVisibility.Visible;
        public bool IsPublished => PublishDate.HasValue && PublishDate <= DateTime.Now;
        public bool IsInStock => !TrackQuantity || StockQuantity > 0;
        public bool IsLowStock => TrackQuantity && StockQuantity <= LowStockThreshold && StockQuantity > 0;
        public bool HasImages => Images?.Any() == true;
        public bool HasVariants => Variants?.Any() == true;
        public bool HasDiscount => ComparePrice > 0 && ComparePrice > Price;
        public string MainImage => Images?.FirstOrDefault()?.ImageUrl ?? "";
        public string[] TagsList => Tags?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();

        public bool IsOnSale
        {
            get
            {
                if (SalePrice <= 0) return false;
                var now = DateTime.Now;
                return (!SaleStartDate.HasValue || SaleStartDate <= now) &&
                       (!SaleEndDate.HasValue || SaleEndDate >= now);
            }
        }

        public decimal CurrentPrice => IsOnSale ? SalePrice : Price;

        public decimal DiscountPercentage
        {
            get
            {
                if (ComparePrice <= 0 || Price <= 0) return 0;
                return ((ComparePrice - Price) / ComparePrice) * 100;
            }
        }

        public decimal ProfitMargin
        {
            get
            {
                if (CostPrice <= 0 || Price <= 0) return 0;
                return ((Price - CostPrice) / Price) * 100;
            }
        }

        public string StockStatus
        {
            get
            {
                if (!TrackQuantity) return "غير محدود";
                if (StockQuantity <= 0) return "نفد المخزون";
                if (IsLowStock) return "مخزون منخفض";
                return "متوفر";
            }
        }

        // Display Properties
        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    ProductStatus.Active => "نشط",
                    ProductStatus.Inactive => "غير نشط",
                    ProductStatus.Draft => "مسودة",
                    ProductStatus.OutOfStock => "نفد المخزون",
                    _ => "غير محدد"
                };
            }
        }

        public string VisibilityDisplay
        {
            get
            {
                return Visibility switch
                {
                    ProductVisibility.Visible => "مرئي",
                    ProductVisibility.Hidden => "مخفي",
                    ProductVisibility.Catalog => "الكتالوج فقط",
                    ProductVisibility.Search => "البحث فقط",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    ProductStatus.Active => "Green",
                    ProductStatus.Inactive => "Gray",
                    ProductStatus.Draft => "Orange",
                    ProductStatus.OutOfStock => "Red",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedPrice => Price.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedComparePrice => ComparePrice.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedSalePrice => SalePrice.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedCostPrice => CostPrice.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedCurrentPrice => CurrentPrice.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedWeight => $"{Weight:F2} كجم";
        public string FormattedRating => $"{AverageRating:F1}/5";
        public string FormattedPublishDate => PublishDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        private void GenerateSlug()
        {
            if (string.IsNullOrEmpty(Name)) return;
            
            // Simple slug generation - in real implementation, use proper Arabic slug generation
            Slug = Name.ToLower()
                      .Replace(" ", "-")
                      .Replace("أ", "ا")
                      .Replace("إ", "ا")
                      .Replace("آ", "ا");
        }

        public void Activate()
        {
            Status = ProductStatus.Active;
            UpdatedAt = DateTime.Now;
        }

        public void Deactivate()
        {
            Status = ProductStatus.Inactive;
            UpdatedAt = DateTime.Now;
        }

        public void SetOutOfStock()
        {
            Status = ProductStatus.OutOfStock;
            StockQuantity = 0;
            UpdatedAt = DateTime.Now;
        }

        public void UpdateStock(int quantity)
        {
            StockQuantity = quantity;
            if (quantity > 0 && Status == ProductStatus.OutOfStock)
                Status = ProductStatus.Active;
            UpdatedAt = DateTime.Now;
        }

        public void IncrementView()
        {
            ViewCount++;
            UpdatedAt = DateTime.Now;
        }

        public void UpdateRating(decimal newRating, int newReviewCount)
        {
            AverageRating = newRating;
            ReviewCount = newReviewCount;
            UpdatedAt = DateTime.Now;
        }

        public void IncrementSales()
        {
            SalesCount++;
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    public class ProductImage
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string AltText { get; set; } = string.Empty;
        public int SortOrder { get; set; }
        public bool IsMain { get; set; }
    }

    public class ProductVariant
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public decimal PriceAdjustment { get; set; }
        public int StockQuantity { get; set; }
        public string Sku { get; set; } = string.Empty;
    }

    public class ProductAttribute
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public bool IsVisible { get; set; } = true;
    }

    public class ProductReview
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public int Rating { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        public bool IsApproved { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    #endregion

    #region Enums

    public enum ProductStatus
    {
        Active,         // نشط
        Inactive,       // غير نشط
        Draft,          // مسودة
        OutOfStock     // نفد المخزون
    }

    public enum ProductVisibility
    {
        Visible,        // مرئي
        Hidden,         // مخفي
        Catalog,        // الكتالوج فقط
        Search          // البحث فقط
    }

    #endregion

    #region Validation

    public class OnlineProductValidator : AbstractValidator<OnlineProduct>
    {
        public OnlineProductValidator()
        {
            RuleFor(p => p.Name)
                .NotEmpty().WithMessage("اسم المنتج مطلوب")
                .MaximumLength(200).WithMessage("اسم المنتج لا يمكن أن يتجاوز 200 حرف");

            RuleFor(p => p.Sku)
                .NotEmpty().WithMessage("رمز المنتج مطلوب")
                .MaximumLength(50).WithMessage("رمز المنتج لا يمكن أن يتجاوز 50 حرف");

            RuleFor(p => p.Price)
                .GreaterThan(0).WithMessage("سعر المنتج يجب أن يكون أكبر من صفر");

            RuleFor(p => p.StockQuantity)
                .GreaterThanOrEqualTo(0).WithMessage("كمية المخزون لا يمكن أن تكون سالبة");

            RuleFor(p => p.Weight)
                .GreaterThanOrEqualTo(0).WithMessage("وزن المنتج لا يمكن أن يكون سالب");

            RuleFor(p => p.SalePrice)
                .LessThan(p => p.Price).When(p => p.SalePrice > 0)
                .WithMessage("سعر التخفيض يجب أن يكون أقل من السعر الأصلي");
        }
    }

    #endregion
}
