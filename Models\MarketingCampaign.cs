using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج حملة التسويق
    /// </summary>
    public class MarketingCampaign : INotifyPropertyChanged
    {
        private int _id;
        private string _campaignCode = string.Empty;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private CampaignType _type = CampaignType.Email;
        private CampaignStatus _status = CampaignStatus.Draft;
        private CampaignPriority _priority = CampaignPriority.Medium;
        private string _targetAudience = string.Empty;
        private string _objectives = string.Empty;
        private decimal _budget;
        private decimal _actualCost;
        private DateTime _startDate = DateTime.Now;
        private DateTime _endDate = DateTime.Now.AddDays(30);
        private DateTime? _launchDate;
        private string _channels = string.Empty;
        private string _content = string.Empty;
        private string _callToAction = string.Empty;
        private string _landingPageUrl = string.Empty;
        private string _trackingCode = string.Empty;
        private int _targetReach;
        private int _actualReach;
        private int _impressions;
        private int _clicks;
        private int _conversions;
        private decimal _conversionRate;
        private decimal _clickThroughRate;
        private decimal _costPerClick;
        private decimal _costPerConversion;
        private decimal _returnOnInvestment;
        private int _leadsGenerated;
        private int _salesGenerated;
        private decimal _revenueGenerated;
        private string _tags = string.Empty;
        private string _notes = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private string _createdBy = string.Empty;
        private int _assignedManagerId;
        private string _assignedManager = string.Empty;
        private ObservableCollection<CampaignActivity> _activities = new();
        private ObservableCollection<CampaignLead> _leads = new();
        private ObservableCollection<CampaignMetric> _metrics = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CampaignCode
        {
            get => _campaignCode;
            set
            {
                if (_campaignCode != value)
                {
                    _campaignCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public CampaignType Type
        {
            get => _type;
            set
            {
                if (_type != value)
                {
                    _type = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TypeDisplay));
                    OnPropertyChanged(nameof(TypeIcon));
                }
            }
        }

        public CampaignStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsActive));
                    OnPropertyChanged(nameof(IsCompleted));
                }
            }
        }

        public CampaignPriority Priority
        {
            get => _priority;
            set
            {
                if (_priority != value)
                {
                    _priority = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PriorityDisplay));
                    OnPropertyChanged(nameof(PriorityColor));
                }
            }
        }

        public string TargetAudience
        {
            get => _targetAudience;
            set
            {
                if (_targetAudience != value)
                {
                    _targetAudience = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Objectives
        {
            get => _objectives;
            set
            {
                if (_objectives != value)
                {
                    _objectives = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Budget
        {
            get => _budget;
            set
            {
                if (_budget != value)
                {
                    _budget = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedBudget));
                    OnPropertyChanged(nameof(BudgetUtilization));
                    OnPropertyChanged(nameof(RemainingBudget));
                }
            }
        }

        public decimal ActualCost
        {
            get => _actualCost;
            set
            {
                if (_actualCost != value)
                {
                    _actualCost = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedActualCost));
                    OnPropertyChanged(nameof(BudgetUtilization));
                    OnPropertyChanged(nameof(RemainingBudget));
                }
            }
        }

        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (_startDate != value)
                {
                    _startDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedStartDate));
                    OnPropertyChanged(nameof(Duration));
                }
            }
        }

        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                if (_endDate != value)
                {
                    _endDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedEndDate));
                    OnPropertyChanged(nameof(Duration));
                    OnPropertyChanged(nameof(DaysRemaining));
                }
            }
        }

        public DateTime? LaunchDate
        {
            get => _launchDate;
            set
            {
                if (_launchDate != value)
                {
                    _launchDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLaunchDate));
                }
            }
        }

        public string Channels
        {
            get => _channels;
            set
            {
                if (_channels != value)
                {
                    _channels = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Content
        {
            get => _content;
            set
            {
                if (_content != value)
                {
                    _content = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CallToAction
        {
            get => _callToAction;
            set
            {
                if (_callToAction != value)
                {
                    _callToAction = value;
                    OnPropertyChanged();
                }
            }
        }

        public string LandingPageUrl
        {
            get => _landingPageUrl;
            set
            {
                if (_landingPageUrl != value)
                {
                    _landingPageUrl = value;
                    OnPropertyChanged();
                }
            }
        }

        public string TrackingCode
        {
            get => _trackingCode;
            set
            {
                if (_trackingCode != value)
                {
                    _trackingCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public int TargetReach
        {
            get => _targetReach;
            set
            {
                if (_targetReach != value)
                {
                    _targetReach = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTargetReach));
                    OnPropertyChanged(nameof(ReachPercentage));
                }
            }
        }

        public int ActualReach
        {
            get => _actualReach;
            set
            {
                if (_actualReach != value)
                {
                    _actualReach = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedActualReach));
                    OnPropertyChanged(nameof(ReachPercentage));
                }
            }
        }

        public int Impressions
        {
            get => _impressions;
            set
            {
                if (_impressions != value)
                {
                    _impressions = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedImpressions));
                }
            }
        }

        public int Clicks
        {
            get => _clicks;
            set
            {
                if (_clicks != value)
                {
                    _clicks = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedClicks));
                    CalculateClickThroughRate();
                    CalculateCostPerClick();
                }
            }
        }

        public int Conversions
        {
            get => _conversions;
            set
            {
                if (_conversions != value)
                {
                    _conversions = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedConversions));
                    CalculateConversionRate();
                    CalculateCostPerConversion();
                }
            }
        }

        public decimal ConversionRate
        {
            get => _conversionRate;
            set
            {
                if (_conversionRate != value)
                {
                    _conversionRate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedConversionRate));
                }
            }
        }

        public decimal ClickThroughRate
        {
            get => _clickThroughRate;
            set
            {
                if (_clickThroughRate != value)
                {
                    _clickThroughRate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedClickThroughRate));
                }
            }
        }

        public decimal CostPerClick
        {
            get => _costPerClick;
            set
            {
                if (_costPerClick != value)
                {
                    _costPerClick = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCostPerClick));
                }
            }
        }

        public decimal CostPerConversion
        {
            get => _costPerConversion;
            set
            {
                if (_costPerConversion != value)
                {
                    _costPerConversion = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCostPerConversion));
                }
            }
        }

        public decimal ReturnOnInvestment
        {
            get => _returnOnInvestment;
            set
            {
                if (_returnOnInvestment != value)
                {
                    _returnOnInvestment = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedReturnOnInvestment));
                }
            }
        }

        public int LeadsGenerated
        {
            get => _leadsGenerated;
            set
            {
                if (_leadsGenerated != value)
                {
                    _leadsGenerated = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLeadsGenerated));
                }
            }
        }

        public int SalesGenerated
        {
            get => _salesGenerated;
            set
            {
                if (_salesGenerated != value)
                {
                    _salesGenerated = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedSalesGenerated));
                }
            }
        }

        public decimal RevenueGenerated
        {
            get => _revenueGenerated;
            set
            {
                if (_revenueGenerated != value)
                {
                    _revenueGenerated = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedRevenueGenerated));
                    CalculateReturnOnInvestment();
                }
            }
        }

        public string Tags
        {
            get => _tags;
            set
            {
                if (_tags != value)
                {
                    _tags = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public int AssignedManagerId
        {
            get => _assignedManagerId;
            set
            {
                if (_assignedManagerId != value)
                {
                    _assignedManagerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string AssignedManager
        {
            get => _assignedManager;
            set
            {
                if (_assignedManager != value)
                {
                    _assignedManager = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<CampaignActivity> Activities
        {
            get => _activities;
            set
            {
                if (_activities != value)
                {
                    _activities = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<CampaignLead> Leads
        {
            get => _leads;
            set
            {
                if (_leads != value)
                {
                    _leads = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<CampaignMetric> Metrics
        {
            get => _metrics;
            set
            {
                if (_metrics != value)
                {
                    _metrics = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public int Duration => (EndDate - StartDate).Days;

        public int DaysRemaining => Math.Max(0, (EndDate - DateTime.Now).Days);

        public decimal BudgetUtilization => Budget > 0 ? (ActualCost / Budget) * 100 : 0;

        public decimal RemainingBudget => Budget - ActualCost;

        public decimal ReachPercentage => TargetReach > 0 ? (decimal)ActualReach / TargetReach * 100 : 0;

        public bool IsActive => Status == CampaignStatus.Active;

        public bool IsCompleted => Status == CampaignStatus.Completed;

        // Display Properties
        public string TypeDisplay
        {
            get
            {
                return Type switch
                {
                    CampaignType.Email => "بريد إلكتروني",
                    CampaignType.SocialMedia => "وسائل التواصل",
                    CampaignType.PPC => "إعلانات مدفوعة",
                    CampaignType.SEO => "تحسين محركات البحث",
                    CampaignType.ContentMarketing => "تسويق المحتوى",
                    CampaignType.Event => "فعالية",
                    CampaignType.Print => "مطبوعات",
                    CampaignType.Radio => "راديو",
                    CampaignType.TV => "تلفزيون",
                    CampaignType.Outdoor => "إعلانات خارجية",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    CampaignStatus.Draft => "مسودة",
                    CampaignStatus.Planned => "مخطط",
                    CampaignStatus.Active => "نشط",
                    CampaignStatus.Paused => "متوقف",
                    CampaignStatus.Completed => "مكتمل",
                    CampaignStatus.Cancelled => "ملغي",
                    _ => "غير محدد"
                };
            }
        }

        public string PriorityDisplay
        {
            get
            {
                return Priority switch
                {
                    CampaignPriority.Low => "منخفضة",
                    CampaignPriority.Medium => "متوسطة",
                    CampaignPriority.High => "عالية",
                    CampaignPriority.Critical => "حرجة",
                    _ => "غير محدد"
                };
            }
        }

        public string TypeIcon
        {
            get
            {
                return Type switch
                {
                    CampaignType.Email => "Email",
                    CampaignType.SocialMedia => "Facebook",
                    CampaignType.PPC => "GoogleAds",
                    CampaignType.SEO => "Magnify",
                    CampaignType.ContentMarketing => "FileDocument",
                    CampaignType.Event => "Calendar",
                    CampaignType.Print => "Printer",
                    CampaignType.Radio => "Radio",
                    CampaignType.TV => "Television",
                    CampaignType.Outdoor => "Billboard",
                    _ => "Campaign"
                };
            }
        }

        // Color Properties
        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    CampaignStatus.Draft => "Gray",
                    CampaignStatus.Planned => "Blue",
                    CampaignStatus.Active => "Green",
                    CampaignStatus.Paused => "Orange",
                    CampaignStatus.Completed => "Purple",
                    CampaignStatus.Cancelled => "Red",
                    _ => "Gray"
                };
            }
        }

        public string PriorityColor
        {
            get
            {
                return Priority switch
                {
                    CampaignPriority.Low => "Green",
                    CampaignPriority.Medium => "Orange",
                    CampaignPriority.High => "Red",
                    CampaignPriority.Critical => "Purple",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedBudget => $"{Budget:C}";
        public string FormattedActualCost => $"{ActualCost:C}";
        public string FormattedRemainingBudget => $"{RemainingBudget:C}";
        public string FormattedStartDate => StartDate.ToString("dd/MM/yyyy");
        public string FormattedEndDate => EndDate.ToString("dd/MM/yyyy");
        public string FormattedLaunchDate => LaunchDate?.ToString("dd/MM/yyyy") ?? "لم يتم الإطلاق";
        public string FormattedTargetReach => $"{TargetReach:N0}";
        public string FormattedActualReach => $"{ActualReach:N0}";
        public string FormattedImpressions => $"{Impressions:N0}";
        public string FormattedClicks => $"{Clicks:N0}";
        public string FormattedConversions => $"{Conversions:N0}";
        public string FormattedConversionRate => $"{ConversionRate:F2}%";
        public string FormattedClickThroughRate => $"{ClickThroughRate:F2}%";
        public string FormattedCostPerClick => $"{CostPerClick:C}";
        public string FormattedCostPerConversion => $"{CostPerConversion:C}";
        public string FormattedReturnOnInvestment => $"{ReturnOnInvestment:F2}%";
        public string FormattedLeadsGenerated => $"{LeadsGenerated:N0}";
        public string FormattedSalesGenerated => $"{SalesGenerated:N0}";
        public string FormattedRevenueGenerated => $"{RevenueGenerated:C}";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        private void CalculateConversionRate()
        {
            if (Clicks > 0)
                ConversionRate = (decimal)Conversions / Clicks * 100;
            else
                ConversionRate = 0;
        }

        private void CalculateClickThroughRate()
        {
            if (Impressions > 0)
                ClickThroughRate = (decimal)Clicks / Impressions * 100;
            else
                ClickThroughRate = 0;
        }

        private void CalculateCostPerClick()
        {
            if (Clicks > 0)
                CostPerClick = ActualCost / Clicks;
            else
                CostPerClick = 0;
        }

        private void CalculateCostPerConversion()
        {
            if (Conversions > 0)
                CostPerConversion = ActualCost / Conversions;
            else
                CostPerConversion = 0;
        }

        private void CalculateReturnOnInvestment()
        {
            if (ActualCost > 0)
                ReturnOnInvestment = ((RevenueGenerated - ActualCost) / ActualCost) * 100;
            else
                ReturnOnInvestment = 0;
        }

        public void Launch()
        {
            Status = CampaignStatus.Active;
            LaunchDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        public void Pause()
        {
            Status = CampaignStatus.Paused;
            UpdatedAt = DateTime.Now;
        }

        public void Complete()
        {
            Status = CampaignStatus.Completed;
            UpdatedAt = DateTime.Now;
        }

        public void Cancel()
        {
            Status = CampaignStatus.Cancelled;
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// نشاط الحملة التسويقية
    /// </summary>
    public class CampaignActivity
    {
        public int Id { get; set; }
        public int CampaignId { get; set; }
        public string ActivityType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime ActivityDate { get; set; } = DateTime.Now;
        public string PerformedBy { get; set; } = string.Empty;

        public string FormattedActivityDate => ActivityDate.ToString("dd/MM/yyyy HH:mm");
    }

    /// <summary>
    /// عميل محتمل من الحملة
    /// </summary>
    public class CampaignLead
    {
        public int Id { get; set; }
        public int CampaignId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
        public string Status { get; set; } = "New";

        public string FormattedGeneratedAt => GeneratedAt.ToString("dd/MM/yyyy HH:mm");
    }

    /// <summary>
    /// مقياس الحملة
    /// </summary>
    public class CampaignMetric
    {
        public int Id { get; set; }
        public int CampaignId { get; set; }
        public DateTime Date { get; set; } = DateTime.Now;
        public string MetricName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Unit { get; set; } = string.Empty;

        public string FormattedDate => Date.ToString("dd/MM/yyyy");
        public string FormattedValue => $"{Value:N2} {Unit}";
    }

    #endregion

    #region Enums

    public enum CampaignType
    {
        Email,              // بريد إلكتروني
        SocialMedia,        // وسائل التواصل
        PPC,                // إعلانات مدفوعة
        SEO,                // تحسين محركات البحث
        ContentMarketing,   // تسويق المحتوى
        Event,              // فعالية
        Print,              // مطبوعات
        Radio,              // راديو
        TV,                 // تلفزيون
        Outdoor             // إعلانات خارجية
    }

    public enum CampaignStatus
    {
        Draft,              // مسودة
        Planned,            // مخطط
        Active,             // نشط
        Paused,             // متوقف
        Completed,          // مكتمل
        Cancelled           // ملغي
    }

    public enum CampaignPriority
    {
        Low,                // منخفضة
        Medium,             // متوسطة
        High,               // عالية
        Critical            // حرجة
    }

    #endregion

    #region Validation

    public class MarketingCampaignValidator : AbstractValidator<MarketingCampaign>
    {
        public MarketingCampaignValidator()
        {
            RuleFor(c => c.Name)
                .NotEmpty().WithMessage("اسم الحملة مطلوب")
                .MaximumLength(200).WithMessage("اسم الحملة لا يمكن أن يتجاوز 200 حرف");

            RuleFor(c => c.Budget)
                .GreaterThan(0).WithMessage("الميزانية يجب أن تكون أكبر من صفر");

            RuleFor(c => c.StartDate)
                .LessThan(c => c.EndDate).WithMessage("تاريخ البداية يجب أن يكون قبل تاريخ النهاية");

            RuleFor(c => c.EndDate)
                .GreaterThan(c => c.StartDate).WithMessage("تاريخ النهاية يجب أن يكون بعد تاريخ البداية");

            RuleFor(c => c.TargetReach)
                .GreaterThan(0).WithMessage("الوصول المستهدف يجب أن يكون أكبر من صفر");
        }
    }

    #endregion
}
