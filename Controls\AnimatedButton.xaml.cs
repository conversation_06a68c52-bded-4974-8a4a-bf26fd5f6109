using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using MaterialDesignThemes.Wpf;

namespace SalesManagementSystem.Controls
{
    /// <summary>
    /// زر متحرك مع تأثيرات بصرية متقدمة وحالات مختلفة
    /// </summary>
    public partial class AnimatedButton : UserControl
    {
        #region Enums

        public enum ButtonState
        {
            Normal,
            Loading,
            Success,
            Error
        }

        #endregion

        #region Dependency Properties

        public static readonly DependencyProperty ButtonTextProperty =
            DependencyProperty.Register(nameof(ButtonText), typeof(string), typeof(AnimatedButton));

        public static readonly DependencyProperty LoadingTextProperty =
            DependencyProperty.Register(nameof(LoadingText), typeof(string), typeof(AnimatedButton),
                new PropertyMetadata("جاري التحميل..."));

        public static readonly DependencyProperty SuccessTextProperty =
            DependencyProperty.Register(nameof(SuccessText), typeof(string), typeof(AnimatedButton),
                new PropertyMetadata("تم بنجاح"));

        public static readonly DependencyProperty ErrorTextProperty =
            DependencyProperty.Register(nameof(ErrorText), typeof(string), typeof(AnimatedButton),
                new PropertyMetadata("حدث خطأ"));

        public static readonly DependencyProperty IconKindProperty =
            DependencyProperty.Register(nameof(IconKind), typeof(PackIconKind?), typeof(AnimatedButton));

        public static readonly DependencyProperty IconSizeProperty =
            DependencyProperty.Register(nameof(IconSize), typeof(double), typeof(AnimatedButton),
                new PropertyMetadata(20.0));

        public static readonly DependencyProperty IconMarginProperty =
            DependencyProperty.Register(nameof(IconMargin), typeof(Thickness), typeof(AnimatedButton),
                new PropertyMetadata(new Thickness(0, 0, 8, 0)));

        public static readonly DependencyProperty ButtonBackgroundProperty =
            DependencyProperty.Register(nameof(ButtonBackground), typeof(Brush), typeof(AnimatedButton),
                new PropertyMetadata(Application.Current.Resources["PrimaryHueMidBrush"]));

        public static readonly DependencyProperty ButtonForegroundProperty =
            DependencyProperty.Register(nameof(ButtonForeground), typeof(Brush), typeof(AnimatedButton),
                new PropertyMetadata(Brushes.White));

        public static readonly DependencyProperty RippleColorProperty =
            DependencyProperty.Register(nameof(RippleColor), typeof(Brush), typeof(AnimatedButton),
                new PropertyMetadata(new SolidColorBrush(Colors.White) { Opacity = 0.3 }));

        public static readonly DependencyProperty CornerRadiusProperty =
            DependencyProperty.Register(nameof(CornerRadius), typeof(CornerRadius), typeof(AnimatedButton),
                new PropertyMetadata(new CornerRadius(8)));

        public static readonly DependencyProperty ButtonPaddingProperty =
            DependencyProperty.Register(nameof(ButtonPadding), typeof(Thickness), typeof(AnimatedButton),
                new PropertyMetadata(new Thickness(16, 12, 16, 12)));

        public static readonly DependencyProperty ContentOrientationProperty =
            DependencyProperty.Register(nameof(ContentOrientation), typeof(Orientation), typeof(AnimatedButton),
                new PropertyMetadata(Orientation.Horizontal));

        public static readonly DependencyProperty CurrentStateProperty =
            DependencyProperty.Register(nameof(CurrentState), typeof(ButtonState), typeof(AnimatedButton),
                new PropertyMetadata(ButtonState.Normal, OnStateChanged));

        public static readonly DependencyProperty CommandProperty =
            DependencyProperty.Register(nameof(Command), typeof(ICommand), typeof(AnimatedButton));

        public static readonly DependencyProperty CommandParameterProperty =
            DependencyProperty.Register(nameof(CommandParameter), typeof(object), typeof(AnimatedButton));

        public new static readonly DependencyProperty FontSizeProperty =
            DependencyProperty.Register(nameof(FontSize), typeof(double), typeof(AnimatedButton),
                new PropertyMetadata(16.0));

        public new static readonly DependencyProperty FontWeightProperty =
            DependencyProperty.Register(nameof(FontWeight), typeof(FontWeight), typeof(AnimatedButton),
                new PropertyMetadata(FontWeights.Medium));

        public new static readonly DependencyProperty MinWidthProperty =
            DependencyProperty.Register(nameof(MinWidth), typeof(double), typeof(AnimatedButton),
                new PropertyMetadata(120.0));

        public new static readonly DependencyProperty MinHeightProperty =
            DependencyProperty.Register(nameof(MinHeight), typeof(double), typeof(AnimatedButton),
                new PropertyMetadata(40.0));

        #endregion

        #region Properties

        public string ButtonText
        {
            get => (string)GetValue(ButtonTextProperty);
            set => SetValue(ButtonTextProperty, value);
        }

        public string LoadingText
        {
            get => (string)GetValue(LoadingTextProperty);
            set => SetValue(LoadingTextProperty, value);
        }

        public string SuccessText
        {
            get => (string)GetValue(SuccessTextProperty);
            set => SetValue(SuccessTextProperty, value);
        }

        public string ErrorText
        {
            get => (string)GetValue(ErrorTextProperty);
            set => SetValue(ErrorTextProperty, value);
        }

        public PackIconKind? IconKind
        {
            get => (PackIconKind?)GetValue(IconKindProperty);
            set => SetValue(IconKindProperty, value);
        }

        public double IconSize
        {
            get => (double)GetValue(IconSizeProperty);
            set => SetValue(IconSizeProperty, value);
        }

        public Thickness IconMargin
        {
            get => (Thickness)GetValue(IconMarginProperty);
            set => SetValue(IconMarginProperty, value);
        }

        public Brush ButtonBackground
        {
            get => (Brush)GetValue(ButtonBackgroundProperty);
            set => SetValue(ButtonBackgroundProperty, value);
        }

        public Brush ButtonForeground
        {
            get => (Brush)GetValue(ButtonForegroundProperty);
            set => SetValue(ButtonForegroundProperty, value);
        }

        public Brush RippleColor
        {
            get => (Brush)GetValue(RippleColorProperty);
            set => SetValue(RippleColorProperty, value);
        }

        public CornerRadius CornerRadius
        {
            get => (CornerRadius)GetValue(CornerRadiusProperty);
            set => SetValue(CornerRadiusProperty, value);
        }

        public Thickness ButtonPadding
        {
            get => (Thickness)GetValue(ButtonPaddingProperty);
            set => SetValue(ButtonPaddingProperty, value);
        }

        public Orientation ContentOrientation
        {
            get => (Orientation)GetValue(ContentOrientationProperty);
            set => SetValue(ContentOrientationProperty, value);
        }

        public ButtonState CurrentState
        {
            get => (ButtonState)GetValue(CurrentStateProperty);
            set => SetValue(CurrentStateProperty, value);
        }

        public ICommand? Command
        {
            get => (ICommand?)GetValue(CommandProperty);
            set => SetValue(CommandProperty, value);
        }

        public object CommandParameter
        {
            get => GetValue(CommandParameterProperty);
            set => SetValue(CommandParameterProperty, value);
        }

        public new double FontSize
        {
            get => (double)GetValue(FontSizeProperty);
            set => SetValue(FontSizeProperty, value);
        }

        public new FontWeight FontWeight
        {
            get => (FontWeight)GetValue(FontWeightProperty);
            set => SetValue(FontWeightProperty, value);
        }

        public new double MinWidth
        {
            get => (double)GetValue(MinWidthProperty);
            set => SetValue(MinWidthProperty, value);
        }

        public new double MinHeight
        {
            get => (double)GetValue(MinHeightProperty);
            set => SetValue(MinHeightProperty, value);
        }

        #endregion

        #region Events

        public event EventHandler<RoutedEventArgs>? Click;

        #endregion

        #region Constructor

        public AnimatedButton()
        {
            InitializeComponent();
            DataContext = this;
        }

        #endregion

        #region Event Handlers

        protected override void OnMouseEnter(MouseEventArgs e)
        {
            base.OnMouseEnter(e);
            if (CurrentState == ButtonState.Normal)
            {
                var storyboard = FindResource("HoverInAnimation") as Storyboard;
                storyboard?.Begin(ButtonBorder);
            }
        }

        protected override void OnMouseLeave(MouseEventArgs e)
        {
            base.OnMouseLeave(e);
            if (CurrentState == ButtonState.Normal)
            {
                var storyboard = FindResource("HoverOutAnimation") as Storyboard;
                storyboard?.Begin(ButtonBorder);
            }
        }

        protected override void OnPreviewMouseLeftButtonDown(MouseButtonEventArgs e)
        {
            base.OnPreviewMouseLeftButtonDown(e);
            if (CurrentState == ButtonState.Normal)
            {
                var storyboard = FindResource("PressAnimation") as Storyboard;
                storyboard?.Begin(ButtonBorder);

                CreateRippleEffect(e.GetPosition(RippleContainer));
            }
        }

        protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonUp(e);
            if (CurrentState == ButtonState.Normal)
            {
                var storyboard = FindResource("ReleaseAnimation") as Storyboard;
                storyboard?.Begin(ButtonBorder);

                // تنفيذ الأمر أو الحدث
                if (Command?.CanExecute(CommandParameter) == true)
                {
                    Command.Execute(CommandParameter);
                }

                Click?.Invoke(this, e);
            }
        }

        private static void OnStateChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AnimatedButton button)
            {
                button.UpdateVisualState((ButtonState)e.NewValue);
            }
        }

        #endregion

        #region Public Methods

        public async Task SetLoadingAsync(bool isLoading)
        {
            await Task.Delay(1); // إضافة await لتجنب التحذير
            if (isLoading)
            {
                CurrentState = ButtonState.Loading;
            }
            else
            {
                CurrentState = ButtonState.Normal;
            }
        }

        public async Task ShowSuccessAsync(int durationMs = 2000)
        {
            CurrentState = ButtonState.Success;
            await Task.Delay(durationMs);
            CurrentState = ButtonState.Normal;
        }

        public async Task ShowErrorAsync(int durationMs = 3000)
        {
            CurrentState = ButtonState.Error;
            await Task.Delay(durationMs);
            CurrentState = ButtonState.Normal;
        }

        #endregion

        #region Private Methods

        private void UpdateVisualState(ButtonState state)
        {
            // إخفاء جميع المحتويات
            NormalContent.Visibility = Visibility.Collapsed;
            LoadingContent.Visibility = Visibility.Collapsed;
            SuccessContent.Visibility = Visibility.Collapsed;
            ErrorContent.Visibility = Visibility.Collapsed;

            // إيقاف الرسوم المتحركة
            var loadingStoryboard = FindResource("LoadingAnimation") as Storyboard;
            loadingStoryboard?.Stop();

            switch (state)
            {
                case ButtonState.Normal:
                    NormalContent.Visibility = Visibility.Visible;
                    IsEnabled = true;
                    break;

                case ButtonState.Loading:
                    LoadingContent.Visibility = Visibility.Visible;
                    IsEnabled = false;
                    loadingStoryboard?.Begin(LoadingContent.Children[0] as FrameworkElement);
                    break;

                case ButtonState.Success:
                    SuccessContent.Visibility = Visibility.Visible;
                    IsEnabled = false;
                    var successStoryboard = FindResource("SuccessAnimation") as Storyboard;
                    successStoryboard?.Begin(SuccessContent.Children[0] as FrameworkElement);
                    break;

                case ButtonState.Error:
                    ErrorContent.Visibility = Visibility.Visible;
                    IsEnabled = false;
                    break;
            }
        }

        private void CreateRippleEffect(Point position)
        {
            try
            {
                var ripple = RippleEllipse;
                var container = RippleContainer;

                if (ripple == null || container == null) return;

                var maxRadius = Math.Max(container.ActualWidth, container.ActualHeight);

                ripple.Width = ripple.Height = maxRadius * 2;
                Canvas.SetLeft(ripple, position.X - maxRadius);
                Canvas.SetTop(ripple, position.Y - maxRadius);

                var storyboard = new Storyboard();

                var scaleXAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = TimeSpan.FromMilliseconds(600),
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                };

                var scaleYAnimation = scaleXAnimation.Clone();
                var opacityAnimation = new DoubleAnimation
                {
                    From = 0.3,
                    To = 0,
                    Duration = TimeSpan.FromMilliseconds(600)
                };

                Storyboard.SetTarget(scaleXAnimation, ripple);
                Storyboard.SetTargetProperty(scaleXAnimation,
                    new PropertyPath("(UIElement.RenderTransform).(ScaleTransform.ScaleX)"));

                Storyboard.SetTarget(scaleYAnimation, ripple);
                Storyboard.SetTargetProperty(scaleYAnimation,
                    new PropertyPath("(UIElement.RenderTransform).(ScaleTransform.ScaleY)"));

                Storyboard.SetTarget(opacityAnimation, ripple);
                Storyboard.SetTargetProperty(opacityAnimation, new PropertyPath("Opacity"));

                storyboard.Children.Add(scaleXAnimation);
                storyboard.Children.Add(scaleYAnimation);
                storyboard.Children.Add(opacityAnimation);

                storyboard.Begin();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تأثير الريبل: {ex.Message}");
            }
        }

        #endregion
    }
}
