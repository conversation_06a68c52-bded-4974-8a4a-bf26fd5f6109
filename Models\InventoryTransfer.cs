using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج نقل المخزون بين المخازن
    /// </summary>
    public class InventoryTransfer : INotifyPropertyChanged
    {
        private int _id;
        private string _transferNumber = string.Empty;
        private TransferType _transferType = TransferType.WarehouseToWarehouse;
        private TransferStatus _status = TransferStatus.Pending;
        private int _fromWarehouseId;
        private int _toWarehouseId;
        private int? _fromLocationId;
        private int? _toLocationId;
        private DateTime _requestDate = DateTime.Now;
        private DateTime? _scheduledDate;
        private DateTime? _shippedDate;
        private DateTime? _receivedDate;
        private string _reason = string.Empty;
        private string _notes = string.Empty;
        private string _requestedBy = string.Empty;
        private string _approvedBy = string.Empty;
        private string _shippedBy = string.Empty;
        private string _receivedBy = string.Empty;
        private decimal _totalValue;
        private string _trackingNumber = string.Empty;
        private string _carrierName = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private ObservableCollection<InventoryTransferItem> _items = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string TransferNumber
        {
            get => _transferNumber;
            set
            {
                if (_transferNumber != value)
                {
                    _transferNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public TransferType TransferType
        {
            get => _transferType;
            set
            {
                if (_transferType != value)
                {
                    _transferType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TransferTypeDisplay));
                    OnPropertyChanged(nameof(TransferTypeIcon));
                }
            }
        }

        public TransferStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(CanEdit));
                    OnPropertyChanged(nameof(CanApprove));
                    OnPropertyChanged(nameof(CanShip));
                    OnPropertyChanged(nameof(CanReceive));
                }
            }
        }

        public int FromWarehouseId
        {
            get => _fromWarehouseId;
            set
            {
                if (_fromWarehouseId != value)
                {
                    _fromWarehouseId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ToWarehouseId
        {
            get => _toWarehouseId;
            set
            {
                if (_toWarehouseId != value)
                {
                    _toWarehouseId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? FromLocationId
        {
            get => _fromLocationId;
            set
            {
                if (_fromLocationId != value)
                {
                    _fromLocationId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? ToLocationId
        {
            get => _toLocationId;
            set
            {
                if (_toLocationId != value)
                {
                    _toLocationId = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime RequestDate
        {
            get => _requestDate;
            set
            {
                if (_requestDate != value)
                {
                    _requestDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedRequestDate));
                }
            }
        }

        public DateTime? ScheduledDate
        {
            get => _scheduledDate;
            set
            {
                if (_scheduledDate != value)
                {
                    _scheduledDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedScheduledDate));
                }
            }
        }

        public DateTime? ShippedDate
        {
            get => _shippedDate;
            set
            {
                if (_shippedDate != value)
                {
                    _shippedDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedShippedDate));
                }
            }
        }

        public DateTime? ReceivedDate
        {
            get => _receivedDate;
            set
            {
                if (_receivedDate != value)
                {
                    _receivedDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedReceivedDate));
                    OnPropertyChanged(nameof(TransferDuration));
                }
            }
        }

        public string Reason
        {
            get => _reason;
            set
            {
                if (_reason != value)
                {
                    _reason = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string RequestedBy
        {
            get => _requestedBy;
            set
            {
                if (_requestedBy != value)
                {
                    _requestedBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ApprovedBy
        {
            get => _approvedBy;
            set
            {
                if (_approvedBy != value)
                {
                    _approvedBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ShippedBy
        {
            get => _shippedBy;
            set
            {
                if (_shippedBy != value)
                {
                    _shippedBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ReceivedBy
        {
            get => _receivedBy;
            set
            {
                if (_receivedBy != value)
                {
                    _receivedBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal TotalValue
        {
            get => _totalValue;
            set
            {
                if (_totalValue != value)
                {
                    _totalValue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalValue));
                }
            }
        }

        public string TrackingNumber
        {
            get => _trackingNumber;
            set
            {
                if (_trackingNumber != value)
                {
                    _trackingNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CarrierName
        {
            get => _carrierName;
            set
            {
                if (_carrierName != value)
                {
                    _carrierName = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<InventoryTransferItem> Items
        {
            get => _items;
            set
            {
                if (_items != value)
                {
                    _items = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TotalItems));
                    OnPropertyChanged(nameof(TotalQuantity));
                    CalculateTotalValue();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public int TotalItems => Items?.Count ?? 0;
        public decimal TotalQuantity => Items?.Sum(i => i.Quantity) ?? 0;

        public bool CanEdit => Status == TransferStatus.Pending;
        public bool CanApprove => Status == TransferStatus.Pending;
        public bool CanShip => Status == TransferStatus.Approved;
        public bool CanReceive => Status == TransferStatus.Shipped;

        public string TransferDuration
        {
            get
            {
                if (ShippedDate.HasValue && ReceivedDate.HasValue)
                {
                    var duration = ReceivedDate.Value - ShippedDate.Value;
                    if (duration.TotalDays >= 1)
                        return $"{duration.Days} يوم";
                    else if (duration.TotalHours >= 1)
                        return $"{(int)duration.TotalHours} ساعة";
                    else
                        return $"{(int)duration.TotalMinutes} دقيقة";
                }
                return "غير محدد";
            }
        }

        public string FormattedTotalValue => TotalValue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedRequestDate => RequestDate.ToString("dd/MM/yyyy");
        public string FormattedScheduledDate => ScheduledDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedShippedDate => ShippedDate?.ToString("dd/MM/yyyy HH:mm") ?? "غير محدد";
        public string FormattedReceivedDate => ReceivedDate?.ToString("dd/MM/yyyy HH:mm") ?? "غير محدد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");

        public string TransferTypeDisplay
        {
            get
            {
                return TransferType switch
                {
                    TransferType.WarehouseToWarehouse => "مخزن إلى مخزن",
                    TransferType.LocationToLocation => "موقع إلى موقع",
                    TransferType.Adjustment => "تسوية مخزون",
                    TransferType.Return => "إرجاع",
                    TransferType.Damage => "تالف",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    TransferStatus.Pending => "معلق",
                    TransferStatus.Approved => "موافق عليه",
                    TransferStatus.Shipped => "تم الشحن",
                    TransferStatus.Received => "تم الاستلام",
                    TransferStatus.Cancelled => "ملغي",
                    TransferStatus.Rejected => "مرفوض",
                    _ => "غير محدد"
                };
            }
        }

        public string TransferTypeIcon
        {
            get
            {
                return TransferType switch
                {
                    TransferType.WarehouseToWarehouse => "TruckDelivery",
                    TransferType.LocationToLocation => "SwapHorizontal",
                    TransferType.Adjustment => "Tune",
                    TransferType.Return => "Undo",
                    TransferType.Damage => "AlertCircle",
                    _ => "Transfer"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    TransferStatus.Pending => "Orange",
                    TransferStatus.Approved => "Blue",
                    TransferStatus.Shipped => "Purple",
                    TransferStatus.Received => "Green",
                    TransferStatus.Cancelled => "Gray",
                    TransferStatus.Rejected => "Red",
                    _ => "Gray"
                };
            }
        }

        #endregion

        #region Methods

        public void AddItem(InventoryTransferItem item)
        {
            item.TransferId = Id;
            Items.Add(item);
            CalculateTotalValue();
        }

        public void RemoveItem(InventoryTransferItem item)
        {
            Items.Remove(item);
            CalculateTotalValue();
        }

        public void CalculateTotalValue()
        {
            TotalValue = Items.Sum(i => i.TotalValue);
        }

        public void Approve(string approvedBy)
        {
            Status = TransferStatus.Approved;
            ApprovedBy = approvedBy;
            UpdatedAt = DateTime.Now;
        }

        public void Ship(string shippedBy, string trackingNumber = "", string carrierName = "")
        {
            Status = TransferStatus.Shipped;
            ShippedBy = shippedBy;
            ShippedDate = DateTime.Now;
            TrackingNumber = trackingNumber;
            CarrierName = carrierName;
            UpdatedAt = DateTime.Now;
        }

        public void Receive(string receivedBy)
        {
            Status = TransferStatus.Received;
            ReceivedBy = receivedBy;
            ReceivedDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        public void Cancel(string reason = "")
        {
            Status = TransferStatus.Cancelled;
            if (!string.IsNullOrEmpty(reason))
            {
                Notes += $"\nسبب الإلغاء: {reason}";
            }
            UpdatedAt = DateTime.Now;
        }

        public void Reject(string reason = "")
        {
            Status = TransferStatus.Rejected;
            if (!string.IsNullOrEmpty(reason))
            {
                Notes += $"\nسبب الرفض: {reason}";
            }
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// عنصر نقل المخزون
    /// </summary>
    public class InventoryTransferItem : INotifyPropertyChanged
    {
        private int _id;
        private int _transferId;
        private int _productId;
        private string _productName = string.Empty;
        private string _productCode = string.Empty;
        private decimal _quantity;
        private decimal _unitCost;
        private decimal _totalValue;
        private string _notes = string.Empty;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int TransferId
        {
            get => _transferId;
            set
            {
                if (_transferId != value)
                {
                    _transferId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ProductId
        {
            get => _productId;
            set
            {
                if (_productId != value)
                {
                    _productId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductName
        {
            get => _productName;
            set
            {
                if (_productName != value)
                {
                    _productName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductCode
        {
            get => _productCode;
            set
            {
                if (_productCode != value)
                {
                    _productCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                    CalculateTotalValue();
                }
            }
        }

        public decimal UnitCost
        {
            get => _unitCost;
            set
            {
                if (_unitCost != value)
                {
                    _unitCost = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedUnitCost));
                    CalculateTotalValue();
                }
            }
        }

        public decimal TotalValue
        {
            get => _totalValue;
            set
            {
                if (_totalValue != value)
                {
                    _totalValue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalValue));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string FormattedUnitCost => UnitCost.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalValue => TotalValue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));

        private void CalculateTotalValue()
        {
            TotalValue = Quantity * UnitCost;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    #region Enums

    public enum TransferType
    {
        WarehouseToWarehouse,   // مخزن إلى مخزن
        LocationToLocation,     // موقع إلى موقع
        Adjustment,             // تسوية مخزون
        Return,                 // إرجاع
        Damage                  // تالف
    }

    public enum TransferStatus
    {
        Pending,                // معلق
        Approved,               // موافق عليه
        Shipped,                // تم الشحن
        Received,               // تم الاستلام
        Cancelled,              // ملغي
        Rejected                // مرفوض
    }

    #endregion

    #region Validation

    public class InventoryTransferValidator : AbstractValidator<InventoryTransfer>
    {
        public InventoryTransferValidator()
        {
            RuleFor(t => t.TransferNumber)
                .NotEmpty().WithMessage("رقم النقل مطلوب")
                .MaximumLength(50).WithMessage("رقم النقل لا يمكن أن يتجاوز 50 حرف");

            RuleFor(t => t.FromWarehouseId)
                .GreaterThan(0).WithMessage("المخزن المصدر مطلوب");

            RuleFor(t => t.ToWarehouseId)
                .GreaterThan(0).WithMessage("المخزن الوجهة مطلوب")
                .NotEqual(t => t.FromWarehouseId).WithMessage("المخزن الوجهة يجب أن يكون مختلف عن المخزن المصدر");

            RuleFor(t => t.Reason)
                .NotEmpty().WithMessage("سبب النقل مطلوب")
                .MaximumLength(500).WithMessage("سبب النقل لا يمكن أن يتجاوز 500 حرف");

            RuleFor(t => t.Items)
                .NotEmpty().WithMessage("يجب إضافة عنصر واحد على الأقل للنقل");
        }
    }

    public class InventoryTransferItemValidator : AbstractValidator<InventoryTransferItem>
    {
        public InventoryTransferItemValidator()
        {
            RuleFor(i => i.ProductId)
                .GreaterThan(0).WithMessage("المنتج مطلوب");

            RuleFor(i => i.Quantity)
                .GreaterThan(0).WithMessage("الكمية يجب أن تكون أكبر من صفر");

            RuleFor(i => i.UnitCost)
                .GreaterThanOrEqualTo(0).WithMessage("تكلفة الوحدة لا يمكن أن تكون سالبة");
        }
    }

    #endregion
}
