<UserControl x:Class="SalesManagementSystem.Controls.NotificationCenter"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
        <Style x:Key="NotificationItemStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Border Background="{DynamicResource MaterialDesignPaper}"
            CornerRadius="8"
            Effect="{DynamicResource MaterialDesignShadowDepth2}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0"
                   Background="{DynamicResource PrimaryHueMidBrush}"
                   CornerRadius="8,8,0,0"
                   Padding="16,12">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Bell"
                                               Width="24" Height="24"
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="مركز الإشعارات"
                                  Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                                  Foreground="White"
                                  VerticalAlignment="Center"
                                  Margin="8,0,0,0"/>
                        <Border Background="White"
                               CornerRadius="10"
                               Padding="6,2"
                               Margin="8,0,0,0"
                               Visibility="{Binding HasUnreadNotifications, Converter={StaticResource BoolToVisConverter}}">
                            <TextBlock Text="{Binding UnreadCount}"
                                      FontSize="12"
                                      FontWeight="Bold"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </Border>
                    </StackPanel>

                    <Button Grid.Column="1"
                           Command="{Binding MarkAllAsReadCommand}"
                           Style="{DynamicResource MaterialDesignIconButton}"
                           ToolTip="تحديد الكل كمقروء"
                           Foreground="White"
                           Margin="0,0,8,0">
                        <materialDesign:PackIcon Kind="CheckAll"/>
                    </Button>

                    <Button Grid.Column="2"
                           Command="{Binding RefreshCommand}"
                           Style="{DynamicResource MaterialDesignIconButton}"
                           ToolTip="تحديث"
                           Foreground="White">
                        <materialDesign:PackIcon Kind="Refresh"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Filter Tabs -->
            <TabControl Grid.Row="1"
                       Style="{DynamicResource MaterialDesignTabControl}"
                       materialDesign:ColorZoneAssist.Mode="PrimaryMid">
                
                <!-- All Notifications -->
                <TabItem Header="الكل">
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                 HorizontalScrollBarVisibility="Disabled">
                        <ItemsControl ItemsSource="{Binding AllNotifications}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource NotificationItemStyle}"
                                           MouseLeftButtonUp="NotificationItem_Click"
                                           Tag="{Binding}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Icon -->
                                            <materialDesign:PackIcon Grid.Column="0"
                                                                   Kind="{Binding IconKind}"
                                                                   Width="24" Height="24"
                                                                   Foreground="{Binding ColorBrush}"
                                                                   VerticalAlignment="Top"
                                                                   Margin="0,0,12,0"/>

                                            <!-- Content -->
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Title}"
                                                          FontWeight="Medium"
                                                          TextWrapping="Wrap"
                                                          Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding Message}"
                                                          Style="{DynamicResource MaterialDesignBody2TextBlock}"
                                                          TextWrapping="Wrap"
                                                          Opacity="0.7"
                                                          Margin="0,0,0,4"/>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding TimeAgo}"
                                                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                                              Opacity="0.6"/>
                                                    <Border Background="{Binding PriorityColor}"
                                                           CornerRadius="8"
                                                           Padding="4,2"
                                                           Margin="8,0,0,0">
                                                        <TextBlock Text="{Binding PriorityDisplayName}"
                                                                  FontSize="10"
                                                                  Foreground="White"/>
                                                    </Border>
                                                </StackPanel>
                                            </StackPanel>

                                            <!-- Unread Indicator -->
                                            <Ellipse Grid.Column="2"
                                                    Width="8" Height="8"
                                                    Fill="{DynamicResource PrimaryHueMidBrush}"
                                                    VerticalAlignment="Top"
                                                    Margin="8,4,8,0"
                                                    Visibility="{Binding IsRead, Converter={StaticResource BoolToVisConverter}}"/>

                                            <!-- Actions -->
                                            <StackPanel Grid.Column="3" Orientation="Horizontal">
                                                <Button Command="{Binding DataContext.SnoozeNotificationCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       Style="{DynamicResource MaterialDesignIconButton}"
                                                       ToolTip="تأجيل"
                                                       Width="24" Height="24">
                                                    <materialDesign:PackIcon Kind="ClockOutline" Width="16" Height="16"/>
                                                </Button>
                                                <Button Command="{Binding DataContext.DeleteNotificationCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       Style="{DynamicResource MaterialDesignIconButton}"
                                                       ToolTip="حذف"
                                                       Width="24" Height="24">
                                                    <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </TabItem>

                <!-- Unread Notifications -->
                <TabItem Header="غير مقروءة">
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                 HorizontalScrollBarVisibility="Disabled">
                        <ItemsControl ItemsSource="{Binding UnreadNotifications}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource NotificationItemStyle}"
                                           MouseLeftButtonUp="NotificationItem_Click"
                                           Tag="{Binding}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Icon -->
                                            <materialDesign:PackIcon Grid.Column="0"
                                                                   Kind="{Binding IconKind}"
                                                                   Width="24" Height="24"
                                                                   Foreground="{Binding ColorBrush}"
                                                                   VerticalAlignment="Top"
                                                                   Margin="0,0,12,0"/>

                                            <!-- Content -->
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Title}"
                                                          FontWeight="Medium"
                                                          TextWrapping="Wrap"
                                                          Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding Message}"
                                                          Style="{DynamicResource MaterialDesignBody2TextBlock}"
                                                          TextWrapping="Wrap"
                                                          Opacity="0.7"
                                                          Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding TimeAgo}"
                                                          Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                                          Opacity="0.6"/>
                                            </StackPanel>

                                            <!-- Actions -->
                                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                <Button Command="{Binding DataContext.MarkAsReadCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       Style="{DynamicResource MaterialDesignIconButton}"
                                                       ToolTip="تحديد كمقروء"
                                                       Width="24" Height="24">
                                                    <materialDesign:PackIcon Kind="Check" Width="16" Height="16"/>
                                                </Button>
                                                <Button Command="{Binding DataContext.DeleteNotificationCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       Style="{DynamicResource MaterialDesignIconButton}"
                                                       ToolTip="حذف"
                                                       Width="24" Height="24">
                                                    <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </TabItem>

                <!-- Important Notifications -->
                <TabItem Header="مهمة">
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                 HorizontalScrollBarVisibility="Disabled">
                        <ItemsControl ItemsSource="{Binding ImportantNotifications}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource NotificationItemStyle}"
                                           MouseLeftButtonUp="NotificationItem_Click"
                                           Tag="{Binding}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Icon -->
                                            <materialDesign:PackIcon Grid.Column="0"
                                                                   Kind="{Binding IconKind}"
                                                                   Width="24" Height="24"
                                                                   Foreground="{Binding ColorBrush}"
                                                                   VerticalAlignment="Top"
                                                                   Margin="0,0,12,0"/>

                                            <!-- Content -->
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Title}"
                                                          FontWeight="Medium"
                                                          TextWrapping="Wrap"
                                                          Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding Message}"
                                                          Style="{DynamicResource MaterialDesignBody2TextBlock}"
                                                          TextWrapping="Wrap"
                                                          Opacity="0.7"
                                                          Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding TimeAgo}"
                                                          Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                                          Opacity="0.6"/>
                                            </StackPanel>

                                            <!-- Priority Badge -->
                                            <Border Grid.Column="2"
                                                   Background="{Binding PriorityColor}"
                                                   CornerRadius="8"
                                                   Padding="6,2"
                                                   VerticalAlignment="Top">
                                                <TextBlock Text="{Binding PriorityDisplayName}"
                                                          FontSize="10"
                                                          Foreground="White"/>
                                            </Border>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </TabItem>
            </TabControl>

            <!-- Empty State -->
            <StackPanel Grid.Row="1"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Visibility="{Binding HasNotifications, Converter={StaticResource BoolToVisConverter}}">
                <materialDesign:PackIcon Kind="BellOff"
                                       Width="64" Height="64"
                                       Opacity="0.3"
                                       HorizontalAlignment="Center"/>
                <TextBlock Text="لا توجد إشعارات"
                          Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                          Opacity="0.6"
                          HorizontalAlignment="Center"
                          Margin="0,16,0,0"/>
            </StackPanel>

            <!-- Footer -->
            <Border Grid.Row="2"
                   Background="{DynamicResource MaterialDesignDivider}"
                   Padding="16,8">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Command="{Binding ClearAllCommand}"
                           Style="{DynamicResource MaterialDesignFlatButton}"
                           Content="مسح الكل"
                           Margin="0,0,8,0"/>
                    <Button Command="{Binding OpenSettingsCommand}"
                           Style="{DynamicResource MaterialDesignFlatButton}"
                           Content="إعدادات الإشعارات"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</UserControl>
