using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج التقسيط المتقدم
    /// </summary>
    public class InstallmentPlan : INotifyPropertyChanged
    {
        private int _id;
        private string _planNumber = string.Empty;
        private int? _customerId;
        private int? _invoiceId;
        private int? _saleId;
        private decimal _totalAmount;
        private decimal _downPayment;
        private decimal _installmentAmount;
        private int _numberOfInstallments;
        private InstallmentFrequency _frequency = InstallmentFrequency.Monthly;
        private decimal _interestRate;
        private DateTime _startDate = DateTime.Now;
        private DateTime _endDate;
        private InstallmentStatus _status = InstallmentStatus.Active;
        private string _notes = string.Empty;
        private string _createdBy = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private ObservableCollection<InstallmentPayment> _payments = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PlanNumber
        {
            get => _planNumber;
            set
            {
                if (_planNumber != value)
                {
                    _planNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? InvoiceId
        {
            get => _invoiceId;
            set
            {
                if (_invoiceId != value)
                {
                    _invoiceId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? SaleId
        {
            get => _saleId;
            set
            {
                if (_saleId != value)
                {
                    _saleId = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set
            {
                if (_totalAmount != value)
                {
                    _totalAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalAmount));
                    CalculateInstallmentAmount();
                }
            }
        }

        public decimal DownPayment
        {
            get => _downPayment;
            set
            {
                if (_downPayment != value)
                {
                    _downPayment = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDownPayment));
                    OnPropertyChanged(nameof(RemainingAmount));
                    CalculateInstallmentAmount();
                }
            }
        }

        public decimal InstallmentAmount
        {
            get => _installmentAmount;
            set
            {
                if (_installmentAmount != value)
                {
                    _installmentAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedInstallmentAmount));
                }
            }
        }

        public int NumberOfInstallments
        {
            get => _numberOfInstallments;
            set
            {
                if (_numberOfInstallments != value)
                {
                    _numberOfInstallments = value;
                    OnPropertyChanged();
                    CalculateInstallmentAmount();
                    CalculateEndDate();
                }
            }
        }

        public InstallmentFrequency Frequency
        {
            get => _frequency;
            set
            {
                if (_frequency != value)
                {
                    _frequency = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FrequencyDisplay));
                    CalculateEndDate();
                }
            }
        }

        public decimal InterestRate
        {
            get => _interestRate;
            set
            {
                if (_interestRate != value)
                {
                    _interestRate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedInterestRate));
                    CalculateInstallmentAmount();
                }
            }
        }

        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (_startDate != value)
                {
                    _startDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedStartDate));
                    CalculateEndDate();
                }
            }
        }

        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                if (_endDate != value)
                {
                    _endDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedEndDate));
                }
            }
        }

        public InstallmentStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(CanEdit));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<InstallmentPayment> Payments
        {
            get => _payments;
            set
            {
                if (_payments != value)
                {
                    _payments = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PaidAmount));
                    OnPropertyChanged(nameof(RemainingAmount));
                    OnPropertyChanged(nameof(PaymentProgress));
                }
            }
        }

        #endregion

        #region Calculated Properties

        public decimal RemainingAmount => TotalAmount - DownPayment - PaidAmount;
        public decimal PaidAmount => Payments.Where(p => p.Status == PaymentStatus.Paid).Sum(p => p.Amount);
        public double PaymentProgress => TotalAmount > 0 ? (double)(PaidAmount / TotalAmount) * 100 : 0;
        public bool CanEdit => Status == InstallmentStatus.Active || Status == InstallmentStatus.Pending;

        public string FormattedTotalAmount => TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedDownPayment => DownPayment.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedInstallmentAmount => InstallmentAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedPaidAmount => PaidAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedRemainingAmount => RemainingAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedInterestRate => $"{InterestRate:F2}%";
        public string FormattedStartDate => StartDate.ToString("dd/MM/yyyy");
        public string FormattedEndDate => EndDate.ToString("dd/MM/yyyy");
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");

        public string FrequencyDisplay
        {
            get
            {
                return Frequency switch
                {
                    InstallmentFrequency.Weekly => "أسبوعي",
                    InstallmentFrequency.BiWeekly => "كل أسبوعين",
                    InstallmentFrequency.Monthly => "شهري",
                    InstallmentFrequency.Quarterly => "ربع سنوي",
                    InstallmentFrequency.SemiAnnual => "نصف سنوي",
                    InstallmentFrequency.Annual => "سنوي",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    InstallmentStatus.Pending => "معلق",
                    InstallmentStatus.Active => "نشط",
                    InstallmentStatus.Completed => "مكتمل",
                    InstallmentStatus.Defaulted => "متعثر",
                    InstallmentStatus.Cancelled => "ملغي",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    InstallmentStatus.Pending => "Orange",
                    InstallmentStatus.Active => "Blue",
                    InstallmentStatus.Completed => "Green",
                    InstallmentStatus.Defaulted => "Red",
                    InstallmentStatus.Cancelled => "Gray",
                    _ => "Gray"
                };
            }
        }

        #endregion

        #region Methods

        private void CalculateInstallmentAmount()
        {
            if (NumberOfInstallments <= 0) return;

            var principalAmount = TotalAmount - DownPayment;
            var monthlyInterestRate = InterestRate / 100 / 12;

            if (InterestRate > 0)
            {
                // حساب القسط مع الفوائد
                var factor = Math.Pow(1 + (double)monthlyInterestRate, NumberOfInstallments);
                InstallmentAmount = principalAmount * monthlyInterestRate * (decimal)factor / (decimal)(factor - 1);
            }
            else
            {
                // حساب القسط بدون فوائد
                InstallmentAmount = principalAmount / NumberOfInstallments;
            }
        }

        private void CalculateEndDate()
        {
            var addMonths = Frequency switch
            {
                InstallmentFrequency.Weekly => NumberOfInstallments / 4,
                InstallmentFrequency.BiWeekly => NumberOfInstallments / 2,
                InstallmentFrequency.Monthly => NumberOfInstallments,
                InstallmentFrequency.Quarterly => NumberOfInstallments * 3,
                InstallmentFrequency.SemiAnnual => NumberOfInstallments * 6,
                InstallmentFrequency.Annual => NumberOfInstallments * 12,
                _ => NumberOfInstallments
            };

            EndDate = StartDate.AddMonths(addMonths);
        }

        public void GeneratePaymentSchedule()
        {
            Payments.Clear();
            var currentDate = StartDate;

            for (int i = 1; i <= NumberOfInstallments; i++)
            {
                var payment = new InstallmentPayment
                {
                    InstallmentPlanId = Id,
                    PaymentNumber = i,
                    Amount = InstallmentAmount,
                    DueDate = currentDate,
                    Status = PaymentStatus.Pending
                };

                Payments.Add(payment);

                // حساب التاريخ التالي
                currentDate = Frequency switch
                {
                    InstallmentFrequency.Weekly => currentDate.AddDays(7),
                    InstallmentFrequency.BiWeekly => currentDate.AddDays(14),
                    InstallmentFrequency.Monthly => currentDate.AddMonths(1),
                    InstallmentFrequency.Quarterly => currentDate.AddMonths(3),
                    InstallmentFrequency.SemiAnnual => currentDate.AddMonths(6),
                    InstallmentFrequency.Annual => currentDate.AddYears(1),
                    _ => currentDate.AddMonths(1)
                };
            }
        }

        public void MarkAsCompleted()
        {
            Status = InstallmentStatus.Completed;
            UpdatedAt = DateTime.Now;
        }

        public void MarkAsDefaulted()
        {
            Status = InstallmentStatus.Defaulted;
            UpdatedAt = DateTime.Now;
        }

        public void Cancel()
        {
            Status = InstallmentStatus.Cancelled;
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// دفعة التقسيط
    /// </summary>
    public class InstallmentPayment : INotifyPropertyChanged
    {
        private int _id;
        private int _installmentPlanId;
        private int _paymentNumber;
        private decimal _amount;
        private DateTime _dueDate;
        private DateTime? _paidDate;
        private PaymentStatus _status = PaymentStatus.Pending;
        private decimal _lateFee;
        private string _notes = string.Empty;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int InstallmentPlanId
        {
            get => _installmentPlanId;
            set
            {
                if (_installmentPlanId != value)
                {
                    _installmentPlanId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int PaymentNumber
        {
            get => _paymentNumber;
            set
            {
                if (_paymentNumber != value)
                {
                    _paymentNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Amount
        {
            get => _amount;
            set
            {
                if (_amount != value)
                {
                    _amount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAmount));
                    OnPropertyChanged(nameof(TotalAmount));
                }
            }
        }

        public DateTime DueDate
        {
            get => _dueDate;
            set
            {
                if (_dueDate != value)
                {
                    _dueDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDueDate));
                    OnPropertyChanged(nameof(IsOverdue));
                    OnPropertyChanged(nameof(DaysOverdue));
                }
            }
        }

        public DateTime? PaidDate
        {
            get => _paidDate;
            set
            {
                if (_paidDate != value)
                {
                    _paidDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedPaidDate));
                }
            }
        }

        public PaymentStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                }
            }
        }

        public decimal LateFee
        {
            get => _lateFee;
            set
            {
                if (_lateFee != value)
                {
                    _lateFee = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLateFee));
                    OnPropertyChanged(nameof(TotalAmount));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        // Calculated Properties
        public decimal TotalAmount => Amount + LateFee;
        public bool IsOverdue => DueDate < DateTime.Now && Status != PaymentStatus.Paid;
        public int DaysOverdue => IsOverdue ? (int)(DateTime.Now - DueDate).TotalDays : 0;

        public string FormattedAmount => Amount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedLateFee => LateFee.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalAmount => TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedDueDate => DueDate.ToString("dd/MM/yyyy");
        public string FormattedPaidDate => PaidDate?.ToString("dd/MM/yyyy") ?? "غير مدفوع";

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    PaymentStatus.Pending => "معلق",
                    PaymentStatus.Paid => "مدفوع",
                    PaymentStatus.Overdue => "متأخر",
                    PaymentStatus.Cancelled => "ملغي",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    PaymentStatus.Pending => "Orange",
                    PaymentStatus.Paid => "Green",
                    PaymentStatus.Overdue => "Red",
                    PaymentStatus.Cancelled => "Gray",
                    _ => "Gray"
                };
            }
        }

        public void MarkAsPaid()
        {
            Status = PaymentStatus.Paid;
            PaidDate = DateTime.Now;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    #region Enums

    public enum InstallmentFrequency
    {
        Weekly,         // أسبوعي
        BiWeekly,       // كل أسبوعين
        Monthly,        // شهري
        Quarterly,      // ربع سنوي
        SemiAnnual,     // نصف سنوي
        Annual          // سنوي
    }

    public enum InstallmentStatus
    {
        Pending,        // معلق
        Active,         // نشط
        Completed,      // مكتمل
        Defaulted,      // متعثر
        Cancelled       // ملغي
    }

    #endregion

    #region Validation

    public class InstallmentPlanValidator : AbstractValidator<InstallmentPlan>
    {
        public InstallmentPlanValidator()
        {
            RuleFor(p => p.PlanNumber)
                .NotEmpty().WithMessage("رقم خطة التقسيط مطلوب")
                .MaximumLength(50).WithMessage("رقم خطة التقسيط لا يمكن أن يتجاوز 50 حرف");

            RuleFor(p => p.TotalAmount)
                .GreaterThan(0).WithMessage("المبلغ الإجمالي يجب أن يكون أكبر من صفر");

            RuleFor(p => p.NumberOfInstallments)
                .GreaterThan(0).WithMessage("عدد الأقساط يجب أن يكون أكبر من صفر")
                .LessThanOrEqualTo(120).WithMessage("عدد الأقساط لا يمكن أن يتجاوز 120 قسط");

            RuleFor(p => p.DownPayment)
                .GreaterThanOrEqualTo(0).WithMessage("الدفعة المقدمة لا يمكن أن تكون سالبة")
                .LessThan(p => p.TotalAmount).WithMessage("الدفعة المقدمة لا يمكن أن تتجاوز المبلغ الإجمالي");

            RuleFor(p => p.InterestRate)
                .GreaterThanOrEqualTo(0).WithMessage("معدل الفائدة لا يمكن أن يكون سالب")
                .LessThanOrEqualTo(100).WithMessage("معدل الفائدة لا يمكن أن يتجاوز 100%");
        }
    }

    #endregion
}
