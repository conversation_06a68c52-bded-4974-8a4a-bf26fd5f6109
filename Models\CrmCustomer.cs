using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج العميل المتقدم لنظام CRM
    /// </summary>
    public class CrmCustomer : INotifyPropertyChanged
    {
        private int _id;
        private string _customerNumber = string.Empty;
        private string _companyName = string.Empty;
        private string _contactPerson = string.Empty;
        private CustomerType _customerType = CustomerType.Individual;
        private CustomerStatus _status = CustomerStatus.Active;
        private CustomerCategory _category = CustomerCategory.Regular;
        private string _industry = string.Empty;
        private string _website = string.Empty;
        private string _taxNumber = string.Empty;
        private string _commercialRegister = string.Empty;
        private string _primaryEmail = string.Empty;
        private string _primaryPhone = string.Empty;
        private string _secondaryPhone = string.Empty;
        private string _fax = string.Empty;
        private string _address = string.Empty;
        private string _city = string.Empty;
        private string _state = string.Empty;
        private string _country = string.Empty;
        private string _postalCode = string.Empty;
        private decimal _creditLimit;
        private decimal _currentBalance;
        private decimal _totalPurchases;
        private decimal _averageOrderValue;
        private int _totalOrders;
        private DateTime? _lastOrderDate;
        private DateTime? _lastContactDate;
        private CustomerSource _source = CustomerSource.Direct;
        private string _assignedSalesperson = string.Empty;
        private int _loyaltyPoints;
        private decimal _discountPercentage;
        private string _notes = string.Empty;
        private string _tags = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private string _createdBy = string.Empty;
        private ObservableCollection<Contact> _contacts = new();
        private ObservableCollection<Opportunity> _opportunities = new();
        private ObservableCollection<Interaction> _interactions = new();
        private ObservableCollection<FollowUp> _followUps = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CustomerNumber
        {
            get => _customerNumber;
            set
            {
                if (_customerNumber != value)
                {
                    _customerNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CompanyName
        {
            get => _companyName;
            set
            {
                if (_companyName != value)
                {
                    _companyName = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(DisplayName));
                }
            }
        }

        public string ContactPerson
        {
            get => _contactPerson;
            set
            {
                if (_contactPerson != value)
                {
                    _contactPerson = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(DisplayName));
                }
            }
        }

        public CustomerType CustomerType
        {
            get => _customerType;
            set
            {
                if (_customerType != value)
                {
                    _customerType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CustomerTypeDisplay));
                    OnPropertyChanged(nameof(CustomerTypeIcon));
                }
            }
        }

        public CustomerStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        public CustomerCategory Category
        {
            get => _category;
            set
            {
                if (_category != value)
                {
                    _category = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CategoryDisplay));
                    OnPropertyChanged(nameof(CategoryColor));
                }
            }
        }

        public string Industry
        {
            get => _industry;
            set
            {
                if (_industry != value)
                {
                    _industry = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Website
        {
            get => _website;
            set
            {
                if (_website != value)
                {
                    _website = value;
                    OnPropertyChanged();
                }
            }
        }

        public string TaxNumber
        {
            get => _taxNumber;
            set
            {
                if (_taxNumber != value)
                {
                    _taxNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CommercialRegister
        {
            get => _commercialRegister;
            set
            {
                if (_commercialRegister != value)
                {
                    _commercialRegister = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PrimaryEmail
        {
            get => _primaryEmail;
            set
            {
                if (_primaryEmail != value)
                {
                    _primaryEmail = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PrimaryPhone
        {
            get => _primaryPhone;
            set
            {
                if (_primaryPhone != value)
                {
                    _primaryPhone = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SecondaryPhone
        {
            get => _secondaryPhone;
            set
            {
                if (_secondaryPhone != value)
                {
                    _secondaryPhone = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Fax
        {
            get => _fax;
            set
            {
                if (_fax != value)
                {
                    _fax = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Address
        {
            get => _address;
            set
            {
                if (_address != value)
                {
                    _address = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string City
        {
            get => _city;
            set
            {
                if (_city != value)
                {
                    _city = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string State
        {
            get => _state;
            set
            {
                if (_state != value)
                {
                    _state = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string Country
        {
            get => _country;
            set
            {
                if (_country != value)
                {
                    _country = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FullAddress));
                }
            }
        }

        public string PostalCode
        {
            get => _postalCode;
            set
            {
                if (_postalCode != value)
                {
                    _postalCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal CreditLimit
        {
            get => _creditLimit;
            set
            {
                if (_creditLimit != value)
                {
                    _creditLimit = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreditLimit));
                    OnPropertyChanged(nameof(AvailableCredit));
                }
            }
        }

        public decimal CurrentBalance
        {
            get => _currentBalance;
            set
            {
                if (_currentBalance != value)
                {
                    _currentBalance = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCurrentBalance));
                    OnPropertyChanged(nameof(AvailableCredit));
                    OnPropertyChanged(nameof(CreditUtilization));
                }
            }
        }

        public decimal TotalPurchases
        {
            get => _totalPurchases;
            set
            {
                if (_totalPurchases != value)
                {
                    _totalPurchases = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalPurchases));
                }
            }
        }

        public decimal AverageOrderValue
        {
            get => _averageOrderValue;
            set
            {
                if (_averageOrderValue != value)
                {
                    _averageOrderValue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAverageOrderValue));
                }
            }
        }

        public int TotalOrders
        {
            get => _totalOrders;
            set
            {
                if (_totalOrders != value)
                {
                    _totalOrders = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? LastOrderDate
        {
            get => _lastOrderDate;
            set
            {
                if (_lastOrderDate != value)
                {
                    _lastOrderDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLastOrderDate));
                    OnPropertyChanged(nameof(DaysSinceLastOrder));
                }
            }
        }

        public DateTime? LastContactDate
        {
            get => _lastContactDate;
            set
            {
                if (_lastContactDate != value)
                {
                    _lastContactDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLastContactDate));
                    OnPropertyChanged(nameof(DaysSinceLastContact));
                }
            }
        }

        public CustomerSource Source
        {
            get => _source;
            set
            {
                if (_source != value)
                {
                    _source = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(SourceDisplay));
                }
            }
        }

        public string AssignedSalesperson
        {
            get => _assignedSalesperson;
            set
            {
                if (_assignedSalesperson != value)
                {
                    _assignedSalesperson = value;
                    OnPropertyChanged();
                }
            }
        }

        public int LoyaltyPoints
        {
            get => _loyaltyPoints;
            set
            {
                if (_loyaltyPoints != value)
                {
                    _loyaltyPoints = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal DiscountPercentage
        {
            get => _discountPercentage;
            set
            {
                if (_discountPercentage != value)
                {
                    _discountPercentage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDiscountPercentage));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Tags
        {
            get => _tags;
            set
            {
                if (_tags != value)
                {
                    _tags = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TagsList));
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<Contact> Contacts
        {
            get => _contacts;
            set
            {
                if (_contacts != value)
                {
                    _contacts = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ContactsCount));
                }
            }
        }

        public ObservableCollection<Opportunity> Opportunities
        {
            get => _opportunities;
            set
            {
                if (_opportunities != value)
                {
                    _opportunities = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(OpportunitiesCount));
                    OnPropertyChanged(nameof(TotalOpportunityValue));
                }
            }
        }

        public ObservableCollection<Interaction> Interactions
        {
            get => _interactions;
            set
            {
                if (_interactions != value)
                {
                    _interactions = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(InteractionsCount));
                }
            }
        }

        public ObservableCollection<FollowUp> FollowUps
        {
            get => _followUps;
            set
            {
                if (_followUps != value)
                {
                    _followUps = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PendingFollowUpsCount));
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsActive => Status == CustomerStatus.Active;
        public string DisplayName => CustomerType == CustomerType.Company ? CompanyName : ContactPerson;
        public string FullAddress => $"{Address}, {City}, {State}, {Country}".Trim(' ', ',');
        public decimal AvailableCredit => CreditLimit - CurrentBalance;
        public double CreditUtilization => CreditLimit > 0 ? (double)(CurrentBalance / CreditLimit) * 100 : 0;
        public int ContactsCount => Contacts?.Count ?? 0;
        public int OpportunitiesCount => Opportunities?.Count ?? 0;
        public int InteractionsCount => Interactions?.Count ?? 0;
        public int PendingFollowUpsCount => FollowUps?.Count(f => f.Status == FollowUpStatus.Pending) ?? 0;
        public decimal TotalOpportunityValue => Opportunities?.Sum(o => o.Value) ?? 0;
        public string[] TagsList => Tags?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();

        public int DaysSinceLastOrder
        {
            get
            {
                if (!LastOrderDate.HasValue) return int.MaxValue;
                return (int)(DateTime.Now - LastOrderDate.Value).TotalDays;
            }
        }

        public int DaysSinceLastContact
        {
            get
            {
                if (!LastContactDate.HasValue) return int.MaxValue;
                return (int)(DateTime.Now - LastContactDate.Value).TotalDays;
            }
        }

        // Display Properties
        public string CustomerTypeDisplay
        {
            get
            {
                return CustomerType switch
                {
                    CustomerType.Individual => "فرد",
                    CustomerType.Company => "شركة",
                    CustomerType.Government => "حكومي",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    CustomerStatus.Active => "نشط",
                    CustomerStatus.Inactive => "غير نشط",
                    CustomerStatus.Prospect => "عميل محتمل",
                    CustomerStatus.Blocked => "محظور",
                    _ => "غير محدد"
                };
            }
        }

        public string CategoryDisplay
        {
            get
            {
                return Category switch
                {
                    CustomerCategory.VIP => "VIP",
                    CustomerCategory.Premium => "مميز",
                    CustomerCategory.Regular => "عادي",
                    CustomerCategory.Basic => "أساسي",
                    _ => "غير محدد"
                };
            }
        }

        public string SourceDisplay
        {
            get
            {
                return Source switch
                {
                    CustomerSource.Direct => "مباشر",
                    CustomerSource.Website => "موقع إلكتروني",
                    CustomerSource.SocialMedia => "وسائل التواصل",
                    CustomerSource.Referral => "إحالة",
                    CustomerSource.Advertisement => "إعلان",
                    CustomerSource.Exhibition => "معرض",
                    CustomerSource.ColdCall => "اتصال بارد",
                    CustomerSource.Other => "أخرى",
                    _ => "غير محدد"
                };
            }
        }

        public string CustomerTypeIcon
        {
            get
            {
                return CustomerType switch
                {
                    CustomerType.Individual => "Account",
                    CustomerType.Company => "Domain",
                    CustomerType.Government => "Bank",
                    _ => "Account"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    CustomerStatus.Active => "Green",
                    CustomerStatus.Inactive => "Gray",
                    CustomerStatus.Prospect => "Orange",
                    CustomerStatus.Blocked => "Red",
                    _ => "Gray"
                };
            }
        }

        public string CategoryColor
        {
            get
            {
                return Category switch
                {
                    CustomerCategory.VIP => "Purple",
                    CustomerCategory.Premium => "Gold",
                    CustomerCategory.Regular => "Blue",
                    CustomerCategory.Basic => "Gray",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedCreditLimit => CreditLimit.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedCurrentBalance => CurrentBalance.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalPurchases => TotalPurchases.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedAverageOrderValue => AverageOrderValue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedDiscountPercentage => $"{DiscountPercentage:F1}%";
        public string FormattedLastOrderDate => LastOrderDate?.ToString("dd/MM/yyyy") ?? "لا يوجد";
        public string FormattedLastContactDate => LastContactDate?.ToString("dd/MM/yyyy") ?? "لا يوجد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        public void UpdateLastContactDate()
        {
            LastContactDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        public void AddLoyaltyPoints(int points)
        {
            LoyaltyPoints += points;
            UpdatedAt = DateTime.Now;
        }

        public void UpdatePurchaseStats(decimal orderValue)
        {
            TotalOrders++;
            TotalPurchases += orderValue;
            AverageOrderValue = TotalPurchases / TotalOrders;
            LastOrderDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        public void UpdateBalance(decimal amount)
        {
            CurrentBalance += amount;
            UpdatedAt = DateTime.Now;
        }

        public bool CanPurchase(decimal amount)
        {
            return IsActive && (AvailableCredit >= amount || CreditLimit == 0);
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Enums

    public enum CustomerType
    {
        Individual,     // فرد
        Company,        // شركة
        Government      // حكومي
    }

    public enum CustomerStatus
    {
        Active,         // نشط
        Inactive,       // غير نشط
        Prospect,       // عميل محتمل
        Blocked         // محظور
    }

    public enum CustomerCategory
    {
        VIP,            // VIP
        Premium,        // مميز
        Regular,        // عادي
        Basic           // أساسي
    }

    public enum CustomerSource
    {
        Direct,         // مباشر
        Website,        // موقع إلكتروني
        SocialMedia,    // وسائل التواصل
        Referral,       // إحالة
        Advertisement,  // إعلان
        Exhibition,     // معرض
        ColdCall,       // اتصال بارد
        Other           // أخرى
    }

    #endregion

    #region Validation

    public class CrmCustomerValidator : AbstractValidator<CrmCustomer>
    {
        public CrmCustomerValidator()
        {
            RuleFor(c => c.CustomerNumber)
                .NotEmpty().WithMessage("رقم العميل مطلوب")
                .MaximumLength(20).WithMessage("رقم العميل لا يمكن أن يتجاوز 20 حرف");

            RuleFor(c => c.CompanyName)
                .NotEmpty().When(c => c.CustomerType == CustomerType.Company)
                .WithMessage("اسم الشركة مطلوب للعملاء من نوع شركة")
                .MaximumLength(200).WithMessage("اسم الشركة لا يمكن أن يتجاوز 200 حرف");

            RuleFor(c => c.ContactPerson)
                .NotEmpty().When(c => c.CustomerType == CustomerType.Individual)
                .WithMessage("اسم الشخص مطلوب للعملاء الأفراد")
                .MaximumLength(100).WithMessage("اسم الشخص لا يمكن أن يتجاوز 100 حرف");

            RuleFor(c => c.PrimaryEmail)
                .EmailAddress().When(c => !string.IsNullOrEmpty(c.PrimaryEmail))
                .WithMessage("البريد الإلكتروني غير صحيح");

            RuleFor(c => c.CreditLimit)
                .GreaterThanOrEqualTo(0).WithMessage("حد الائتمان لا يمكن أن يكون سالب");

            RuleFor(c => c.DiscountPercentage)
                .InclusiveBetween(0, 100).WithMessage("نسبة الخصم يجب أن تكون بين 0 و 100");
        }
    }

    #endregion
}
