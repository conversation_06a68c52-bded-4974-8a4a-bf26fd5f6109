using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Controls
{
    /// <summary>
    /// إشعار Toast منبثق
    /// </summary>
    public partial class ToastNotification : UserControl, INotifyPropertyChanged
    {
        private readonly DispatcherTimer _autoDismissTimer;
        private readonly DispatcherTimer _progressTimer;
        private Notification _notification = null!;
        private int _autoDismissSeconds = 5;
        private int _progressValue = 0;
        private bool _showProgressBar = true;
        private bool _hasActions = true;

        public ToastNotification()
        {
            InitializeComponent();
            DataContext = this;

            // إعداد مؤقت الإغلاق التلقائي
            _autoDismissTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(_autoDismissSeconds)
            };
            _autoDismissTimer.Tick += AutoDismissTimer_Tick;

            // إعداد مؤقت شريط التقدم
            _progressTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(50)
            };
            _progressTimer.Tick += ProgressTimer_Tick;
        }

        public ToastNotification(Notification notification, int autoDismissSeconds = 5) : this()
        {
            Notification = notification;
            AutoDismissSeconds = autoDismissSeconds;
        }

        #region Properties

        public Notification Notification
        {
            get => _notification;
            set
            {
                if (_notification != value)
                {
                    _notification = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(Title));
                    OnPropertyChanged(nameof(Message));
                    OnPropertyChanged(nameof(TimeAgo));
                    OnPropertyChanged(nameof(IconKind));
                    OnPropertyChanged(nameof(NotificationColorBrush));
                    OnPropertyChanged(nameof(HasAction));
                }
            }
        }

        public int AutoDismissSeconds
        {
            get => _autoDismissSeconds;
            set
            {
                if (_autoDismissSeconds != value)
                {
                    _autoDismissSeconds = value;
                    OnPropertyChanged();
                    _autoDismissTimer.Interval = TimeSpan.FromSeconds(value);
                }
            }
        }

        public int ProgressValue
        {
            get => _progressValue;
            set
            {
                if (_progressValue != value)
                {
                    _progressValue = value;
                    OnPropertyChanged();
                    AutoDismissProgressBar.Value = value;
                }
            }
        }

        public bool ShowProgressBar
        {
            get => _showProgressBar;
            set
            {
                if (_showProgressBar != value)
                {
                    _showProgressBar = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool HasActions
        {
            get => _hasActions;
            set
            {
                if (_hasActions != value)
                {
                    _hasActions = value;
                    OnPropertyChanged();
                }
            }
        }

        // خصائص مربوطة بالإشعار
        public string Title => Notification?.Title ?? string.Empty;
        public string Message => Notification?.Message ?? string.Empty;
        public string TimeAgo => Notification?.TimeAgo ?? string.Empty;
        public bool HasAction => Notification?.HasAction ?? false;

        public string IconKind
        {
            get
            {
                if (Notification?.Type == null) return "Bell";

                return Notification.Type.ToLower() switch
                {
                    "info" => "Information",
                    "warning" => "Warning",
                    "error" => "Error",
                    "success" => "CheckCircle",
                    "lowstock" => "PackageDown",
                    "outofstock" => "PackageRemove",
                    "paymentdue" => "CurrencyUsd",
                    "system" => "Cog",
                    _ => "Bell"
                };
            }
        }

        public Brush NotificationColorBrush
        {
            get
            {
                if (Notification?.Type == null)
                    return Application.Current.FindResource("PrimaryHueMidBrush") as Brush ?? Brushes.Blue;

                var resourceName = Notification.Type.ToLower() switch
                {
                    "info" => "PrimaryHueMidBrush",
                    "warning" => "SecondaryHueMidBrush",
                    "error" => "ValidationErrorBrush",
                    "success" => "SecondaryHueMidBrush",
                    "lowstock" => "SecondaryHueMidBrush",
                    "outofstock" => "ValidationErrorBrush",
                    "paymentdue" => "PrimaryHueMidBrush",
                    "system" => "MaterialDesignBody",
                    _ => "PrimaryHueMidBrush"
                };

                return Application.Current.FindResource(resourceName) as Brush ?? Brushes.Blue;
            }
        }

        #endregion

        #region Events

        public event EventHandler? Dismissed;
        public event EventHandler? ActionRequested;
        public event EventHandler? SnoozeRequested;
        public event PropertyChangedEventHandler? PropertyChanged;

        #endregion

        #region Methods

        /// <summary>
        /// عرض Toast مع الرسوم المتحركة
        /// </summary>
        public void Show()
        {
            try
            {
                var slideInAnimation = FindResource("SlideInAnimation") as Storyboard;
                slideInAnimation?.Begin(this);

                if (AutoDismissSeconds > 0)
                {
                    StartAutoDismissTimer();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في عرض Toast notification");
            }
        }

        /// <summary>
        /// إخفاء Toast مع الرسوم المتحركة
        /// </summary>
        public void Hide()
        {
            try
            {
                StopTimers();

                var slideOutAnimation = FindResource("SlideOutAnimation") as Storyboard;
                if (slideOutAnimation != null)
                {
                    slideOutAnimation.Completed += (s, e) => Dismissed?.Invoke(this, EventArgs.Empty);
                    slideOutAnimation.Begin(this);
                }
                else
                {
                    Dismissed?.Invoke(this, EventArgs.Empty);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إخفاء Toast notification");
                Dismissed?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// بدء مؤقت الإغلاق التلقائي
        /// </summary>
        private void StartAutoDismissTimer()
        {
            if (ShowProgressBar)
            {
                ProgressValue = 0;
                _progressTimer.Start();
            }

            _autoDismissTimer.Start();
        }

        /// <summary>
        /// إيقاف المؤقتات
        /// </summary>
        private void StopTimers()
        {
            _autoDismissTimer.Stop();
            _progressTimer.Stop();
        }

        /// <summary>
        /// إيقاف مؤقت الإغلاق التلقائي مؤقتاً
        /// </summary>
        public void PauseAutoDismiss()
        {
            StopTimers();
        }

        /// <summary>
        /// استئناف مؤقت الإغلاق التلقائي
        /// </summary>
        public void ResumeAutoDismiss()
        {
            if (AutoDismissSeconds > 0)
            {
                StartAutoDismissTimer();
            }
        }

        #endregion

        #region Event Handlers

        private void AutoDismissTimer_Tick(object? sender, EventArgs e)
        {
            Hide();
        }

        private void ProgressTimer_Tick(object? sender, EventArgs e)
        {
            ProgressValue += 100 / (AutoDismissSeconds * 20); // 20 ticks per second

            if (ProgressValue >= 100)
            {
                ProgressValue = 100;
                _progressTimer.Stop();
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Hide();
        }

        private void ViewButton_Click(object sender, RoutedEventArgs e)
        {
            ActionRequested?.Invoke(this, EventArgs.Empty);
            Hide();
        }

        private void SnoozeButton_Click(object sender, RoutedEventArgs e)
        {
            SnoozeRequested?.Invoke(this, EventArgs.Empty);
            Hide();
        }

        private void UserControl_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            PauseAutoDismiss();
        }

        private void UserControl_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            ResumeAutoDismiss();
        }

        #endregion

        #region INotifyPropertyChanged

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region Cleanup

        public void Dispose()
        {
            StopTimers();
            _autoDismissTimer?.Stop();
            _progressTimer?.Stop();
        }

        #endregion
    }
}
