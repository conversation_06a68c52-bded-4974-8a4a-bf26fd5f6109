<Window x:Class="SalesManagementSystem.Views.Dialogs.ProductDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}"
        Height="700" Width="550"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>

        <Style x:Key="FormTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="FormComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="{Binding HeaderIcon}"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="{Binding WindowTitle}"
                      Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                      VerticalAlignment="Center"
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Product Code (Auto-generated) -->
                <TextBox x:Name="CodeTextBox"
                        Style="{StaticResource FormTextBox}"
                        materialDesign:HintAssist.Hint="كود المنتج (يتولد تلقائياً)"
                        Text="{Binding Product.Code, UpdateSourceTrigger=PropertyChanged}"
                        IsReadOnly="True"
                        Background="{DynamicResource MaterialDesignSelection}"
                        ToolTip="يتم توليد كود المنتج تلقائياً عند الحفظ"/>

                <!-- Product Name -->
                <TextBox x:Name="NameTextBox"
                        Style="{StaticResource FormTextBox}"
                        materialDesign:HintAssist.Hint="اسم المنتج *"
                        Text="{Binding Product.Name, UpdateSourceTrigger=PropertyChanged}"/>

                <!-- Category -->
                <ComboBox x:Name="CategoryComboBox"
                         Style="{StaticResource FormComboBox}"
                         materialDesign:HintAssist.Hint="الفئة *"
                         ItemsSource="{Binding Categories}"
                         SelectedItem="{Binding SelectedCategory}"
                         DisplayMemberPath="Name"/>

                <!-- Description -->
                <TextBox x:Name="DescriptionTextBox"
                        Style="{StaticResource FormTextBox}"
                        materialDesign:HintAssist.Hint="الوصف"
                        Text="{Binding Product.Description, UpdateSourceTrigger=PropertyChanged}"
                        Height="80"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"/>

                <!-- Price Section -->
                <GroupBox Header="الأسعار"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,16,0,8">
                    <StackPanel Margin="16">
                        <!-- Purchase Price -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="سعر الشراء *"
                                Text="{Binding Product.PurchasePrice, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"/>

                        <!-- Sale Price -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="سعر البيع الأول *"
                                Text="{Binding Product.SalePrice, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"/>

                        <!-- Sale Price 2 -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="سعر البيع الثاني"
                                Text="{Binding Product.SalePrice2, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"/>

                        <!-- Profit Margins and Profits Display -->
                        <Grid Margin="0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="8"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- First Price Profit Info -->
                            <StackPanel Grid.Column="0">
                                <!-- Profit Margin -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                    <TextBlock Text="هامش الربح الأول:"
                                              VerticalAlignment="Center"
                                              Margin="0,0,8,0"
                                              FontSize="12"/>
                                    <TextBlock Text="{Binding ProfitMargin, StringFormat=F2}"
                                              FontWeight="Bold"
                                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                                              VerticalAlignment="Center"/>
                                    <TextBlock Text="%"
                                              VerticalAlignment="Center"
                                              Margin="4,0,0,0"/>
                                </StackPanel>
                                <!-- Profit Amount -->
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="الربح:"
                                              VerticalAlignment="Center"
                                              Margin="0,0,8,0"
                                              FontSize="11"
                                              Opacity="0.8"/>
                                    <TextBlock Text="{Binding Profit, StringFormat=F2}"
                                              FontWeight="Medium"
                                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                                              VerticalAlignment="Center"
                                              FontSize="11"/>
                                    <TextBlock Text="دج"
                                              VerticalAlignment="Center"
                                              Margin="4,0,0,0"
                                              FontSize="11"
                                              Opacity="0.8"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- Second Price Profit Info -->
                            <StackPanel Grid.Column="2">
                                <!-- Profit Margin -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                    <TextBlock Text="هامش الربح الثاني:"
                                              VerticalAlignment="Center"
                                              Margin="0,0,8,0"
                                              FontSize="12"/>
                                    <TextBlock Text="{Binding ProfitMargin2, StringFormat=F2}"
                                              FontWeight="Bold"
                                              Foreground="{DynamicResource SecondaryHueMidBrush}"
                                              VerticalAlignment="Center"/>
                                    <TextBlock Text="%"
                                              VerticalAlignment="Center"
                                              Margin="4,0,0,0"/>
                                </StackPanel>
                                <!-- Profit Amount -->
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="الربح:"
                                              VerticalAlignment="Center"
                                              Margin="0,0,8,0"
                                              FontSize="11"
                                              Opacity="0.8"/>
                                    <TextBlock Text="{Binding Profit2, StringFormat=F2}"
                                              FontWeight="Medium"
                                              Foreground="{DynamicResource SecondaryHueMidBrush}"
                                              VerticalAlignment="Center"
                                              FontSize="11"/>
                                    <TextBlock Text="دج"
                                              VerticalAlignment="Center"
                                              Margin="4,0,0,0"
                                              FontSize="11"
                                              Opacity="0.8"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- Stock Section -->
                <GroupBox Header="المخزون"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,8">
                    <StackPanel Margin="16">
                        <!-- Current Quantity -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="الكمية الحالية *"
                                Text="{Binding Product.Quantity, UpdateSourceTrigger=PropertyChanged}"/>

                        <!-- Minimum Quantity -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="الحد الأدنى للكمية *"
                                Text="{Binding Product.MinQuantity, UpdateSourceTrigger=PropertyChanged}"/>

                        <!-- Unit -->
                        <ComboBox Style="{StaticResource FormComboBox}"
                                 materialDesign:HintAssist.Hint="الوحدة"
                                 ItemsSource="{Binding Units}"
                                 Text="{Binding Product.Unit, UpdateSourceTrigger=PropertyChanged}"
                                 IsEditable="True"/>

                        <!-- Warehouse Selection -->
                        <ComboBox Style="{StaticResource FormComboBox}"
                                 materialDesign:HintAssist.Hint="المخزن *"
                                 ItemsSource="{Binding Warehouses}"
                                 SelectedItem="{Binding SelectedWarehouse}">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🏪" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding Name}" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBlock Text=" - " VerticalAlignment="Center" Opacity="0.7"/>
                                        <TextBlock Text="{Binding City}"
                                                  VerticalAlignment="Center"
                                                  Opacity="0.7"
                                                  FontSize="12"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <!-- Low Stock Warning -->
                        <Border Background="{DynamicResource MaterialDesignSelection}"
                               CornerRadius="4"
                               Padding="12,8"
                               Margin="0,8"
                               Visibility="{Binding IsLowStock, Converter={StaticResource BoolToVisConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                       Foreground="Orange"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="تحذير: الكمية أقل من الحد الأدنى"
                                          Foreground="Orange"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </GroupBox>

                <!-- Additional Options -->
                <GroupBox Header="خيارات إضافية"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,8">
                    <StackPanel Margin="16">
                        <!-- Multiple Barcodes -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="8"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0"
                                    Style="{StaticResource FormTextBox}"
                                    materialDesign:HintAssist.Hint="الباركودات (كل باركود في سطر منفصل)"
                                    Text="{Binding Barcodes, UpdateSourceTrigger=PropertyChanged}"
                                    AcceptsReturn="True"
                                    TextWrapping="Wrap"
                                    VerticalScrollBarVisibility="Auto"
                                    MinHeight="80"
                                    MaxHeight="120"/>

                            <Button Grid.Column="2"
                                   Command="{Binding GenerateBarcodeCommand}"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Width="120"
                                   ToolTip="توليد باركود تلقائي">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Barcode"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,4,0"/>
                                    <TextBlock Text="توليد" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                        <!-- Barcode Info -->
                        <Border Background="{DynamicResource MaterialDesignSelection}"
                               CornerRadius="4"
                               Padding="12,8"
                               Margin="0,8"
                               Visibility="{Binding HasBarcode, Converter={StaticResource BoolToVisConverter}}">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                    <materialDesign:PackIcon Kind="Information"
                                                           Foreground="{DynamicResource MaterialDesignBody}"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="الباركود الأساسي:"
                                              Foreground="{DynamicResource MaterialDesignBody}"
                                              VerticalAlignment="Center"
                                              FontWeight="Medium"/>
                                    <TextBlock Text="{Binding Product.Barcode}"
                                              Foreground="{DynamicResource MaterialDesignBody}"
                                              VerticalAlignment="Center"
                                              Margin="8,0,0,0"
                                              FontFamily="Consolas"/>
                                </StackPanel>
                                <TextBlock Text="يمكنك إدخال عدة باركودات، كل واحد في سطر منفصل. الباركود الأول سيكون الباركود الأساسي."
                                          Foreground="{DynamicResource MaterialDesignBody}"
                                          FontSize="12"
                                          Opacity="0.8"
                                          TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- Barcode Display -->
                        <Expander Header="عرض الباركود"
                                 Style="{DynamicResource MaterialDesignExpander}"
                                 Margin="0,8"
                                 Visibility="{Binding HasBarcode, Converter={StaticResource BoolToVisConverter}}">
                            <Border Background="White"
                                   BorderBrush="{DynamicResource MaterialDesignDivider}"
                                   BorderThickness="1"
                                   CornerRadius="4"
                                   Padding="16"
                                   HorizontalAlignment="Center"
                                   Margin="8">
                                <StackPanel HorizontalAlignment="Center">
                                    <Image Source="{Binding BarcodeImage}"
                                          Stretch="None"
                                          HorizontalAlignment="Center"
                                          Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding Product.Barcode}"
                                              HorizontalAlignment="Center"
                                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"/>
                                </StackPanel>
                            </Border>
                        </Expander>

                        <!-- Is Active -->
                        <CheckBox Content="المنتج نشط"
                                 IsChecked="{Binding Product.IsActive}"
                                 Margin="0,8"
                                 Style="{DynamicResource MaterialDesignCheckBox}"/>

                        <!-- Track Stock -->
                        <CheckBox Content="تتبع المخزون"
                                 IsChecked="{Binding Product.TrackStock}"
                                 Margin="0,8"
                                 Style="{DynamicResource MaterialDesignCheckBox}"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2"
                   Orientation="Horizontal"
                   HorizontalAlignment="Left"
                   Margin="0,24,0,0">

            <Button Command="{Binding SaveCommand}"
                   Style="{DynamicResource MaterialDesignRaisedButton}"
                   Width="100"
                   Margin="0,0,12,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding CancelCommand}"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Width="100">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cancel"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button.Content>
            </Button>
        </StackPanel>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}">
            <StackPanel HorizontalAlignment="Center"
                       VerticalAlignment="Center">
                <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="48" Height="48"/>
                <TextBlock Text="{Binding LoadingMessage}"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
