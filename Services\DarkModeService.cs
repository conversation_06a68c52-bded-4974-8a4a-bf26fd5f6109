using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Media;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الوضع الليلي
    /// </summary>
    public class DarkModeService : INotifyPropertyChanged
    {
        private static DarkModeService? _instance;
        private bool _isDarkMode;

        public static DarkModeService Instance => _instance ??= new DarkModeService();

        private DarkModeService()
        {
            _isDarkMode = false; // البداية بالوضع العادي
        }

        public bool IsDarkMode
        {
            get => _isDarkMode;
            set
            {
                if (_isDarkMode != value)
                {
                    _isDarkMode = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ThemeIcon));
                    OnPropertyChanged(nameof(ThemeText));
                    OnPropertyChanged(nameof(ThemeTooltip));
                    OnPropertyChanged(nameof(Background));
                    OnPropertyChanged(nameof(Foreground));
                    OnPropertyChanged(nameof(HeaderBackground));
                    OnPropertyChanged(nameof(CardBackground));
                    OnPropertyChanged(nameof(BorderBrush));
                    OnPropertyChanged(nameof(SecondaryText));
                    ApplyTheme();
                }
            }
        }

        public string ThemeIcon => _isDarkMode ? "☀️" : "🌙";
        public string ThemeText => _isDarkMode ? "الوضع العادي" : "الوضع الليلي";
        public string ThemeTooltip => _isDarkMode ? "التبديل إلى الوضع العادي" : "التبديل إلى الوضع الليلي";

        // ألوان الوضع العادي
        public static class LightTheme
        {
            public static readonly SolidColorBrush Background = new(Color.FromRgb(255, 255, 255)); // أبيض
            public static readonly SolidColorBrush Foreground = new(Color.FromRgb(33, 33, 33)); // أسود داكن
            public static readonly SolidColorBrush HeaderBackground = new(Color.FromRgb(33, 150, 243)); // أزرق
            public static readonly SolidColorBrush HeaderForeground = new(Color.FromRgb(255, 255, 255)); // أبيض
            public static readonly SolidColorBrush CardBackground = new(Color.FromRgb(250, 250, 250)); // رمادي فاتح جداً
            public static readonly SolidColorBrush BorderBrush = new(Color.FromRgb(224, 224, 224)); // رمادي فاتح
            public static readonly SolidColorBrush ButtonBackground = new(Color.FromRgb(76, 175, 80)); // أخضر
            public static readonly SolidColorBrush ButtonHover = new(Color.FromRgb(102, 187, 106)); // أخضر فاتح
            public static readonly SolidColorBrush SecondaryText = new(Color.FromRgb(117, 117, 117)); // رمادي
            public static readonly SolidColorBrush MenuBackground = new(Color.FromRgb(248, 248, 248)); // رمادي فاتح للقوائم
        }

        // ألوان الوضع الليلي
        public static class DarkTheme
        {
            public static readonly SolidColorBrush Background = new(Color.FromRgb(18, 18, 18)); // أسود داكن
            public static readonly SolidColorBrush Foreground = new(Color.FromRgb(255, 255, 255)); // أبيض
            public static readonly SolidColorBrush HeaderBackground = new(Color.FromRgb(25, 25, 25)); // أسود أفتح قليلاً
            public static readonly SolidColorBrush HeaderForeground = new(Color.FromRgb(255, 255, 255)); // أبيض
            public static readonly SolidColorBrush CardBackground = new(Color.FromRgb(33, 33, 33)); // رمادي داكن
            public static readonly SolidColorBrush BorderBrush = new(Color.FromRgb(66, 66, 66)); // رمادي متوسط
            public static readonly SolidColorBrush ButtonBackground = new(Color.FromRgb(76, 175, 80)); // أخضر (نفس اللون)
            public static readonly SolidColorBrush ButtonHover = new(Color.FromRgb(102, 187, 106)); // أخضر فاتح
            public static readonly SolidColorBrush SecondaryText = new(Color.FromRgb(158, 158, 158)); // رمادي فاتح
            public static readonly SolidColorBrush MenuBackground = new(Color.FromRgb(28, 28, 28)); // رمادي داكن للقوائم
        }

        // الألوان الحالية
        public SolidColorBrush Background => _isDarkMode ? DarkTheme.Background : LightTheme.Background;
        public SolidColorBrush Foreground => _isDarkMode ? DarkTheme.Foreground : LightTheme.Foreground;
        public SolidColorBrush HeaderBackground => _isDarkMode ? DarkTheme.HeaderBackground : LightTheme.HeaderBackground;
        public SolidColorBrush HeaderForeground => _isDarkMode ? DarkTheme.HeaderForeground : LightTheme.HeaderForeground;
        public SolidColorBrush CardBackground => _isDarkMode ? DarkTheme.CardBackground : LightTheme.CardBackground;
        public SolidColorBrush BorderBrush => _isDarkMode ? DarkTheme.BorderBrush : LightTheme.BorderBrush;
        public SolidColorBrush ButtonBackground => _isDarkMode ? DarkTheme.ButtonBackground : LightTheme.ButtonBackground;
        public SolidColorBrush ButtonHover => _isDarkMode ? DarkTheme.ButtonHover : LightTheme.ButtonHover;
        public SolidColorBrush SecondaryText => _isDarkMode ? DarkTheme.SecondaryText : LightTheme.SecondaryText;
        public SolidColorBrush MenuBackground => _isDarkMode ? DarkTheme.MenuBackground : LightTheme.MenuBackground;

        public void ToggleTheme()
        {
            IsDarkMode = !IsDarkMode;
        }

        private void ApplyTheme()
        {
            // تطبيق الثيم على الموارد العامة
            var app = Application.Current;
            if (app != null)
            {
                app.Resources["DarkModeBackground"] = Background;
                app.Resources["DarkModeForeground"] = Foreground;
                app.Resources["DarkModeHeaderBackground"] = HeaderBackground;
                app.Resources["DarkModeHeaderForeground"] = HeaderForeground;
                app.Resources["DarkModeCardBackground"] = CardBackground;
                app.Resources["DarkModeBorderBrush"] = BorderBrush;
                app.Resources["DarkModeButtonBackground"] = ButtonBackground;
                app.Resources["DarkModeButtonHover"] = ButtonHover;
                app.Resources["DarkModeSecondaryText"] = SecondaryText;
                app.Resources["DarkModeMenuBackground"] = MenuBackground;
            }

            // إشعار بتغيير الثيم
            ThemeChanged?.Invoke(this, EventArgs.Empty);
        }

        public event EventHandler? ThemeChanged;
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // طرق مساعدة للحصول على الألوان
        public static SolidColorBrush GetCurrentBackground() => Instance.Background;
        public static SolidColorBrush GetCurrentForeground() => Instance.Foreground;
        public static SolidColorBrush GetCurrentHeaderBackground() => Instance.HeaderBackground;
        public static SolidColorBrush GetCurrentCardBackground() => Instance.CardBackground;

        // حفظ واستعادة إعدادات الثيم
        public void SaveThemeSettings()
        {
            try
            {
                // يمكن إضافة حفظ الإعدادات هنا لاحقاً
                System.Diagnostics.Debug.WriteLine($"تم حفظ إعدادات الثيم: {(_isDarkMode ? "ليلي" : "عادي")}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ إعدادات الثيم: {ex.Message}");
            }
        }

        public void LoadThemeSettings()
        {
            try
            {
                // يمكن إضافة تحميل الإعدادات هنا لاحقاً
                IsDarkMode = false; // القيمة الافتراضية
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل إعدادات الثيم: {ex.Message}");
                IsDarkMode = false; // القيمة الافتراضية
            }
        }
    }
}
