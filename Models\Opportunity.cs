using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج الفرصة التجارية
    /// </summary>
    public class Opportunity : INotifyPropertyChanged
    {
        private int _id;
        private string _opportunityNumber = string.Empty;
        private int _customerId;
        private int? _contactId;
        private string _title = string.Empty;
        private string _description = string.Empty;
        private OpportunityStage _stage = OpportunityStage.Qualification;
        private OpportunityStatus _status = OpportunityStatus.Open;
        private OpportunityPriority _priority = OpportunityPriority.Medium;
        private OpportunitySource _source = OpportunitySource.Website;
        private decimal _value;
        private decimal _probability;
        private decimal _weightedValue;
        private string _currency = "SAR";
        private DateTime _expectedCloseDate = DateTime.Now.AddDays(30);
        private DateTime? _actualCloseDate;
        private string _assignedSalesperson = string.Empty;
        private string _competitorInfo = string.Empty;
        private string _nextSteps = string.Empty;
        private string _lossReason = string.Empty;
        private string _notes = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private string _createdBy = string.Empty;

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string OpportunityNumber
        {
            get => _opportunityNumber;
            set
            {
                if (_opportunityNumber != value)
                {
                    _opportunityNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? ContactId
        {
            get => _contactId;
            set
            {
                if (_contactId != value)
                {
                    _contactId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public OpportunityStage Stage
        {
            get => _stage;
            set
            {
                if (_stage != value)
                {
                    _stage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StageDisplay));
                    OnPropertyChanged(nameof(StageColor));
                    OnPropertyChanged(nameof(StageProgress));
                    UpdateProbability();
                }
            }
        }

        public OpportunityStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsOpen));
                }
            }
        }

        public OpportunityPriority Priority
        {
            get => _priority;
            set
            {
                if (_priority != value)
                {
                    _priority = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PriorityDisplay));
                    OnPropertyChanged(nameof(PriorityColor));
                }
            }
        }

        public OpportunitySource Source
        {
            get => _source;
            set
            {
                if (_source != value)
                {
                    _source = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(SourceDisplay));
                }
            }
        }

        public decimal Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    _value = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedValue));
                    UpdateWeightedValue();
                }
            }
        }

        public decimal Probability
        {
            get => _probability;
            set
            {
                if (_probability != value)
                {
                    _probability = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedProbability));
                    UpdateWeightedValue();
                }
            }
        }

        public decimal WeightedValue
        {
            get => _weightedValue;
            set
            {
                if (_weightedValue != value)
                {
                    _weightedValue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedWeightedValue));
                }
            }
        }

        public string Currency
        {
            get => _currency;
            set
            {
                if (_currency != value)
                {
                    _currency = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedValue));
                    OnPropertyChanged(nameof(FormattedWeightedValue));
                }
            }
        }

        public DateTime ExpectedCloseDate
        {
            get => _expectedCloseDate;
            set
            {
                if (_expectedCloseDate != value)
                {
                    _expectedCloseDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedExpectedCloseDate));
                    OnPropertyChanged(nameof(DaysToClose));
                    OnPropertyChanged(nameof(IsOverdue));
                }
            }
        }

        public DateTime? ActualCloseDate
        {
            get => _actualCloseDate;
            set
            {
                if (_actualCloseDate != value)
                {
                    _actualCloseDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedActualCloseDate));
                }
            }
        }

        public string AssignedSalesperson
        {
            get => _assignedSalesperson;
            set
            {
                if (_assignedSalesperson != value)
                {
                    _assignedSalesperson = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CompetitorInfo
        {
            get => _competitorInfo;
            set
            {
                if (_competitorInfo != value)
                {
                    _competitorInfo = value;
                    OnPropertyChanged();
                }
            }
        }

        public string NextSteps
        {
            get => _nextSteps;
            set
            {
                if (_nextSteps != value)
                {
                    _nextSteps = value;
                    OnPropertyChanged();
                }
            }
        }

        public string LossReason
        {
            get => _lossReason;
            set
            {
                if (_lossReason != value)
                {
                    _lossReason = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsOpen => Status == OpportunityStatus.Open;
        public bool IsOverdue => IsOpen && ExpectedCloseDate < DateTime.Today;

        public int DaysToClose
        {
            get
            {
                if (!IsOpen) return 0;
                return (int)(ExpectedCloseDate - DateTime.Today).TotalDays;
            }
        }

        public int StageProgress
        {
            get
            {
                return Stage switch
                {
                    OpportunityStage.Qualification => 10,
                    OpportunityStage.NeedsAnalysis => 25,
                    OpportunityStage.Proposal => 50,
                    OpportunityStage.Negotiation => 75,
                    OpportunityStage.Closing => 90,
                    _ => 0
                };
            }
        }

        // Display Properties
        public string StageDisplay
        {
            get
            {
                return Stage switch
                {
                    OpportunityStage.Qualification => "تأهيل",
                    OpportunityStage.NeedsAnalysis => "تحليل الاحتياجات",
                    OpportunityStage.Proposal => "عرض سعر",
                    OpportunityStage.Negotiation => "تفاوض",
                    OpportunityStage.Closing => "إغلاق",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    OpportunityStatus.Open => "مفتوح",
                    OpportunityStatus.Won => "فوز",
                    OpportunityStatus.Lost => "خسارة",
                    OpportunityStatus.Cancelled => "ملغي",
                    _ => "غير محدد"
                };
            }
        }

        public string PriorityDisplay
        {
            get
            {
                return Priority switch
                {
                    OpportunityPriority.Low => "منخفض",
                    OpportunityPriority.Medium => "متوسط",
                    OpportunityPriority.High => "عالي",
                    OpportunityPriority.Critical => "حرج",
                    _ => "غير محدد"
                };
            }
        }

        public string SourceDisplay
        {
            get
            {
                return Source switch
                {
                    OpportunitySource.Website => "موقع إلكتروني",
                    OpportunitySource.SocialMedia => "وسائل التواصل",
                    OpportunitySource.Referral => "إحالة",
                    OpportunitySource.Advertisement => "إعلان",
                    OpportunitySource.Event => "فعالية",
                    OpportunitySource.ColdCall => "اتصال بارد",
                    OpportunitySource.Email => "بريد إلكتروني",
                    OpportunitySource.Partner => "شريك",
                    OpportunitySource.Other => "أخرى",
                    _ => "غير محدد"
                };
            }
        }

        public string StageColor
        {
            get
            {
                return Stage switch
                {
                    OpportunityStage.Qualification => "Orange",
                    OpportunityStage.NeedsAnalysis => "Blue",
                    OpportunityStage.Proposal => "Purple",
                    OpportunityStage.Negotiation => "Teal",
                    OpportunityStage.Closing => "Indigo",
                    _ => "Gray"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    OpportunityStatus.Open => "Blue",
                    OpportunityStatus.Won => "Green",
                    OpportunityStatus.Lost => "Red",
                    OpportunityStatus.Cancelled => "Gray",
                    _ => "Gray"
                };
            }
        }

        public string PriorityColor
        {
            get
            {
                return Priority switch
                {
                    OpportunityPriority.Low => "Green",
                    OpportunityPriority.Medium => "Orange",
                    OpportunityPriority.High => "Red",
                    OpportunityPriority.Critical => "Purple",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedValue => Value.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedWeightedValue => WeightedValue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedProbability => $"{Probability:F0}%";
        public string FormattedExpectedCloseDate => ExpectedCloseDate.ToString("dd/MM/yyyy");
        public string FormattedActualCloseDate => ActualCloseDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        private void UpdateProbability()
        {
            Probability = Stage switch
            {
                OpportunityStage.Qualification => 10,
                OpportunityStage.NeedsAnalysis => 25,
                OpportunityStage.Proposal => 50,
                OpportunityStage.Negotiation => 75,
                OpportunityStage.Closing => 90,
                _ => 0
            };
        }

        private void UpdateWeightedValue()
        {
            WeightedValue = Value * (Probability / 100);
        }

        public void MoveToNextStage()
        {
            Stage = Stage switch
            {
                OpportunityStage.Qualification => OpportunityStage.NeedsAnalysis,
                OpportunityStage.NeedsAnalysis => OpportunityStage.Proposal,
                OpportunityStage.Proposal => OpportunityStage.Negotiation,
                OpportunityStage.Negotiation => OpportunityStage.Closing,
                OpportunityStage.Closing => OpportunityStage.Closing,
                _ => Stage
            };
            UpdatedAt = DateTime.Now;
        }

        public void Win()
        {
            Stage = OpportunityStage.Closing;
            Status = OpportunityStatus.Won;
            ActualCloseDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        public void Lose(string reason)
        {
            Stage = OpportunityStage.Closing;
            Status = OpportunityStatus.Lost;
            LossReason = reason;
            ActualCloseDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        public void Cancel()
        {
            Status = OpportunityStatus.Cancelled;
            ActualCloseDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }



    #region Validation

    public class OpportunityValidator : AbstractValidator<Opportunity>
    {
        public OpportunityValidator()
        {
            RuleFor(o => o.OpportunityNumber)
                .NotEmpty().WithMessage("رقم الفرصة مطلوب")
                .MaximumLength(20).WithMessage("رقم الفرصة لا يمكن أن يتجاوز 20 حرف");

            RuleFor(o => o.Title)
                .NotEmpty().WithMessage("عنوان الفرصة مطلوب")
                .MaximumLength(200).WithMessage("عنوان الفرصة لا يمكن أن يتجاوز 200 حرف");

            RuleFor(o => o.CustomerId)
                .GreaterThan(0).WithMessage("العميل مطلوب");

            RuleFor(o => o.Value)
                .GreaterThan(0).WithMessage("قيمة الفرصة يجب أن تكون أكبر من صفر");

            RuleFor(o => o.Probability)
                .InclusiveBetween(0, 100).WithMessage("احتمالية النجاح يجب أن تكون بين 0 و 100");

            RuleFor(o => o.ExpectedCloseDate)
                .GreaterThanOrEqualTo(DateTime.Today).WithMessage("تاريخ الإغلاق المتوقع يجب أن يكون في المستقبل");
        }
    }

    #endregion
}
