<UserControl x:Class="SalesManagementSystem.Controls.FloatingActionButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <UserControl.Resources>
        <!-- FAB Animations -->
        <Storyboard x:Key="FabHoverIn">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1.1" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1.1" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.BlurRadius)"
                           To="24" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.ShadowDepth)"
                           To="8" Duration="0:0:0.2"/>
        </Storyboard>

        <Storyboard x:Key="FabHoverOut">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.BlurRadius)"
                           To="16" Duration="0:0:0.2"/>
            <DoubleAnimation Storyboard.TargetProperty="(Border.Effect).(DropShadowEffect.ShadowDepth)"
                           To="6" Duration="0:0:0.2"/>
        </Storyboard>

        <Storyboard x:Key="FabPress">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="0.95" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="0.95" Duration="0:0:0.1"/>
        </Storyboard>

        <Storyboard x:Key="FabRelease">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1.1" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1.1" Duration="0:0:0.1"/>
        </Storyboard>

        <!-- Extended FAB Animation -->
        <Storyboard x:Key="ExtendFab">
            <DoubleAnimation Storyboard.TargetProperty="Width"
                           Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(TextBlock.Opacity)"
                           From="0" To="1" Duration="0:0:0.2" BeginTime="0:0:0.1"/>
        </Storyboard>

        <Storyboard x:Key="CollapseFab">
            <DoubleAnimation Storyboard.TargetProperty="(TextBlock.Opacity)"
                           From="1" To="0" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="Width"
                           Duration="0:0:0.3" BeginTime="0:0:0.1">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </UserControl.Resources>

    <Grid>
        <Border x:Name="FabBorder"
            Background="{Binding FabBackground, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Width="{Binding FabSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Height="{Binding FabSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
            CornerRadius="{Binding FabCornerRadius, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Cursor="Hand"
            RenderTransformOrigin="0.5,0.5">

        <Border.RenderTransform>
            <ScaleTransform ScaleX="1" ScaleY="1"/>
        </Border.RenderTransform>

        <Border.Effect>
            <DropShadowEffect BlurRadius="16"
                            ShadowDepth="6"
                            Direction="270"
                            Opacity="0.3"
                            Color="#000000"/>
        </Border.Effect>

        <Grid>
            <!-- Normal FAB Content -->
            <Grid x:Name="NormalContent"
                  Visibility="{Binding IsExtended, RelativeSource={RelativeSource AncestorType=UserControl},
                             Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                <materialDesign:PackIcon Kind="{Binding IconKind, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Width="{Binding IconSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Height="{Binding IconSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Foreground="{Binding FabForeground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"/>
            </Grid>

            <!-- Extended FAB Content -->
            <StackPanel x:Name="ExtendedContent"
                       Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Visibility="{Binding IsExtended, RelativeSource={RelativeSource AncestorType=UserControl},
                                  Converter={StaticResource BooleanToVisibilityConverter}}">

                <materialDesign:PackIcon Kind="{Binding IconKind, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Width="{Binding IconSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Height="{Binding IconSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Foreground="{Binding FabForeground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>

                <TextBlock x:Name="ExtendedTextBlock"
                          Text="{Binding ExtendedText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          FontSize="{Binding TextSize, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          FontWeight="Medium"
                          Foreground="{Binding FabForeground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          VerticalAlignment="Center"
                          Opacity="1"/>
            </StackPanel>

            <!-- Ripple Effect -->
            <Border x:Name="RippleContainer"
                    Background="Transparent"
                    CornerRadius="{Binding FabCornerRadius, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    ClipToBounds="True">
                <Ellipse x:Name="RippleEllipse"
                         Fill="{Binding RippleColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                         Opacity="0"
                         RenderTransformOrigin="0.5,0.5">
                    <Ellipse.RenderTransform>
                        <ScaleTransform ScaleX="0" ScaleY="0"/>
                    </Ellipse.RenderTransform>
                </Ellipse>
            </Border>
        </Grid>
        </Border>

        <!-- Mini FABs Container -->
        <StackPanel x:Name="MiniFabsContainer"
                   Orientation="Vertical"
                   VerticalAlignment="Bottom"
                   Margin="0,0,0,80"
                   Visibility="Collapsed"/>
    </Grid>

</UserControl>
