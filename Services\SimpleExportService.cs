using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using OfficeOpenXml;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Services
{
    public class SimpleExportService
    {
        public SimpleExportService()
        {
            // Set EPPlus license context
            OfficeOpenXml.ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
        }

        public async Task<bool> ExportSalesReportToExcelAsync(SalesReport salesReport)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    Title = "حفظ تقرير المبيعات",
                    FileName = $"SalesReport_{DateTime.Now:yyyyMMdd}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    using var package = new ExcelPackage();
                    var worksheet = package.Workbook.Worksheets.Add("تقرير المبيعات");

                    // Headers
                    worksheet.Cells[1, 1].Value = "تقرير المبيعات";
                    worksheet.Cells[2, 1].Value = $"من {salesReport.StartDate:dd/MM/yyyy} إلى {salesReport.EndDate:dd/MM/yyyy}";
                    worksheet.Cells[4, 1].Value = "إجمالي المبيعات";
                    worksheet.Cells[4, 2].Value = salesReport.TotalSales;

                    // Save file
                    await package.SaveAsAsync(new FileInfo(saveFileDialog.FileName));
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصدير Excel");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public async Task<bool> ExportToPdfAsync(object reportData, string reportTitle)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "PDF Files|*.pdf",
                    Title = "حفظ التقرير",
                    FileName = $"{reportTitle}_{DateTime.Now:yyyyMMdd}.pdf"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // Simple text-based PDF export
                    var content = ConvertReportToText(reportData);
                    await File.WriteAllTextAsync(saveFileDialog.FileName.Replace(".pdf", ".txt"), content, Encoding.UTF8);

                    MessageBox.Show("تم حفظ التقرير كملف نصي", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصدير PDF");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public async Task<bool> ExportToCsvAsync<T>(IEnumerable<T> data, string fileName)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV Files|*.csv",
                    Title = "حفظ البيانات",
                    FileName = $"{fileName}_{DateTime.Now:yyyyMMdd}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var csv = new StringBuilder();

                    // Add headers
                    var properties = typeof(T).GetProperties();
                    csv.AppendLine(string.Join(",", properties.Select(p => p.Name)));

                    // Add data
                    foreach (var item in data)
                    {
                        var values = properties.Select(p => p.GetValue(item)?.ToString() ?? "").ToArray();
                        csv.AppendLine(string.Join(",", values));
                    }

                    await File.WriteAllTextAsync(saveFileDialog.FileName, csv.ToString(), Encoding.UTF8);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصدير CSV");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public async Task<bool> ExportToExcelAsync<T>(IEnumerable<T> data, string filePath, string sheetName)
        {
            try
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add(sheetName);

                // Add headers
                var properties = typeof(T).GetProperties();
                for (int i = 0; i < properties.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = properties[i].Name;
                }

                // Add data
                int row = 2;
                foreach (var item in data)
                {
                    for (int i = 0; i < properties.Length; i++)
                    {
                        var value = properties[i].GetValue(item);
                        worksheet.Cells[row, i + 1].Value = value;
                    }
                    row++;
                }

                await package.SaveAsAsync(new FileInfo(filePath));
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصدير Excel");
                return false;
            }
        }

        public async Task<bool> ExportToPdfAsync<T>(IEnumerable<T> data, string filePath, string title)
        {
            try
            {
                var content = ConvertDataToText(data, title);
                await File.WriteAllTextAsync(filePath.Replace(".pdf", ".txt"), content, Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصدير PDF");
                return false;
            }
        }

        private string ConvertReportToText(object reportData)
        {
            var sb = new StringBuilder();
            sb.AppendLine("تقرير النظام");
            sb.AppendLine($"تاريخ الإنشاء: {DateTime.Now}");
            sb.AppendLine(new string('=', 50));

            if (reportData != null)
            {
                var properties = reportData.GetType().GetProperties();
                foreach (var prop in properties)
                {
                    var value = prop.GetValue(reportData);
                    sb.AppendLine($"{prop.Name}: {value}");
                }
            }

            return sb.ToString();
        }

        private string ConvertDataToText<T>(IEnumerable<T> data, string title)
        {
            var sb = new StringBuilder();
            sb.AppendLine(title);
            sb.AppendLine($"تاريخ الإنشاء: {DateTime.Now}");
            sb.AppendLine(new string('=', 50));

            var properties = typeof(T).GetProperties();

            // Headers
            sb.AppendLine(string.Join("\t", properties.Select(p => p.Name)));
            sb.AppendLine(new string('-', 50));

            // Data
            foreach (var item in data)
            {
                var values = properties.Select(p => p.GetValue(item)?.ToString() ?? "").ToArray();
                sb.AppendLine(string.Join("\t", values));
            }

            return sb.ToString();
        }
    }
}
