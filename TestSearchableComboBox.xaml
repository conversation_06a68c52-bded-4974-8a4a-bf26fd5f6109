<Window x:Class="SalesManagementSystem.TestSearchableComboBox"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:controls="clr-namespace:SalesManagementSystem.Controls"
        Title="اختبار البحث المحسن" 
        Height="600" 
        Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="Magnify" 
                                   Width="32" Height="32" 
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="اختبار البحث المحسن في ComboBox" 
                      Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                      VerticalAlignment="Center" 
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Customer Search -->
        <GroupBox Grid.Row="1" Header="البحث في العملاء" 
                 Style="{DynamicResource MaterialDesignCardGroupBox}"
                 Margin="0,0,0,16">
            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <controls:SearchableComboBox Grid.Column="0"
                                           x:Name="CustomerSearchBox"
                                           ItemsSource="{Binding SampleCustomers}"
                                           SelectedItem="{Binding SelectedCustomer}"
                                           DisplayMemberPath="Name"
                                           Hint="ابحث عن عميل..."
                                           ActionButtonIcon="AccountPlus"
                                           ActionButtonTooltip="إضافة عميل جديد"
                                           EnableAddNew="True"
                                           AddNewRequested="CustomerSearchBox_AddNewRequested"/>

                <StackPanel Grid.Column="2">
                    <TextBlock Text="العميل المحدد:" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              Margin="0,0,0,8"/>
                    <TextBlock Text="{Binding SelectedCustomer.Name}" 
                              Style="{DynamicResource MaterialDesignBody1TextBlock}"
                              FontWeight="Medium"/>
                    <TextBlock Text="{Binding SelectedCustomer.Phone}" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              Opacity="0.7"/>
                    <TextBlock Text="{Binding SelectedCustomer.Balance, StringFormat='الرصيد: {0:F2}'}" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              Opacity="0.7"/>
                </StackPanel>
            </Grid>
        </GroupBox>

        <!-- Product Search -->
        <GroupBox Grid.Row="2" Header="البحث في المنتجات" 
                 Style="{DynamicResource MaterialDesignCardGroupBox}"
                 Margin="0,0,0,16">
            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <controls:SearchableComboBox Grid.Column="0"
                                           x:Name="ProductSearchBox"
                                           ItemsSource="{Binding SampleProducts}"
                                           SelectedItem="{Binding SelectedProduct}"
                                           DisplayMemberPath="Name"
                                           Hint="ابحث عن منتج..."
                                           ActionButtonIcon="PackagePlus"
                                           ActionButtonTooltip="إضافة منتج جديد"
                                           EnableAddNew="True"
                                           AddNewRequested="ProductSearchBox_AddNewRequested"/>

                <StackPanel Grid.Column="2">
                    <TextBlock Text="المنتج المحدد:" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              Margin="0,0,0,8"/>
                    <TextBlock Text="{Binding SelectedProduct.Name}" 
                              Style="{DynamicResource MaterialDesignBody1TextBlock}"
                              FontWeight="Medium"/>
                    <TextBlock Text="{Binding SelectedProduct.Code}" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              Opacity="0.7"/>
                    <TextBlock Text="{Binding SelectedProduct.Quantity, StringFormat='المخزون: {0}'}" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              Opacity="0.7"/>
                    <TextBlock Text="{Binding SelectedProduct.SalePrice, StringFormat='السعر: {0:F2}'}" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              Opacity="0.7"/>
                </StackPanel>
            </Grid>
        </GroupBox>

        <!-- Search Tips -->
        <GroupBox Grid.Row="3" Header="نصائح البحث" 
                 Style="{DynamicResource MaterialDesignCardGroupBox}"
                 Margin="0,0,0,16">
            <StackPanel Margin="16">
                <TextBlock Text="• يمكنك البحث بالاسم، الكود، أو رقم الهاتف" 
                          Style="{DynamicResource MaterialDesignBody2TextBlock}"
                          Margin="0,4"/>
                <TextBlock Text="• استخدم مفاتيح الأسهم للتنقل في النتائج" 
                          Style="{DynamicResource MaterialDesignBody2TextBlock}"
                          Margin="0,4"/>
                <TextBlock Text="• اضغط Enter لاختيار العنصر الأول" 
                          Style="{DynamicResource MaterialDesignBody2TextBlock}"
                          Margin="0,4"/>
                <TextBlock Text="• اضغط Escape لإلغاء البحث" 
                          Style="{DynamicResource MaterialDesignBody2TextBlock}"
                          Margin="0,4"/>
                <TextBlock Text="• انقر على زر + لإضافة عنصر جديد" 
                          Style="{DynamicResource MaterialDesignBody2TextBlock}"
                          Margin="0,4"/>
            </StackPanel>
        </GroupBox>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="4" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Center"
                   VerticalAlignment="Bottom"
                   Margin="0,24,0,0">
            
            <Button x:Name="ClearButton"
                   Click="ClearButton_Click"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Width="120"
                   Margin="0,0,12,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Refresh" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="مسح الكل" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <Button x:Name="TestSaleButton"
                   Click="TestSaleButton_Click"
                   Style="{DynamicResource MaterialDesignRaisedButton}"
                   Width="150">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ShoppingCart" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="اختبار فاتورة المبيعات" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Window>
