using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج تحليلات الأعمال
    /// </summary>
    public class BusinessAnalytics : INotifyPropertyChanged
    {
        private int _id;
        private string _reportName = string.Empty;
        private string _reportCode = string.Empty;
        private AnalyticsType _analyticsType = AnalyticsType.Sales;
        private ReportPeriod _period = ReportPeriod.ThisMonth;
        private DateTime _startDate = DateTime.Now.AddMonths(-1);
        private DateTime _endDate = DateTime.Now;
        private string _dataSource = string.Empty;
        private string _filters = string.Empty;
        private string _metrics = string.Empty;
        private string _dimensions = string.Empty;
        private decimal _totalRevenue;
        private decimal _totalCost;
        private decimal _profit;
        private decimal _profitMargin;
        private int _totalTransactions;
        private decimal _averageOrderValue;
        private int _totalCustomers;
        private int _newCustomers;
        private decimal _customerRetentionRate;
        private decimal _customerLifetimeValue;
        private string _topProducts = string.Empty;
        private string _topCategories = string.Empty;
        private string _topCustomers = string.Empty;
        private string _salesTrends = string.Empty;
        private string _seasonalPatterns = string.Empty;
        private string _insights = string.Empty;
        private string _recommendations = string.Empty;
        private AnalyticsStatus _status = AnalyticsStatus.Generated;
        private DateTime _generatedAt = DateTime.Now;
        private string _generatedBy = string.Empty;
        private DateTime? _lastUpdated;
        private ObservableCollection<AnalyticsDataPoint> _dataPoints = new();
        private ObservableCollection<AnalyticsChart> _charts = new();
        private ObservableCollection<KPI> _kpis = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ReportName
        {
            get => _reportName;
            set
            {
                if (_reportName != value)
                {
                    _reportName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ReportCode
        {
            get => _reportCode;
            set
            {
                if (_reportCode != value)
                {
                    _reportCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public AnalyticsType AnalyticsType
        {
            get => _analyticsType;
            set
            {
                if (_analyticsType != value)
                {
                    _analyticsType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(AnalyticsTypeDisplay));
                    OnPropertyChanged(nameof(AnalyticsTypeIcon));
                }
            }
        }

        public ReportPeriod Period
        {
            get => _period;
            set
            {
                if (_period != value)
                {
                    _period = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PeriodDisplay));
                }
            }
        }

        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (_startDate != value)
                {
                    _startDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedStartDate));
                    OnPropertyChanged(nameof(PeriodDays));
                }
            }
        }

        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                if (_endDate != value)
                {
                    _endDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedEndDate));
                    OnPropertyChanged(nameof(PeriodDays));
                }
            }
        }

        public string DataSource
        {
            get => _dataSource;
            set
            {
                if (_dataSource != value)
                {
                    _dataSource = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Filters
        {
            get => _filters;
            set
            {
                if (_filters != value)
                {
                    _filters = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Metrics
        {
            get => _metrics;
            set
            {
                if (_metrics != value)
                {
                    _metrics = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Dimensions
        {
            get => _dimensions;
            set
            {
                if (_dimensions != value)
                {
                    _dimensions = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal TotalRevenue
        {
            get => _totalRevenue;
            set
            {
                if (_totalRevenue != value)
                {
                    _totalRevenue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalRevenue));
                    CalculateProfit();
                }
            }
        }

        public decimal TotalCost
        {
            get => _totalCost;
            set
            {
                if (_totalCost != value)
                {
                    _totalCost = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalCost));
                    CalculateProfit();
                }
            }
        }

        public decimal Profit
        {
            get => _profit;
            set
            {
                if (_profit != value)
                {
                    _profit = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedProfit));
                    OnPropertyChanged(nameof(ProfitColor));
                }
            }
        }

        public decimal ProfitMargin
        {
            get => _profitMargin;
            set
            {
                if (_profitMargin != value)
                {
                    _profitMargin = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedProfitMargin));
                    OnPropertyChanged(nameof(ProfitMarginColor));
                }
            }
        }

        public int TotalTransactions
        {
            get => _totalTransactions;
            set
            {
                if (_totalTransactions != value)
                {
                    _totalTransactions = value;
                    OnPropertyChanged();
                    CalculateAverageOrderValue();
                }
            }
        }

        public decimal AverageOrderValue
        {
            get => _averageOrderValue;
            set
            {
                if (_averageOrderValue != value)
                {
                    _averageOrderValue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedAverageOrderValue));
                }
            }
        }

        public int TotalCustomers
        {
            get => _totalCustomers;
            set
            {
                if (_totalCustomers != value)
                {
                    _totalCustomers = value;
                    OnPropertyChanged();
                    CalculateCustomerMetrics();
                }
            }
        }

        public int NewCustomers
        {
            get => _newCustomers;
            set
            {
                if (_newCustomers != value)
                {
                    _newCustomers = value;
                    OnPropertyChanged();
                    CalculateCustomerMetrics();
                }
            }
        }

        public decimal CustomerRetentionRate
        {
            get => _customerRetentionRate;
            set
            {
                if (_customerRetentionRate != value)
                {
                    _customerRetentionRate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCustomerRetentionRate));
                    OnPropertyChanged(nameof(RetentionRateColor));
                }
            }
        }

        public decimal CustomerLifetimeValue
        {
            get => _customerLifetimeValue;
            set
            {
                if (_customerLifetimeValue != value)
                {
                    _customerLifetimeValue = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCustomerLifetimeValue));
                }
            }
        }

        public string TopProducts
        {
            get => _topProducts;
            set
            {
                if (_topProducts != value)
                {
                    _topProducts = value;
                    OnPropertyChanged();
                }
            }
        }

        public string TopCategories
        {
            get => _topCategories;
            set
            {
                if (_topCategories != value)
                {
                    _topCategories = value;
                    OnPropertyChanged();
                }
            }
        }

        public string TopCustomers
        {
            get => _topCustomers;
            set
            {
                if (_topCustomers != value)
                {
                    _topCustomers = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SalesTrends
        {
            get => _salesTrends;
            set
            {
                if (_salesTrends != value)
                {
                    _salesTrends = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SeasonalPatterns
        {
            get => _seasonalPatterns;
            set
            {
                if (_seasonalPatterns != value)
                {
                    _seasonalPatterns = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Insights
        {
            get => _insights;
            set
            {
                if (_insights != value)
                {
                    _insights = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Recommendations
        {
            get => _recommendations;
            set
            {
                if (_recommendations != value)
                {
                    _recommendations = value;
                    OnPropertyChanged();
                }
            }
        }

        public AnalyticsStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                }
            }
        }

        public DateTime GeneratedAt
        {
            get => _generatedAt;
            set
            {
                if (_generatedAt != value)
                {
                    _generatedAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedGeneratedAt));
                }
            }
        }

        public string GeneratedBy
        {
            get => _generatedBy;
            set
            {
                if (_generatedBy != value)
                {
                    _generatedBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? LastUpdated
        {
            get => _lastUpdated;
            set
            {
                if (_lastUpdated != value)
                {
                    _lastUpdated = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedLastUpdated));
                }
            }
        }

        public ObservableCollection<AnalyticsDataPoint> DataPoints
        {
            get => _dataPoints;
            set
            {
                if (_dataPoints != value)
                {
                    _dataPoints = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<AnalyticsChart> Charts
        {
            get => _charts;
            set
            {
                if (_charts != value)
                {
                    _charts = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<KPI> KPIs
        {
            get => _kpis;
            set
            {
                if (_kpis != value)
                {
                    _kpis = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public int PeriodDays => (int)(EndDate - StartDate).TotalDays + 1;

        // Display Properties
        public string AnalyticsTypeDisplay
        {
            get
            {
                return AnalyticsType switch
                {
                    AnalyticsType.Sales => "تحليل المبيعات",
                    AnalyticsType.Customer => "تحليل العملاء",
                    AnalyticsType.Product => "تحليل المنتجات",
                    AnalyticsType.Financial => "التحليل المالي",
                    AnalyticsType.Inventory => "تحليل المخزون",
                    AnalyticsType.Marketing => "تحليل التسويق",
                    AnalyticsType.Performance => "تحليل الأداء",
                    AnalyticsType.Predictive => "التحليل التنبؤي",
                    _ => "غير محدد"
                };
            }
        }

        public string PeriodDisplay
        {
            get
            {
                return Period switch
                {
                    ReportPeriod.Today => "اليوم",
                    ReportPeriod.Yesterday => "أمس",
                    ReportPeriod.ThisWeek => "هذا الأسبوع",
                    ReportPeriod.LastWeek => "الأسبوع الماضي",
                    ReportPeriod.ThisMonth => "هذا الشهر",
                    ReportPeriod.LastMonth => "الشهر الماضي",
                    ReportPeriod.ThisQuarter => "هذا الربع",
                    ReportPeriod.LastQuarter => "الربع الماضي",
                    ReportPeriod.ThisYear => "هذا العام",
                    ReportPeriod.LastYear => "العام الماضي",
                    ReportPeriod.Custom => "مخصص",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    AnalyticsStatus.Generated => "تم الإنشاء",
                    AnalyticsStatus.Processing => "قيد المعالجة",
                    AnalyticsStatus.Completed => "مكتمل",
                    AnalyticsStatus.Failed => "فشل",
                    AnalyticsStatus.Scheduled => "مجدول",
                    _ => "غير محدد"
                };
            }
        }

        public string AnalyticsTypeIcon
        {
            get
            {
                return AnalyticsType switch
                {
                    AnalyticsType.Sales => "TrendingUp",
                    AnalyticsType.Customer => "AccountGroup",
                    AnalyticsType.Product => "Package",
                    AnalyticsType.Financial => "CurrencyUsd",
                    AnalyticsType.Inventory => "Warehouse",
                    AnalyticsType.Marketing => "Bullhorn",
                    AnalyticsType.Performance => "Speedometer",
                    AnalyticsType.Predictive => "Crystal",
                    _ => "ChartLine"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    AnalyticsStatus.Generated => "Blue",
                    AnalyticsStatus.Processing => "Orange",
                    AnalyticsStatus.Completed => "Green",
                    AnalyticsStatus.Failed => "Red",
                    AnalyticsStatus.Scheduled => "Purple",
                    _ => "Gray"
                };
            }
        }

        public string ProfitColor => Profit >= 0 ? "Green" : "Red";
        public string ProfitMarginColor => ProfitMargin >= 20 ? "Green" : ProfitMargin >= 10 ? "Orange" : "Red";
        public string RetentionRateColor => CustomerRetentionRate >= 80 ? "Green" : CustomerRetentionRate >= 60 ? "Orange" : "Red";

        // Formatted Properties
        public string FormattedTotalRevenue => TotalRevenue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalCost => TotalCost.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedProfit => Profit.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedProfitMargin => $"{ProfitMargin:F1}%";
        public string FormattedAverageOrderValue => AverageOrderValue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedCustomerRetentionRate => $"{CustomerRetentionRate:F1}%";
        public string FormattedCustomerLifetimeValue => CustomerLifetimeValue.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedStartDate => StartDate.ToString("dd/MM/yyyy");
        public string FormattedEndDate => EndDate.ToString("dd/MM/yyyy");
        public string FormattedGeneratedAt => GeneratedAt.ToString("dd/MM/yyyy HH:mm");
        public string FormattedLastUpdated => LastUpdated?.ToString("dd/MM/yyyy HH:mm") ?? "لم يتم التحديث";

        #endregion

        #region Methods

        private void CalculateProfit()
        {
            Profit = TotalRevenue - TotalCost;
            if (TotalRevenue > 0)
                ProfitMargin = (Profit / TotalRevenue) * 100;
            else
                ProfitMargin = 0;
        }

        private void CalculateAverageOrderValue()
        {
            if (TotalTransactions > 0)
                AverageOrderValue = TotalRevenue / TotalTransactions;
            else
                AverageOrderValue = 0;
        }

        private void CalculateCustomerMetrics()
        {
            if (TotalCustomers > 0)
            {
                var returningCustomers = TotalCustomers - NewCustomers;
                CustomerRetentionRate = (decimal)returningCustomers / TotalCustomers * 100;
            }
            else
            {
                CustomerRetentionRate = 0;
            }
        }

        public void AddDataPoint(AnalyticsDataPoint dataPoint)
        {
            DataPoints.Add(dataPoint);
            LastUpdated = DateTime.Now;
        }

        public void AddChart(AnalyticsChart chart)
        {
            Charts.Add(chart);
            LastUpdated = DateTime.Now;
        }

        public void AddKPI(KPI kpi)
        {
            KPIs.Add(kpi);
            LastUpdated = DateTime.Now;
        }

        public void UpdateStatus(AnalyticsStatus newStatus)
        {
            Status = newStatus;
            LastUpdated = DateTime.Now;
        }

        public void GenerateInsights()
        {
            var insights = new List<string>();

            // تحليل الربحية
            if (ProfitMargin > 20)
                insights.Add("هامش الربح ممتاز ويتجاوز 20%");
            else if (ProfitMargin < 10)
                insights.Add("هامش الربح منخفض ويحتاج إلى تحسين");

            // تحليل الاحتفاظ بالعملاء
            if (CustomerRetentionRate > 80)
                insights.Add("معدل الاحتفاظ بالعملاء ممتاز");
            else if (CustomerRetentionRate < 60)
                insights.Add("معدل الاحتفاظ بالعملاء يحتاج إلى تحسين");

            // تحليل متوسط قيمة الطلب
            if (AverageOrderValue > 500)
                insights.Add("متوسط قيمة الطلب مرتفع");
            else if (AverageOrderValue < 200)
                insights.Add("متوسط قيمة الطلب منخفض - فكر في استراتيجيات البيع المتقاطع");

            Insights = string.Join("\n", insights);
        }

        public void GenerateRecommendations()
        {
            var recommendations = new List<string>();

            // توصيات بناءً على الربحية
            if (ProfitMargin < 15)
            {
                recommendations.Add("راجع هيكل التكاليف وابحث عن فرص لتقليل النفقات");
                recommendations.Add("فكر في زيادة الأسعار للمنتجات عالية الطلب");
            }

            // توصيات بناءً على الاحتفاظ بالعملاء
            if (CustomerRetentionRate < 70)
            {
                recommendations.Add("طور برنامج ولاء العملاء");
                recommendations.Add("حسن خدمة العملاء وتجربة ما بعد البيع");
            }

            // توصيات بناءً على متوسط قيمة الطلب
            if (AverageOrderValue < 300)
            {
                recommendations.Add("طبق استراتيجيات البيع المتقاطع والبيع الإضافي");
                recommendations.Add("قدم عروض الشحن المجاني للطلبات الأكبر");
            }

            Recommendations = string.Join("\n", recommendations);
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// نقطة بيانات التحليلات
    /// </summary>
    public class AnalyticsDataPoint
    {
        public int Id { get; set; }
        public int AnalyticsId { get; set; }
        public string Label { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Category { get; set; } = string.Empty;
        public DateTime Date { get; set; } = DateTime.Now;
        public string MetricType { get; set; } = string.Empty;
        public string FormattedValue => Value.ToString("N2");
    }

    /// <summary>
    /// مخطط التحليلات
    /// </summary>
    public class AnalyticsChart
    {
        public int Id { get; set; }
        public int AnalyticsId { get; set; }
        public string ChartName { get; set; } = string.Empty;
        public ChartType ChartType { get; set; } = ChartType.Line;
        public string DataSeries { get; set; } = string.Empty;
        public string XAxisLabel { get; set; } = string.Empty;
        public string YAxisLabel { get; set; } = string.Empty;
        public string ChartData { get; set; } = string.Empty;
        public string ChartOptions { get; set; } = string.Empty;
    }

    /// <summary>
    /// مؤشر الأداء الرئيسي
    /// </summary>
    public class KPI
    {
        public int Id { get; set; }
        public int AnalyticsId { get; set; }
        public string Name { get; set; } = string.Empty;
        public decimal CurrentValue { get; set; }
        public decimal TargetValue { get; set; }
        public decimal PreviousValue { get; set; }
        public string Unit { get; set; } = string.Empty;
        public KPITrend Trend { get; set; } = KPITrend.Stable;
        public string Description { get; set; } = string.Empty;

        public decimal PercentageChange
        {
            get
            {
                if (PreviousValue == 0) return 0;
                return ((CurrentValue - PreviousValue) / PreviousValue) * 100;
            }
        }

        public string FormattedCurrentValue => CurrentValue.ToString("N2");
        public string FormattedTargetValue => TargetValue.ToString("N2");
        public string FormattedPercentageChange => $"{PercentageChange:+0.0;-0.0;0}%";

        public string TrendColor
        {
            get
            {
                return Trend switch
                {
                    KPITrend.Up => "Green",
                    KPITrend.Down => "Red",
                    KPITrend.Stable => "Orange",
                    _ => "Gray"
                };
            }
        }

        public string TrendIcon
        {
            get
            {
                return Trend switch
                {
                    KPITrend.Up => "TrendingUp",
                    KPITrend.Down => "TrendingDown",
                    KPITrend.Stable => "TrendingFlat",
                    _ => "Minus"
                };
            }
        }
    }

    #endregion

    #region Enums

    public enum AnalyticsType
    {
        Sales,          // تحليل المبيعات
        Customer,       // تحليل العملاء
        Product,        // تحليل المنتجات
        Financial,      // التحليل المالي
        Inventory,      // تحليل المخزون
        Marketing,      // تحليل التسويق
        Performance,    // تحليل الأداء
        Predictive      // التحليل التنبؤي
    }



    public enum AnalyticsStatus
    {
        Generated,      // تم الإنشاء
        Processing,     // قيد المعالجة
        Completed,      // مكتمل
        Failed,         // فشل
        Scheduled       // مجدول
    }

    public enum ChartType
    {
        Line,           // خطي
        Bar,            // أعمدة
        Pie,            // دائري
        Area,           // منطقة
        Scatter,        // نقطي
        Donut,          // كعكة
        Radar           // رادار
    }

    public enum KPITrend
    {
        Up,             // صاعد
        Down,           // هابط
        Stable          // مستقر
    }

    #endregion

    #region Validation

    public class BusinessAnalyticsValidator : AbstractValidator<BusinessAnalytics>
    {
        public BusinessAnalyticsValidator()
        {
            RuleFor(a => a.ReportName)
                .NotEmpty().WithMessage("اسم التقرير مطلوب")
                .MaximumLength(200).WithMessage("اسم التقرير لا يمكن أن يتجاوز 200 حرف");

            RuleFor(a => a.ReportCode)
                .NotEmpty().WithMessage("كود التقرير مطلوب")
                .MaximumLength(50).WithMessage("كود التقرير لا يمكن أن يتجاوز 50 حرف");

            RuleFor(a => a.StartDate)
                .LessThanOrEqualTo(a => a.EndDate).WithMessage("تاريخ البداية يجب أن يكون قبل تاريخ النهاية");

            RuleFor(a => a.EndDate)
                .LessThanOrEqualTo(DateTime.Now).WithMessage("تاريخ النهاية لا يمكن أن يكون في المستقبل");

            RuleFor(a => a.TotalRevenue)
                .GreaterThanOrEqualTo(0).WithMessage("إجمالي الإيرادات لا يمكن أن يكون سالب");

            RuleFor(a => a.TotalCost)
                .GreaterThanOrEqualTo(0).WithMessage("إجمالي التكاليف لا يمكن أن يكون سالب");
        }
    }

    #endregion
}
