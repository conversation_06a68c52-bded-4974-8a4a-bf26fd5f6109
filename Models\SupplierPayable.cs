using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج مستحقات المورد
    /// </summary>
    public class SupplierPayable : INotifyPropertyChanged
    {
        private int _supplierId;
        private string _supplierName = "";
        private string _phone = "";
        private decimal _totalPurchases;
        private decimal _totalPaid;
        private decimal _remainingPayable;
        private DateTime _lastPaymentDate;
        private string _status = "";

        /// <summary>
        /// معرف المورد
        /// </summary>
        public int SupplierId
        {
            get => _supplierId;
            set
            {
                _supplierId = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// اسم المورد
        /// </summary>
        public string SupplierName
        {
            get => _supplierName;
            set
            {
                _supplierName = value ?? "";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        public string Phone
        {
            get => _phone;
            set
            {
                _phone = value ?? "";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// إجمالي المشتريات من المورد
        /// </summary>
        public decimal TotalPurchases
        {
            get => _totalPurchases;
            set
            {
                _totalPurchases = value;
                OnPropertyChanged();
                UpdateRemainingPayable();
            }
        }

        /// <summary>
        /// إجمالي المدفوع للمورد
        /// </summary>
        public decimal TotalPaid
        {
            get => _totalPaid;
            set
            {
                _totalPaid = value;
                OnPropertyChanged();
                UpdateRemainingPayable();
            }
        }

        /// <summary>
        /// المبلغ المتبقي (المستحق للمورد)
        /// </summary>
        public decimal RemainingPayable
        {
            get => _remainingPayable;
            set
            {
                _remainingPayable = value;
                OnPropertyChanged();
                UpdateStatus();
            }
        }

        /// <summary>
        /// تاريخ آخر دفعة للمورد
        /// </summary>
        public DateTime LastPaymentDate
        {
            get => _lastPaymentDate;
            set
            {
                _lastPaymentDate = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// حالة المستحقات
        /// </summary>
        public string Status
        {
            get => _status;
            set
            {
                _status = value ?? "";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// تحديث المبلغ المتبقي
        /// </summary>
        private void UpdateRemainingPayable()
        {
            RemainingPayable = Math.Max(0, TotalPurchases - TotalPaid);
        }

        /// <summary>
        /// تحديث حالة المستحقات
        /// </summary>
        private void UpdateStatus()
        {
            Status = RemainingPayable > 0 ? "مستحقات معلقة" : "مسدد";
        }

        /// <summary>
        /// إضافة دفعة جديدة للمورد
        /// </summary>
        /// <param name="amount">مبلغ الدفعة</param>
        /// <returns>true إذا تمت الإضافة بنجاح</returns>
        public bool AddPayment(decimal amount)
        {
            if (amount <= 0 || amount > RemainingPayable)
                return false;

            TotalPaid += amount;
            LastPaymentDate = DateTime.Now;
            return true;
        }

        /// <summary>
        /// إضافة مشتريات جديدة من المورد
        /// </summary>
        /// <param name="amount">مبلغ المشتريات</param>
        /// <returns>true إذا تمت الإضافة بنجاح</returns>
        public bool AddPurchase(decimal amount)
        {
            if (amount <= 0)
                return false;

            TotalPurchases += amount;
            return true;
        }

        /// <summary>
        /// التحقق من وجود مستحقات معلقة
        /// </summary>
        public bool HasPendingPayables => RemainingPayable > 0;

        /// <summary>
        /// نسبة السداد للمورد
        /// </summary>
        public decimal PaymentPercentage => TotalPurchases > 0 ? (TotalPaid / TotalPurchases) * 100 : 0;

        /// <summary>
        /// عدد الأيام منذ آخر دفعة للمورد
        /// </summary>
        public int DaysSinceLastPayment => (DateTime.Now - LastPaymentDate).Days;

        /// <summary>
        /// مستوى الأولوية للدفع (بناءً على المبلغ والوقت)
        /// </summary>
        public string PaymentPriority
        {
            get
            {
                if (RemainingPayable == 0) return "مسدد";
                if (RemainingPayable > 50000 && DaysSinceLastPayment > 30) return "عالية";
                if (RemainingPayable > 20000 && DaysSinceLastPayment > 15) return "متوسطة";
                return "منخفضة";
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
