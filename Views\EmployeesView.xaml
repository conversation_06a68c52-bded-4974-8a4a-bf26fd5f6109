<UserControl x:Class="SalesManagementSystem.Views.EmployeesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid Margin="20">
        <materialDesign:Card Padding="40">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="AccountGroup" Width="64" Height="64" 
                                       Foreground="{StaticResource PrimaryHueMidBrush}" 
                                       HorizontalAlignment="Center" Margin="0,0,0,20"/>
                <TextBlock Text="إدارة الموظفين" FontSize="28" FontWeight="Bold" 
                         HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <TextBlock Text="سيتم تطوير هذه الصفحة قريباً" FontSize="16" 
                         Foreground="{StaticResource MaterialDesignBodyLight}"
                         HorizontalAlignment="Center" Margin="0,0,0,20"/>
                
                <Button Style="{StaticResource MaterialDesignRaisedButton}" 
                       Content="إضافة موظف جديد" 
                       Click="AddEmployeeButton_Click"
                       Margin="0,10,0,0"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
