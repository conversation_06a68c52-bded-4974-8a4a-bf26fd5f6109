using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج إعدادات النسخ الاحتياطي
    /// </summary>
    public class BackupSettings : INotifyPropertyChanged
    {
        private int _id;
        private string _settingsName = "إعدادات النسخ الاحتياطي الافتراضية";
        private bool _enableAutomaticBackup = true;
        private BackupFrequency _defaultFrequency = BackupFrequency.Daily;
        private TimeSpan _defaultBackupTime = new TimeSpan(2, 0, 0); // 2:00 AM
        private string _defaultBackupPath = string.Empty;
        private bool _enableCloudBackup;
        private string _cloudProvider = string.Empty;
        private string _cloudAccessKey = string.Empty;
        private string _cloudSecretKey = string.Empty;
        private string _cloudBucketName = string.Empty;
        private string _cloudRegion = string.Empty;
        private bool _enableEncryption = true;
        private string _encryptionAlgorithm = "AES-256";
        private string _encryptionKey = string.Empty;
        private bool _enableCompression = true;
        private CompressionLevel _defaultCompressionLevel = CompressionLevel.Medium;
        private int _maxRetentionDays = 30;
        private int _maxBackupCopies = 10;
        private bool _enableIntegrityCheck = true;
        private bool _enableNotifications = true;
        private string _notificationEmails = string.Empty;
        private bool _notifyOnSuccess = true;
        private bool _notifyOnFailure = true;
        private bool _notifyOnWarning = true;
        private int _maxConcurrentJobs = 3;
        private int _backupTimeoutMinutes = 120;
        private bool _enableBandwidthThrottling;
        private int _maxBandwidthMbps = 100;
        private bool _enableScheduleOptimization = true;
        private bool _pauseOnBatteryPower;
        private bool _pauseOnMeteredConnection;
        private string _excludeFileTypes = ".tmp,.log,.cache";
        private string _excludeFolders = "temp,cache,logs";
        private long _maxFileSize = **********; // 1GB
        private bool _enableDifferentialBackup = true;
        private bool _enableIncrementalBackup = true;
        private bool _enableMirrorBackup;
        private string _customScriptPath = string.Empty;
        private bool _runCustomScriptBefore;
        private bool _runCustomScriptAfter;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private string _createdBy = string.Empty;

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SettingsName
        {
            get => _settingsName;
            set
            {
                if (_settingsName != value)
                {
                    _settingsName = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableAutomaticBackup
        {
            get => _enableAutomaticBackup;
            set
            {
                if (_enableAutomaticBackup != value)
                {
                    _enableAutomaticBackup = value;
                    OnPropertyChanged();
                }
            }
        }

        public BackupFrequency DefaultFrequency
        {
            get => _defaultFrequency;
            set
            {
                if (_defaultFrequency != value)
                {
                    _defaultFrequency = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(DefaultFrequencyDisplay));
                }
            }
        }

        public TimeSpan DefaultBackupTime
        {
            get => _defaultBackupTime;
            set
            {
                if (_defaultBackupTime != value)
                {
                    _defaultBackupTime = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDefaultBackupTime));
                }
            }
        }

        public string DefaultBackupPath
        {
            get => _defaultBackupPath;
            set
            {
                if (_defaultBackupPath != value)
                {
                    _defaultBackupPath = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableCloudBackup
        {
            get => _enableCloudBackup;
            set
            {
                if (_enableCloudBackup != value)
                {
                    _enableCloudBackup = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CloudProvider
        {
            get => _cloudProvider;
            set
            {
                if (_cloudProvider != value)
                {
                    _cloudProvider = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CloudProviderDisplay));
                }
            }
        }

        public string CloudAccessKey
        {
            get => _cloudAccessKey;
            set
            {
                if (_cloudAccessKey != value)
                {
                    _cloudAccessKey = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CloudSecretKey
        {
            get => _cloudSecretKey;
            set
            {
                if (_cloudSecretKey != value)
                {
                    _cloudSecretKey = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CloudBucketName
        {
            get => _cloudBucketName;
            set
            {
                if (_cloudBucketName != value)
                {
                    _cloudBucketName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CloudRegion
        {
            get => _cloudRegion;
            set
            {
                if (_cloudRegion != value)
                {
                    _cloudRegion = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableEncryption
        {
            get => _enableEncryption;
            set
            {
                if (_enableEncryption != value)
                {
                    _enableEncryption = value;
                    OnPropertyChanged();
                }
            }
        }

        public string EncryptionAlgorithm
        {
            get => _encryptionAlgorithm;
            set
            {
                if (_encryptionAlgorithm != value)
                {
                    _encryptionAlgorithm = value;
                    OnPropertyChanged();
                }
            }
        }

        public string EncryptionKey
        {
            get => _encryptionKey;
            set
            {
                if (_encryptionKey != value)
                {
                    _encryptionKey = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableCompression
        {
            get => _enableCompression;
            set
            {
                if (_enableCompression != value)
                {
                    _enableCompression = value;
                    OnPropertyChanged();
                }
            }
        }

        public CompressionLevel DefaultCompressionLevel
        {
            get => _defaultCompressionLevel;
            set
            {
                if (_defaultCompressionLevel != value)
                {
                    _defaultCompressionLevel = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(DefaultCompressionLevelDisplay));
                }
            }
        }

        public int MaxRetentionDays
        {
            get => _maxRetentionDays;
            set
            {
                if (_maxRetentionDays != value)
                {
                    _maxRetentionDays = value;
                    OnPropertyChanged();
                }
            }
        }

        public int MaxBackupCopies
        {
            get => _maxBackupCopies;
            set
            {
                if (_maxBackupCopies != value)
                {
                    _maxBackupCopies = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableIntegrityCheck
        {
            get => _enableIntegrityCheck;
            set
            {
                if (_enableIntegrityCheck != value)
                {
                    _enableIntegrityCheck = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableNotifications
        {
            get => _enableNotifications;
            set
            {
                if (_enableNotifications != value)
                {
                    _enableNotifications = value;
                    OnPropertyChanged();
                }
            }
        }

        public string NotificationEmails
        {
            get => _notificationEmails;
            set
            {
                if (_notificationEmails != value)
                {
                    _notificationEmails = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool NotifyOnSuccess
        {
            get => _notifyOnSuccess;
            set
            {
                if (_notifyOnSuccess != value)
                {
                    _notifyOnSuccess = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool NotifyOnFailure
        {
            get => _notifyOnFailure;
            set
            {
                if (_notifyOnFailure != value)
                {
                    _notifyOnFailure = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool NotifyOnWarning
        {
            get => _notifyOnWarning;
            set
            {
                if (_notifyOnWarning != value)
                {
                    _notifyOnWarning = value;
                    OnPropertyChanged();
                }
            }
        }

        public int MaxConcurrentJobs
        {
            get => _maxConcurrentJobs;
            set
            {
                if (_maxConcurrentJobs != value)
                {
                    _maxConcurrentJobs = value;
                    OnPropertyChanged();
                }
            }
        }

        public int BackupTimeoutMinutes
        {
            get => _backupTimeoutMinutes;
            set
            {
                if (_backupTimeoutMinutes != value)
                {
                    _backupTimeoutMinutes = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedBackupTimeout));
                }
            }
        }

        public bool EnableBandwidthThrottling
        {
            get => _enableBandwidthThrottling;
            set
            {
                if (_enableBandwidthThrottling != value)
                {
                    _enableBandwidthThrottling = value;
                    OnPropertyChanged();
                }
            }
        }

        public int MaxBandwidthMbps
        {
            get => _maxBandwidthMbps;
            set
            {
                if (_maxBandwidthMbps != value)
                {
                    _maxBandwidthMbps = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableScheduleOptimization
        {
            get => _enableScheduleOptimization;
            set
            {
                if (_enableScheduleOptimization != value)
                {
                    _enableScheduleOptimization = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool PauseOnBatteryPower
        {
            get => _pauseOnBatteryPower;
            set
            {
                if (_pauseOnBatteryPower != value)
                {
                    _pauseOnBatteryPower = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool PauseOnMeteredConnection
        {
            get => _pauseOnMeteredConnection;
            set
            {
                if (_pauseOnMeteredConnection != value)
                {
                    _pauseOnMeteredConnection = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ExcludeFileTypes
        {
            get => _excludeFileTypes;
            set
            {
                if (_excludeFileTypes != value)
                {
                    _excludeFileTypes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ExcludeFolders
        {
            get => _excludeFolders;
            set
            {
                if (_excludeFolders != value)
                {
                    _excludeFolders = value;
                    OnPropertyChanged();
                }
            }
        }

        public long MaxFileSize
        {
            get => _maxFileSize;
            set
            {
                if (_maxFileSize != value)
                {
                    _maxFileSize = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedMaxFileSize));
                }
            }
        }

        public bool EnableDifferentialBackup
        {
            get => _enableDifferentialBackup;
            set
            {
                if (_enableDifferentialBackup != value)
                {
                    _enableDifferentialBackup = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableIncrementalBackup
        {
            get => _enableIncrementalBackup;
            set
            {
                if (_enableIncrementalBackup != value)
                {
                    _enableIncrementalBackup = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool EnableMirrorBackup
        {
            get => _enableMirrorBackup;
            set
            {
                if (_enableMirrorBackup != value)
                {
                    _enableMirrorBackup = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CustomScriptPath
        {
            get => _customScriptPath;
            set
            {
                if (_customScriptPath != value)
                {
                    _customScriptPath = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool RunCustomScriptBefore
        {
            get => _runCustomScriptBefore;
            set
            {
                if (_runCustomScriptBefore != value)
                {
                    _runCustomScriptBefore = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool RunCustomScriptAfter
        {
            get => _runCustomScriptAfter;
            set
            {
                if (_runCustomScriptAfter != value)
                {
                    _runCustomScriptAfter = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        // Display Properties
        public string DefaultFrequencyDisplay
        {
            get
            {
                return DefaultFrequency switch
                {
                    BackupFrequency.Hourly => "كل ساعة",
                    BackupFrequency.Daily => "يومي",
                    BackupFrequency.Weekly => "أسبوعي",
                    BackupFrequency.Monthly => "شهري",
                    BackupFrequency.Manual => "يدوي",
                    _ => "غير محدد"
                };
            }
        }

        public string DefaultCompressionLevelDisplay
        {
            get
            {
                return DefaultCompressionLevel switch
                {
                    CompressionLevel.None => "بدون ضغط",
                    CompressionLevel.Low => "ضغط منخفض",
                    CompressionLevel.Medium => "ضغط متوسط",
                    CompressionLevel.High => "ضغط عالي",
                    CompressionLevel.Maximum => "ضغط أقصى",
                    _ => "غير محدد"
                };
            }
        }

        public string CloudProviderDisplay
        {
            get
            {
                return CloudProvider switch
                {
                    "aws" => "Amazon S3",
                    "azure" => "Microsoft Azure",
                    "gcp" => "Google Cloud",
                    "dropbox" => "Dropbox",
                    "onedrive" => "OneDrive",
                    _ => CloudProvider
                };
            }
        }

        // Formatted Properties
        public string FormattedDefaultBackupTime => DefaultBackupTime.ToString(@"hh\:mm");
        public string FormattedBackupTimeout => $"{BackupTimeoutMinutes} دقيقة";
        public string FormattedMaxFileSize => FormatFileSize(MaxFileSize);
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";
            
            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:F2} {sizes[order]}";
        }

        public void ApplyToJob(BackupJob job)
        {
            if (job == null) return;

            job.IsEncrypted = EnableEncryption;
            job.EncryptionKey = EncryptionKey;
            job.IsCompressed = EnableCompression;
            job.CompressionLevel = DefaultCompressionLevel;
            job.RetentionDays = MaxRetentionDays;
            job.MaxBackupCopies = MaxBackupCopies;
            job.VerifyIntegrity = EnableIntegrityCheck;
            job.SendNotifications = EnableNotifications;
            job.NotificationEmails = NotificationEmails;
            
            if (EnableCloudBackup)
            {
                job.CloudProvider = CloudProvider;
                job.CloudCredentials = $"{CloudAccessKey}:{CloudSecretKey}:{CloudBucketName}:{CloudRegion}";
            }
        }

        public void LoadDefaults()
        {
            EnableAutomaticBackup = true;
            DefaultFrequency = BackupFrequency.Daily;
            DefaultBackupTime = new TimeSpan(2, 0, 0);
            EnableEncryption = true;
            EncryptionAlgorithm = "AES-256";
            EnableCompression = true;
            DefaultCompressionLevel = CompressionLevel.Medium;
            MaxRetentionDays = 30;
            MaxBackupCopies = 10;
            EnableIntegrityCheck = true;
            EnableNotifications = true;
            NotifyOnSuccess = true;
            NotifyOnFailure = true;
            NotifyOnWarning = true;
            MaxConcurrentJobs = 3;
            BackupTimeoutMinutes = 120;
            ExcludeFileTypes = ".tmp,.log,.cache";
            ExcludeFolders = "temp,cache,logs";
            MaxFileSize = **********; // 1GB
            EnableDifferentialBackup = true;
            EnableIncrementalBackup = true;
            UpdatedAt = DateTime.Now;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Validation

    public class BackupSettingsValidator : AbstractValidator<BackupSettings>
    {
        public BackupSettingsValidator()
        {
            RuleFor(s => s.SettingsName)
                .NotEmpty().WithMessage("اسم الإعدادات مطلوب")
                .MaximumLength(200).WithMessage("اسم الإعدادات لا يمكن أن يتجاوز 200 حرف");

            RuleFor(s => s.DefaultBackupPath)
                .NotEmpty().When(s => s.EnableAutomaticBackup)
                .WithMessage("مسار النسخ الاحتياطي الافتراضي مطلوب");

            RuleFor(s => s.CloudAccessKey)
                .NotEmpty().When(s => s.EnableCloudBackup)
                .WithMessage("مفتاح الوصول السحابي مطلوب");

            RuleFor(s => s.CloudSecretKey)
                .NotEmpty().When(s => s.EnableCloudBackup)
                .WithMessage("المفتاح السري السحابي مطلوب");

            RuleFor(s => s.CloudBucketName)
                .NotEmpty().When(s => s.EnableCloudBackup)
                .WithMessage("اسم الحاوية السحابية مطلوب");

            RuleFor(s => s.EncryptionKey)
                .NotEmpty().When(s => s.EnableEncryption)
                .WithMessage("مفتاح التشفير مطلوب")
                .MinimumLength(8).When(s => s.EnableEncryption)
                .WithMessage("مفتاح التشفير يجب أن يكون 8 أحرف على الأقل");

            RuleFor(s => s.MaxRetentionDays)
                .GreaterThan(0).WithMessage("فترة الاحتفاظ يجب أن تكون أكبر من صفر")
                .LessThanOrEqualTo(3650).WithMessage("فترة الاحتفاظ لا يمكن أن تتجاوز 10 سنوات");

            RuleFor(s => s.MaxBackupCopies)
                .GreaterThan(0).WithMessage("عدد النسخ الأقصى يجب أن يكون أكبر من صفر")
                .LessThanOrEqualTo(100).WithMessage("عدد النسخ الأقصى لا يمكن أن يتجاوز 100");

            RuleFor(s => s.MaxConcurrentJobs)
                .GreaterThan(0).WithMessage("عدد المهام المتزامنة يجب أن يكون أكبر من صفر")
                .LessThanOrEqualTo(10).WithMessage("عدد المهام المتزامنة لا يمكن أن يتجاوز 10");

            RuleFor(s => s.BackupTimeoutMinutes)
                .GreaterThan(0).WithMessage("مهلة النسخ الاحتياطي يجب أن تكون أكبر من صفر")
                .LessThanOrEqualTo(1440).WithMessage("مهلة النسخ الاحتياطي لا يمكن أن تتجاوز 24 ساعة");

            RuleFor(s => s.MaxBandwidthMbps)
                .GreaterThan(0).When(s => s.EnableBandwidthThrottling)
                .WithMessage("الحد الأقصى لعرض النطاق يجب أن يكون أكبر من صفر");

            RuleFor(s => s.MaxFileSize)
                .GreaterThan(0).WithMessage("الحد الأقصى لحجم الملف يجب أن يكون أكبر من صفر");

            RuleFor(s => s.NotificationEmails)
                .NotEmpty().When(s => s.EnableNotifications)
                .WithMessage("بريد الإشعارات مطلوب عند تفعيل الإشعارات");
        }
    }

    #endregion
}
