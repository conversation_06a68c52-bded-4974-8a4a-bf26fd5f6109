using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.Views.Dialogs;
using SalesManagementSystem.Views;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Controls
{
    /// <summary>
    /// مركز الإشعارات - عرض وإدارة جميع الإشعارات
    /// </summary>
    public partial class NotificationCenter : UserControl, INotifyPropertyChanged
    {
        private readonly NotificationService _notificationService;

        private ObservableCollection<Notification> _allNotifications = new();
        private ObservableCollection<Notification> _unreadNotifications = new();
        private ObservableCollection<Notification> _importantNotifications = new();
        private int _unreadCount;
        private bool _hasNotifications;
        private bool _hasUnreadNotifications;

        public NotificationCenter()
        {
            InitializeComponent();
            DataContext = this;

            var dbService = new DatabaseService();
            var settingsService = new SettingsService(dbService);
            _notificationService = new NotificationService(dbService, settingsService);

            InitializeCommands();
            _ = LoadNotificationsAsync();

            // الاشتراك في أحداث الإشعارات
            _notificationService.NotificationReceived += OnNotificationReceived;
            _notificationService.NotificationsChanged += OnNotificationsChanged;
        }

        #region Properties

        public ObservableCollection<Notification> AllNotifications
        {
            get => _allNotifications;
            set
            {
                if (_allNotifications != value)
                {
                    _allNotifications = value;
                    OnPropertyChanged();
                    UpdateCounts();
                }
            }
        }

        public ObservableCollection<Notification> UnreadNotifications
        {
            get => _unreadNotifications;
            set
            {
                if (_unreadNotifications != value)
                {
                    _unreadNotifications = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<Notification> ImportantNotifications
        {
            get => _importantNotifications;
            set
            {
                if (_importantNotifications != value)
                {
                    _importantNotifications = value;
                    OnPropertyChanged();
                }
            }
        }

        public int UnreadCount
        {
            get => _unreadCount;
            set
            {
                if (_unreadCount != value)
                {
                    _unreadCount = value;
                    OnPropertyChanged();
                    HasUnreadNotifications = value > 0;
                }
            }
        }

        public bool HasNotifications
        {
            get => _hasNotifications;
            set
            {
                if (_hasNotifications != value)
                {
                    _hasNotifications = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool HasUnreadNotifications
        {
            get => _hasUnreadNotifications;
            set
            {
                if (_hasUnreadNotifications != value)
                {
                    _hasUnreadNotifications = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Commands

        public ICommand MarkAsReadCommand { get; private set; } = null!;
        public ICommand MarkAllAsReadCommand { get; private set; } = null!;
        public ICommand DeleteNotificationCommand { get; private set; } = null!;
        public ICommand SnoozeNotificationCommand { get; private set; } = null!;
        public ICommand RefreshCommand { get; private set; } = null!;
        public ICommand ClearAllCommand { get; private set; } = null!;
        public ICommand OpenSettingsCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            MarkAsReadCommand = new RelayCommand<Notification>(async (notification) => await MarkAsReadAsync(notification));
            MarkAllAsReadCommand = new RelayCommand(async () => await MarkAllAsReadAsync());
            DeleteNotificationCommand = new RelayCommand<Notification>(async (notification) => await DeleteNotificationAsync(notification));
            SnoozeNotificationCommand = new RelayCommand<Notification>((notification) => SnoozeNotification(notification));
            RefreshCommand = new RelayCommand(async () => await LoadNotificationsAsync());
            ClearAllCommand = new RelayCommand(async () => await ClearAllNotificationsAsync());
            OpenSettingsCommand = new RelayCommand(() => OpenNotificationSettings());
        }

        #endregion

        #region Events

        public event EventHandler<Notification>? NotificationClicked;
        public event PropertyChangedEventHandler? PropertyChanged;

        #endregion

        #region Methods

        /// <summary>
        /// تحميل الإشعارات
        /// </summary>
        private async Task LoadNotificationsAsync()
        {
            try
            {
                await _notificationService.InitializeAsync();

                var allNotifications = await _notificationService.GetAllNotificationsAsync(100);
                var unreadNotifications = await _notificationService.GetUnreadNotificationsAsync();

                AllNotifications = new ObservableCollection<Notification>(allNotifications);
                UnreadNotifications = new ObservableCollection<Notification>(unreadNotifications);

                // الإشعارات المهمة (أولوية عالية أو حرجة)
                var importantNotifications = allNotifications.Where(n =>
                    n.Priority?.ToLower() == "high" || n.Priority?.ToLower() == "critical");
                ImportantNotifications = new ObservableCollection<Notification>(importantNotifications);

                UpdateCounts();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل الإشعارات");
                MessageBox.Show($"خطأ في تحميل الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث العدادات
        /// </summary>
        private void UpdateCounts()
        {
            UnreadCount = UnreadNotifications.Count;
            HasNotifications = AllNotifications.Count == 0;
        }

        /// <summary>
        /// تحديد إشعار كمقروء
        /// </summary>
        private async Task MarkAsReadAsync(Notification? notification)
        {
            if (notification == null) return;

            try
            {
                var success = await _notificationService.MarkAsReadAsync(notification.Id);
                if (success)
                {
                    notification.IsRead = true;
                    UnreadNotifications.Remove(notification);
                    UpdateCounts();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديد الإشعار كمقروء: {notification.Id}");
                MessageBox.Show($"خطأ في تحديد الإشعار كمقروء: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديد جميع الإشعارات كمقروءة
        /// </summary>
        private async Task MarkAllAsReadAsync()
        {
            try
            {
                var result = MessageBox.Show("هل تريد تحديد جميع الإشعارات كمقروءة؟", "تأكيد",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var success = await _notificationService.MarkAllAsReadAsync();
                    if (success)
                    {
                        foreach (var notification in AllNotifications)
                        {
                            notification.IsRead = true;
                        }
                        UnreadNotifications.Clear();
                        UpdateCounts();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحديد جميع الإشعارات كمقروءة");
                MessageBox.Show($"خطأ في تحديد جميع الإشعارات كمقروءة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف إشعار
        /// </summary>
        private async Task DeleteNotificationAsync(Notification? notification)
        {
            if (notification == null) return;

            try
            {
                var result = MessageBox.Show($"هل تريد حذف الإشعار '{notification.Title}'؟", "تأكيد الحذف",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var success = await _notificationService.DeleteNotificationAsync(notification.Id);
                    if (success)
                    {
                        AllNotifications.Remove(notification);
                        UnreadNotifications.Remove(notification);
                        ImportantNotifications.Remove(notification);
                        UpdateCounts();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في حذف الإشعار: {notification.Id}");
                MessageBox.Show($"خطأ في حذف الإشعار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تأجيل إشعار
        /// </summary>
        private void SnoozeNotification(Notification? notification)
        {
            if (notification == null) return;

            try
            {
                // عرض خيارات التأجيل
                var snoozeDialog = new Views.Dialogs.SnoozeNotificationDialog();
                if (snoozeDialog.ShowDialog() == true)
                {
                    // تم التأجيل بنجاح
                    UnreadNotifications.Remove(notification);
                    UpdateCounts();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تأجيل الإشعار: {notification.Id}");
                MessageBox.Show($"خطأ في تأجيل الإشعار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مسح جميع الإشعارات
        /// </summary>
        private async Task ClearAllNotificationsAsync()
        {
            try
            {
                var result = MessageBox.Show("هل تريد حذف جميع الإشعارات؟ هذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    var success = await _notificationService.DeleteAllNotificationsAsync();
                    if (success)
                    {
                        AllNotifications.Clear();
                        UnreadNotifications.Clear();
                        ImportantNotifications.Clear();
                        UpdateCounts();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في مسح جميع الإشعارات");
                MessageBox.Show($"خطأ في مسح جميع الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// فتح إعدادات الإشعارات
        /// </summary>
        private void OpenNotificationSettings()
        {
            try
            {
                var settingsWindow = new NotificationSettingsWindow();
                settingsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في فتح إعدادات الإشعارات");
                MessageBox.Show($"خطأ في فتح إعدادات الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void OnNotificationReceived(object? sender, Notification notification)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                AllNotifications.Insert(0, notification);
                if (!notification.IsRead)
                {
                    UnreadNotifications.Insert(0, notification);
                }

                if (notification.Priority?.ToLower() == "high" || notification.Priority?.ToLower() == "critical")
                {
                    ImportantNotifications.Insert(0, notification);
                }

                UpdateCounts();
            });
        }

        private void OnNotificationsChanged(object? sender, EventArgs e)
        {
            Application.Current.Dispatcher.Invoke(async () =>
            {
                await LoadNotificationsAsync();
            });
        }

        private void NotificationItem_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (sender is Border border && border.Tag is Notification notification)
            {
                NotificationClicked?.Invoke(this, notification);

                // تحديد كمقروء عند النقر
                if (!notification.IsRead)
                {
                    _ = MarkAsReadAsync(notification);
                }
            }
        }

        #endregion

        #region INotifyPropertyChanged

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region Cleanup

        public void Dispose()
        {
            if (_notificationService != null)
            {
                _notificationService.NotificationReceived -= OnNotificationReceived;
                _notificationService.NotificationsChanged -= OnNotificationsChanged;
            }
        }

        #endregion
    }
}
