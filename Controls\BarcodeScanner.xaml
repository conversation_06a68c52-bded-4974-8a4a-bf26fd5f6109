<UserControl x:Class="SalesManagementSystem.Controls.BarcodeScanner"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">
    
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
    </UserControl.Resources>

    <Border Background="{DynamicResource MaterialDesignPaper}"
            CornerRadius="8"
            Padding="16">
        <StackPanel>
            <!-- Header -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                <materialDesign:PackIcon Kind="Barcode"
                                       Width="24" Height="24"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                <TextBlock Text="ماسح الباركود"
                          Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                          VerticalAlignment="Center"
                          Margin="8,0,0,0"/>
            </StackPanel>

            <!-- Manual Input -->
            <GroupBox Header="إدخال الباركود يدوياً"
                     Style="{DynamicResource MaterialDesignCardGroupBox}"
                     Margin="0,0,0,16">
                <StackPanel Margin="16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="8"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0"
                                x:Name="BarcodeTextBox"
                                Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                                materialDesign:HintAssist.Hint="أدخل الباركود"
                                Text="{Binding BarcodeText, UpdateSourceTrigger=PropertyChanged}"
                                KeyDown="BarcodeTextBox_KeyDown"/>

                        <Button Grid.Column="2"
                               Command="{Binding ScanBarcodeCommand}"
                               Style="{DynamicResource MaterialDesignRaisedButton}"
                               Width="100">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Magnify"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="بحث" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!-- Camera Scanner (Future Implementation) -->
            <GroupBox Header="مسح بالكاميرا"
                     Style="{DynamicResource MaterialDesignCardGroupBox}"
                     Margin="0,0,0,16"
                     Visibility="Collapsed">
                <StackPanel Margin="16">
                    <Button Command="{Binding StartCameraScanCommand}"
                           Style="{DynamicResource MaterialDesignOutlinedButton}"
                           HorizontalAlignment="Center">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Camera"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="تشغيل الكاميرا" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </GroupBox>

            <!-- File Scanner -->
            <GroupBox Header="مسح من ملف"
                     Style="{DynamicResource MaterialDesignCardGroupBox}"
                     Margin="0,0,0,16">
                <StackPanel Margin="16">
                    <Button Command="{Binding ScanFromFileCommand}"
                           Style="{DynamicResource MaterialDesignOutlinedButton}"
                           HorizontalAlignment="Center">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileImage"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="اختيار صورة" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </GroupBox>

            <!-- Scan Result -->
            <GroupBox Header="نتيجة المسح"
                     Style="{DynamicResource MaterialDesignCardGroupBox}"
                     Visibility="{Binding HasScanResult, Converter={StaticResource BoolToVisConverter}}">
                <StackPanel Margin="16">
                    <!-- Product Info -->
                    <Border Background="{DynamicResource MaterialDesignSelection}"
                           CornerRadius="4"
                           Padding="12"
                           Visibility="{Binding FoundProduct, Converter={StaticResource BoolToVisConverter}}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="اسم المنتج"
                                          Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                          Opacity="0.7"/>
                                <TextBlock Text="{Binding FoundProduct.Name}"
                                          FontWeight="Medium"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1">
                                <TextBlock Text="كود المنتج"
                                          Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                          Opacity="0.7"/>
                                <TextBlock Text="{Binding FoundProduct.Code}"
                                          FontWeight="Medium"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Text="السعر"
                                          Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                          Opacity="0.7"/>
                                <TextBlock Text="{Binding FoundProduct.SalePrice, StringFormat=F2}"
                                          FontWeight="Medium"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- No Product Found -->
                    <Border Background="{DynamicResource ValidationErrorBrush}"
                           CornerRadius="4"
                           Padding="12"
                           Opacity="0.1"
                           Visibility="{Binding ProductNotFound, Converter={StaticResource BoolToVisConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AlertCircle"
                                                   VerticalAlignment="Center"
                                                   Foreground="{DynamicResource ValidationErrorBrush}"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="لم يتم العثور على منتج بهذا الباركود"
                                      VerticalAlignment="Center"
                                      Foreground="{DynamicResource ValidationErrorBrush}"/>
                        </StackPanel>
                    </Border>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal"
                               HorizontalAlignment="Center"
                               Margin="0,16,0,0"
                               Visibility="{Binding FoundProduct, Converter={StaticResource BoolToVisConverter}}">
                        
                        <Button Command="{Binding AddToSaleCommand}"
                               Style="{DynamicResource MaterialDesignRaisedButton}"
                               Margin="0,0,8,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="إضافة للفاتورة" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Command="{Binding ViewProductDetailsCommand}"
                               Style="{DynamicResource MaterialDesignOutlinedButton}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Information"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="تفاصيل المنتج" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </GroupBox>

            <!-- Loading Indicator -->
            <StackPanel Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       Margin="0,16,0,0"
                       Visibility="{Binding IsScanning, Converter={StaticResource BoolToVisConverter}}">
                <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="24" Height="24"
                           Margin="0,0,8,0"/>
                <TextBlock Text="جاري البحث..."
                          VerticalAlignment="Center"/>
            </StackPanel>
        </StackPanel>
    </Border>
</UserControl>
