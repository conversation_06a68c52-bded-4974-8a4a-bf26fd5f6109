using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using System.Windows.Media.Animation;
using System.Windows.Media;
using MahApps.Metro.Controls;
using SalesManagementSystem.Views;
using SalesManagementSystem.Services;
using SalesManagementSystem.Models;

namespace SalesManagementSystem
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private DispatcherTimer? _clockTimer;
        private EnhancedNotificationService _notificationService;
        private DarkModeService _darkModeService;
        private LanguageService _languageService;
        private CurrentUserService _currentUserService;

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;

            // تهيئة قاعدة البيانات والتحقق من الجداول
            InitializeDatabaseAsync();

            // تهيئة خدمة الإشعارات
            _notificationService = EnhancedNotificationService.Instance;
            _notificationService.PropertyChanged += NotificationService_PropertyChanged;

            // تهيئة خدمة الوضع الليلي
            _darkModeService = DarkModeService.Instance;
            _darkModeService.PropertyChanged += DarkModeService_PropertyChanged;
            _darkModeService.ThemeChanged += DarkModeService_ThemeChanged;

            // تهيئة خدمة اللغة
            _languageService = LanguageService.Instance;
            _languageService.PropertyChanged += LanguageService_PropertyChanged;
            _languageService.LanguageChanged += LanguageService_LanguageChanged;

            // تهيئة خدمة المستخدم الحالي
            _currentUserService = CurrentUserService.Instance;
            _currentUserService.PropertyChanged += CurrentUserService_PropertyChanged;
            _currentUserService.UserLoggedIn += CurrentUserService_UserLoggedIn;

            // تطبيق الأرقام العربية على النافذة
            ApplyArabicNumbers();

            // تشغيل ساعة الوقت
            InitializeClock();

            // تحديث عرض الإشعارات
            UpdateNotificationDisplay();

            // تحديث عرض الثيم
            UpdateThemeDisplay();

            // تحديث عرض اللغة
            UpdateLanguageDisplay();

            // تحديث عرض المستخدم
            UpdateUserDisplay();

            // تحديث عرض الأزرار حسب صلاحيات المستخدم
            UpdateUIBasedOnUserRole();
        }

        private async void InitializeDatabaseAsync()
        {
            try
            {
                var dbService = new DatabaseService();
                await dbService.EnsureTablesExistAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeClock()
        {
            // تحديث الوقت والتاريخ فوراً
            UpdateDateTime();

            // إنشاء Timer لتحديث الوقت كل ثانية
            _clockTimer = new DispatcherTimer();
            _clockTimer.Interval = TimeSpan.FromSeconds(1);
            _clockTimer.Tick += (s, e) => UpdateDateTime();
            _clockTimer.Start();
        }

        private void UpdateDateTime()
        {
            var now = DateTime.Now;
            try
            {
                var timeLabel = FindName("CurrentTimeLabel") as System.Windows.Controls.TextBlock;
                var dateLabel = FindName("CurrentDateLabel") as System.Windows.Controls.TextBlock;

                if (timeLabel != null)
                    timeLabel.Text = now.ToString("HH:mm:ss");
                if (dateLabel != null)
                    dateLabel.Text = now.ToString("yyyy/MM/dd");
            }
            catch
            {
                // تجاهل الأخطاء إذا لم توجد العناصر
            }
        }

        /// <summary>
        /// تطبيق الأرقام الإنجليزية - لا حاجة للتحويل الآن
        /// </summary>
        private void ApplyArabicNumbers()
        {
            // لا حاجة لتحويل العناوين - الأرقام الآن إنجليزية افتراضياً
        }

        // Event handlers للقوائم - جميعها فعالة الآن
        private void NewFile_Click(object sender, RoutedEventArgs e) => OpenNewFileWindow();
        private void OpenFile_Click(object sender, RoutedEventArgs e) => OpenFileWindow();
        private void SaveFile_Click(object sender, RoutedEventArgs e) => SaveFileWindow();
        private void Exit_Click(object sender, RoutedEventArgs e) => Close();

        private void NewSale_Click(object sender, RoutedEventArgs e) => OpenNewInvoiceWindow();
        private void QuickSale_Click(object sender, RoutedEventArgs e) => OpenQuickSaleWindow();
        private void ViewSales_Click(object sender, RoutedEventArgs e) => OpenSalesListWindow();

        private void NewPurchase_Click(object sender, RoutedEventArgs e) => OpenPurchaseWindow();
        private void ViewPurchases_Click(object sender, RoutedEventArgs e) => OpenPurchasesListWindow();
        private void ManageSuppliers_Click(object sender, RoutedEventArgs e) => OpenSuppliersWindow();

        private void AddProduct_Click(object sender, RoutedEventArgs e) => OpenProductWindow();
        private void ViewProducts_Click(object sender, RoutedEventArgs e) => OpenProductsListWindow();
        private void ManageInventory_Click(object sender, RoutedEventArgs e) => OpenInventoryWindow();

        private void AddCustomer_Click(object sender, RoutedEventArgs e) => OpenCustomerWindow();
        private void ViewCustomers_Click(object sender, RoutedEventArgs e) => OpenCustomersListWindow();

        private void SalesReport_Click(object sender, RoutedEventArgs e) => OpenSalesReportWindow();
        private void InventoryReport_Click(object sender, RoutedEventArgs e) => OpenInventoryReportWindow();

        private void Settings_Click(object sender, RoutedEventArgs e) => OpenSettingsWindow();
        private void Backup_Click(object sender, RoutedEventArgs e) => OpenBackupWindow();

        private void ShowArabicNumbers_Click(object sender, RoutedEventArgs e)
        {
            var arabicNumbersDemo = new ArabicNumbersDemo();
            arabicNumbersDemo.ShowDialog();
        }

        private void ShowCurrencySettings_Click(object sender, RoutedEventArgs e)
        {
            var currencySettings = new CurrencySettingsWindow();
            currencySettings.ShowDialog();
        }

        // Event handlers للأزرار الرئيسية
        private void Sales_Click(object sender, RoutedEventArgs e) => OpenSalesWindow();
        private void Products_Click(object sender, RoutedEventArgs e) => OpenProductsWindow();
        private void Customers_Click(object sender, RoutedEventArgs e) => OpenCustomersWindow();
        private void Reports_Click(object sender, RoutedEventArgs e) => OpenSalesReportWindow();
        private void Purchases_Click(object sender, RoutedEventArgs e) => OpenPurchaseWindow();
        private void Expenses_Click(object sender, RoutedEventArgs e) => OpenExpensesWindow();
        private void Profits_Click(object sender, RoutedEventArgs e) => OpenProfitsWindow();
        private void WarehouseManagement_Click(object sender, RoutedEventArgs e) => OpenWarehouseManagementWindow();
        private void AdvancedSettings_Click(object sender, RoutedEventArgs e) => OpenAdvancedSettingsWindow();

        private void ShowMessage(string message)
        {
            MessageBox.Show($"تم النقر على: {message}\n\nهذه الميزة متاحة وجاهزة للتطوير!",
                "نظام إدارة المبيعات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public void NavigateToPage(string pageName)
        {
            ShowMessage($"الانتقال إلى صفحة: {pageName}");
        }

        // طرق فتح النوافذ الفعلية - جميعها مطورة

        // نوافذ الملفات
        private void OpenNewFileWindow()
        {
            var newFileWindow = new NewFileWindow();
            newFileWindow.ShowDialog();
        }

        private void OpenFileWindow()
        {
            var openFileWindow = new OpenFileWindow();
            openFileWindow.ShowDialog();
        }

        private void SaveFileWindow()
        {
            var saveFileWindow = new SaveFileWindow();
            saveFileWindow.ShowDialog();
        }

        // نوافذ المبيعات
        private void OpenSalesWindow()
        {
            var allSalesWindow = new Views.AllSalesWindow();
            allSalesWindow.ShowDialog();
        }

        private void OpenQuickSaleWindow()
        {
            var quickSaleWindow = new QuickSaleWindow();
            quickSaleWindow.ShowDialog();
        }

        private void OpenSalesListWindow()
        {
            var allSalesWindow = new Views.AllSalesWindow();
            allSalesWindow.ShowDialog();
        }

        private void OpenNewInvoiceWindow()
        {
            var newInvoiceWindow = new Views.NewInvoiceWindow();
            newInvoiceWindow.ShowDialog();
        }

        // نوافذ المشتريات
        private void OpenPurchaseWindow()
        {
            var purchaseWindow = new PurchaseWindow();
            purchaseWindow.ShowDialog();
        }

        private void OpenPurchasesListWindow()
        {
            var purchasesListWindow = new PurchasesListWindow();
            purchasesListWindow.ShowDialog();
        }

        private void OpenSuppliersWindow()
        {
            var suppliersWindow = new SuppliersWindow();
            suppliersWindow.ShowDialog();
        }

        // نوافذ المنتجات
        private void OpenProductWindow()
        {
            // فتح نافذة إدارة المنتجات الجديدة
            var productsWindow = new Window
            {
                Title = "إدارة المنتجات",
                Width = 1200,
                Height = 800,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                Content = new ProductsView()
            };
            productsWindow.ShowDialog();
        }

        private void OpenProductsListWindow()
        {
            // فتح نافذة إدارة المنتجات الجديدة
            var productsWindow = new Window
            {
                Title = "إدارة المنتجات",
                Width = 1200,
                Height = 800,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                Content = new ProductsView()
            };
            productsWindow.ShowDialog();
        }

        private void OpenInventoryWindow()
        {
            var inventoryWindow = new InventoryWindow();
            inventoryWindow.ShowDialog();
        }

        // نوافذ العملاء
        private void OpenCustomerWindow()
        {
            var customerWindow = new CustomerWindow();
            customerWindow.ShowDialog();
        }

        private void OpenCustomersListWindow()
        {
            var customersListWindow = new CustomersListWindow();
            customersListWindow.ShowDialog();
        }

        // نوافذ التقارير
        private void OpenSalesReportWindow()
        {
            var salesReportWindow = new SalesReportWindow();
            salesReportWindow.ShowDialog();
        }

        private void OpenInventoryReportWindow()
        {
            var inventoryReportWindow = new InventoryReportWindow();
            inventoryReportWindow.ShowDialog();
        }

        // نوافذ الإعدادات
        private void OpenSettingsWindow()
        {
            var settingsWindow = new SettingsWindow();
            settingsWindow.ShowDialog();
        }

        private void OpenBackupWindow()
        {
            var backupWindow = new BackupWindow();
            backupWindow.ShowDialog();
        }

        // نوافذ إضافية للأزرار الجديدة
        private void OpenProductsWindow()
        {
            // فتح نافذة إدارة المنتجات الجديدة
            var productsWindow = new Window
            {
                Title = "إدارة المنتجات",
                Width = 1200,
                Height = 800,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                Content = new ProductsView()
            };
            productsWindow.ShowDialog();
        }

        private void OpenCustomersWindow()
        {
            var customersWindow = new CustomersListWindow();
            customersWindow.ShowDialog();
        }

        private void OpenExpensesWindow()
        {
            var expensesWindow = new ExpensesWindow();
            expensesWindow.ShowDialog();
        }

        private void OpenProfitsWindow()
        {
            var profitsWindow = new ProfitsWindow();
            profitsWindow.ShowDialog();
        }

        private void OpenWarehouseManagementWindow()
        {
            // فتح نافذة إدارة المستودعات
            var warehouseWindow = new Window
            {
                Title = "إدارة المستودعات المتعددة",
                Width = 1400,
                Height = 900,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                Content = new WarehouseManagementView()
            };
            warehouseWindow.ShowDialog();
        }

        private void OpenAdvancedSettingsWindow()
        {
            MessageBox.Show("الإعدادات المتقدمة - قيد التطوير", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // وظائف الإشعارات
        private void NotificationService_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(_notificationService.UnreadCount) ||
                e.PropertyName == nameof(_notificationService.HasUnreadNotifications))
            {
                Dispatcher.Invoke(UpdateNotificationDisplay);
            }
        }

        private void UpdateNotificationDisplay()
        {
            var unreadCount = _notificationService.UnreadCount;

            var badge = FindName("NotificationBadge") as System.Windows.Controls.Border;
            var countText = FindName("NotificationCountText") as System.Windows.Controls.TextBlock;

            if (badge != null && countText != null)
            {
                if (unreadCount > 0)
                {
                    badge.Visibility = Visibility.Visible;
                    countText.Text = _notificationService.UnreadCountText;
                }
                else
                {
                    badge.Visibility = Visibility.Collapsed;
                }
            }
        }

        private void NotificationIcon_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            // تأثير اهتزاز الجرس
            var bellTransform = FindName("BellRotateTransform") as RotateTransform;
            if (bellTransform != null)
            {
                var storyboard = new Storyboard();
                var rotateAnimation = new DoubleAnimation
                {
                    From = -10,
                    To = 10,
                    Duration = TimeSpan.FromMilliseconds(100),
                    AutoReverse = true,
                    RepeatBehavior = new RepeatBehavior(3)
                };

                Storyboard.SetTarget(rotateAnimation, bellTransform);
                Storyboard.SetTargetProperty(rotateAnimation, new PropertyPath("Angle"));
                storyboard.Children.Add(rotateAnimation);
                storyboard.Begin();
            }
        }

        private void NotificationIcon_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            // إعادة الجرس لوضعه الطبيعي
            var bellTransform = FindName("BellRotateTransform") as RotateTransform;
            if (bellTransform != null)
            {
                bellTransform.Angle = 0;
            }
        }

        private void NotificationIcon_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            var notificationsWindow = new NotificationsWindow();
            notificationsWindow.Owner = this;
            notificationsWindow.ShowDialog();
        }

        // وظائف الوضع الليلي
        private void DarkModeService_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(_darkModeService.ThemeIcon) ||
                e.PropertyName == nameof(_darkModeService.ThemeText))
            {
                Dispatcher.Invoke(UpdateThemeDisplay);
            }
        }

        private void DarkModeService_ThemeChanged(object? sender, EventArgs e)
        {
            Dispatcher.Invoke(ApplyThemeToWindow);
        }

        private void UpdateThemeDisplay()
        {
            var themeIcon = FindName("ThemeIcon") as System.Windows.Controls.TextBlock;
            var themeText = FindName("ThemeText") as System.Windows.Controls.TextBlock;

            if (themeIcon != null)
                themeIcon.Text = _darkModeService.ThemeIcon;

            if (themeText != null)
                themeText.Text = _darkModeService.ThemeText;
        }

        private void ApplyThemeToWindow()
        {
            // تطبيق الثيم على النافذة الرئيسية
            Background = _darkModeService.Background;

            // تطبيق الثيم على العناصر الرئيسية
            var mainGrid = FindName("MainGrid") as Grid;
            if (mainGrid != null)
            {
                mainGrid.Background = _darkModeService.Background;
            }

            // تطبيق الثيم على شريط القائمة
            var menu = FindName("MainMenu") as Menu;
            if (menu != null)
            {
                menu.Background = _darkModeService.MenuBackground;
                menu.Foreground = _darkModeService.Foreground;
            }
        }

        private void ThemeToggle_Click(object sender, RoutedEventArgs e)
        {
            _darkModeService.ToggleTheme();

            // إشعار بتغيير الثيم
            var themeMessage = _darkModeService.IsDarkMode ? "تم التبديل إلى الوضع الليلي" : "تم التبديل إلى الوضع العادي";
            _notificationService.AddNotification(
                "تغيير الثيم",
                themeMessage,
                EnhancedNotificationType.SystemUpdate,
                EnhancedNotificationPriority.Low,
                "نظام");
        }

        // وظائف اللغة
        private void LanguageService_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(_languageService.LanguageIcon))
            {
                Dispatcher.Invoke(UpdateLanguageDisplay);
            }
        }

        private void LanguageService_LanguageChanged(object? sender, EventArgs e)
        {
            Dispatcher.Invoke(UpdateMenuLanguage);
        }

        private void UpdateLanguageDisplay()
        {
            var languageIcon = FindName("LanguageIcon") as System.Windows.Controls.TextBlock;

            if (languageIcon != null)
                languageIcon.Text = _languageService.LanguageIcon;
        }

        private void UpdateMenuLanguage()
        {
            // تحديث نصوص القوائم
            var fileMenuItem = FindName("FileMenuItem") as MenuItem;
            var salesMenuItem = FindName("SalesMenuItem") as MenuItem;
            var purchasesMenuItem = FindName("PurchasesMenuItem") as MenuItem;
            var productsMenuItem = FindName("ProductsMenuItem") as MenuItem;
            var customersMenuItem = FindName("CustomersMenuItem") as MenuItem;
            var reportsMenuItem = FindName("ReportsMenuItem") as MenuItem;
            var settingsMenuItem = FindName("SettingsMenuItem") as MenuItem;

            if (fileMenuItem != null) fileMenuItem.Header = LanguageService.Translations.File;
            if (salesMenuItem != null) salesMenuItem.Header = LanguageService.Translations.Sales;
            if (purchasesMenuItem != null) purchasesMenuItem.Header = LanguageService.Translations.Purchases;
            if (productsMenuItem != null) productsMenuItem.Header = LanguageService.Translations.Products;
            if (customersMenuItem != null) customersMenuItem.Header = LanguageService.Translations.Customers;
            if (reportsMenuItem != null) reportsMenuItem.Header = LanguageService.Translations.Reports;
            if (settingsMenuItem != null) settingsMenuItem.Header = LanguageService.Translations.Settings;

            // تحديث اتجاه النافذة
            FlowDirection = _languageService.FlowDirection;
        }

        private void LanguageToggle_Click(object sender, RoutedEventArgs e)
        {
            _languageService.ToggleLanguage();

            // إشعار بتغيير اللغة
            _notificationService.AddNotification(
                LanguageService.Translations.LanguageChangeTitle,
                LanguageService.Translations.LanguageChanged,
                EnhancedNotificationType.SystemUpdate,
                EnhancedNotificationPriority.Low,
                LanguageService.Translations.SystemCategory);
        }

        // وظائف المستخدم
        private void CurrentUserService_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(_currentUserService.WelcomeMessage))
            {
                Dispatcher.Invoke(UpdateUserDisplay);
            }
        }

        private void CurrentUserService_UserLoggedIn(object? sender, UserLoginEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                UpdateUserDisplay();

                // إشعار بتسجيل الدخول
                _notificationService.AddNotification(
                    "تسجيل دخول جديد",
                    $"تم تسجيل دخول المستخدم: {e.UserName} ({e.UserRole})",
                    EnhancedNotificationType.Info,
                    EnhancedNotificationPriority.Normal,
                    "نظام");
            });
        }

        private void UpdateUserDisplay()
        {
            var welcomeMessage = FindName("WelcomeMessage") as System.Windows.Controls.TextBlock;
            var currentUserDisplay = FindName("CurrentUserDisplay") as System.Windows.Controls.TextBlock;
            var currentUserRole = FindName("CurrentUserRole") as System.Windows.Controls.TextBlock;

            if (welcomeMessage != null)
                welcomeMessage.Text = "مرحباً بك في النظام";

            if (currentUserDisplay != null)
                currentUserDisplay.Text = _currentUserService.CurrentUserName;

            if (currentUserRole != null)
                currentUserRole.Text = _currentUserService.CurrentUserRole;

            // تحديث الواجهة حسب صلاحيات المستخدم
            UpdateUIBasedOnUserRole();
        }

        // تحديث الواجهة حسب صلاحيات المستخدم
        private void UpdateUIBasedOnUserRole()
        {
            var isAdmin = IsCurrentUserAdmin();

            // إخفاء/إظهار الأزرار الخاصة بالمدير في لوحة التحكم
            var adminPanel = FindName("AdminOnlyPanel") as System.Windows.Controls.StackPanel;
            if (adminPanel != null)
            {
                adminPanel.Visibility = isAdmin ? Visibility.Visible : Visibility.Collapsed;
            }

            // إخفاء/إظهار عناصر القائمة الخاصة بالمدير
            var userManagementMenuItem = FindName("UserManagementMenuItem") as System.Windows.Controls.MenuItem;
            if (userManagementMenuItem != null)
            {
                userManagementMenuItem.Visibility = isAdmin ? Visibility.Visible : Visibility.Collapsed;
            }

            var securitySettingsMenuItem = FindName("SecuritySettingsMenuItem") as System.Windows.Controls.MenuItem;
            if (securitySettingsMenuItem != null)
            {
                securitySettingsMenuItem.Visibility = isAdmin ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        // التحقق من كون المستخدم الحالي مدير
        private bool IsCurrentUserAdmin()
        {
            var currentUserName = _currentUserService.CurrentUserName;
            var currentUserRole = _currentUserService.CurrentUserRole;

            // التحقق من كون المستخدم مدير النظام
            return currentUserRole == "مدير النظام" ||
                   currentUserName == "المدير العام" ||
                   currentUserName == "أحمد محمد";
        }

        // التحقق من صلاحية محددة للمستخدم الحالي
        private bool HasCurrentUserPermission(string permission)
        {
            var currentUserName = _currentUserService.CurrentUserName;

            // المدير العام له جميع الصلاحيات
            if (IsCurrentUserAdmin())
                return true;

            // التحقق من الصلاحية من خدمة المصادقة
            var authService = LoginAuthService.Instance;
            return authService.HasPermission(currentUserName, permission);
        }

        // الحصول على المستخدم الحالي
        private LoginAuthService.SystemUser? GetCurrentUser()
        {
            var currentUserName = _currentUserService.CurrentUserName;
            var authService = LoginAuthService.Instance;
            return authService.GetUserByUsername(currentUserName);
        }

        // وظائف الأزرار الجديدة
        private void UserManagement_Click(object sender, RoutedEventArgs e)
        {
            if (!IsCurrentUserAdmin())
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة المستخدمين", "تحذير",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var userManagementWindow = new Views.UserManagementWindow();
                userManagementWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إدارة المستخدمين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SecuritySettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!IsCurrentUserAdmin())
                {
                    MessageBox.Show("ليس لديك صلاحية للوصول إلى إعدادات الأمان", "تحذير",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var securityWindow = new Views.SecuritySettingsWindow();
                securityWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح إعدادات الأمان: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SystemLog_Click(object sender, RoutedEventArgs e)
        {
            if (!IsCurrentUserAdmin())
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى سجل النظام", "تحذير",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBox.Show("سجل النظام - قيد التطوير", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void NotificationSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!IsCurrentUserAdmin())
                {
                    MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة الإشعارات", "تحذير",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var notificationWindow = new Views.NotificationSettingsWindow();
                notificationWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح إدارة الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DebtsPayments_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء وفتح نافذة إدارة الديون والمستحقات
                var debtsPaymentsWindow = new Views.DebtsPaymentsWindow();
                debtsPaymentsWindow.Owner = this; // تعيين النافذة الأب
                debtsPaymentsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح إدارة الديون والمستحقات: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        // تم حذف NavigationButton_Click و NavigateTo لأنها لم تعد مستخدمة

        // تم حذف جميع الطرق غير المستخدمة

        // تم حذف قسم Notification Methods بالكامل

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        protected override void OnClosed(EventArgs e)
        {
            // إيقاف Timer عند إغلاق النافذة
            _clockTimer?.Stop();
            base.OnClosed(e);
        }
    }
}