using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class SalesView : UserControl
    {
        private readonly DatabaseService _dbService;
        private readonly SaleService _saleService;
        private readonly ProductService _productService;
        private readonly CustomerService _customerService;
        private List<Sale> _allSales = new();

        public SalesView()
        {
            InitializeComponent();
            
            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);
            _customerService = new CustomerService(_dbService);
            _saleService = new SaleService(_dbService, _productService, _customerService);
            
            Loaded += SalesView_Loaded;
        }

        private async void SalesView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSalesAsync();
        }

        private async Task LoadSalesAsync()
        {
            try
            {
                _allSales = (await _saleService.GetAllSalesAsync()).ToList();
                SalesDataGrid.ItemsSource = _allSales;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المبيعات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddSaleButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("إضافة فاتورة جديدة - سيتم تطوير هذه الميزة قريباً", "قريباً", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ViewSale_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Sale sale)
            {
                MessageBox.Show($"عرض الفاتورة: {sale.InvoiceNumber} - سيتم تطوير هذه الميزة قريباً", "قريباً", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PrintSale_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Sale sale)
            {
                MessageBox.Show($"طباعة الفاتورة: {sale.InvoiceNumber} - سيتم تطوير هذه الميزة قريباً", "قريباً", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void EditSale_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Sale sale)
            {
                MessageBox.Show($"تعديل الفاتورة: {sale.InvoiceNumber} - سيتم تطوير هذه الميزة قريباً", "قريباً", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void DeleteSale_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Sale sale)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الفاتورة '{sale.InvoiceNumber}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _saleService.DeleteSaleAsync(sale.Id);
                        await LoadSalesAsync();
                        
                        MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح", 
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ", 
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("طباعة تقرير المبيعات - سيتم تطوير هذه الميزة قريباً", "قريباً", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadSalesAsync();
        }
    }
}
